/**
 *******************************************************************************
 * @file  ev_hc32f460_lqfp100_v2_lsm6dsl.c
 * @brief This file provides firmware functions for LSM6DSL 6-axis IMU sensor.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-11-01       CDT             First version
   2023-11-02       CDT             Add SPI interface support
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2022-2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "ev_hc32f460_lqfp100_v2_lsm6dsl.h"
#include "hc32_ll_clk.h"

/**
 * @addtogroup BSP
 * @{
 */

/**
 * @addtogroup EV_HC32F460_LQFP100_V2
 * @{
 */

/**
 * @defgroup EV_HC32F460_LQFP100_V2_LSM6DSL EV_HC32F460_LQFP100_V2 LSM6DSL
 * @{
 */

#if ((BSP_LSM6DSL_ENABLE == DDL_ON) && (BSP_EV_HC32F460_LQFP100_V2 == BSP_EV_HC32F4XX))

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/
#if (BSP_LSM6DSL_INTERFACE_TYPE == BSP_LSM6DSL_INTERFACE_SPI)
#define LSM6DSL_SPI_READ_CMD           (0x80U)  /* Read command for SPI interface */
#endif

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/
/**
 * @addtogroup EV_HC32F460_LQFP100_V2_LSM6DSL_Local_Functions
 * @{
 */
#if (BSP_LSM6DSL_INTERFACE_TYPE == BSP_LSM6DSL_INTERFACE_I2C)
static void BSP_I2C_Init(void);
static void BSP_I2C_DeInit(void);
static int32_t BSP_I2C_WriteReg(uint8_t u8Reg, const uint8_t *pu8Data, uint16_t u16Len);
static int32_t BSP_I2C_ReadReg(uint8_t u8Reg, uint8_t *pu8Data, uint16_t u16Len);
#else /* BSP_LSM6DSL_INTERFACE_SPI */
static void BSP_SPI_Init(void);
static void BSP_SPI_DeInit(void);
static void BSP_SPI_Active(void);
static void BSP_SPI_Inactive(void);
static int32_t BSP_SPI_WriteReg(uint8_t u8Reg, const uint8_t *pu8Data, uint16_t u16Len);
static int32_t BSP_SPI_ReadReg(uint8_t u8Reg, uint8_t *pu8Data, uint16_t u16Len);
#endif /* BSP_LSM6DSL_INTERFACE_TYPE */
/**
 * @}
 */

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/
/**
 * @defgroup EV_HC32F460_LQFP100_V2_LSM6DSL_Local_Variables EV_HC32F460_LQFP100_V2 LSM6DSL Local Variables
 * @{
 */
static stc_lsm6dsl_ll_t m_stcLsm6dslLL = {
    .Delay    = DDL_DelayMS,
#if (BSP_LSM6DSL_INTERFACE_TYPE == BSP_LSM6DSL_INTERFACE_I2C)
    .Init     = BSP_I2C_Init,
    .DeInit   = BSP_I2C_DeInit,
    .WriteReg = BSP_I2C_WriteReg,
    .ReadReg  = BSP_I2C_ReadReg,
#else /* BSP_LSM6DSL_INTERFACE_SPI */
    .Init     = BSP_SPI_Init,
    .DeInit   = BSP_SPI_DeInit,
    .WriteReg = BSP_SPI_WriteReg,
    .ReadReg  = BSP_SPI_ReadReg,
#endif /* BSP_LSM6DSL_INTERFACE_TYPE */
};
/**
 * @}
 */

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/
/**
 * @defgroup EV_HC32F460_LQFP100_V2_LSM6DSL_Local_Functions EV_HC32F460_LQFP100_V2 LSM6DSL Local Functions
 * @{
 */

#if (BSP_LSM6DSL_INTERFACE_TYPE == BSP_LSM6DSL_INTERFACE_I2C)
/**
 * @brief  Initialize I2C peripheral used for LSM6DSL.
 * @param  None
 * @retval None
 */
static void BSP_I2C_Init(void)
{
    stc_gpio_init_t stcGpioInit;
    stc_i2c_init_t stcI2cInit;

    /* GPIO configuration for I2C pins */
    (void)GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinDrv = PIN_HIGH_DRV;
    stcGpioInit.u16PinOutputType = PIN_OUT_TYPE_NMOS;
    (void)GPIO_Init(BSP_LSM6DSL_I2C_SCL_PORT, BSP_LSM6DSL_I2C_SCL_PIN, &stcGpioInit);
    (void)GPIO_Init(BSP_LSM6DSL_I2C_SDA_PORT, BSP_LSM6DSL_I2C_SDA_PIN, &stcGpioInit);

    /* Set I2C pin function */
    GPIO_SetFunc(BSP_LSM6DSL_I2C_SCL_PORT, BSP_LSM6DSL_I2C_SCL_PIN, BSP_LSM6DSL_I2C_SCL_PIN_FUNC);
    GPIO_SetFunc(BSP_LSM6DSL_I2C_SDA_PORT, BSP_LSM6DSL_I2C_SDA_PIN, BSP_LSM6DSL_I2C_SDA_PIN_FUNC);

    /* Configure I2C peripheral */
    (void)I2C_StructInit(&stcI2cInit);
    stcI2cInit.u32ClockDiv = I2C_CLK_DIV16;
    stcI2cInit.u32Baudrate = 400000UL;
    stcI2cInit.u32SclTime = 0UL;

    /* Enable peripheral clock */
    FCG_Fcg1PeriphClockCmd(BSP_LSM6DSL_I2C_PERIPH_CLK, ENABLE);

    /* Initialize I2C peripheral */
    (void)I2C_Init(BSP_LSM6DSL_I2C_UNIT, &stcI2cInit);
    I2C_Cmd(BSP_LSM6DSL_I2C_UNIT, ENABLE);
}

/**
 * @brief  De-Initialize I2C peripheral used for LSM6DSL.
 * @param  None
 * @retval None
 */
static void BSP_I2C_DeInit(void)
{
    /* Disable I2C peripheral */
    I2C_Cmd(BSP_LSM6DSL_I2C_UNIT, DISABLE);
    I2C_DeInit(BSP_LSM6DSL_I2C_UNIT);

    /* Disable peripheral clock */
    FCG_Fcg1PeriphClockCmd(BSP_LSM6DSL_I2C_PERIPH_CLK, DISABLE);
}

/**
 * @brief  Write data to LSM6DSL registers.
 * @param  [in]  u8Reg                Register address.
 * @param  [in]  pu8Data              Pointer to the data buffer.
 * @param  [in]  u16Len               Number of bytes to write.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
static int32_t BSP_I2C_WriteReg(uint8_t u8Reg, const uint8_t *pu8Data, uint16_t u16Len)
{
    uint16_t i;
    uint32_t u32Timeout;
    int32_t i32Ret = LL_OK;

    I2C_GenerateStartCondition(BSP_LSM6DSL_I2C_UNIT);

    /* Check start condition generate */
    u32Timeout = BSP_LSM6DSL_I2C_TIMEOUT;
    while (RESET == I2C_GetStatus(BSP_LSM6DSL_I2C_UNIT, I2C_SR_STARTF)) {
        if (0UL == (u32Timeout--)) {
            i32Ret = LL_ERR_TIMEOUT;
            break;
        }
    }

    if (i32Ret == LL_OK) {
        /* Send device address with write flag */
        I2C_WriteData(BSP_LSM6DSL_I2C_UNIT, (uint8_t)(BSP_LSM6DSL_I2C_ADDRESS << 1U));
        /* Check ACK */
        u32Timeout = BSP_LSM6DSL_I2C_TIMEOUT;
        while (SET != I2C_GetStatus(BSP_LSM6DSL_I2C_UNIT, I2C_SR_TRA)) {
            if (0UL == (u32Timeout--)) {
                i32Ret = LL_ERR_TIMEOUT;
                break;
            }
        }
    }

    if (i32Ret == LL_OK) {
        /* Check ACK */
        u32Timeout = BSP_LSM6DSL_I2C_TIMEOUT;
        while (SET == I2C_GetStatus(BSP_LSM6DSL_I2C_UNIT, I2C_SR_NACKF)) {
            if (0UL == (u32Timeout--)) {
                i32Ret = LL_ERR_TIMEOUT;
                break;
            }
        }
    }

    if (i32Ret == LL_OK) {
        /* Send register address */
        I2C_WriteData(BSP_LSM6DSL_I2C_UNIT, u8Reg);
        /* Check transmission completed */
        u32Timeout = BSP_LSM6DSL_I2C_TIMEOUT;
        while (RESET == I2C_GetStatus(BSP_LSM6DSL_I2C_UNIT, I2C_SR_TENDF)) {
            if (0UL == (u32Timeout--)) {
                i32Ret = LL_ERR_TIMEOUT;
                break;
            }
        }
    }

    if (i32Ret == LL_OK) {
        /* Write data */
        for (i = 0U; i < u16Len; i++) {
            I2C_WriteData(BSP_LSM6DSL_I2C_UNIT, pu8Data[i]);
            /* Check transmission completed */
            u32Timeout = BSP_LSM6DSL_I2C_TIMEOUT;
            while (RESET == I2C_GetStatus(BSP_LSM6DSL_I2C_UNIT, I2C_SR_TENDF)) {
                if (0UL == (u32Timeout--)) {
                    i32Ret = LL_ERR_TIMEOUT;
                    break;
                }
            }
            if (i32Ret != LL_OK) {
                break;
            }
            /* Clear flag */
            I2C_ClearStatus(BSP_LSM6DSL_I2C_UNIT, I2C_CLR_TENDF);
        }
    }

    /* Generate stop condition */
    I2C_GenerateStopCondition(BSP_LSM6DSL_I2C_UNIT);

    return i32Ret;
}

/**
 * @brief  Read data from LSM6DSL registers.
 * @param  [in]  u8Reg                Register address.
 * @param  [out] pu8Data              Pointer to the data buffer.
 * @param  [in]  u16Len               Number of bytes to read.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
static int32_t BSP_I2C_ReadReg(uint8_t u8Reg, uint8_t *pu8Data, uint16_t u16Len)
{
    uint16_t i;
    uint32_t u32Timeout;
    int32_t i32Ret = LL_OK;

    /* Send register address */
    i32Ret = BSP_I2C_WriteReg(u8Reg, NULL, 0U);
    if (i32Ret != LL_OK) {
        return i32Ret;
    }

    /* Generate start condition */
    I2C_GenerateStartCondition(BSP_LSM6DSL_I2C_UNIT);

    /* Check start condition generate */
    u32Timeout = BSP_LSM6DSL_I2C_TIMEOUT;
    while (RESET == I2C_GetStatus(BSP_LSM6DSL_I2C_UNIT, I2C_SR_STARTF)) {
        if (0UL == (u32Timeout--)) {
            i32Ret = LL_ERR_TIMEOUT;
            break;
        }
    }

    if (i32Ret == LL_OK) {
        /* Send device address with read flag */
        I2C_WriteData(BSP_LSM6DSL_I2C_UNIT, (uint8_t)((BSP_LSM6DSL_I2C_ADDRESS << 1U) | 0x01U));
        /* Check ACK */
        u32Timeout = BSP_LSM6DSL_I2C_TIMEOUT;
        while (SET != I2C_GetStatus(BSP_LSM6DSL_I2C_UNIT, I2C_SR_TRA)) {
            if (0UL == (u32Timeout--)) {
                i32Ret = LL_ERR_TIMEOUT;
                break;
            }
        }
    }

    if (i32Ret == LL_OK) {
        /* Check ACK */
        u32Timeout = BSP_LSM6DSL_I2C_TIMEOUT;
        while (SET == I2C_GetStatus(BSP_LSM6DSL_I2C_UNIT, I2C_SR_NACKF)) {
            if (0UL == (u32Timeout--)) {
                i32Ret = LL_ERR_TIMEOUT;
                break;
            }
        }
    }

    if (i32Ret == LL_OK) {
        /* Read data */
        for (i = 0U; i < u16Len; i++) {
            if (i == (u16Len - 1U)) {
                /* For last byte, generate NACK */
                I2C_AckConfig(BSP_LSM6DSL_I2C_UNIT, I2C_NACK);
            }

            /* Wait until rx data register is full */
            u32Timeout = BSP_LSM6DSL_I2C_TIMEOUT;
            while (RESET == I2C_GetStatus(BSP_LSM6DSL_I2C_UNIT, I2C_SR_RFULLF)) {
                if (0UL == (u32Timeout--)) {
                    i32Ret = LL_ERR_TIMEOUT;
                    break;
                }
            }
            if (i32Ret != LL_OK) {
                break;
            }

            /* Read data from rx register */
            pu8Data[i] = I2C_ReadData(BSP_LSM6DSL_I2C_UNIT);
        }
    }

    /* Generate stop condition */
    I2C_GenerateStopCondition(BSP_LSM6DSL_I2C_UNIT);
    
    /* Restore ACK state */
    I2C_AckConfig(BSP_LSM6DSL_I2C_UNIT, I2C_ACK);

    return i32Ret;
}
#else /* BSP_LSM6DSL_INTERFACE_SPI */
/**
 * @brief  SPI CS pin active.
 * @param  None
 * @retval None
 */
static void BSP_SPI_Active(void)
{
    BSP_LSM6DSL_CS_ACTIVE();
}

/**
 * @brief  SPI CS pin inactive.
 * @param  None
 * @retval None
 */
static void BSP_SPI_Inactive(void)
{
    BSP_LSM6DSL_CS_INACTIVE();
}

/**
 * @brief  Initialize SPI peripheral used for LSM6DSL.
 * @param  None
 * @retval None
 */
static void BSP_SPI_Init(void)
{
    stc_gpio_init_t stcGpioInit;
    stc_spi_init_t stcSpiInit;
    uint32_t u32BusFreq;
    uint32_t u32Cnt = 0UL;
    uint32_t u32TargetHz = BSP_LSM6DSL_SPI_MAX_HZ; /* 1MHz */

    /* GPIO configuration for SPI pins */
    (void)GPIO_StructInit(&stcGpioInit);
    /* SPI CS pin */
    stcGpioInit.u16PinState = PIN_STAT_SET;
    stcGpioInit.u16PinDir = PIN_DIR_OUT;
    stcGpioInit.u16PullUp = PIN_PU_ON;
    (void)GPIO_Init(BSP_LSM6DSL_CS_PORT, BSP_LSM6DSL_CS_PIN, &stcGpioInit);


    (void)GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinDrv = PIN_HIGH_DRV;
    (void)GPIO_Init(BSP_LSM6DSL_SCK_PORT, BSP_LSM6DSL_SCK_PIN, &stcGpioInit);
    (void)GPIO_Init(BSP_LSM6DSL_MOSI_PORT, BSP_LSM6DSL_MOSI_PIN, &stcGpioInit);
    (void)GPIO_Init(BSP_LSM6DSL_MISO_PORT, BSP_LSM6DSL_MISO_PIN, &stcGpioInit);

    /* Set CS pin to high */
    BSP_SPI_Inactive();

    /* Set SPI pin function */
    GPIO_SetFunc(BSP_LSM6DSL_SCK_PORT, BSP_LSM6DSL_SCK_PIN, BSP_LSM6DSL_SCK_PIN_FUNC);
    GPIO_SetFunc(BSP_LSM6DSL_MOSI_PORT, BSP_LSM6DSL_MOSI_PIN, BSP_LSM6DSL_MOSI_PIN_FUNC);
    GPIO_SetFunc(BSP_LSM6DSL_MISO_PORT, BSP_LSM6DSL_MISO_PIN, BSP_LSM6DSL_MISO_PIN_FUNC);

    /* Configure SPI peripheral */
    (void)SPI_StructInit(&stcSpiInit);
    stcSpiInit.u32WireMode          = SPI_3_WIRE;
    stcSpiInit.u32TransMode         = SPI_FULL_DUPLEX;
    stcSpiInit.u32MasterSlave       = SPI_MASTER;
    // stcSpiInit.u32ModeFaultDetect   = SPI_MD_FAULT_DETECT_DISABLE;
    // stcSpiInit.u32Parity            = SPI_PARITY_INVD;
    stcSpiInit.u32SpiMode           = SPI_MD_3;          /* Use SPI mode 3 (CPOL=1, CPHA=1) */
    stcSpiInit.u32FirstBit          = SPI_FIRST_MSB;

    /* Set baud rate close to 1MHz */
    /* Get BUS clock */
    u32BusFreq = CLK_GetBusClockFreq(CLK_BUS_PCLK1);
    while (u32TargetHz < u32BusFreq / (1UL << (u32Cnt + 1U)))
    {
        u32Cnt++;
        if (u32Cnt >= 7UL)  /* Max division is 2^8 = 256 */
        {
            break;
        }
    }
    stcSpiInit.u32BaudRatePrescaler = (u32Cnt << SPI_CFG2_MBR_POS);

    stcSpiInit.u32DataBits          = SPI_DATA_SIZE_8BIT;

    /* Enable peripheral clock */
    FCG_Fcg1PeriphClockCmd(BSP_LSM6DSL_SPI_PERIPH_CLK, ENABLE);

    /* Initialize SPI peripheral */
    (void)SPI_Init(BSP_LSM6DSL_SPI_UNIT, &stcSpiInit);
    SPI_Cmd(BSP_LSM6DSL_SPI_UNIT, ENABLE);
}

/**
 * @brief  De-Initialize SPI peripheral used for LSM6DSL.
 * @param  None
 * @retval None
 */
static void BSP_SPI_DeInit(void)
{
    /* Disable SPI peripheral */
    SPI_Cmd(BSP_LSM6DSL_SPI_UNIT, DISABLE);
    SPI_DeInit(BSP_LSM6DSL_SPI_UNIT);

    /* Disable peripheral clock */
    FCG_Fcg1PeriphClockCmd(BSP_LSM6DSL_SPI_PERIPH_CLK, DISABLE);
}

/**
 * @brief  SPI transmit and receive data.
 * @param  [in]  pu8TxData            Pointer to the data to be transmitted.
 * @param  [out] pu8RxData            Pointer to the buffer to store the received data.
 * @param  [in]  u16Size              The number of bytes to transmit/receive.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_TIMEOUT:        SPI timeout.
 */
static int32_t BSP_SPI_TransReceive(const uint8_t *pu8TxData, uint8_t *pu8RxData, uint16_t u16Size)
{
    uint16_t i;
    uint32_t u32Timeout;
    uint8_t u8DummyByte = 0xFFU;
    uint8_t u8ReceivedData;
    int32_t i32Ret = LL_OK;

    /* Activate the CS line */
    BSP_SPI_Active();

    for (i = 0U; i < u16Size; i++) {
        /* Send data */
        if (pu8TxData == NULL) {
            SPI_WriteData(BSP_LSM6DSL_SPI_UNIT, u8DummyByte);
        } else {
            SPI_WriteData(BSP_LSM6DSL_SPI_UNIT, pu8TxData[i]);
        }

        /* Wait for transmit to complete */
        u32Timeout = BSP_LSM6DSL_SPI_TIMEOUT;
        while (RESET == SPI_GetStatus(BSP_LSM6DSL_SPI_UNIT, SPI_FLAG_TX_BUF_EMPTY)) {
            if (0UL == (u32Timeout--)) {
                i32Ret = LL_ERR_TIMEOUT;
                break;
            }
        }
        if (i32Ret != LL_OK) {
            break;
        }

        /* Wait for receive to complete */
        u32Timeout = BSP_LSM6DSL_SPI_TIMEOUT;
        while (RESET == SPI_GetStatus(BSP_LSM6DSL_SPI_UNIT, SPI_FLAG_RX_BUF_FULL)) {
            if (0UL == (u32Timeout--)) {
                i32Ret = LL_ERR_TIMEOUT;
                break;
            }
        }
        if (i32Ret != LL_OK) {
            break;
        }

        /* Read data */
        u8ReceivedData = (uint8_t)SPI_ReadData(BSP_LSM6DSL_SPI_UNIT);
        if (pu8RxData != NULL) {
            pu8RxData[i] = u8ReceivedData;
        }
    }

    /* Deactivate the CS line */
    BSP_SPI_Inactive();

    return i32Ret;
}

/**
 * @brief  Write data to LSM6DSL registers.
 * @param  [in]  u8Reg                Register address.
 * @param  [in]  pu8Data              Pointer to the data buffer.
 * @param  [in]  u16Len               Number of bytes to write.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
static int32_t BSP_SPI_WriteReg(uint8_t u8Reg, const uint8_t *pu8Data, uint16_t u16Len)
{
    uint8_t au8TxBuffer[32];
    uint16_t i;
    int32_t i32Ret = LL_OK;

    /* Check buffer size */
    if (u16Len > (sizeof(au8TxBuffer) - 1U)) {
        return LL_ERR_INVD_PARAM;
    }

    /* Prepare the buffer to send with register address and data */
    au8TxBuffer[0] = u8Reg;
    if ((pu8Data != NULL) && (u16Len > 0U)) {
        for (i = 0U; i < u16Len; i++) {
            au8TxBuffer[i + 1U] = pu8Data[i];
        }
    }

    /* Send the buffer */
    i32Ret = BSP_SPI_TransReceive(au8TxBuffer, NULL, u16Len + 1U);

    return i32Ret;
}

/**
 * @brief  Read data from LSM6DSL registers.
 * @param  [in]  u8Reg                Register address.
 * @param  [out] pu8Data              Pointer to the data buffer.
 * @param  [in]  u16Len               Number of bytes to read.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
static int32_t BSP_SPI_ReadReg(uint8_t u8Reg, uint8_t *pu8Data, uint16_t u16Len)
{
    uint8_t au8TxBuffer[32];
    uint8_t au8RxBuffer[32];
    uint16_t i;
    int32_t i32Ret = LL_OK;

    /* Check buffer size */
    if (u16Len > (sizeof(au8RxBuffer) - 1U)) {
        return LL_ERR_INVD_PARAM;
    }

    /* Prepare the read command */
    au8TxBuffer[0] = u8Reg | LSM6DSL_SPI_READ_CMD; /* Add read bit for SPI */

    /* Fill the rest of buffer with dummy bytes */
    for (i = 1U; i <= u16Len; i++) {
        au8TxBuffer[i] = 0xFFU;
    }

    /* Send the command and read data */
    i32Ret = BSP_SPI_TransReceive(au8TxBuffer, au8RxBuffer, u16Len + 1U);
    if ((i32Ret == LL_OK) && (pu8Data != NULL)) {
        /* Copy the received data (skipping first byte which is dummy) */
        for (i = 0U; i < u16Len; i++) {
            pu8Data[i] = au8RxBuffer[i + 1U];
        }
    }

    return i32Ret;
}
#endif /* BSP_LSM6DSL_INTERFACE_TYPE */

/**
 * @}
 */

/**
 * @defgroup EV_HC32F460_LQFP100_V2_LSM6DSL_Global_Functions EV_HC32F460_LQFP100_V2 LSM6DSL Global Functions
 * @{
 */

/**
 * @brief  Initializes BSP LSM6DSL.
 * @param  None
 * @retval None
 */
void BSP_LSM6DSL_Init(void)
{
    (void)LSM6DSL_Init(&m_stcLsm6dslLL, LSM6DSL_ACCEL_SCALE_2G, 
                        LSM6DSL_GYRO_SCALE_250DPS, LSM6DSL_ODR_104HZ);
}

/**
 * @brief  De-Initializes BSP LSM6DSL.
 * @param  None
 * @retval None
 */
void BSP_LSM6DSL_DeInit(void)
{
    (void)LSM6DSL_DeInit(&m_stcLsm6dslLL);
}

/**
 * @brief  Read accelerometer data.
 * @param  [out] pstcAccel            Pointer to acceleration data structure.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t BSP_LSM6DSL_ReadAccel(stc_lsm6dsl_axis_t *pstcAccel)
{
    return LSM6DSL_ReadAccel(&m_stcLsm6dslLL, pstcAccel);
}

/**
 * @brief  Read gyroscope data.
 * @param  [out] pstcGyro             Pointer to gyroscope data structure.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t BSP_LSM6DSL_ReadGyro(stc_lsm6dsl_axis_t *pstcGyro)
{
    return LSM6DSL_ReadGyro(&m_stcLsm6dslLL, pstcGyro);
}

/**
 * @brief  Read temperature data.
 * @param  [out] pfTemp               Pointer to temperature value (Celsius).
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t BSP_LSM6DSL_ReadTemp(float *pfTemp)
{
    return LSM6DSL_ReadTemp(&m_stcLsm6dslLL, pfTemp);
}

/**
 * @brief  Read all sensor data (accelerometer, gyroscope, temperature).
 * @param  [out] pstcData             Pointer to sensor data structure.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t BSP_LSM6DSL_ReadSensor(stc_lsm6dsl_data_t *pstcData)
{
    return LSM6DSL_ReadSensor(&m_stcLsm6dslLL, pstcData);
}

/**
 * @brief  Configure INT1/INT2 interrupts.
 * @param  [in]  u8IntPin             Interrupt pin (0: INT1, 1: INT2).
 * @param  [in]  u8IntType            Interrupt type (bit mask, refer to datasheet).
 * @param  [in]  enNewState           Interrupt state (ENABLE or DISABLE).
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t BSP_LSM6DSL_ConfigInt(uint8_t u8IntPin, uint8_t u8IntType, en_functional_state_t enNewState)
{
    uint8_t u8RegValue;
    uint8_t u8RegAddr;
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if (u8IntPin > 1U) {
        return LL_ERR_INVD_PARAM;
    }

    /* Select the interrupt control register */
    u8RegAddr = (u8IntPin == 0U) ? LSM6DSL_INT1_CTRL : LSM6DSL_INT2_CTRL;

    /* Read current register value */
#if (BSP_LSM6DSL_INTERFACE_TYPE == BSP_LSM6DSL_INTERFACE_I2C)
    i32Ret = BSP_I2C_ReadReg(u8RegAddr, &u8RegValue, 1U);
#else /* BSP_LSM6DSL_INTERFACE_SPI */
    i32Ret = BSP_SPI_ReadReg(u8RegAddr, &u8RegValue, 1U);
#endif /* BSP_LSM6DSL_INTERFACE_TYPE */
    if (i32Ret == LL_OK) {
        /* Update register value */
        if (enNewState == ENABLE) {
            u8RegValue |= u8IntType;
        } else {
            u8RegValue &= (uint8_t)(~u8IntType);
        }

        /* Write back to register */
#if (BSP_LSM6DSL_INTERFACE_TYPE == BSP_LSM6DSL_INTERFACE_I2C)
        i32Ret = BSP_I2C_WriteReg(u8RegAddr, &u8RegValue, 1U);
#else /* BSP_LSM6DSL_INTERFACE_SPI */
        i32Ret = BSP_SPI_WriteReg(u8RegAddr, &u8RegValue, 1U);
#endif /* BSP_LSM6DSL_INTERFACE_TYPE */
    }

    return i32Ret;
}

static es_async_t BSP_SPI_Co_TransReceive(es_coro_t *coro, const uint8_t *pu8TxData, uint8_t *pu8RxData, uint16_t u16Size)
{
    static uint16_t i;
    uint32_t u32Timeout;
    uint8_t u8DummyByte = 0xFFU;
    uint8_t u8ReceivedData;
    int32_t i32Ret = LL_OK;

    es_co_begin(coro);

    /* Activate the CS line */
    BSP_SPI_Active();

    for (i = 0U; i < u16Size; i++) {
        /* Send data */
        if (pu8TxData == NULL) {
            SPI_WriteData(BSP_LSM6DSL_SPI_UNIT, u8DummyByte);
        } else {
            SPI_WriteData(BSP_LSM6DSL_SPI_UNIT, pu8TxData[i]);
        }

        es_co_wait_timeout_ex(err, SPI_GetStatus(BSP_LSM6DSL_SPI_UNIT, SPI_FLAG_TX_BUF_EMPTY) != RESET, 50U);

        es_co_wait_timeout_ex(err, SPI_GetStatus(BSP_LSM6DSL_SPI_UNIT, SPI_FLAG_RX_BUF_FULL) != RESET, 50U);

        /* Read data */
        u8ReceivedData = (uint8_t)SPI_ReadData(BSP_LSM6DSL_SPI_UNIT);
        if (pu8RxData != NULL) {
            pu8RxData[i] = u8ReceivedData;
        }
    }

    /* Deactivate the CS line */
    BSP_SPI_Inactive();

    es_co_eee(BSP_SPI_Inactive());
}

/**
 * @brief  Write data to LSM6DSL registers.
 * @param  [in]  u8Reg                Register address.
 * @param  [in]  pu8Data              Pointer to the data buffer.
 * @param  [in]  u16Len               Number of bytes to write.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
static es_async_t BSP_SPI_Co_WriteReg(es_coro_t *coro, uint8_t u8Reg, const uint8_t *pu8Data, uint16_t u16Len)
{
    static uint8_t au8TxBuffer[32] = {0};
    static uint16_t i;
    int32_t i32Ret = LL_OK;

    es_co_begin(coro);

    /* Check buffer size */
    if (u16Len > (sizeof(au8TxBuffer) - 1U)) {
        // return LL_ERR_INVD_PARAM;
        es_co_err;
    }

    /* Prepare the buffer to send with register address and data */
    au8TxBuffer[0] = u8Reg;
    if ((pu8Data != NULL) && (u16Len > 0U)) {
        for (i = 0U; i < u16Len; i++) {
            au8TxBuffer[i + 1U] = pu8Data[i];
        }
    }

    /* Send the buffer */
    // i32Ret = BSP_SPI_TransReceive(au8TxBuffer, NULL, u16Len + 1U);
    es_co_await_ex(err, BSP_SPI_Co_TransReceive, au8TxBuffer, NULL, u16Len + 1U);

    es_co_eee();
}

/**
 * @brief  Read data from LSM6DSL registers.
 * @param  [in]  u8Reg                Register address.
 * @param  [out] pu8Data              Pointer to the data buffer.
 * @param  [in]  u16Len               Number of bytes to read.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
static es_async_t BSP_SPI_Co_ReadReg(es_coro_t *coro, uint8_t u8Reg, uint8_t *pu8Data, uint16_t u16Len)
{
    static uint8_t au8TxBuffer[16] = {0};
    static uint8_t au8RxBuffer[16] = {0};
    static uint16_t i;
    int32_t i32Ret = LL_OK;

    es_co_begin(coro);

    /* Check buffer size */
    if (u16Len > (sizeof(au8RxBuffer) - 1U)) {
        // return LL_ERR_INVD_PARAM;
        es_co_err;
    }

    /* Prepare the read command */
    au8TxBuffer[0] = u8Reg | LSM6DSL_SPI_READ_CMD; /* Add read bit for SPI */

    /* Fill the rest of buffer with dummy bytes */
    for (i = 1U; i <= u16Len; i++) {
        au8TxBuffer[i] = 0xFFU;
    }

    /* Send the command and read data */
    // i32Ret = BSP_SPI_TransReceive(au8TxBuffer, au8RxBuffer, u16Len + 1U);
    es_co_await_ex(err, BSP_SPI_Co_TransReceive, au8TxBuffer, au8RxBuffer, u16Len + 1U);

    for (i = 0U; i < u16Len; i++) {
        pu8Data[i] = au8RxBuffer[i + 1U];
    }

    es_co_eee();
}

static uint8_t au8Data[6] = {0};
static uint8_t u8RegValue;
static float fSensitivity;

es_async_t BSP_LSM6DSL_Co_ReadAccel(es_coro_t *coro, stc_lsm6dsl_axis_t *pstcAccel) {

    
    es_co_begin(coro);

    // 获取当前加速度量程设置
    es_co_await_ex(err, BSP_SPI_Co_ReadReg, LSM6DSL_CTRL1_XL, &u8RegValue, 1U);
    
    // 根据量程设置获取对应的灵敏度
    switch(u8RegValue & 0x0CU) {
        case LSM6DSL_ACCEL_SCALE_2G:
            fSensitivity = LSM6DSL_ACCEL_SCALE_FACTOR_2G;
            break;
        case LSM6DSL_ACCEL_SCALE_4G:
            fSensitivity = LSM6DSL_ACCEL_SCALE_FACTOR_4G;
            break;
        case LSM6DSL_ACCEL_SCALE_8G:
            fSensitivity = LSM6DSL_ACCEL_SCALE_FACTOR_8G;
            break;
        case LSM6DSL_ACCEL_SCALE_16G:
            fSensitivity = LSM6DSL_ACCEL_SCALE_FACTOR_16G;
            break;
        default:
            fSensitivity = LSM6DSL_ACCEL_SCALE_FACTOR_2G;
            break;
    }

    // 读取原始加速度数据
    es_co_await_ex(err, BSP_SPI_Co_ReadReg, LSM6DSL_OUTX_L_XL, au8Data, 6U);
    
    // 转换原始数据并应用灵敏度
    pstcAccel->x = (int16_t)((uint16_t)au8Data[1] << 8U) | au8Data[0];
    pstcAccel->y = (int16_t)((uint16_t)au8Data[3] << 8U) | au8Data[2];
    pstcAccel->z = (int16_t)((uint16_t)au8Data[5] << 8U) | au8Data[4];

    // 应用灵敏度值
    pstcAccel->x = (int16_t)((float)pstcAccel->x * fSensitivity);
    pstcAccel->y = (int16_t)((float)pstcAccel->y * fSensitivity);
    pstcAccel->z = (int16_t)((float)pstcAccel->z * fSensitivity);

    es_co_eee();
}

es_async_t BSP_LSM6DSL_Co_ReadGyro(es_coro_t *coro, stc_lsm6dsl_axis_t *pstcGyro) {
    es_co_begin(coro);

    // 获取当前陀螺仪量程设置
    es_co_await_ex(err, BSP_SPI_Co_ReadReg, LSM6DSL_CTRL2_G, &u8RegValue, 1U);
    
    // 根据量程设置获取对应的灵敏度
    switch(u8RegValue & 0x0CU) {
        case LSM6DSL_GYRO_SCALE_250DPS:
            fSensitivity = LSM6DSL_GYRO_SCALE_FACTOR_250;
            break;
        case LSM6DSL_GYRO_SCALE_500DPS:
            fSensitivity = LSM6DSL_GYRO_SCALE_FACTOR_500;
            break;
        case LSM6DSL_GYRO_SCALE_1000DPS:
            fSensitivity = LSM6DSL_GYRO_SCALE_FACTOR_1000;
            break;
        case LSM6DSL_GYRO_SCALE_2000DPS:
            fSensitivity = LSM6DSL_GYRO_SCALE_FACTOR_2000;
            break;
        default:
            fSensitivity = LSM6DSL_GYRO_SCALE_FACTOR_250;
            break;
    }

    // 读取原始陀螺仪数据
    es_co_await_ex(err, BSP_SPI_Co_ReadReg, LSM6DSL_OUTX_L_G, au8Data, 6U);
    
    // 转换原始数据并应用灵敏度
    pstcGyro->x = (int16_t)((uint16_t)au8Data[1] << 8U) | au8Data[0];
    pstcGyro->y = (int16_t)((uint16_t)au8Data[3] << 8U) | au8Data[2];
    pstcGyro->z = (int16_t)((uint16_t)au8Data[5] << 8U) | au8Data[4];

    // 应用灵敏度值
    pstcGyro->x = (int16_t)((float)pstcGyro->x * fSensitivity);
    pstcGyro->y = (int16_t)((float)pstcGyro->y * fSensitivity);
    pstcGyro->z = (int16_t)((float)pstcGyro->z * fSensitivity);

    es_co_eee();
}

es_async_t BSP_LSM6DSL_Co_ReadTemp(es_coro_t *coro, float *pfTemp) {
    int16_t i16RawTemp;
    es_co_begin(coro);

    es_co_await_ex(err, BSP_SPI_Co_ReadReg, LSM6DSL_OUT_TEMP_L, au8Data, 2U);
    i16RawTemp = (int16_t)((uint16_t)au8Data[1] << 8U) | au8Data[0];
    /* Temperature in degree Celsius: T = 25 + (Raw / 16) */
    *pfTemp = (float)i16RawTemp / 16.0f + 25.0f;

    es_co_eee();
}

/**
 * @}
 */

#endif /* (BSP_LSM6DSL_ENABLE && BSP_EV_HC32F460_LQFP100_V2) */

/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

/******************************************************************************
 * EOF (not truncated)
 *****************************************************************************/ 
