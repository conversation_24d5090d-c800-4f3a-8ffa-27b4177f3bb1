#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON二进制转换器
将规则引擎JSON转换为二进制格式，支持变长编码、枚举编码和差值编码
"""

import json
import sys
from typing import Dict, List, Any, Tuple
from io import BytesIO


class RuleEngineConverter:
    def __init__(self):
        self.messages = []
        self.can_id_to_index = {}
        self.signal_cache = {}  # 用于检测信号冲突

    def parse_id_value(self, value) -> int:
        """
        解析ID值，支持十进制和十六进制格式

        Args:
            value: ID值，可以是int、str（十进制）或str（0x开头的十六进制）

        Returns:
            int: 解析后的整数值

        Raises:
            ValueError: 如果值无法解析或为负数
        """
        if isinstance(value, int):
            if value < 0:
                raise ValueError(f"ID值不能为负数: {value}")
            return value
        elif isinstance(value, str):
            value = value.strip()
            if value.lower().startswith('0x'):
                # 十六进制格式
                try:
                    parsed_value = int(value, 16)
                    if parsed_value < 0:
                        raise ValueError(f"ID值不能为负数: {value} (解析为 {parsed_value})")
                    return parsed_value
                except ValueError as e:
                    if "invalid literal" in str(e):
                        raise ValueError(f"无效的十六进制ID格式: {value}")
                    raise
            else:
                # 十进制格式
                try:
                    parsed_value = int(value)
                    if parsed_value < 0:
                        raise ValueError(f"ID值不能为负数: {value}")
                    return parsed_value
                except ValueError:
                    raise ValueError(f"无效的十进制ID格式: {value}")
        else:
            raise ValueError(f"不支持的ID值类型: {type(value)}, 值: {value}")

    def validate_signal_consistency(self, can_id: int, signal: Dict[str, Any]) -> None:
        """
        验证信号的一致性
        如果同一个信号ID在同一个CAN ID下有不同的start_bit或len，则报错
        """
        signal_id = self.parse_id_value(signal['id'])
        start_bit = signal['start_bit']
        length = signal['len']

        # 创建信号的唯一标识
        signal_key = (can_id, signal_id)

        if signal_key in self.signal_cache:
            cached_signal = self.signal_cache[signal_key]
            if (cached_signal['start_bit'] != start_bit or
                cached_signal['len'] != length):
                raise ValueError(
                    f"信号冲突检测到错误！\n"
                    f"CAN ID: {can_id} (0x{can_id:X})\n"
                    f"信号ID: {signal_id}\n"
                    f"已存在的信号: start_bit={cached_signal['start_bit']}, len={cached_signal['len']}\n"
                    f"冲突的信号: start_bit={start_bit}, len={length}\n"
                    f"错误原因: 同一个CAN ID下的同一个信号ID不能有不同的start_bit或len值"
                )
        else:
            self.signal_cache[signal_key] = {
                'start_bit': start_bit,
                'len': length
            }

    def process_message(self, can_id: int, signals: List[Dict[str, Any]]) -> int:
        """
        处理消息，返回消息在messages数组中的索引
        """
        # 验证所有信号的一致性
        for signal in signals:
            self.validate_signal_consistency(can_id, signal)

        # 检查是否已经存在相同的消息
        if can_id in self.can_id_to_index:
            return self.can_id_to_index[can_id]

        # 创建新的消息，确保信号ID被解析为整数
        parsed_signals = []
        for signal in signals:
            parsed_signal = signal.copy()
            parsed_signal['id'] = self.parse_id_value(signal['id'])
            parsed_signals.append(parsed_signal)

        message = {
            "id": can_id,
            "signals": parsed_signals
        }

        # 添加到messages数组
        message_index = len(self.messages)
        self.messages.append(message)
        self.can_id_to_index[can_id] = message_index

        return message_index

    def get_signal_indices(self, can_id: int, signals: List[Dict[str, Any]]) -> List[int]:
        """
        获取信号在对应消息中的索引列表
        """
        message_index = self.can_id_to_index[can_id]
        message_signals = self.messages[message_index]['signals']

        signal_indices = []
        for signal in signals:
            signal_id = self.parse_id_value(signal['id'])
            # 在消息的signals数组中查找对应的信号索引
            for i, msg_signal in enumerate(message_signals):
                if msg_signal['id'] == signal_id:
                    signal_indices.append(i)
                    break
            else:
                raise ValueError(f"在CAN ID {can_id}中找不到信号ID {signal_id}")

        return signal_indices

    def convert(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换JSON数据
        """
        try:
            # 重置状态
            self.messages = []
            self.can_id_to_index = {}
            self.signal_cache = {}

            # 复制输入数据
            output_data = input_data.copy()

            # 处理frames
            if 'frames' not in input_data:
                raise ValueError("输入JSON缺少frames字段")

            new_frames = []
            for frame_idx, frame in enumerate(input_data['frames']):
                try:
                    new_frame = frame.copy()
                    new_data = []

                    # 处理每个frame的data
                    for data_item in frame['data']:
                        can_id = self.parse_id_value(data_item['id'])
                        signals = data_item['signals']

                        # 处理消息，获取消息索引
                        message_index = self.process_message(can_id, signals)

                        # 获取信号索引
                        signal_indices = self.get_signal_indices(can_id, signals)

                        # 创建新的data项
                        new_data_item = {
                            "id": message_index,
                            "signals": signal_indices
                        }
                        new_data.append(new_data_item)

                    new_frame['data'] = new_data
                    new_frames.append(new_frame)

                except Exception as e:
                    raise ValueError(f"处理frame[{frame_idx}]时出错: {str(e)}")

            # 对messages按ID排序
            sorted_messages = sorted(self.messages, key=lambda x: x['id'])

            # 对每个message中的signals按ID排序
            for message in sorted_messages:
                message['signals'] = sorted(message['signals'], key=lambda x: x['id'])

            # 重新建立CAN ID到新索引的映射
            new_can_id_to_index = {}
            for i, message in enumerate(sorted_messages):
                new_can_id_to_index[message['id']] = i

            # 更新frames中的引用下标
            updated_frames = []
            for frame in new_frames:
                updated_frame = frame.copy()
                updated_data = []

                for data_item in frame['data']:
                    # 获取原始的CAN ID
                    old_message_index = data_item['id']
                    original_can_id = self.messages[old_message_index]['id']

                    # 获取新的message索引
                    new_message_index = new_can_id_to_index[original_can_id]

                    # 重新计算signal索引
                    old_signal_indices = data_item['signals']
                    old_message = self.messages[old_message_index]
                    new_message = sorted_messages[new_message_index]

                    new_signal_indices = []
                    for old_signal_idx in old_signal_indices:
                        old_signal = old_message['signals'][old_signal_idx]
                        # 在新的排序后的signals中找到对应的索引
                        for new_idx, new_signal in enumerate(new_message['signals']):
                            if new_signal['id'] == old_signal['id']:
                                new_signal_indices.append(new_idx)
                                break

                    updated_data_item = {
                        'id': new_message_index,
                        'signals': new_signal_indices
                    }
                    updated_data.append(updated_data_item)

                updated_frame['data'] = updated_data
                updated_frames.append(updated_frame)

            # 更新输出数据，确保messages字段在frames前面
            result = {
                'version': output_data['version'],
                'messages': sorted_messages,
                'frames': updated_frames
            }

            # 添加其他可能存在的字段
            for key, value in output_data.items():
                if key not in ['version', 'messages', 'frames']:
                    result[key] = value

            return result

        except Exception as e:
            raise ValueError(f"转换过程中出错: {str(e)}")

    def print_conversion_summary(self):
        """
        打印转换摘要
        """
        print("\n=== 转换摘要 ===")
        print(f"生成的消息数量: {len(self.messages)}")
        print("\n消息列表:")
        for i, message in enumerate(self.messages):
            print(f"  [{i}] CAN ID: {message['id']} (0x{message['id']:X})")
            print(f"      信号数量: {len(message['signals'])}")
            for j, signal in enumerate(message['signals']):
                print(f"        [{j}] ID: {signal['id']}, start_bit: {signal['start_bit']}, len: {signal['len']}")


class JsonBinaryConverter:
    def __init__(self):
        # 表达式类型枚举
        self.expression_types = {
            'constant': 0,
            'signal': 1,
            'event': 2,
            'message_time': 3,
            'system_time': 4,
            'io': 5,
            'compare': 6,
            'logic': 7,
            'arithmetic': 8,
            'bitwise': 9
        }
        
        # 反向映射
        self.reverse_expression_types = {v: k for k, v in self.expression_types.items()}
        
        # 比较操作符枚举
        self.compare_operators = {
            '==': 0,
            '!=': 1,
            '>': 2,
            '<': 3,
            '>=': 4,
            '<=': 5
        }
        self.reverse_compare_operators = {v: k for k, v in self.compare_operators.items()}
        
        # 逻辑操作符枚举
        self.logic_operators = {
            'and': 0,
            'or': 1,
            'not': 2
        }
        self.reverse_logic_operators = {v: k for k, v in self.logic_operators.items()}
        
        # 算术操作符枚举
        self.arithmetic_operators = {
            '+': 0,
            '-': 1,
            '*': 2,
            '/': 3
        }
        self.reverse_arithmetic_operators = {v: k for k, v in self.arithmetic_operators.items()}
        
        # 位操作符枚举
        self.bitwise_operators = {
            '<<': 0,
            '>>': 1,
            '&': 2,
            '|': 3,
            '~': 4
        }
        self.reverse_bitwise_operators = {v: k for k, v in self.bitwise_operators.items()}

    def parse_id_value(self, value) -> int:
        """
        解析ID值，支持十进制和十六进制格式

        Args:
            value: ID值，可以是int、str（十进制）或str（0x开头的十六进制）

        Returns:
            int: 解析后的整数值

        Raises:
            ValueError: 如果值无法解析或为负数
        """
        if isinstance(value, int):
            if value < 0:
                raise ValueError(f"ID值不能为负数: {value}")
            return value
        elif isinstance(value, str):
            value = value.strip()
            if value.lower().startswith('0x'):
                # 十六进制格式
                try:
                    parsed_value = int(value, 16)
                    if parsed_value < 0:
                        raise ValueError(f"ID值不能为负数: {value} (解析为 {parsed_value})")
                    return parsed_value
                except ValueError as e:
                    if "invalid literal" in str(e):
                        raise ValueError(f"无效的十六进制ID格式: {value}")
                    raise
            else:
                # 十进制格式
                try:
                    parsed_value = int(value)
                    if parsed_value < 0:
                        raise ValueError(f"ID值不能为负数: {value}")
                    return parsed_value
                except ValueError:
                    raise ValueError(f"无效的十进制ID格式: {value}")
        else:
            raise ValueError(f"不支持的ID值类型: {type(value)}, 值: {value}")

    def encode_type_operator_combined(self, expr_type_id: int, operator_id: int = 0) -> bytes:
        """
        将表达式类型和操作符合并编码到1字节

        编码格式: TTTT OOOO
        - TTTT (高4位): 表达式类型ID (0-15)
        - OOOO (低4位): 操作符ID (0-15)

        对于有操作符的类型 (compare=6, logic=7, arithmetic=8, bitwise=9):
        - 低4位存储对应的操作符ID

        对于无操作符的类型 (constant=0, signal=1, event=2, message_time=3, system_time=4, io=5):
        - 低4位设为0000

        Args:
            expr_type_id: 表达式类型ID (0-9)
            operator_id: 操作符ID (0-15)，对于不需要操作符的类型传入0

        Returns:
            1字节的合并编码
        """
        if expr_type_id > 15:
            raise ValueError(f"表达式类型ID超出4位范围: {expr_type_id}")
        if operator_id > 15:
            raise ValueError(f"操作符ID超出4位范围: {operator_id}")

        # 验证操作符ID是否在对应类型的有效范围内
        if expr_type_id == 6:  # compare
            if operator_id >= len(self.compare_operators):
                raise ValueError(f"比较操作符ID超出范围: {operator_id}")
        elif expr_type_id == 7:  # logic
            if operator_id >= len(self.logic_operators):
                raise ValueError(f"逻辑操作符ID超出范围: {operator_id}")
        elif expr_type_id == 8:  # arithmetic
            if operator_id >= len(self.arithmetic_operators):
                raise ValueError(f"算术操作符ID超出范围: {operator_id}")
        elif expr_type_id == 9:  # bitwise
            if operator_id >= len(self.bitwise_operators):
                raise ValueError(f"位操作符ID超出范围: {operator_id}")
        elif expr_type_id in [0, 1, 2, 3, 4, 5]:  # 无操作符类型
            if operator_id != 0:
                raise ValueError(f"类型{expr_type_id}不应该有操作符")

        # 高4位存储类型，低4位存储操作符
        combined = (expr_type_id << 4) | operator_id
        return bytes([combined])

    def decode_type_operator_combined(self, data: bytes, offset: int) -> Tuple[Tuple[int, int], int]:
        """
        解码合并的类型和操作符

        Args:
            data: 二进制数据
            offset: 当前偏移量

        Returns:
            ((expr_type_id, operator_id), new_offset)
        """
        if offset >= len(data):
            raise ValueError("数据不足，无法解码合并的类型和操作符")

        combined = data[offset]
        expr_type_id = (combined >> 4) & 0x0F  # 高4位
        operator_id = combined & 0x0F          # 低4位

        # 验证类型ID是否有效
        if expr_type_id not in self.reverse_expression_types:
            raise ValueError(f"未知的表达式类型ID: {expr_type_id}")

        # 验证操作符ID是否在对应类型的有效范围内
        if expr_type_id == 6:  # compare
            if operator_id >= len(self.compare_operators):
                raise ValueError(f"比较操作符ID超出范围: {operator_id}")
        elif expr_type_id == 7:  # logic
            if operator_id >= len(self.logic_operators):
                raise ValueError(f"逻辑操作符ID超出范围: {operator_id}")
        elif expr_type_id == 8:  # arithmetic
            if operator_id >= len(self.arithmetic_operators):
                raise ValueError(f"算术操作符ID超出范围: {operator_id}")
        elif expr_type_id == 9:  # bitwise
            if operator_id >= len(self.bitwise_operators):
                raise ValueError(f"位操作符ID超出范围: {operator_id}")
        elif expr_type_id in [0, 1, 2, 3, 4, 5]:  # 无操作符类型
            if operator_id != 0:
                raise ValueError(f"类型{expr_type_id}不应该有操作符，但解码出操作符ID: {operator_id}")

        return (expr_type_id, operator_id), offset + 1
    
    def encode_varint(self, value: int) -> bytes:
        """变长编码整数"""
        if value < 0:
            raise ValueError(f"变长编码不支持负数: {value}")

        result = bytearray()
        while value >= 0x80:
            result.append((value & 0x7F) | 0x80)
            value >>= 7
        result.append(value & 0x7F)
        return bytes(result)

    def encode_signal_field(self, start_bit: int, bit_length: int) -> bytes:
        """
        信号字段编码：使用优化的多格式编码（匹配C代码格式）

        编码格式：
        1. 1字节格式 (0xxx xxxx): start_bit <= 15, bit_length <= 8
           - bit 6-3: start_bit (4位)
           - bit 2-0: bit_length - 1 (3位，存储1-8)
        2. 2字节格式 (10xx xxxx xxxx xxxx): start_bit <= 63, bit_length <= 63
           - bit 13-8: start_bit (6位)
           - bit 7-2: bit_length (6位)
        3. varint格式 (11xx xxxx): 其他情况
           - 标识字节 + varint(start_bit) + varint(bit_length)
        """
        if start_bit < 0:
            raise ValueError(f"start_bit不能为负数: {start_bit}")
        if bit_length < 0:
            raise ValueError(f"bit_length不能为负数: {bit_length}")

        # 1字节格式：start_bit <= 15, bit_length 1-8
        if start_bit <= 15 and 1 <= bit_length <= 8:
            # 格式：0xxx xxxx
            # bit 6-3: start_bit, bit 2-0: bit_length - 1
            encoded = (start_bit << 3) | (bit_length - 1)
            return bytes([encoded])

        # 2字节格式：start_bit <= 63, bit_length <= 63
        elif start_bit <= 63 and bit_length <= 63:
            # 格式：10xx xxxx xxxx xxxx (大端序)
            # bit 13-8: start_bit, bit 7-2: bit_length
            encoded = 0x8000 | (start_bit << 8) | (bit_length << 2)
            return bytes([(encoded >> 8) & 0xFF, encoded & 0xFF])

        # varint格式：其他情况
        else:
            # 格式：11xx xxxx + varint(start_bit) + varint(bit_length)
            result = bytearray([0xC0])  # 标识字节
            result.extend(self.encode_varint(start_bit))
            result.extend(self.encode_varint(bit_length))
            return bytes(result)

    def decode_signal_field(self, data: bytes, offset: int) -> Tuple[Tuple[int, int], int]:
        """
        解码信号字段，返回((start_bit, bit_length), 新偏移量)；
        支持多格式编码（匹配C代码格式）
        """
        if offset >= len(data):
            raise ValueError("数据不足，无法解码信号字段")

        first_byte = data[offset]

        # 检查编码格式
        if (first_byte & 0x80) == 0:
            # 1字节格式：0xxx xxxx
            start_bit = (first_byte >> 3) & 0x0F    # bit 6-3
            bit_length = (first_byte & 0x07) + 1    # bit 2-0 + 1
            return (start_bit, bit_length), offset + 1

        elif (first_byte & 0xC0) == 0x80:
            # 2字节格式：10xx xxxx xxxx xxxx
            if offset + 1 >= len(data):
                raise ValueError("数据不足，无法解码2字节信号字段")

            # 读取2字节大端序数据
            encoded = (data[offset] << 8) | data[offset + 1]
            start_bit = (encoded >> 8) & 0x3F      # bit 13-8
            bit_length = (encoded >> 2) & 0x3F     # bit 7-2
            return (start_bit, bit_length), offset + 2

        elif (first_byte & 0xC0) == 0xC0:
            # varint格式：11xx xxxx
            offset += 1  # 跳过标识字节
            start_bit, offset = self.decode_varint(data, offset)
            bit_length, offset = self.decode_varint(data, offset)
            return (start_bit, bit_length), offset

        else:
            raise ValueError(f"无效的信号字段编码格式: 0x{first_byte:02x}")
    
    def decode_varint(self, data: bytes, offset: int) -> Tuple[int, int]:
        """解码变长整数，返回(值, 新偏移量)"""
        value = 0
        shift = 0
        pos = offset
        
        while pos < len(data):
            byte = data[pos]
            value |= (byte & 0x7F) << shift
            pos += 1
            
            if (byte & 0x80) == 0:
                break
            
            shift += 7
            if shift >= 64:
                raise ValueError("变长编码过长")
        
        return value, pos
    
    # def encode_signed_varint(self, value: int) -> bytes:
    #     """编码有符号变长整数（ZigZag编码）"""
    #     # ZigZag编码：将有符号数映射到无符号数
    #     encoded = (value << 1) ^ (value >> 31) if value >= 0 else (((-value) << 1) - 1)
    #     return self.encode_varint(encoded)
    
    # def decode_signed_varint(self, data: bytes, offset: int) -> Tuple[int, int]:
    #     """解码有符号变长整数"""
    #     encoded, new_offset = self.decode_varint(data, offset)
    #     # ZigZag解码
    #     value = (encoded >> 1) ^ (-(encoded & 1))
    #     return value, new_offset
    
    def encode_expression(self, expr: Dict[str, Any]) -> bytes:
        """编码表达式（优化版：合并typ和operator到1字节）"""
        result = BytesIO()

        expr_type = expr['type']
        if expr_type not in self.expression_types:
            raise ValueError(f"未知的表达式类型: {expr_type}")

        expr_type_id = self.expression_types[expr_type]

        # 根据类型编码值
        if expr_type in ['constant', 'signal', 'event', 'io']:
            # 无操作符类型：使用合并编码，操作符部分为0
            result.write(self.encode_type_operator_combined(expr_type_id, 0))

            if expr.get('value') is not None:
                value = self.parse_id_value(expr['value'])
                result.write(self.encode_varint(value))
            else:
                result.write(self.encode_varint(0))  # null值编码为0

        elif expr_type == 'message_time':
            # message_time类型：需要将消息ID转换为消息索引
            result.write(self.encode_type_operator_combined(expr_type_id, 0))

            if expr.get('value') is not None:
                message_id = self.parse_id_value(expr['value'])
                # 查找消息ID对应的索引
                message_index = None
                for i, message in enumerate(self.current_messages):
                    if message['id'] == message_id:
                        message_index = i
                        break

                if message_index is None:
                    raise ValueError(f"message_time表达式中找不到消息ID {message_id}")

                result.write(self.encode_varint(message_index))
            else:
                result.write(self.encode_varint(0))  # null值编码为0

        elif expr_type == 'system_time':
            # system_time没有值，不需要编码额外数据
            result.write(self.encode_type_operator_combined(expr_type_id, 0))

        elif expr_type == 'compare':
            value = expr['value']
            # 验证操作符
            if value['operator'] not in self.compare_operators:
                raise ValueError(f"未知的比较操作符: {value['operator']}")

            operator_id = self.compare_operators[value['operator']]
            # 使用合并编码：typ + operator
            result.write(self.encode_type_operator_combined(expr_type_id, operator_id))

            # 编码左右表达式
            result.write(self.encode_expression(value['left']))
            result.write(self.encode_expression(value['right']))

        elif expr_type == 'logic':
            value = expr['value']
            # 验证操作符
            if value['operator'] not in self.logic_operators:
                raise ValueError(f"未知的逻辑操作符: {value['operator']}")

            operator_id = self.logic_operators[value['operator']]
            # 使用合并编码：typ + operator
            result.write(self.encode_type_operator_combined(expr_type_id, operator_id))

            # 编码操作数数量
            operands = value['operands']
            result.write(self.encode_varint(len(operands)))
            # 编码每个操作数
            for operand in operands:
                result.write(self.encode_expression(operand))

        elif expr_type == 'arithmetic':
            value = expr['value']
            # 验证操作符
            if value['operator'] not in self.arithmetic_operators:
                raise ValueError(f"未知的算术操作符: {value['operator']}")

            operator_id = self.arithmetic_operators[value['operator']]
            # 使用合并编码：typ + operator
            result.write(self.encode_type_operator_combined(expr_type_id, operator_id))

            # 编码左右表达式
            result.write(self.encode_expression(value['left']))
            result.write(self.encode_expression(value['right']))

        elif expr_type == 'bitwise':
            value = expr['value']
            # 验证操作符
            if value['operator'] not in self.bitwise_operators:
                raise ValueError(f"未知的位操作符: {value['operator']}")

            operator_id = self.bitwise_operators[value['operator']]
            # 使用合并编码：typ + operator
            result.write(self.encode_type_operator_combined(expr_type_id, operator_id))

            # 编码左表达式
            result.write(self.encode_expression(value['left']))
            # 根据操作符判断是否需要右操作数，只有~操作符不需要右操作数
            if value['operator'] != '~':
                # 双目操作符：<<, >>, &, | 需要右操作数
                if 'right' not in value or value['right'] is None:
                    raise ValueError(f"位操作符 {value['operator']} 需要右操作数")
                result.write(self.encode_expression(value['right']))
            else:
                # 单目操作符：~ 不需要右操作数
                if 'right' in value and value['right'] is not None:
                    raise ValueError(f"位操作符 {value['operator']} 不应该有右操作数")

        return result.getvalue()
    
    def decode_expression(self, data: bytes, offset: int) -> Tuple[Dict[str, Any], int]:
        """解码表达式（优化版：从合并的typ和operator中解码）"""
        # 使用合并解码获取类型和操作符
        (expr_type_id, operator_id), offset = self.decode_type_operator_combined(data, offset)

        if expr_type_id not in self.reverse_expression_types:
            raise ValueError(f"未知的表达式类型ID: {expr_type_id}")

        expr_type = self.reverse_expression_types[expr_type_id]
        expr = {'type': expr_type}

        if expr_type in ['constant', 'signal', 'event', 'io']:
            value, offset = self.decode_varint(data, offset)
            expr['value'] = value if value != 0 or expr_type != 'system_time' else None

        elif expr_type == 'message_time':
            # message_time类型：需要将消息索引转换回消息ID
            message_index, offset = self.decode_varint(data, offset)
            if hasattr(self, 'current_messages') and message_index < len(self.current_messages):
                expr['value'] = self.current_messages[message_index]['id']
            else:
                expr['value'] = message_index  # 如果没有messages信息，直接使用索引值

        elif expr_type == 'system_time':
            expr['value'] = None

        elif expr_type == 'compare':
            # 操作符已从合并字节中解码
            if operator_id not in self.reverse_compare_operators:
                raise ValueError(f"未知的比较操作符ID: {operator_id}")

            left_expr, offset = self.decode_expression(data, offset)
            right_expr, offset = self.decode_expression(data, offset)

            # 确保operator在left和right之前
            expr['value'] = {}
            expr['value']['operator'] = self.reverse_compare_operators[operator_id]
            expr['value']['left'] = left_expr
            expr['value']['right'] = right_expr

        elif expr_type == 'logic':
            # 操作符已从合并字节中解码
            if operator_id not in self.reverse_logic_operators:
                raise ValueError(f"未知的逻辑操作符ID: {operator_id}")

            operand_count, offset = self.decode_varint(data, offset)
            operands = []
            for _ in range(operand_count):
                operand, offset = self.decode_expression(data, offset)
                operands.append(operand)

            # 确保operator在operands之前
            expr['value'] = {}
            expr['value']['operator'] = self.reverse_logic_operators[operator_id]
            expr['value']['operands'] = operands

        elif expr_type == 'arithmetic':
            # 操作符已从合并字节中解码
            if operator_id not in self.reverse_arithmetic_operators:
                raise ValueError(f"未知的算术操作符ID: {operator_id}")

            left_expr, offset = self.decode_expression(data, offset)
            right_expr, offset = self.decode_expression(data, offset)

            # 确保operator在left和right之前
            expr['value'] = {}
            expr['value']['operator'] = self.reverse_arithmetic_operators[operator_id]
            expr['value']['left'] = left_expr
            expr['value']['right'] = right_expr

        elif expr_type == 'bitwise':
            # 操作符已从合并字节中解码
            if operator_id not in self.reverse_bitwise_operators:
                raise ValueError(f"未知的位操作符ID: {operator_id}")

            operator = self.reverse_bitwise_operators[operator_id]
            left_expr, offset = self.decode_expression(data, offset)

            # 根据操作符判断是否需要右操作数，只有~操作符不需要右操作数
            right_expr = None
            if operator != '~':
                # 双目操作符：<<, >>, &, | 需要右操作数
                right_expr, offset = self.decode_expression(data, offset)

            # 确保operator在left和right之前
            expr['value'] = {}
            expr['value']['operator'] = operator
            expr['value']['left'] = left_expr
            if right_expr is not None:
                expr['value']['right'] = right_expr

        return expr, offset
    
    def json_to_binary(self, json_data: Dict[str, Any]) -> bytes:
        """将JSON转换为二进制（支持原始格式和索引格式）"""
        # 检查是否为原始格式（没有messages字段）
        if 'messages' not in json_data:
            # 使用RuleEngineConverter转换为索引格式
            converter = RuleEngineConverter()
            json_data = converter.convert(json_data)

        # 保存messages信息供表达式编码使用
        self.current_messages = json_data['messages']

        result = BytesIO()
        
        # 写入版本号
        result.write(self.encode_varint(json_data['version']))
        
        # 写入messages数量
        messages = json_data['messages']
        result.write(self.encode_varint(len(messages)))
        
        # 写入messages（使用差值编码）
        prev_id = 0
        for message in messages:
            # 使用差值编码ID（由于ID是非负数且按升序排列，差值也是非负数）
            diff = message['id'] - prev_id
            if diff < 0:
                raise ValueError(f"消息ID必须按升序排列，当前ID: {message['id']}, 前一个ID: {prev_id}")
            result.write(self.encode_varint(diff))
            prev_id = message['id']
            
            # 写入signals数量
            signals = message['signals']
            result.write(self.encode_varint(len(signals)))
            
            # 写入每个signal
            for signal in signals:
                result.write(self.encode_varint(signal['id']))
                # 使用优化的字段编码
                result.write(self.encode_signal_field(signal['start_bit'], signal['len']))
        
        # 写入frames数量
        frames = json_data['frames']
        result.write(self.encode_varint(len(frames)))
        
        # 写入每个frame
        for frame in frames:
            # 写入cmd（支持十六进制格式）
            cmd_value = self.parse_id_value(frame['cmd'])
            result.write(self.encode_varint(cmd_value))
            
            # 写入data数量
            data_items = frame['data']
            result.write(self.encode_varint(len(data_items)))
            
            # 写入每个data项
            for data_item in data_items:
                result.write(self.encode_varint(data_item['id']))
                
                # 写入signals数量
                signals = data_item['signals']
                result.write(self.encode_varint(len(signals)))
                
                # 写入每个signal ID值
                for signal_idx in signals:
                    # 获取对应的message
                    message = messages[data_item['id']]
                    # 获取信号ID值
                    signal_id = message['signals'][signal_idx]['id']
                    result.write(self.encode_varint(signal_id))
            
            # 写入rule
            rule = frame['rule']
            result.write(self.encode_varint(rule['cycle']))
            result.write(self.encode_varint(rule['delay']))
            
            # 写入start_cond表达式
            result.write(self.encode_expression(rule['start_cond']))
        
        return result.getvalue()
    
    def binary_to_json(self, binary_data: bytes) -> Dict[str, Any]:
        """将二进制转换为JSON"""
        offset = 0
        
        # 读取版本号
        version, offset = self.decode_varint(binary_data, offset)
        
        # 读取messages
        messages_count, offset = self.decode_varint(binary_data, offset)
        messages = []
        
        current_id = 0
        for _ in range(messages_count):
            # 读取差值编码的ID
            diff, offset = self.decode_varint(binary_data, offset)
            current_id += diff
            
            # 读取signals数量
            signals_count, offset = self.decode_varint(binary_data, offset)
            signals = []
            
            # 读取每个signal
            for _ in range(signals_count):
                signal_id, offset = self.decode_varint(binary_data, offset)
                # 使用优化的字段解码
                (start_bit, length), offset = self.decode_signal_field(binary_data, offset)

                signals.append({
                    'id': signal_id,
                    'start_bit': start_bit,
                    'len': length
                })
            
            messages.append({
                'id': current_id,
                'signals': signals
            })

        # 保存messages信息供表达式解码使用
        self.current_messages = messages

        # 读取frames
        frames_count, offset = self.decode_varint(binary_data, offset)
        frames = []
        
        for _ in range(frames_count):
            # 读取cmd
            cmd, offset = self.decode_varint(binary_data, offset)
            
            # 读取data数量
            data_count, offset = self.decode_varint(binary_data, offset)
            data_items = []
            
            # 读取每个data项
            for _ in range(data_count):
                data_id, offset = self.decode_varint(binary_data, offset)
                
                # 读取signals数量
                signals_count, offset = self.decode_varint(binary_data, offset)
                signals = []
                
                # 读取每个signal ID值
                for _ in range(signals_count):
                    signal_id, offset = self.decode_varint(binary_data, offset)
                    signals.append(signal_id)
                
                data_items.append({
                    'id': data_id,
                    'signals': signals
                })
            
            # 读取rule
            cycle, offset = self.decode_varint(binary_data, offset)
            delay, offset = self.decode_varint(binary_data, offset)
            
            # 读取start_cond表达式
            start_cond, offset = self.decode_expression(binary_data, offset)
            
            frames.append({
                'cmd': cmd,
                'data': data_items,
                'rule': {
                    'cycle': cycle,
                    'delay': delay,
                    'start_cond': start_cond
                }
            })
        
        return {
            'version': version,
            'messages': messages,
            'frames': frames
        }
    
    def indexed_to_raw_format(self, indexed_data: Dict[str, Any]) -> Dict[str, Any]:
        """将索引格式转换回原始格式"""
        result = {
            'version': indexed_data['version'],
            'frames': []
        }
        
        messages = indexed_data['messages']
        
        for frame in indexed_data['frames']:
            new_frame = {
                'cmd': frame['cmd'],
                'data': [],
                'rule': frame['rule']
            }
            
            # 转换data项
            for data_item in frame['data']:
                message_idx = data_item['id']
                signal_ids = data_item['signals']  # 现在已经是信号ID值了
                
                # 获取对应的message
                message = messages[message_idx]
                can_id = message['id']
                
                # 根据信号ID获取对应的信号对象
                signals = []
                for signal_id in signal_ids:
                    # 在message的signals中查找对应的信号
                    for signal in message['signals']:
                        if signal['id'] == signal_id:
                            signals.append(signal)
                            break
                    else:
                        raise ValueError(f"在消息 {can_id} 中找不到信号ID {signal_id}")
                
                new_data_item = {
                    'id': can_id,
                    'signals': signals
                }
                new_frame['data'].append(new_data_item)
            
            result['frames'].append(new_frame)
        
        return result

    def convert_to_hybrid_format(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换为混合格式：data item id 使用 message idx，signals 使用 signal id
        
        Args:
            json_data: 原始格式的 JSON 数据
            
        Returns:
            混合格式的 JSON 数据
        """
        # 检查是否为原始格式（没有messages字段）
        if 'messages' not in json_data:
            # 使用RuleEngineConverter转换为索引格式
            converter = RuleEngineConverter()
            indexed_data = converter.convert(json_data)
        else:
            indexed_data = json_data
        
        result = {
            'version': indexed_data['version'],
            'messages': indexed_data['messages'],
            'frames': []
        }
        
        messages = indexed_data['messages']
        
        for frame in indexed_data['frames']:
            new_frame = {
                'cmd': frame['cmd'],
                'data': [],
                'rule': frame['rule']
            }
            
            # 转换data项
            for data_item in frame['data']:
                message_idx = data_item['id']
                signal_data = data_item['signals']
                
                # 检查signals是索引还是信号ID值
                # 如果第一个元素是整数且小于100，可能是索引；否则是信号ID
                if (isinstance(signal_data, list) and len(signal_data) > 0 and 
                    isinstance(signal_data[0], int) and signal_data[0] < 100):
                    # 可能是索引，需要转换为信号ID
                    signal_ids = []
                    for signal_idx in signal_data:
                        if signal_idx < len(messages[message_idx]['signals']):
                            signal_ids.append(messages[message_idx]['signals'][signal_idx]['id'])
                        else:
                            raise ValueError(f"信号索引 {signal_idx} 超出范围")
                else:
                    # 已经是信号ID值
                    signal_ids = signal_data
                
                new_data_item = {
                    'id': message_idx,  # 使用 message idx
                    'signals': signal_ids  # 使用 signal id
                }
                new_frame['data'].append(new_data_item)
            
            result['frames'].append(new_frame)
        
        return result


def main():
    if len(sys.argv) < 3:
        print("用法:")
        print("  JSON转二进制: python convert_json_bin.py json2bin <input.json> [output.bin]")
        print("  二进制转JSON: python convert_json_bin.py bin2json <input.bin> [output.json]")
        sys.exit(1)
    
    mode = sys.argv[1]
    input_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    converter = JsonBinaryConverter()
    
    try:
        if mode == 'json2bin':
            # JSON转二进制
            if output_file is None:
                output_file = input_file.replace('.json', '.bin')
            
            print(f"读取JSON文件: {input_file}")
            with open(input_file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print("转换为二进制...")
            binary_data = converter.json_to_binary(json_data)
            
            print(f"写入二进制文件: {output_file}")
            with open(output_file, 'wb') as f:
                f.write(binary_data)
            
            # 输出转换后的JSON文件（混合格式）
            hybrid_output_file = input_file.replace('.json', '_converted.json')
            print(f"生成转换后的JSON文件: {hybrid_output_file}")
            hybrid_data = converter.convert_to_hybrid_format(json_data)
            with open(hybrid_output_file, 'w', encoding='utf-8') as f:
                json.dump(hybrid_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 转换完成！")
            print(f"原始JSON大小: {len(json.dumps(json_data, separators=(',', ':')).encode('utf-8'))} bytes")
            print(f"二进制大小: {len(binary_data)} bytes")
            print(f"压缩率: {len(binary_data) / len(json.dumps(json_data, separators=(',', ':')).encode('utf-8')) * 100:.1f}%")
            print(f"转换后JSON文件: {hybrid_output_file}")
        
        elif mode == 'bin2json':
            # 二进制转JSON
            if output_file is None:
                output_file = input_file.replace('.bin', '_decoded.json')
            
            print(f"读取二进制文件: {input_file}")
            with open(input_file, 'rb') as f:
                binary_data = f.read()
            
            print("转换为JSON...")
            indexed_data = converter.binary_to_json(binary_data)
            
            print("转换为混合格式...")
            # 使用 convert_to_hybrid_format 将索引格式转换为混合格式
            # （data item id 使用 message idx，signals 使用 signal id）
            hybrid_data = converter.convert_to_hybrid_format(indexed_data)
            
            print(f"写入JSON文件: {output_file}")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(hybrid_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 转换完成！")
            print(f"二进制大小: {len(binary_data)} bytes")
            print(f"解码JSON大小: {len(json.dumps(hybrid_data, separators=(',', ':')).encode('utf-8'))} bytes")
        
        else:
            print(f"❌ 未知模式: {mode}")
            print("支持的模式: json2bin, bin2json")
            sys.exit(1)
    
    except FileNotFoundError:
        print(f"❌ 错误: 找不到输入文件 '{input_file}'")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 错误: JSON格式错误 - {str(e)}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 