﻿/**
 * @file es_at_srv.h
 * @brief AT service module - parse serial data and support application layer registration of command processing coroutines
 * 
 * This module processes AT commands received from serial port through coroutines, and allows application layer
 * to register custom commands and their handler functions.
 */

#ifndef __ES_AT_SRV_H__
#define __ES_AT_SRV_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "es_coro.h"
#include "es_ringbuffer.h"
#include "es_list.h"
#include "es_config.h"  /* Add reference to configuration file */
#include "es_at_def.h"
#include "es_scheduler.h"
#include "es_list.h"

/**
 * @brief Buffer size definitions (imported from kconfig)
 */
#define ES_AT_SRV_RX_BUF_SIZE     512    /* Receive buffer size */
#define ES_AT_SRV_TX_BUF_SIZE     512    /* Transmit buffer size */
#define ES_AT_SRV_MAX_CMD_COUNT   32      /* Maximum supported AT command count */
#define ES_AT_SRV_MAX_CMD_NAME    16      /* Maximum AT command name length */
#define ES_AT_SRV_MAX_PARAMS      8       /* Maximum parameter count per AT command */
#define ES_AT_SRV_MAX_USER_CMDS   8      /* Maximum user dynamic AT command count */

/**
 * @brief AT command index definitions
 */
#define ES_AT_SRV_CMD_INVALID     0xFFFFFFFF    /* Invalid command */
#define ES_AT_SRV_CMD_USER_FLAG   0x80000000    /* User command flag bit */
#define ES_AT_SRV_CMD_INDEX_MASK  0x7FFFFFFF    /* Index mask */

/**
 * @brief Construct standard command index
 */
#define ES_AT_SRV_MAKE_STD_CMD_IDX(idx)   ((uint32_t)(idx) & ES_AT_SRV_CMD_INDEX_MASK)

/**
 * @brief Construct user command index
 */
#define ES_AT_SRV_MAKE_USER_CMD_IDX(idx)  (ES_AT_SRV_CMD_USER_FLAG | ((uint32_t)(idx) & ES_AT_SRV_CMD_INDEX_MASK))

/**
 * @brief Check if it's a user command
 */
#define ES_AT_SRV_IS_USER_CMD(cmd_idx)    (((cmd_idx) != ES_AT_SRV_CMD_INVALID) && (((cmd_idx) & ES_AT_SRV_CMD_USER_FLAG) != 0))

/**
 * @brief Get command index
 */
#define ES_AT_SRV_GET_CMD_INDEX(cmd_idx)  ((cmd_idx) & ES_AT_SRV_CMD_INDEX_MASK)

/**
 * @brief AT service error code definitions
 */
typedef enum {
    ES_AT_SRV_ERR_NONE = 0,           /**< No error */
    ES_AT_SRV_ERR_PARAM_MISSING = 1,   /**< Parameter missing */
    ES_AT_SRV_ERR_INVALID_CMD = 2,     /**< Invalid command */
    ES_AT_SRV_ERR_INVALID_PARAM = 3,   /**< Invalid parameter */
    ES_AT_SRV_ERR_OPERATION_FAILED = 4, /**< Operation failed */
    ES_AT_SRV_ERR_NOT_SUPPORTED = 5,   /**< Unsupported operation */
    ES_AT_SRV_ERR_TIMEOUT = 6,         /**< Operation timeout */
    ES_AT_SRV_ERR_BUSY = 7,            /**< System busy */
    ES_AT_SRV_ERR_MEMORY = 8,          /**< Memory error */
    ES_AT_SRV_ERR_NETWORK = 9,         /**< Network error */
    ES_AT_SRV_ERR_UNKNOWN = 99         /**< Unknown error */
} es_at_srv_err_t;

typedef uint8_t es_at_srv_src_type_t;

/**
 * @brief AT command type enumeration
 */
typedef enum {
    ES_AT_SRV_CMD_EXEC = 0,   /**< Execute command (AT+XXX) */
    ES_AT_SRV_CMD_SET,        /**< Set command (AT+XXX=<...>) */
    ES_AT_SRV_CMD_QUERY,      /**< Query command (AT+XXX?) */
    ES_AT_SRV_CMD_TEST        /**< Test command (AT+XXX=?) */
} es_at_srv_cmd_type_t;

/**
 * @brief AT command processing result enumeration
 */
typedef enum {
    ES_AT_SRV_RESULT_OK = 0,      /**< Processing successful */
    ES_AT_SRV_RESULT_ERROR,       /**< Processing error */
    ES_AT_SRV_RESULT_TIMEOUT,     /**< Processing timeout */
    ES_AT_SRV_RESULT_NOT_FOUND,   /**< Command not found */
    ES_AT_SRV_RESULT_BUFFER_FULL  /**< Buffer full */
} es_at_srv_result_t;

/**
 * @brief AT command parameter structure
 */
typedef struct {
    const char *param;      /**< Parameter string */
    uint32_t len;          /**< Parameter length */
} es_at_srv_param_t;

/**
 * @brief AT command processing context
 */
typedef struct {
    uint8_t *rx_buf;                         /**< Receive buffer */
    uint16_t rx_len;                         /**< Receive data length */
    uint8_t param_count;                    /**< Parameter count */
    es_at_srv_param_t params[ES_AT_SRV_MAX_PARAMS];   /**< Parameter list */
} es_at_srv_cmd_ctx_t;

/**
 * @brief Standard AT command handler function type
 * @param coro Coroutine context
 * @param ctx Command processing context
 * @param cmd_type Command type (execute/set/query)
 * @return Coroutine return value
 */
typedef es_async_t (*es_at_cmd_handler_t)(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type);

/**
 * @brief Standard AT command item
 */
typedef struct {
    const char *cmd_name;                /**< Base command name (without ? or =) */
    es_at_cmd_handler_t handler;         /**< Standard command handler function */
} es_at_cmd_item_t;

typedef es_async_t (*es_at_srv_sink_func_t)(es_coro_t *coro, const uint8_t *data, uint16_t len);

typedef struct es_at_srv_sink {
    struct es_hlist_node node;              /**< Used to link sink to list */
    es_at_srv_src_type_t src_type;         /**< Data source type */
    es_at_srv_sink_func_t sink_func;       /**< Output function */
} es_at_srv_sink_t;

/**
 * @brief AT service instance structure
 */
typedef struct {
    es_ringbuffer_t data_ringbuf;           /**< Data ring buffer */
    uint8_t ringbuf_data[32];               /**< Ring buffer data area */
    uint8_t rx_buf[ES_AT_SRV_RX_BUF_SIZE];   /**< Receive buffer */
    uint8_t tx_buf[ES_AT_SRV_TX_BUF_SIZE];   /**< Transmit buffer */
    uint16_t tx_len;                        /**< Transmit data length */
    uint16_t rx_len;                        /**< Receive data length */
    es_at_srv_src_type_t src_type_count;    /**< Data source type count */
    es_at_srv_cmd_ctx_t cmd_ctx;            /**< Current processing command context */
    es_at_srv_cmd_type_t current_cmd_type;   /**< Current command type */
    struct es_hlist_head sinks;             /**< Output handler functions */
    es_at_srv_sink_t* current_sink;         /**< Current output handler function */
    es_at_cmd_item_t user_cmds[ES_AT_SRV_MAX_USER_CMDS];   /**< User dynamic AT command array */
    uint8_t user_cmd_count;               /**< User command count */
    es_coro_task_t rx_task;                 /**< Receive task */
    uint8_t inited : 1;                    /**< Service initialization flag */
    uint8_t is_sink_busy : 1;              /**< Output handler function busy flag */
} es_at_srv_t;

/**
 * @brief Get AT service singleton pointer (internal use)
 * @return AT service singleton pointer
 */
es_at_srv_t* es_at_srv_get_instance(void);

/**
 * @brief Initialize AT service
 * @param config Service configuration
 * @return 0 success, -1 failure
 */
int es_at_srv_init(void);


/**
 * @brief Allocate data source type
 * @return Data source type
 */
es_at_srv_src_type_t es_at_srv_alloc_src_type(void);


/**
 * @brief Register output handler function
 * @param name Output handler function name
 * @param sink_func Output handler function
 * @return 0 success, -1 failure
 */
int es_at_srv_add_sink(es_at_srv_sink_t *sink);

/**
 * @brief Unregister output handler function
 * @param name Output handler function name
 * @return 0 success, -1 failure
 */
int es_at_srv_remove_sink(es_at_srv_sink_t *sink);

/**
 * @brief Receive data
 * @param src_type Data source type
 * @param data Data
 * @param len Data length
 * @return 0 success, -1 failure
 */
int es_at_srv_recv(es_at_srv_src_type_t src_type, const uint8_t *data, uint16_t len);

/**
 * @brief Read data
 * @param data Data
 * @param len Data length
 * @return 0 success, -1 failure
 */
int es_at_srv_read_data(uint8_t *data, uint16_t len);

/**
 * @brief Send formatted response
 * @param fmt Format string
 * @param ... Parameter list
 * @return 0 success, -1 failure
 */
es_async_t _es_at_srv_send_fmt(es_coro_t *coro, const char *fmt, ...);
#define es_at_srv_fmt_send(fmt, ...) es_co_await(_es_at_srv_send_fmt, fmt, ##__VA_ARGS__);

/**
 * @brief Send raw data packet
 * @param data Data to be sent
 * @param len Data length
 * @return Coroutine return value
 */
es_async_t _es_at_srv_send_data(es_coro_t *coro, const uint8_t *data, uint16_t len);
#define es_at_srv_send_data(data, len) es_co_await(_es_at_srv_send_data, data, len);

/**
 * @brief Send success response
 * @return 0 success, -1 failure
 */
#define es_at_srv_send_ok() es_at_srv_send_data((const uint8_t *)"OK\r\n", 4)

/**
 * @brief Send error response
 * @param error_code Error code
 * @return 0 success, -1 failure
 */
#define es_at_srv_send_error_code(error_code) es_at_srv_fmt_send("ERROR:%d\r\n", error_code)

/**
 * @brief Send generic error response (no error code)
 * @return 0 success, -1 failure
 */
#define es_at_srv_send_error() es_at_srv_send_data((const uint8_t *)"ERROR\r\n", 7)

/**
 * @brief Deinitialize AT service
 */
void es_at_srv_deinit(void);

/**
 * @brief Register user AT command
 * @param cmd_item AT command item pointer
 * @return 0 success, -1 failure
 */
int es_at_srv_register_user_cmd(const es_at_cmd_item_t *cmd_item);

/**
 * @brief Unregister user AT command
 * @param cmd_name Command name
 * @return 0 success, -1 failure
 */
int es_at_srv_unregister_user_cmd(const char *cmd_name);

/**
 * @brief Get user command count
 * @return User command count
 */
uint8_t es_at_srv_get_user_cmd_count(void);

#ifdef __cplusplus
}
#endif

#endif /* __ES_AT_SRV_H__ */ 
