#!/usr/bin/env python3
"""
C语言子集编译器示例程序

展示编译器支持的各种C语言特性
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from c_to_vm_compiler import Compiler

def example_basic_arithmetic():
    """基本算术运算示例"""
    print("=== 基本算术运算示例 ===")
    
    c_code = """
    int main() {
        register int a = 15;
        register int b = 7;
        register int sum = a + b;
        register int diff = a - b;
        register int prod = a * b;
        register int quot = a / b;
        register int rem = a % b;
        return sum + diff + prod + quot + rem;
    }
    """
    
    compiler = Compiler()
    bytecode = compiler.compile(c_code)
    
    print("C代码:")
    print(c_code)
    print("生成的字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def example_bitwise_operations():
    """位运算示例"""
    print("=== 位运算示例 ===")
    
    c_code = """
    int main() {
        register int a = 0xF0;
        register int b = 0x0F;
        register int and_result = a & b;
        register int or_result = a | b;
        register int xor_result = a ^ b;
        register int shift_left = a << 2;
        register int shift_right = a >> 2;
        return and_result + or_result + xor_result + shift_left + shift_right;
    }
    """
    
    compiler = Compiler()
    bytecode = compiler.compile(c_code)
    
    print("C代码:")
    print(c_code)
    print("生成的字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def example_conditional_logic():
    """条件逻辑示例"""
    print("=== 条件逻辑示例 ===")
    
    c_code = """
    int main() {
        register int x = 25;
        register int y = 30;
        register int result = 0;
        
        if (x > y) {
            result = x - y;
        } else {
            result = y - x;
        }
        
        if (result == 5) {
            result = result * 2;
        }
        
        return result;
    }
    """
    
    compiler = Compiler()
    bytecode = compiler.compile(c_code)
    
    print("C代码:")
    print(c_code)
    print("生成的字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def example_loop():
    """循环示例"""
    print("=== 循环示例 ===")
    
    c_code = """
    int main() {
        register int i = 0;
        register int sum = 0;
        
        while (i < 10) {
            sum = sum + i;
            i = i + 1;
        }
        
        return sum;
    }
    """
    
    compiler = Compiler()
    bytecode = compiler.compile(c_code)
    
    print("C代码:")
    print(c_code)
    print("生成的字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def example_complex_expression():
    """复杂表达式示例"""
    print("=== 复杂表达式示例 ===")
    
    c_code = """
    int main() {
        register int a = 10;
        register int b = 5;
        register int c = 3;
        register int result = (a + b) * c - (a - b) / 2;
        
        if (result > 40) {
            result = result & 0xFF;
        } else {
            result = result | 0x100;
        }
        
        return result;
    }
    """
    
    compiler = Compiler()
    bytecode = compiler.compile(c_code)
    
    print("C代码:")
    print(c_code)
    print("生成的字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def example_comparison_operations():
    """比较运算示例"""
    print("=== 比较运算示例 ===")
    
    c_code = """
    int main() {
        register int a = 20;
        register int b = 15;
        register int result = 0;
        
        if (a == b) {
            result = 1;
        } else if (a != b) {
            result = 2;
        }
        
        if (a >= b) {
            result = result + 10;
        }
        
        if (b <= a) {
            result = result + 100;
        }
        
        return result;
    }
    """
    
    compiler = Compiler()
    bytecode = compiler.compile(c_code)
    
    print("C代码:")
    print(c_code)
    print("生成的字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def save_example_to_file(name, c_code):
    """保存示例到文件并编译"""
    c_filename = f"example_{name}.c"
    bin_filename = f"example_{name}.bin"
    
    # 保存C代码
    with open(c_filename, 'w', encoding='utf-8') as f:
        f.write(c_code)
    
    # 编译并保存字节码
    compiler = Compiler()
    bytecode = compiler.compile(c_code)
    compiler.save_bytecode(bytecode, bin_filename)
    
    print(f"✅ 示例已保存: {c_filename} -> {bin_filename}")
    return bytecode

def create_test_suite():
    """创建测试套件"""
    print("=== 创建测试套件 ===")
    
    examples = {
        "factorial": """
        int main() {
            register int n = 5;
            register int result = 1;
            register int i = 1;
            
            while (i <= n) {
                result = result * i;
                i = i + 1;
            }
            
            return result;
        }
        """,
        
        "fibonacci": """
        int main() {
            register int n = 10;
            register int a = 0;
            register int b = 1;
            register int i = 2;
            register int temp = 0;
            
            if (n <= 1) {
                return n;
            }
            
            while (i <= n) {
                temp = a + b;
                a = b;
                b = temp;
                i = i + 1;
            }
            
            return b;
        }
        """,
        
        "power": """
        int main() {
            register int base = 2;
            register int exp = 8;
            register int result = 1;
            register int i = 0;
            
            while (i < exp) {
                result = result * base;
                i = i + 1;
            }
            
            return result;
        }
        """,
        
        "gcd": """
        int main() {
            register int a = 48;
            register int b = 18;
            register int temp = 0;
            
            while (b != 0) {
                temp = b;
                b = a % b;
                a = temp;
            }
            
            return a;
        }
        """
    }
    
    for name, code in examples.items():
        print(f"\n--- {name.upper()} ---")
        bytecode = save_example_to_file(name, code)
        print(f"字节码长度: {len(bytecode)} bytes")

def main():
    print("🚀 C语言子集编译器示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_basic_arithmetic()
        example_bitwise_operations()
        example_conditional_logic()
        example_loop()
        example_complex_expression()
        example_comparison_operations()
        
        # 创建测试套件
        create_test_suite()
        
        print("\n🎉 所有示例编译成功！")
        print("\n📝 编译器支持的C语言特性:")
        print("  ✅ 基本数据类型: int")
        print("  ✅ 寄存器变量: register int")
        print("  ✅ 算术运算: +, -, *, /, %")
        print("  ✅ 位运算: &, |, ^, ~, <<, >>")
        print("  ✅ 比较运算: ==, !=, <, <=, >, >=")
        print("  ✅ 控制流: if-else, while")
        print("  ✅ 表达式优先级和括号")
        print("  ✅ 变量赋值和初始化")
        print("  ✅ 函数返回值")
        
        print("\n🔧 VM特性:")
        print("  ✅ 8个32位通用寄存器 (R0-R7)")
        print("  ✅ 栈式计算架构")
        print("  ✅ 条件跳转和循环")
        print("  ✅ 完整的算术和逻辑运算")
        
    except Exception as e:
        print(f"❌ 编译错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
