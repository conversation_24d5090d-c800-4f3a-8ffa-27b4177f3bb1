import rtconfig
from building import *

# get current directory
cwd = GetCurrentDir()

# The set of source files associated with this SConscript file.

src = Split('''
drivers/cmsis/Device/HDSC/hc32f4xx/Source/system_hc32f460.c
drivers/hc32_ll_driver/src/hc32_ll.c
drivers/hc32_ll_driver/src/hc32_ll_aos.c
drivers/hc32_ll_driver/src/hc32_ll_clk.c
drivers/hc32_ll_driver/src/hc32_ll_dma.c
drivers/hc32_ll_driver/src/hc32_ll_efm.c
drivers/hc32_ll_driver/src/hc32_ll_fcg.c
drivers/hc32_ll_driver/src/hc32_ll_fcm.c
drivers/hc32_ll_driver/src/hc32_ll_gpio.c
drivers/hc32_ll_driver/src/hc32_ll_interrupts.c
drivers/hc32_ll_driver/src/hc32_ll_pwc.c
drivers/hc32_ll_driver/src/hc32_ll_rmu.c
drivers/hc32_ll_driver/src/hc32_ll_sram.c
drivers/hc32_ll_driver/src/hc32_ll_utility.c
drivers/hc32_ll_driver/src/hc32_ll_spi.c
drivers/hc32_ll_driver/src/hc32_ll_efm.c
drivers/hc32_ll_driver/src/hc32_ll_adc.c
drivers/hc32_ll_driver/src/hc32_ll_usart.c
drivers/hc32_ll_driver/src/hc32_ll_can.c
drivers/hc32_ll_driver/src/hc32f460_ll_interrupts_share.c
drivers/bsp/components/w25qxx/w25qxx.c
drivers/bsp/ev_hc32f460_lqfp100_v2/ev_hc32f460_lqfp100_v2_w25qxx.c
drivers/bsp/components/lsm6dsl/lsm6dsl.c
drivers/bsp/ev_hc32f460_lqfp100_v2/ev_hc32f460_lqfp100_v2_lsm6dsl.c
''')

# drivers/hc32_ll_driver/src/hc32_ll_icg.c

if GetDepend(['RT_USING_SERIAL']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_usart.c']
    src += ['drivers/hc32_ll_driver/src/hc32_ll_tmr0.c']

if GetDepend(['RT_USING_I2C']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_i2c.c']

# if GetDepend(['RT_USING_SPI']):
#     src += ['drivers/hc32_ll_driver/src/hc32_ll_spi.c']

if GetDepend(['RT_USING_QSPI']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_qspi.c']

if GetDepend(['RT_USING_CAN']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_can.c']

if GetDepend(['RT_USING_ADC']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_adc.c']

if GetDepend(['RT_USING_RTC']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_rtc.c']

if GetDepend(['RT_USING_WDT']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_swdt.c']
    src += ['drivers/hc32_ll_driver/src/hc32_ll_wdt.c']

if GetDepend(['RT_USING_SDIO']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_sdioc.c']

if GetDepend(['RT_USING_ON_CHIP_FLASH']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_efm.c']

if GetDepend(['RT_USING_HWTIMER']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_tmr4.c']
    src += ['drivers/hc32_ll_driver/src/hc32_ll_tmr6.c']
    src += ['drivers/hc32_ll_driver/src/hc32_ll_tmra.c']

if GetDepend(['RT_USING_PULSE_ENCODER']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_tmr6.c']
    src += ['drivers/hc32_ll_driver/src/hc32_ll_tmra.c']

if GetDepend(['RT_USING_PWM']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_tmr4.c']
    src += ['drivers/hc32_ll_driver/src/hc32_ll_tmr6.c']
    src += ['drivers/hc32_ll_driver/src/hc32_ll_tmra.c']

if GetDepend(['RT_HWCRYPTO_USING_RNG']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_trng.c']

if GetDepend(['RT_HWCRYPTO_USING_CRC']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_crc.c']

if GetDepend(['RT_HWCRYPTO_USING_AES']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_aes.c']

if GetDepend(['RT_HWCRYPTO_USING_SHA2']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_hash.c']

if GetDepend(['BSP_USING_USBD']) or GetDepend(['BSP_USING_USBH']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_usb.c']

if GetDepend(['BSP_RTC_USING_XTAL32']) or GetDepend(['RT_USING_PM']):
    src += ['drivers/hc32_ll_driver/src/hc32_ll_fcm.c']

path = [
    cwd + '/drivers/cmsis/Device/HDSC/hc32f4xx/Include',
    cwd + '/drivers/cmsis/Include',
    cwd + '/drivers/bsp/components/w25qxx',
    cwd + '/drivers/bsp/components/lsm6dsl',
    cwd + '/drivers/bsp/ev_hc32f460_lqfp100_v2',
    cwd + '/drivers/hc32_ll_driver/inc',]

CPPDEFINES = ['USE_DDL_DRIVER']

group = DefineGroup('Libraries', src, depend = [''], CPPPATH = path, CPPDEFINES = CPPDEFINES)

Return('group')
