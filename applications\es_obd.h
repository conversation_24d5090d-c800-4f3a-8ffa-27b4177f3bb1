/**
 * @file es_obd.h
 * @brief OBD-II (On-Board Diagnostics) Client Implementation
 *
 * This module provides OBD-II client functionality for automotive diagnostics
 * according to SAE J1979 and ISO 15031 standards.
 *
 * Features:
 * - OBD-II service support (Mode 01-09)
 * - Standard OBD-II PIDs (Parameter IDs)
 * - DTC (Diagnostic Trouble Code) management
 * - Non-blocking coroutine-based async operations
 * - Uses ISO-TP transport layer
 * - Standard OBD-II CAN IDs (0x7DF, 0x7E0-0x7E7, 0x7E8-0x7EF)
 *
 * <AUTHOR> MCU Framework
 * @date 2025-01-22
 * @version 1.0.0
 */

#ifndef ES_OBD_H
#define ES_OBD_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "es.h"
#include "es_coro.h"
#include "es_drv_can.h"
#include "es_isotp.h"
#include "es_log.h"
#include "es_mem.h"
#include "es_drv_os.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================== */
/*                              CONFIGURATION                                */
/* ========================================================================== */

/** OBD-II Standard CAN IDs */
#define ES_OBD_FUNCTIONAL_ID        0x7DF   /**< OBD-II functional (broadcast) request ID */
#define ES_OBD_PHYSICAL_TX_BASE     0x7E0   /**< OBD-II physical request ID base (0x7E0-0x7E7) */
#define ES_OBD_PHYSICAL_RX_BASE     0x7E8   /**< OBD-II physical response ID base (0x7E8-0x7EF) */

/** Default buffer sizes */
#ifndef ES_OBD_DEFAULT_TX_BUFFER_SIZE
#define ES_OBD_DEFAULT_TX_BUFFER_SIZE       256
#endif

#ifndef ES_OBD_DEFAULT_RX_BUFFER_SIZE
#define ES_OBD_DEFAULT_RX_BUFFER_SIZE       256
#endif

/** Default timeout values (milliseconds) */
#define ES_OBD_DEFAULT_TIMEOUT_MS           1000
#define ES_OBD_DEFAULT_P2_TIMEOUT_MS        50
#define ES_OBD_DEFAULT_P2_STAR_TIMEOUT_MS   5000

/** OBD-II debug log level configuration */
#ifndef ES_OBD_LOG_LEVEL
#define ES_OBD_LOG_LEVEL 2  // 0=None, 1=Error+Critical, 2=Info, 3=Debug, 4=Verbose
#endif

/* ========================================================================== */
/*                              ERROR CODES                                  */
/* ========================================================================== */

/**
 * @brief OBD-II error codes
 */
typedef enum {
    ES_OBD_OK = 0,                          /**< Success */
    ES_OBD_ERR_INVALID_PARAM = -1,          /**< Invalid parameter */
    ES_OBD_ERR_TIMEOUT = -2,                /**< Timeout occurred */
    ES_OBD_ERR_BUSY = -3,                   /**< Resource busy */
    ES_OBD_ERR_NO_MEMORY = -4,              /**< No memory available */
    ES_OBD_ERR_CAN_ERROR = -5,              /**< CAN communication error */
    ES_OBD_ERR_NEGATIVE_RESPONSE = -6,      /**< OBD negative response */
    ES_OBD_ERR_UNSUPPORTED_SERVICE = -7,    /**< Service not supported */
    ES_OBD_ERR_UNSUPPORTED_PID = -8,        /**< PID not supported */
    ES_OBD_ERR_INVALID_RESPONSE = -9        /**< Invalid response format */
} es_obd_error_t;

/* ========================================================================== */
/*                            OBD-II SERVICE IDS                             */
/* ========================================================================== */

/**
 * @brief OBD-II Service Identifiers (Modes)
 */
typedef enum {
    ES_OBD_SERVICE_01 = 0x01,               /**< Show current data */
    ES_OBD_SERVICE_02 = 0x02,               /**< Show freeze frame data */
    ES_OBD_SERVICE_03 = 0x03,               /**< Show stored DTCs */
    ES_OBD_SERVICE_04 = 0x04,               /**< Clear DTCs and stored values */
    ES_OBD_SERVICE_05 = 0x05,               /**< Test results, oxygen sensor monitoring */
    ES_OBD_SERVICE_06 = 0x06,               /**< Test results, other component/system monitoring */
    ES_OBD_SERVICE_07 = 0x07,               /**< Show pending DTCs */
    ES_OBD_SERVICE_08 = 0x08,               /**< Control operation of on-board component/system */
    ES_OBD_SERVICE_09 = 0x09,               /**< Request vehicle information */
    ES_OBD_SERVICE_0A = 0x0A                /**< Permanent DTCs */
} es_obd_service_t;

/**
 * @brief OBD-II Negative Response Codes
 */
typedef enum {
    ES_OBD_NRC_POSITIVE_RESPONSE = 0x00,            /**< Positive response */
    ES_OBD_NRC_GENERAL_REJECT = 0x10,               /**< General reject */
    ES_OBD_NRC_SERVICE_NOT_SUPPORTED = 0x11,        /**< Service not supported */
    ES_OBD_NRC_SUB_FUNCTION_NOT_SUPPORTED = 0x12,   /**< Sub-function not supported */
    ES_OBD_NRC_INCORRECT_MESSAGE_LENGTH = 0x13,     /**< Incorrect message length */
    ES_OBD_NRC_BUSY_REPEAT_REQUEST = 0x21,          /**< Busy repeat request */
    ES_OBD_NRC_CONDITIONS_NOT_CORRECT = 0x22,       /**< Conditions not correct */
    ES_OBD_NRC_REQUEST_SEQUENCE_ERROR = 0x24,       /**< Request sequence error */
    ES_OBD_NRC_REQUEST_OUT_OF_RANGE = 0x31,         /**< Request out of range */
    ES_OBD_NRC_SECURITY_ACCESS_DENIED = 0x33,       /**< Security access denied */
    ES_OBD_NRC_RESPONSE_PENDING = 0x78              /**< Response pending */
} es_obd_nrc_t;

/* ========================================================================== */
/*                            OBD-II PARAMETER IDS                           */
/* ========================================================================== */

/**
 * @brief Common OBD-II Parameter IDs (PIDs) for Service 01
 */
typedef enum {
    ES_OBD_PID_SUPPORTED_PIDS_01_20 = 0x00,        /**< PIDs supported [01-20] */
    ES_OBD_PID_MONITOR_STATUS = 0x01,               /**< Monitor status since DTCs cleared */
    ES_OBD_PID_FREEZE_DTC = 0x02,                   /**< Freeze DTC */
    ES_OBD_PID_FUEL_SYSTEM_STATUS = 0x03,           /**< Fuel system status */
    ES_OBD_PID_ENGINE_LOAD = 0x04,                  /**< Calculated engine load */
    ES_OBD_PID_COOLANT_TEMP = 0x05,                 /**< Engine coolant temperature */
    ES_OBD_PID_SHORT_TERM_FUEL_TRIM_1 = 0x06,       /**< Short term fuel trim—Bank 1 */
    ES_OBD_PID_LONG_TERM_FUEL_TRIM_1 = 0x07,        /**< Long term fuel trim—Bank 1 */
    ES_OBD_PID_SHORT_TERM_FUEL_TRIM_2 = 0x08,       /**< Short term fuel trim—Bank 2 */
    ES_OBD_PID_LONG_TERM_FUEL_TRIM_2 = 0x09,        /**< Long term fuel trim—Bank 2 */
    ES_OBD_PID_FUEL_PRESSURE = 0x0A,                /**< Fuel pressure */
    ES_OBD_PID_INTAKE_MAP = 0x0B,                    /**< Intake manifold absolute pressure */
    ES_OBD_PID_ENGINE_RPM = 0x0C,                    /**< Engine RPM */
    ES_OBD_PID_VEHICLE_SPEED = 0x0D,                 /**< Vehicle speed */
    ES_OBD_PID_TIMING_ADVANCE = 0x0E,                /**< Timing advance */
    ES_OBD_PID_INTAKE_AIR_TEMP = 0x0F,               /**< Intake air temperature */
    ES_OBD_PID_MAF_RATE = 0x10,                      /**< MAF air flow rate */
    ES_OBD_PID_THROTTLE_POSITION = 0x11,             /**< Throttle position */
    ES_OBD_PID_SECONDARY_AIR_STATUS = 0x12,          /**< Commanded secondary air status */
    ES_OBD_PID_OXYGEN_SENSORS_PRESENT = 0x13,        /**< Oxygen sensors present */
    ES_OBD_PID_OXYGEN_SENSOR_1 = 0x14,               /**< Oxygen sensor 1 */
    ES_OBD_PID_OXYGEN_SENSOR_2 = 0x15,               /**< Oxygen sensor 2 */
    ES_OBD_PID_OXYGEN_SENSOR_3 = 0x16,               /**< Oxygen sensor 3 */
    ES_OBD_PID_OXYGEN_SENSOR_4 = 0x17,               /**< Oxygen sensor 4 */
    ES_OBD_PID_OXYGEN_SENSOR_5 = 0x18,               /**< Oxygen sensor 5 */
    ES_OBD_PID_OXYGEN_SENSOR_6 = 0x19,               /**< Oxygen sensor 6 */
    ES_OBD_PID_OXYGEN_SENSOR_7 = 0x1A,               /**< Oxygen sensor 7 */
    ES_OBD_PID_OXYGEN_SENSOR_8 = 0x1B,               /**< Oxygen sensor 8 */
    ES_OBD_PID_OBD_STANDARDS = 0x1C,                 /**< OBD standards this vehicle conforms to */
    ES_OBD_PID_OXYGEN_SENSORS_PRESENT_2 = 0x1D,      /**< Oxygen sensors present (2 banks) */
    ES_OBD_PID_AUX_INPUT_STATUS = 0x1E,              /**< Auxiliary input status */
    ES_OBD_PID_RUNTIME_SINCE_START = 0x1F,           /**< Runtime since engine start */
    ES_OBD_PID_SUPPORTED_PIDS_21_40 = 0x20,          /**< PIDs supported [21-40] */
    ES_OBD_PID_DISTANCE_WITH_MIL = 0x21,             /**< Distance traveled with MIL on */
    ES_OBD_PID_FUEL_RAIL_PRESSURE = 0x22,            /**< Fuel rail pressure */
    ES_OBD_PID_FUEL_RAIL_GAUGE_PRESSURE = 0x23,      /**< Fuel rail gauge pressure */
    ES_OBD_PID_SUPPORTED_PIDS_41_60 = 0x40,          /**< PIDs supported [41-60] */
    ES_OBD_PID_SUPPORTED_PIDS_61_80 = 0x60,          /**< PIDs supported [61-80] */
    ES_OBD_PID_SUPPORTED_PIDS_81_A0 = 0x80,          /**< PIDs supported [81-A0] */
    ES_OBD_PID_SUPPORTED_PIDS_A1_C0 = 0xA0,          /**< PIDs supported [A1-C0] */
    ES_OBD_PID_SUPPORTED_PIDS_C1_E0 = 0xC0           /**< PIDs supported [C1-E0] */
} es_obd_pid_t;

/**
 * @brief OBD-II Vehicle Information PIDs for Service 09
 */
typedef enum {
    ES_OBD_VIN_SUPPORTED_INFOTYPES = 0x00,           /**< Vehicle info supported */
    ES_OBD_VIN_MESSAGE_COUNT = 0x01,                 /**< VIN message count */
    ES_OBD_VIN_VEHICLE_IDENTIFICATION = 0x02,        /**< Vehicle identification number */
    ES_OBD_VIN_CALIBRATION_ID_COUNT = 0x03,          /**< Calibration ID message count */
    ES_OBD_VIN_CALIBRATION_ID = 0x04,                /**< Calibration ID */
    ES_OBD_VIN_CVN_COUNT = 0x05,                     /**< CVN message count */
    ES_OBD_VIN_CVN = 0x06,                           /**< Calibration verification numbers */
    ES_OBD_VIN_IPT_COUNT = 0x07,                     /**< In-use performance tracking count */
    ES_OBD_VIN_IPT = 0x08,                           /**< In-use performance tracking */
    ES_OBD_VIN_ESN_COUNT = 0x09,                     /**< ECU serial number count */
    ES_OBD_VIN_ESN = 0x0A                            /**< ECU serial number */
} es_obd_vin_pid_t;

/* ========================================================================== */
/*                            CONNECTION STRUCTURE                           */
/* ========================================================================== */

/**
 * @brief OBD-II connection structure
 */
typedef struct {
    /* ISO-TP connection */
    es_isotp_connection_t isotp;                     /**< ISO-TP connection */
    
    /* OBD-II state management */
    uint8_t current_service;                         /**< Current service ID */
    uint8_t current_pid;                             /**< Current PID */
    uint8_t response_pending_count;                  /**< Response pending count */
    uint32_t request_start_time;                     /**< Request start time */
    bool waiting_for_response;                       /**< Waiting for response flag */
    
    /* Last response data */
    uint8_t last_nrc;                                /**< Last negative response code */
    
    /* OBD-II buffers (shared with ISO-TP) */
    uint8_t tx_buffer[ES_OBD_DEFAULT_TX_BUFFER_SIZE]; /**< Transmit buffer */
    uint8_t rx_buffer[ES_OBD_DEFAULT_RX_BUFFER_SIZE]; /**< Receive buffer */
} es_obd_connection_t;

/* ========================================================================== */
/*                             OBD-II MACROS                                 */
/* ========================================================================== */

/**
 * @brief Access the OBD-II response buffer
 */
#define OBD_RESPONSE_BUFFER(conn) ((conn)->isotp.rx_buffer)

/**
 * @brief Access the OBD-II response length
 */
#define OBD_RESPONSE_LENGTH(conn) ((conn)->isotp.rx_length)

/**
 * @brief Access the OBD-II response data (excluding service ID and PID)
 */
#define OBD_RESPONSE_DATA(conn) (&OBD_RESPONSE_BUFFER(conn)[2])

/**
 * @brief Get the OBD-II response data length (excluding service ID and PID)
 */
#define OBD_RESPONSE_DATA_LENGTH(conn) \
    ((OBD_RESPONSE_LENGTH(conn) > 2) ? (OBD_RESPONSE_LENGTH(conn) - 2) : 0)

/**
 * @brief Get the service ID from response
 */
#define OBD_RESPONSE_SERVICE(conn) \
    ((OBD_RESPONSE_LENGTH(conn) >= 1) ? (OBD_RESPONSE_BUFFER(conn)[0] - 0x40) : 0)

/**
 * @brief Get the PID from response
 */
#define OBD_RESPONSE_PID(conn) \
    ((OBD_RESPONSE_LENGTH(conn) >= 2) ? OBD_RESPONSE_BUFFER(conn)[1] : 0)

/**
 * @brief Convert OBD-II DTC to ASCII string format
 * @param dtc_bytes Pointer to 2-byte DTC data
 * @param buffer Pointer to buffer for ASCII string (minimum 6 bytes)
 * @note Converts DTC to standard format: P0XXX, B0XXX, C0XXX, U0XXX
 */
#define OBD_DTC_TO_ASCII(dtc_bytes, buffer) do { \
    uint16_t dtc = ((uint16_t)(dtc_bytes)[0] << 8) | (dtc_bytes)[1]; \
    uint8_t system = (dtc >> 14) & 0x03; \
    uint16_t code = dtc & 0x3FFF; \
    (buffer)[0] = (system == 0) ? 'P' : (system == 1) ? 'C' : (system == 2) ? 'B' : 'U'; \
    (buffer)[1] = '0' + ((code >> 12) & 0x0F); \
    (buffer)[2] = ((code >> 8) & 0x0F) < 10 ? '0' + ((code >> 8) & 0x0F) : 'A' + ((code >> 8) & 0x0F) - 10; \
    (buffer)[3] = ((code >> 4) & 0x0F) < 10 ? '0' + ((code >> 4) & 0x0F) : 'A' + ((code >> 4) & 0x0F) - 10; \
    (buffer)[4] = (code & 0x0F) < 10 ? '0' + (code & 0x0F) : 'A' + (code & 0x0F) - 10; \
    (buffer)[5] = '\0'; \
} while(0)

/**
 * @brief Extract engine RPM from PID 0x0C response
 * @param conn Pointer to OBD connection
 * @return Engine RPM in RPM units
 */
#define OBD_EXTRACT_ENGINE_RPM(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 2) ? \
     (((uint16_t)OBD_RESPONSE_DATA(conn)[0] << 8) | OBD_RESPONSE_DATA(conn)[1]) / 4 : 0)

/**
 * @brief Extract vehicle speed from PID 0x0D response
 * @param conn Pointer to OBD connection
 * @return Vehicle speed in km/h
 */
#define OBD_EXTRACT_VEHICLE_SPEED(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 1) ? OBD_RESPONSE_DATA(conn)[0] : 0)

/**
 * @brief Extract engine coolant temperature from PID 0x05 response
 * @param conn Pointer to OBD connection
 * @return Coolant temperature in Celsius
 */
#define OBD_EXTRACT_COOLANT_TEMP(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 1) ? (OBD_RESPONSE_DATA(conn)[0] - 40) : -40)

/**
 * @brief Extract calculated engine load from PID 0x04 response
 * @param conn Pointer to OBD connection
 * @return Engine load as percentage (0-100)
 */
#define OBD_EXTRACT_ENGINE_LOAD(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 1) ? (OBD_RESPONSE_DATA(conn)[0] * 100 / 255) : 0)

/**
 * @brief Extract throttle position from PID 0x11 response
 * @param conn Pointer to OBD connection
 * @return Throttle position as percentage (0-100)
 */
#define OBD_EXTRACT_THROTTLE_POSITION(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 1) ? (OBD_RESPONSE_DATA(conn)[0] * 100 / 255) : 0)

/**
 * @brief Extract intake air temperature from PID 0x0F response
 * @param conn Pointer to OBD connection
 * @return Intake air temperature in Celsius
 */
#define OBD_EXTRACT_INTAKE_AIR_TEMP(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 1) ? (OBD_RESPONSE_DATA(conn)[0] - 40) : -40)

/**
 * @brief Extract MAF air flow rate from PID 0x10 response
 * @param conn Pointer to OBD connection
 * @return MAF rate in grams/second (scaled by 100)
 */
#define OBD_EXTRACT_MAF_RATE(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 2) ? \
     (((uint16_t)OBD_RESPONSE_DATA(conn)[0] << 8) | OBD_RESPONSE_DATA(conn)[1]) : 0)

/**
 * @brief Extract fuel pressure from PID 0x0A response
 * @param conn Pointer to OBD connection
 * @return Fuel pressure in kPa
 */
#define OBD_EXTRACT_FUEL_PRESSURE(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 1) ? (OBD_RESPONSE_DATA(conn)[0] * 3) : 0)

/**
 * @brief Extract intake manifold pressure from PID 0x0B response
 * @param conn Pointer to OBD connection
 * @return Intake manifold pressure in kPa
 */
#define OBD_EXTRACT_INTAKE_MAP(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 1) ? OBD_RESPONSE_DATA(conn)[0] : 0)

/**
 * @brief Get DTC count from Service 03/07 response
 * @param conn Pointer to OBD connection
 * @return Number of DTCs
 */
#define OBD_GET_DTC_COUNT(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) >= 1) ? OBD_RESPONSE_DATA(conn)[0] : 0)

/**
 * @brief Get DTC data pointer from Service 03/07 response
 * @param conn Pointer to OBD connection
 * @return Pointer to DTC data (2 bytes per DTC)
 */
#define OBD_GET_DTC_DATA(conn) \
    ((OBD_RESPONSE_DATA_LENGTH(conn) > 1) ? &OBD_RESPONSE_DATA(conn)[1] : NULL)

/**
 * @brief Check if PID is supported in supported PIDs response
 * @param supported_data Pointer to 4-byte supported PIDs data
 * @param pid_offset PID offset within the range (0-31)
 * @return true if PID is supported, false otherwise
 */
#define OBD_IS_PID_SUPPORTED(supported_data, pid_offset) \
    (((supported_data)[(pid_offset) / 8] & (0x80 >> ((pid_offset) % 8))) != 0)

/* ========================================================================== */
/*                             FUNCTION DECLARATIONS                         */
/* ========================================================================== */

/**
 * @brief Initialize OBD-II connection
 * @param conn Pointer to OBD-II connection structure
 * @param ecu_id ECU ID (0-7 for physical addressing, or use functional addressing)
 * @param use_functional Use functional addressing (broadcast) if true
 * @return ES_OBD_OK on success, error code on failure
 */
es_obd_error_t es_obd_init(es_obd_connection_t *conn, uint8_t ecu_id, bool use_functional);

/**
 * @brief Process incoming CAN message
 * @param conn Pointer to OBD-II connection
 * @param msg Pointer to CAN message
 * @return ES_OBD_OK on success, error code on failure
 */
es_obd_error_t es_obd_process_can_message(es_obd_connection_t *conn, const es_can_msg_t *msg);

/**
 * @brief Read current data (Service 01) (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to OBD-II connection
 * @param pid Parameter ID to read
 * @return Coroutine return value
 */
es_async_t es_obd_read_current_data(es_coro_t *coro, es_obd_connection_t *conn, es_obd_pid_t pid);

/**
 * @brief Read freeze frame data (Service 02) (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to OBD-II connection
 * @param pid Parameter ID to read
 * @param frame_number Freeze frame number
 * @return Coroutine return value
 */
es_async_t es_obd_read_freeze_frame_data(es_coro_t *coro, es_obd_connection_t *conn, 
                                         es_obd_pid_t pid, uint8_t frame_number);

/**
 * @brief Read stored DTCs (Service 03) (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to OBD-II connection
 * @return Coroutine return value
 */
es_async_t es_obd_read_stored_dtcs(es_coro_t *coro, es_obd_connection_t *conn);

/**
 * @brief Clear DTCs and stored values (Service 04) (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to OBD-II connection
 * @return Coroutine return value
 */
es_async_t es_obd_clear_dtcs(es_coro_t *coro, es_obd_connection_t *conn);

/**
 * @brief Read pending DTCs (Service 07) (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to OBD-II connection
 * @return Coroutine return value
 */
es_async_t es_obd_read_pending_dtcs(es_coro_t *coro, es_obd_connection_t *conn);

/**
 * @brief Request vehicle information (Service 09) (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to OBD-II connection
 * @param info_type Information type (VIN PID)
 * @return Coroutine return value
 */
es_async_t es_obd_request_vehicle_info(es_coro_t *coro, es_obd_connection_t *conn,
                                       es_obd_vin_pid_t info_type);

/* ========================================================================== */
/*                            UTILITY FUNCTIONS                              */
/* ========================================================================== */

/**
 * @brief Parse and display engine data from current data response
 * @param conn Pointer to OBD connection
 * @param pid PID that was requested
 */
void es_obd_parse_engine_data(es_obd_connection_t *conn, es_obd_pid_t pid);

/**
 * @brief Parse and display DTCs from DTC response
 * @param conn Pointer to OBD connection
 */
void es_obd_parse_dtcs(es_obd_connection_t *conn);

/**
 * @brief Check if specific PID is supported
 * @param conn Pointer to OBD connection (after supported PIDs request)
 * @param base_pid Base PID for the supported range (0x00, 0x20, 0x40, etc.)
 * @param target_pid Target PID to check
 * @return true if PID is supported, false otherwise
 */
bool es_obd_is_pid_supported(es_obd_connection_t *conn, uint8_t base_pid, uint8_t target_pid);

/**
 * @brief Extract VIN from Service 09 response
 * @param conn Pointer to OBD connection
 * @param vin_buffer Buffer to store VIN string (minimum 18 bytes)
 * @return Length of VIN string, 0 on error
 */
uint8_t es_obd_extract_vin(es_obd_connection_t *conn, char *vin_buffer);

#ifdef __cplusplus
}
#endif

#endif /* ES_OBD_H */
