/**
 * @file es_ringobj.h
 * @brief Struct ring buffer module
 * @date 2024/10/1
 */

#ifndef __ES_RINGOBJ_H__
#define __ES_RINGOBJ_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/**
 * @brief Struct ring buffer definition
 * Specifically designed for storing fixed-size struct objects
 * @note Key fields use volatile to ensure interrupt safety
 */
typedef struct {
    void *buffer;                    /**< Buffer pointer */
    uint16_t element_size;           /**< Single element size */
    uint16_t capacity;               /**< Buffer capacity (number of elements) */
    volatile uint16_t count;         /**< Current element count (volatile ensures interrupt safety) */
    volatile uint16_t head;          /**< Head index (read position, volatile ensures interrupt safety) */
    volatile uint16_t tail;          /**< Tail index (write position, volatile ensures interrupt safety) */
    uint16_t mask;                   /**< Mask value (capacity - 1) */
} es_ringobj_t;

/**
 * @brief Initialize struct ring buffer
 * @param rb Ring buffer pointer
 * @param buffer Buffer memory pointer
 * @param element_size Single element size
 * @param capacity Buffer capacity (number of elements, must be power of 2)
 * @return 0 on success, -1 on failure
 */
int es_ringobj_init(es_ringobj_t *rb, void *buffer, uint16_t element_size, uint16_t capacity);

/**
 * @brief Reset struct ring buffer
 * @param rb Ring buffer pointer
 * @return 0 on success, -1 on failure
 */
int es_ringobj_reset(es_ringobj_t *rb);

/**
 * @brief Write an element to struct ring buffer
 * @param rb Ring buffer pointer
 * @param element Element pointer to write
 * @return 1 on successful write, 0 if buffer is full
 */
int es_ringobj_put(es_ringobj_t *rb, const void *element);

/**
 * @brief Force write an element, overwrite oldest element if buffer is full
 * @param rb Ring buffer pointer
 * @param element Element pointer to write
 * @return 1 on successful write
 */
int es_ringobj_put_force(es_ringobj_t *rb, const void *element);

/**
 * @brief Read an element from struct ring buffer
 * @param rb Ring buffer pointer
 * @param element Element receive buffer pointer
 * @return 1 on successful read, 0 if buffer is empty
 */
int es_ringobj_get(es_ringobj_t *rb, void *element);

/**
 * @brief Peek at top element of ring buffer without removing it
 * @param rb Ring buffer pointer
 * @param element Element receive buffer pointer
 * @return 1 on successful peek, 0 if buffer is empty
 */
int es_ringobj_peek(es_ringobj_t *rb, void *element);

/**
 * @brief Get element count in ring buffer
 * @param rb Ring buffer pointer
 * @return Current element count
 */
uint16_t es_ringobj_count(es_ringobj_t *rb);

/**
 * @brief Get remaining capacity of ring buffer
 * @param rb Ring buffer pointer
 * @return Remaining capacity (number of elements that can be stored)
 */
uint16_t es_ringobj_free_count(es_ringobj_t *rb);

/**
 * @brief Check if struct ring buffer is empty
 * @param rb Ring buffer pointer
 * @return true if empty, false if not empty
 */
bool es_ringobj_is_empty(es_ringobj_t *rb);

/**
 * @brief Check if struct ring buffer is full
 * @param rb Ring buffer pointer
 * @return true if full, false if not full
 */
bool es_ringobj_is_full(es_ringobj_t *rb);

#ifdef __cplusplus
}
#endif

#endif /* __ES_RINGOBJ_H__ */
