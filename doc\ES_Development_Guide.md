# ES MCU Framework 开发指南

## 1. 概述

本文档为 ES MCU Framework 的开发指南，提供了框架的使用方法、最佳实践、开发规范和故障排除等内容。无论您是初次使用还是深度开发，都可以从本指南中获得有价值的信息。

### 1.1 目标读者

- 嵌入式系统开发工程师
- 车联网/物联网应用开发者
- 系统架构师和技术负责人
- 框架维护和扩展开发者

### 1.2 前置知识

- C语言编程基础
- 嵌入式系统开发经验
- RT-Thread 或其他 RTOS 使用经验
- CAN总线和车载网络基础知识

## 2. 快速开始

### 2.1 环境搭建

#### 2.1.1 开发环境要求

**硬件要求:**
- EV_F460_LQ100_V2 开发板 (HC32F460PETB)
- J-Link 或 ST-Link 调试器
- USB Type-A to Micro USB 连接线

**软件要求:**
- Keil MDK5 或 IAR Embedded Workbench
- Python 3.7+ (用于构建脚本)
- Git 版本控制工具
- 串口调试工具

#### 2.1.2 获取源码

```bash
# 克隆仓库
git clone <repository-url> es_mcu
cd es_mcu

# 初始化子模块
git submodule update --init --recursive
```

#### 2.1.3 编译构建

```bash
# 使用 SCons 构建（推荐）
scons

# 或使用平台特定脚本
build_hc32.bat    # HC32F460 平台
build_win32.bat   # Win32 仿真平台

# 生成 IDE 工程文件
scons --target=mdk5    # 生成 Keil 工程
scons --target=iar     # 生成 IAR 工程
```

### 2.2 第一个应用

#### 2.2.1 创建简单的LED闪烁任务

```c
#include "es.h"
#include "es_scheduler.h"
#include "es_drv_pin.h"

// LED闪烁协程任务
es_async_t led_blink_task(es_coro_t *coro, void *ctx) {
    static uint32_t last_time = 0;
    
    es_co_begin;
    
    // 初始化LED引脚
    es_pin_mode(LED_GREEN_PIN, ES_PIN_MODE_OUTPUT);
    
    while (1) {
        // 切换LED状态
        es_pin_toggle(LED_GREEN_PIN);
        
        // 记录当前时间
        last_time = es_get_timestamp();
        
        // 等待500ms
        es_co_wait(es_get_timestamp() - last_time >= 500);
    }
    
    es_co_end;
}

// 应用初始化
void app_init(void) {
    static es_coro_task_t led_task;
    
    // 初始化LED任务
    es_scheduler_task_init(&led_task, "led_blink", led_blink_task, NULL);
    
    // 添加到调度器
    es_scheduler_task_add(es_scheduler_get_default(), &led_task);
    
    ES_LOGI("APP", "LED blink task started");
}

// 主函数
int main(void) {
    // 系统初始化
    es_init();
    
    // 应用初始化
    app_init();
    
    ES_LOGI("MAIN", "System started");
    
    // 主循环
    while (1) {
        es_scheduler_run(es_scheduler_get_default());
    }
}
```

#### 2.2.2 添加按键处理

```c
#include "es_button.h"

// 按键事件处理
void button_callback(uint8_t pin, es_button_event_t event, void *arg) {
    switch (event) {
        case ES_BUTTON_EVENT_PRESSED:
            ES_LOGI("BTN", "Button pressed");
            break;
            
        case ES_BUTTON_EVENT_RELEASED:
            ES_LOGI("BTN", "Button released");
            break;
            
        case ES_BUTTON_EVENT_LONG_PRESS:
            ES_LOGI("BTN", "Button long pressed");
            // 执行系统重启
            es_system_reset();
            break;
            
        default:
            break;
    }
}

// 按键初始化
void button_init(void) {
    static es_button_t user_button;
    
    // 注册按键
    es_button_register(&user_button, KEY_PIN, button_callback, NULL);
    
    ES_LOGI("BTN", "Button initialized");
}
```

## 3. 协程编程指南

### 3.1 协程基础概念

协程是一种轻量级的用户态线程，通过协作式调度实现并发执行。ES框架的协程基于状态机实现，具有以下特点：

- **无栈设计**: 不需要为每个协程分配独立的栈空间
- **协作式调度**: 协程主动让出CPU控制权
- **状态保持**: 协程可以在任意位置暂停和恢复执行

### 3.2 协程编程模式

#### 3.2.1 基本协程结构

```c
es_async_t my_coroutine(es_coro_t *coro, void *ctx) {
    // 静态变量用于保持状态
    static int state = 0;
    static uint32_t timer = 0;
    
    es_co_begin;
    
    // 初始化代码
    state = 0;
    ES_LOGI("CORO", "Coroutine started");
    
    while (1) {
        // 状态机处理
        switch (state) {
            case 0:
                // 执行状态0的逻辑
                timer = es_get_timestamp();
                state = 1;
                break;
                
            case 1:
                // 等待条件满足
                if (es_get_timestamp() - timer >= 1000) {
                    state = 2;
                }
                break;
                
            case 2:
                // 执行状态2的逻辑
                ES_LOGI("CORO", "State 2 executed");
                state = 0;  // 回到初始状态
                break;
        }
        
        // 让出CPU控制权
        es_co_yield;
    }
    
    es_co_end;
}
```

#### 3.2.2 等待条件的协程

```c
es_async_t wait_condition_task(es_coro_t *coro, void *ctx) {
    static uint32_t start_time;
    static bool condition_met = false;
    
    es_co_begin;
    
    start_time = es_get_timestamp();
    
    // 等待条件满足或超时
    es_co_wait(condition_met || (es_get_timestamp() - start_time > 5000));
    
    if (condition_met) {
        ES_LOGI("WAIT", "Condition met successfully");
    } else {
        ES_LOGW("WAIT", "Timeout occurred");
    }
    
    es_co_end;
}
```

#### 3.2.3 子协程使用

```c
es_async_t parent_coroutine(es_coro_t *coro, void *ctx) {
    static es_coro_t child_coro;
    static es_async_t child_result;
    
    es_co_begin;
    
    // 分配子协程
    if (es_coro_alloc(coro) == 0) {
        ES_LOGE("PARENT", "Failed to allocate child coroutine");
        es_co_err;
    }
    
    // 获取子协程指针
    es_coro_t *child = es_coro_get(coro);
    
    // 调用子协程
    do {
        child_result = child_coroutine(child, NULL);
        es_co_yield;
    } while (child_result == ES_ASYNC_YIELD || child_result == ES_ASYNC_WAIT);
    
    // 释放子协程
    es_coro_free(coro);
    
    if (child_result == ES_ASYNC_DONE) {
        ES_LOGI("PARENT", "Child coroutine completed successfully");
    } else {
        ES_LOGE("PARENT", "Child coroutine failed");
    }
    
    es_co_end;
}
```

### 3.3 协程最佳实践

#### 3.3.1 状态管理

1. **使用静态变量**: 协程函数中的局部变量在让出CPU后会丢失，必须使用静态变量保持状态
2. **初始化检查**: 在协程开始时检查是否需要初始化静态变量
3. **状态重置**: 在协程结束或错误时适当重置状态

#### 3.3.2 错误处理

```c
es_async_t robust_coroutine(es_coro_t *coro, void *ctx) {
    static int retry_count = 0;
    static int result;
    
    es_co_begin;
    
    retry_count = 0;
    
    while (retry_count < 3) {
        result = perform_operation();
        
        if (result == 0) {
            // 操作成功
            ES_LOGI("ROBUST", "Operation succeeded");
            break;
        } else {
            // 操作失败，重试
            retry_count++;
            ES_LOGW("ROBUST", "Operation failed, retry %d/3", retry_count);
            
            if (retry_count >= 3) {
                ES_LOGE("ROBUST", "Operation failed after 3 retries");
                es_co_err;  // 错误退出
            }
            
            // 等待一段时间后重试
            uint32_t wait_start = es_get_timestamp();
            es_co_wait(es_get_timestamp() - wait_start >= 1000);
        }
    }
    
    es_co_end;
}
```

#### 3.3.3 资源管理

```c
es_async_t resource_management_task(es_coro_t *coro, void *ctx) {
    static void *buffer = NULL;
    static es_file_handle_t file = NULL;
    
    es_co_begin;
    
    // 分配资源
    buffer = es_malloc(1024);
    if (buffer == NULL) {
        ES_LOGE("RES", "Failed to allocate buffer");
        es_co_err;
    }
    
    file = es_file_open("data.txt", "r");
    if (file == NULL) {
        ES_LOGE("RES", "Failed to open file");
        goto cleanup;
    }
    
    // 使用资源进行操作
    // ...
    
    // 正常清理资源
    es_file_close(file);
    es_free(buffer);
    file = NULL;
    buffer = NULL;
    
    es_co_exit;  // 正常退出
    
cleanup:
    // 错误清理资源
    if (file != NULL) {
        es_file_close(file);
        file = NULL;
    }
    if (buffer != NULL) {
        es_free(buffer);
        buffer = NULL;
    }
    
    es_co_err;  // 错误退出
}
```

## 4. 配置管理最佳实践

### 4.1 参数设计原则

#### 4.1.1 参数分类

```c
// 工厂参数 - 出厂设置，一般不允许用户修改
#define FACTORY_DEVICE_ID       0x0001
#define FACTORY_HARDWARE_VER    0x0002
#define FACTORY_CALIBRATION     0x0003

// 用户参数 - 用户可配置的参数
#define USER_DEVICE_NAME        0x0010
#define USER_SERVER_ADDR        0x0011
#define USER_REPORT_INTERVAL    0x0012

// 系统参数 - 系统运行时参数
#define SYS_BOOT_COUNT          0x0020
#define SYS_LAST_ERROR          0x0021
#define SYS_RUNTIME_STATS       0x0022
```

#### 4.1.2 参数验证

```c
// 参数设置函数示例
int set_report_interval(uint16_t interval) {
    // 参数范围检查
    if (interval < 10 || interval > 3600) {
        ES_LOGE("CFG", "Invalid report interval: %d", interval);
        return -1;
    }
    
    // 设置参数
    int ret = es_cfg_v2_set(USER_REPORT_INTERVAL, &interval, sizeof(interval));
    if (ret != ES_CFG_V2_OK) {
        ES_LOGE("CFG", "Failed to set report interval: %d", ret);
        return -1;
    }
    
    // 立即保存
    es_cfg_v2_save();
    
    ES_LOGI("CFG", "Report interval set to %d seconds", interval);
    return 0;
}
```

### 4.2 配置管理模式

#### 4.2.1 延迟保存模式

```c
// 批量配置更新
void batch_config_update(void) {
    // 设置多个参数（不立即保存）
    es_cfg_v2_set(USER_DEVICE_NAME, "MyDevice", 8);
    es_cfg_v2_set(USER_SERVER_ADDR, "192.168.1.100", 13);
    es_cfg_v2_set(USER_REPORT_INTERVAL, &interval, sizeof(interval));
    
    // 批量保存
    int ret = es_cfg_v2_save();
    if (ret == ES_CFG_V2_OK) {
        ES_LOGI("CFG", "Batch configuration saved");
    } else {
        ES_LOGE("CFG", "Failed to save configuration: %d", ret);
    }
}
```

#### 4.2.2 配置备份和恢复

```c
// 配置备份
void backup_user_config(void) {
    // 导出用户配置到文件或网络
    // 这里简化为日志输出
    ES_LOGI("CFG", "Backing up user configuration...");
    es_cfg_v2_dump_category(ES_CFG_V2_CATEGORY_USER);
}

// 恢复出厂设置
void factory_reset(void) {
    ES_LOGW("CFG", "Performing factory reset...");
    
    // 重置用户参数
    int ret = es_cfg_v2_reset_by_category(ES_CFG_V2_CATEGORY_USER, true);
    if (ret == ES_CFG_V2_OK) {
        ES_LOGI("CFG", "Factory reset completed");
        
        // 重启系统使配置生效
        es_os_delay_ms(1000);
        es_system_reset();
    } else {
        ES_LOGE("CFG", "Factory reset failed: %d", ret);
    }
}
```

## 5. 通信协议开发

### 5.1 自定义协议实现

#### 5.1.1 协议帧定义

```c
// 自定义协议帧格式
#define CUSTOM_FRAME_HEAD \
    X(custom_head, 0x10, 0x00, \
      M(x16, sync)      /* 同步字 */ \
      M(x8, cmd)        /* 命令字 */ \
      M(x8, len)        /* 数据长度 */ \
      M(x16, crc))      /* CRC校验 */

// 协议命令定义
typedef enum {
    CMD_HEARTBEAT = 0x01,
    CMD_DATA_REPORT = 0x02,
    CMD_CONFIG_SET = 0x03,
    CMD_CONFIG_GET = 0x04,
    CMD_FIRMWARE_UPDATE = 0x05,
} custom_cmd_t;
```

#### 5.1.2 协议处理函数

```c
// 协议处理协程
es_async_t custom_protocol_task(es_coro_t *coro, void *ctx) {
    static uint8_t rx_buffer[256];
    static uint16_t rx_len;
    static custom_frame_t frame;
    
    es_co_begin;
    
    while (1) {
        // 接收数据
        es_co_await(uart_receive(coro, rx_buffer, &rx_len, 1000));
        
        // 解析协议帧
        if (parse_custom_frame(rx_buffer, rx_len, &frame) == 0) {
            // 处理协议命令
            handle_custom_command(&frame);
        } else {
            ES_LOGW("PROTO", "Invalid frame received");
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// 命令处理函数
void handle_custom_command(const custom_frame_t *frame) {
    switch (frame->cmd) {
        case CMD_HEARTBEAT:
            handle_heartbeat(frame);
            break;
            
        case CMD_DATA_REPORT:
            handle_data_report(frame);
            break;
            
        case CMD_CONFIG_SET:
            handle_config_set(frame);
            break;
            
        case CMD_CONFIG_GET:
            handle_config_get(frame);
            break;
            
        default:
            ES_LOGW("PROTO", "Unknown command: 0x%02X", frame->cmd);
            send_error_response(frame->cmd, ERROR_UNKNOWN_COMMAND);
            break;
    }
}
```

### 5.2 CAN通信开发

#### 5.2.1 CAN消息处理

```c
// CAN消息处理协程
es_async_t can_message_task(es_coro_t *coro, void *ctx) {
    static es_can_msg_t msg;
    static int ret;
    
    es_co_begin;
    
    while (1) {
        // 接收CAN消息
        ret = es_can_receive(ES_CAN_PORT_0, &msg);
        if (ret == 0) {
            // 处理接收到的消息
            process_can_message(&msg);
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// CAN消息处理函数
void process_can_message(const es_can_msg_t *msg) {
    switch (msg->id) {
        case 0x123:  // 发动机转速
            process_engine_rpm(msg);
            break;
            
        case 0x456:  // 车速信息
            process_vehicle_speed(msg);
            break;
            
        case 0x7DF:  // 诊断请求
            process_diagnostic_request(msg);
            break;
            
        default:
            ES_LOGD("CAN", "Unknown CAN ID: 0x%03X", msg->id);
            break;
    }
}
```

## 6. 调试和故障排除

### 6.1 调试技巧

#### 6.1.1 日志调试

```c
// 使用不同级别的日志
void debug_function(void) {
    ES_LOGD("DEBUG", "Entering debug_function");
    
    int result = some_operation();
    ES_LOGD("DEBUG", "Operation result: %d", result);
    
    if (result < 0) {
        ES_LOGE("DEBUG", "Operation failed with error: %d", result);
        return;
    }
    
    ES_LOGI("DEBUG", "Operation completed successfully");
}

// 十六进制数据调试
void debug_data(const uint8_t *data, size_t len) {
    ES_LOG_HEX("DATA", 16, data, len);
}
```

#### 6.1.2 状态监控

```c
// 系统状态监控任务
es_async_t system_monitor_task(es_coro_t *coro, void *ctx) {
    static uint32_t last_report = 0;
    static es_mem_stats_t mem_stats;
    
    es_co_begin;
    
    while (1) {
        // 每10秒报告一次系统状态
        if (es_get_timestamp() - last_report >= 10000) {
            // 内存使用情况
            es_mem_get_stats(&mem_stats);
            ES_LOGI("MON", "Memory: used=%d, free=%d, peak=%d", 
                   mem_stats.used_size, mem_stats.free_size, mem_stats.peak_used);
            
            // 系统运行时间
            ES_LOGI("MON", "Uptime: %d seconds", es_get_uptime() / 1000);
            
            last_report = es_get_timestamp();
        }
        
        es_co_yield;
    }
    
    es_co_end;
}
```

### 6.2 常见问题解决

#### 6.2.1 内存泄漏检测

```c
// 内存泄漏检测
void check_memory_leak(void) {
    static size_t last_used = 0;
    size_t current_used = es_mem_get_used_size();
    
    if (current_used > last_used + 1024) {  // 增长超过1KB
        ES_LOGW("MEM", "Potential memory leak detected: %d -> %d", 
               last_used, current_used);
        
        // 打印内存统计
        es_mem_stats_t stats;
        es_mem_get_stats(&stats);
        ES_LOGI("MEM", "Total: %d, Used: %d, Free: %d, Peak: %d",
               stats.total_size, stats.used_size, stats.free_size, stats.peak_used);
    }
    
    last_used = current_used;
}
```

#### 6.2.2 看门狗处理

```c
// 看门狗喂狗任务
es_async_t watchdog_task(es_coro_t *coro, void *ctx) {
    static uint32_t last_feed = 0;
    
    es_co_begin;
    
    while (1) {
        // 每1秒喂一次狗
        if (es_get_timestamp() - last_feed >= 1000) {
            es_os_wdt_feed();
            last_feed = es_get_timestamp();
            ES_LOGV("WDT", "Watchdog fed");
        }
        
        es_co_yield;
    }
    
    es_co_end;
}
```

## 7. 性能优化

### 7.1 内存优化

1. **使用内存池**: 对固定大小的对象使用内存池
2. **减少动态分配**: 尽量使用静态分配
3. **及时释放**: 不再使用的内存及时释放
4. **内存对齐**: 注意结构体内存对齐

### 7.2 CPU优化

1. **合理调度**: 协程适时让出CPU
2. **避免长时间阻塞**: 使用协程等待代替阻塞
3. **优化算法**: 选择合适的算法和数据结构
4. **编译器优化**: 使用编译器优化选项

### 7.3 存储优化

1. **批量操作**: 减少Flash写入次数
2. **压缩数据**: 对大量数据进行压缩
3. **磨损均衡**: 合理使用Flash避免过度磨损
4. **缓存机制**: 使用缓存减少存储访问

这个开发指南为 ES MCU Framework 的使用提供了全面的指导，帮助开发者快速上手并掌握框架的核心功能。
