# ES MCU Framework 使用示例

## 1. 概述

本文档提供了 ES MCU Framework 的典型使用示例，涵盖了各个核心模块的实际应用场景。通过这些示例，开发者可以快速理解框架的使用方法，并将其应用到实际项目中。

### 1.1 示例分类

- **基础应用**: LED控制、按键处理、串口通信
- **协程应用**: 多任务协程、定时器、事件处理
- **通信应用**: CAN通信、ISO-TP、UDS诊断
- **存储应用**: 配置管理、日志记录、Flash操作
- **综合应用**: 完整的车载终端应用

## 2. 基础应用示例

### 2.1 LED控制示例

```c
#include "es.h"
#include "es_scheduler.h"
#include "es_drv_pin.h"

// LED状态枚举
typedef enum {
    LED_STATE_OFF = 0,
    LED_STATE_ON,
    LED_STATE_BLINK_SLOW,
    LED_STATE_BLINK_FAST,
} led_state_t;

// LED控制协程
es_async_t led_control_task(es_coro_t *coro, void *ctx) {
    static led_state_t led_state = LED_STATE_OFF;
    static uint32_t last_toggle = 0;
    static uint32_t blink_interval = 500;
    static bool led_on = false;
    
    es_co_begin;
    
    // 初始化LED引脚
    es_pin_mode(LED_GREEN_PIN, ES_PIN_MODE_OUTPUT);
    es_pin_write(LED_GREEN_PIN, ES_PIN_LEVEL_LOW);
    
    while (1) {
        switch (led_state) {
            case LED_STATE_OFF:
                if (led_on) {
                    es_pin_write(LED_GREEN_PIN, ES_PIN_LEVEL_LOW);
                    led_on = false;
                }
                break;
                
            case LED_STATE_ON:
                if (!led_on) {
                    es_pin_write(LED_GREEN_PIN, ES_PIN_LEVEL_HIGH);
                    led_on = true;
                }
                break;
                
            case LED_STATE_BLINK_SLOW:
                blink_interval = 1000;
                goto blink_process;
                
            case LED_STATE_BLINK_FAST:
                blink_interval = 200;
                
            blink_process:
                if (es_get_timestamp() - last_toggle >= blink_interval) {
                    es_pin_toggle(LED_GREEN_PIN);
                    led_on = !led_on;
                    last_toggle = es_get_timestamp();
                }
                break;
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// LED状态设置函数
void set_led_state(led_state_t state) {
    // 这里可以通过全局变量或消息队列传递状态
    // 简化示例中直接修改静态变量
    ES_LOGI("LED", "LED state changed to %d", state);
}
```

### 2.2 按键处理示例

```c
#include "es_button.h"

// 按键状态机
typedef enum {
    KEY_STATE_IDLE = 0,
    KEY_STATE_PRESSED,
    KEY_STATE_LONG_PRESS,
    KEY_STATE_DOUBLE_CLICK,
} key_state_t;

// 按键事件处理
void button_event_handler(uint8_t pin, es_button_event_t event, void *arg) {
    static uint32_t last_press_time = 0;
    static int press_count = 0;
    
    switch (event) {
        case ES_BUTTON_EVENT_PRESSED:
            ES_LOGI("KEY", "Button pressed");
            
            // 检测双击
            uint32_t current_time = es_get_timestamp();
            if (current_time - last_press_time < 500) {
                press_count++;
                if (press_count >= 2) {
                    ES_LOGI("KEY", "Double click detected");
                    // 执行双击操作
                    handle_double_click();
                    press_count = 0;
                }
            } else {
                press_count = 1;
            }
            last_press_time = current_time;
            break;
            
        case ES_BUTTON_EVENT_RELEASED:
            ES_LOGI("KEY", "Button released");
            
            // 单击处理
            if (press_count == 1) {
                // 延迟处理，等待可能的双击
                // 这里简化为立即处理
                handle_single_click();
            }
            break;
            
        case ES_BUTTON_EVENT_LONG_PRESS:
            ES_LOGI("KEY", "Button long pressed");
            handle_long_press();
            press_count = 0;  // 重置计数
            break;
            
        default:
            break;
    }
}

// 按键操作处理函数
void handle_single_click(void) {
    ES_LOGI("KEY", "Single click action");
    set_led_state(LED_STATE_BLINK_SLOW);
}

void handle_double_click(void) {
    ES_LOGI("KEY", "Double click action");
    set_led_state(LED_STATE_BLINK_FAST);
}

void handle_long_press(void) {
    ES_LOGI("KEY", "Long press action - System reset");
    es_os_delay_ms(1000);
    es_system_reset();
}
```

### 2.3 串口通信示例

```c
#include "es_drv_uart.h"

// 串口数据处理协程
es_async_t uart_comm_task(es_coro_t *coro, void *ctx) {
    static uint8_t rx_buffer[256];
    static uint16_t rx_len = 0;
    static uint8_t rx_byte;
    
    es_co_begin;
    
    // 初始化串口
    es_uart_init(ES_UART_PORT_1, 115200);
    
    while (1) {
        // 检查是否有数据接收
        if (es_uart_is_rx_ready(ES_UART_PORT_1)) {
            if (es_uart_read_byte(ES_UART_PORT_1, &rx_byte) == 0) {
                rx_buffer[rx_len++] = rx_byte;
                
                // 检查是否接收到完整的命令（以\r\n结尾）
                if (rx_len >= 2 && 
                    rx_buffer[rx_len-2] == '\r' && 
                    rx_buffer[rx_len-1] == '\n') {
                    
                    // 处理接收到的命令
                    rx_buffer[rx_len-2] = '\0';  // 去掉\r\n
                    process_uart_command((char*)rx_buffer);
                    
                    // 重置缓冲区
                    rx_len = 0;
                }
                
                // 防止缓冲区溢出
                if (rx_len >= sizeof(rx_buffer) - 1) {
                    rx_len = 0;
                    ES_LOGW("UART", "RX buffer overflow");
                }
            }
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// 串口命令处理
void process_uart_command(const char *cmd) {
    ES_LOGI("UART", "Received command: %s", cmd);
    
    if (strcmp(cmd, "LED_ON") == 0) {
        set_led_state(LED_STATE_ON);
        uart_send_response("OK\r\n");
    } else if (strcmp(cmd, "LED_OFF") == 0) {
        set_led_state(LED_STATE_OFF);
        uart_send_response("OK\r\n");
    } else if (strcmp(cmd, "STATUS") == 0) {
        send_system_status();
    } else {
        uart_send_response("ERROR: Unknown command\r\n");
    }
}

// 发送响应
void uart_send_response(const char *response) {
    es_uart_write(ES_UART_PORT_1, (const uint8_t*)response, strlen(response));
}

// 发送系统状态
void send_system_status(void) {
    char status_msg[128];
    es_mem_stats_t mem_stats;
    
    es_mem_get_stats(&mem_stats);
    
    snprintf(status_msg, sizeof(status_msg),
             "STATUS: Uptime=%ds, Memory=%d/%d bytes\r\n",
             es_get_uptime() / 1000,
             mem_stats.used_size,
             mem_stats.total_size);
    
    uart_send_response(status_msg);
}
```

## 3. 协程应用示例

### 3.1 多任务协程示例

```c
// 数据采集任务
es_async_t data_collection_task(es_coro_t *coro, void *ctx) {
    static uint32_t last_sample = 0;
    static uint16_t adc_value;
    static float temperature;
    
    es_co_begin;
    
    while (1) {
        // 每100ms采集一次数据
        if (es_get_timestamp() - last_sample >= 100) {
            // 读取ADC值
            adc_value = es_adc_read(ES_ADC_CHANNEL_0);
            
            // 转换为温度值
            temperature = (adc_value * 3.3f / 4096.0f - 0.5f) / 0.01f;
            
            // 存储到全局变量或发送到其他任务
            update_sensor_data(SENSOR_TEMPERATURE, temperature);
            
            last_sample = es_get_timestamp();
            
            ES_LOGD("SENSOR", "Temperature: %.1f°C", temperature);
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// 数据上报任务
es_async_t data_report_task(es_coro_t *coro, void *ctx) {
    static uint32_t last_report = 0;
    static uint16_t report_interval = 5000;  // 5秒上报一次
    static sensor_data_t sensor_data;
    
    es_co_begin;
    
    // 从配置中读取上报间隔
    es_cfg_v2_get(USER_REPORT_INTERVAL, &report_interval, sizeof(report_interval));
    report_interval *= 1000;  // 转换为毫秒
    
    while (1) {
        if (es_get_timestamp() - last_report >= report_interval) {
            // 获取传感器数据
            get_sensor_data(&sensor_data);
            
            // 上报数据
            report_sensor_data(&sensor_data);
            
            last_report = es_get_timestamp();
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// 系统监控任务
es_async_t system_monitor_task(es_coro_t *coro, void *ctx) {
    static uint32_t last_check = 0;
    static es_mem_stats_t mem_stats;
    static uint16_t battery_voltage;
    
    es_co_begin;
    
    while (1) {
        // 每30秒检查一次系统状态
        if (es_get_timestamp() - last_check >= 30000) {
            // 检查内存使用情况
            es_mem_get_stats(&mem_stats);
            if (mem_stats.used_size > mem_stats.total_size * 0.8) {
                ES_LOGW("MON", "High memory usage: %d%%", 
                       mem_stats.used_size * 100 / mem_stats.total_size);
            }
            
            // 检查电池电压
            battery_voltage = es_pm_get_battery_voltage();
            if (battery_voltage < 3300) {
                ES_LOGW("MON", "Low battery: %d mV", battery_voltage);
                
                // 进入低功耗模式
                enter_power_save_mode();
            }
            
            last_check = es_get_timestamp();
        }
        
        es_co_yield;
    }
    
    es_co_end;
}
```

### 3.2 定时器应用示例

```c
// 定时器回调函数
es_async_t heartbeat_timer_callback(es_coro_t *coro, void *ctx) {
    static uint32_t heartbeat_count = 0;
    
    es_co_begin;
    
    heartbeat_count++;
    ES_LOGI("TIMER", "Heartbeat #%d", heartbeat_count);
    
    // 发送心跳包
    send_heartbeat_packet();
    
    es_co_end;
}

es_async_t watchdog_timer_callback(es_coro_t *coro, void *ctx) {
    es_co_begin;
    
    // 喂看门狗
    es_os_wdt_feed();
    ES_LOGV("WDT", "Watchdog fed");
    
    es_co_end;
}

// 定时器初始化
void timer_init(void) {
    static es_timer_t heartbeat_timer;
    static es_timer_t watchdog_timer;
    
    // 创建心跳定时器（10秒周期）
    es_scheduler_timer_init(&heartbeat_timer, 10000, 
                           heartbeat_timer_callback, NULL, false);
    es_scheduler_timer_add(es_scheduler_get_default(), &heartbeat_timer);
    
    // 创建看门狗定时器（1秒周期）
    es_scheduler_timer_init(&watchdog_timer, 1000, 
                           watchdog_timer_callback, NULL, false);
    es_scheduler_timer_add(es_scheduler_get_default(), &watchdog_timer);
    
    ES_LOGI("TIMER", "Timers initialized");
}
```

## 4. 通信应用示例

### 4.1 CAN通信示例

```c
#include "es_drv_can.h"

// CAN消息发送任务
es_async_t can_tx_task(es_coro_t *coro, void *ctx) {
    static es_can_msg_t tx_msg;
    static uint32_t last_send = 0;
    static uint8_t counter = 0;
    
    es_co_begin;
    
    // 初始化CAN
    es_can_init(ES_CAN_PORT_0, 500000);  // 500kbps
    
    while (1) {
        // 每1秒发送一次状态消息
        if (es_get_timestamp() - last_send >= 1000) {
            // 构造CAN消息
            tx_msg.id = 0x123;
            tx_msg.ide = 0;  // 标准帧
            tx_msg.rtr = 0;  // 数据帧
            tx_msg.dlc = 8;
            
            // 填充数据
            tx_msg.data[0] = counter++;
            tx_msg.data[1] = es_pm_get_battery_voltage() >> 8;
            tx_msg.data[2] = es_pm_get_battery_voltage() & 0xFF;
            tx_msg.data[3] = get_system_temperature();
            tx_msg.data[4] = get_system_status();
            tx_msg.data[5] = 0x00;
            tx_msg.data[6] = 0x00;
            tx_msg.data[7] = calculate_checksum(tx_msg.data, 7);
            
            // 发送消息
            if (es_can_send(ES_CAN_PORT_0, &tx_msg) == 0) {
                ES_LOGD("CAN", "Status message sent, counter=%d", counter-1);
            } else {
                ES_LOGW("CAN", "Failed to send status message");
            }
            
            last_send = es_get_timestamp();
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// CAN消息接收任务
es_async_t can_rx_task(es_coro_t *coro, void *ctx) {
    static es_can_msg_t rx_msg;
    static int ret;
    
    es_co_begin;
    
    while (1) {
        // 接收CAN消息
        ret = es_can_receive(ES_CAN_PORT_0, &rx_msg);
        if (ret == 0) {
            ES_LOGD("CAN", "Received CAN message: ID=0x%03X, DLC=%d", 
                   rx_msg.id, rx_msg.dlc);
            
            // 处理接收到的消息
            process_can_message(&rx_msg);
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// CAN消息处理
void process_can_message(const es_can_msg_t *msg) {
    switch (msg->id) {
        case 0x456:  // 远程控制命令
            if (msg->dlc >= 2) {
                uint8_t cmd = msg->data[0];
                uint8_t param = msg->data[1];
                
                switch (cmd) {
                    case 0x01:  // LED控制
                        set_led_state(param);
                        break;
                        
                    case 0x02:  // 系统重启
                        ES_LOGI("CAN", "Remote reset command received");
                        es_os_delay_ms(1000);
                        es_system_reset();
                        break;
                        
                    default:
                        ES_LOGW("CAN", "Unknown command: 0x%02X", cmd);
                        break;
                }
            }
            break;
            
        case 0x789:  // 配置更新
            if (msg->dlc >= 4) {
                uint16_t param_id = (msg->data[0] << 8) | msg->data[1];
                uint16_t param_value = (msg->data[2] << 8) | msg->data[3];
                
                // 更新配置参数
                es_cfg_v2_set(param_id, &param_value, sizeof(param_value));
                ES_LOGI("CAN", "Parameter 0x%04X updated to %d", param_id, param_value);
            }
            break;
            
        default:
            ES_LOGV("CAN", "Unhandled CAN message: ID=0x%03X", msg->id);
            break;
    }
}
```

### 4.2 UDS诊断示例

```c
#include "es_uds.h"

// UDS诊断服务任务
es_async_t uds_diagnostic_task(es_coro_t *coro, void *ctx) {
    static es_uds_client_t uds_client;
    static uint8_t response_data[256];
    static uint16_t response_len;
    static int result;
    
    es_co_begin;
    
    // 初始化UDS客户端
    es_uds_client_init(&uds_client, 0x7E0, 0x7E8);
    
    // 进入扩展诊断会话
    ES_LOGI("UDS", "Entering extended diagnostic session");
    es_co_await(es_uds_diagnostic_session_control(coro, &uds_client, 0x03));
    
    // 读取VIN码
    ES_LOGI("UDS", "Reading VIN");
    response_len = sizeof(response_data);
    es_co_await(es_uds_read_data_by_identifier(coro, &uds_client, 0xF190, 
                                              response_data, &response_len));
    
    if (response_len > 0) {
        response_data[response_len] = '\0';
        ES_LOGI("UDS", "VIN: %s", response_data);
    }
    
    // 读取软件版本
    ES_LOGI("UDS", "Reading software version");
    response_len = sizeof(response_data);
    es_co_await(es_uds_read_data_by_identifier(coro, &uds_client, 0xF194, 
                                              response_data, &response_len));
    
    if (response_len > 0) {
        ES_LOGI("UDS", "Software version: %.*s", response_len, response_data);
    }
    
    // 保持测试器在线
    while (1) {
        es_co_await(es_uds_tester_present(coro, &uds_client));
        
        // 等待2秒
        uint32_t start_time = es_get_timestamp();
        es_co_wait(es_get_timestamp() - start_time >= 2000);
    }
    
    es_co_end;
}
```

## 5. 存储应用示例

### 5.1 配置管理示例

```c
// 配置管理任务
es_async_t config_manager_task(es_coro_t *coro, void *ctx) {
    static char device_name[32];
    static uint16_t report_interval;
    static uint32_t device_id;
    
    es_co_begin;
    
    // 读取设备配置
    if (es_cfg_v2_get(USER_DEVICE_NAME, device_name, sizeof(device_name)) > 0) {
        ES_LOGI("CFG", "Device name: %s", device_name);
    }
    
    if (es_cfg_v2_get(USER_REPORT_INTERVAL, &report_interval, sizeof(report_interval)) > 0) {
        ES_LOGI("CFG", "Report interval: %d seconds", report_interval);
    }
    
    if (es_cfg_v2_get(FACTORY_DEVICE_ID, &device_id, sizeof(device_id)) > 0) {
        ES_LOGI("CFG", "Device ID: 0x%08X", device_id);
    }
    
    // 监控配置变化
    while (1) {
        // 这里可以监控配置变化事件
        // 简化示例中每10秒检查一次
        
        uint32_t start_time = es_get_timestamp();
        es_co_wait(es_get_timestamp() - start_time >= 10000);
        
        // 检查配置是否需要保存
        // 这里可以添加配置变化检测逻辑
        
        es_co_yield;
    }
    
    es_co_end;
}

// 配置更新函数
void update_device_config(const char *name, uint16_t interval) {
    int ret;
    
    // 更新设备名称
    if (name != NULL) {
        ret = es_cfg_v2_set(USER_DEVICE_NAME, name, strlen(name));
        if (ret == ES_CFG_V2_OK) {
            ES_LOGI("CFG", "Device name updated: %s", name);
        } else {
            ES_LOGE("CFG", "Failed to update device name: %d", ret);
        }
    }
    
    // 更新上报间隔
    if (interval > 0) {
        ret = es_cfg_v2_set(USER_REPORT_INTERVAL, &interval, sizeof(interval));
        if (ret == ES_CFG_V2_OK) {
            ES_LOGI("CFG", "Report interval updated: %d", interval);
        } else {
            ES_LOGE("CFG", "Failed to update report interval: %d", ret);
        }
    }
    
    // 保存配置
    ret = es_cfg_v2_save();
    if (ret == ES_CFG_V2_OK) {
        ES_LOGI("CFG", "Configuration saved successfully");
    } else {
        ES_LOGE("CFG", "Failed to save configuration: %d", ret);
    }
}
```

### 5.2 日志记录示例

```c
// 日志记录示例
void logging_examples(void) {
    // 基本日志记录
    ES_LOGE("EXAMPLE", "This is an error message");
    ES_LOGW("EXAMPLE", "This is a warning message");
    ES_LOGI("EXAMPLE", "This is an info message");
    ES_LOGD("EXAMPLE", "This is a debug message");
    ES_LOGV("EXAMPLE", "This is a verbose message");
    
    // 格式化日志
    int temperature = 25;
    float voltage = 3.3f;
    ES_LOGI("SENSOR", "Temperature: %d°C, Voltage: %.2fV", temperature, voltage);
    
    // 十六进制数据打印
    uint8_t data[] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
    ES_LOG_HEX("DATA", 8, data, sizeof(data));
    
    // 条件日志
    if (temperature > 30) {
        ES_LOGW("TEMP", "High temperature detected: %d°C", temperature);
    }
    
    // 错误处理日志
    int result = some_operation();
    if (result != 0) {
        ES_LOGE("OP", "Operation failed with error code: %d", result);
    } else {
        ES_LOGI("OP", "Operation completed successfully");
    }
}

// 自定义日志输出器示例
void custom_log_sink(es_log_level_t level, bool to_flash, 
                    const char *log_msg, uint32_t len) {
    // 只处理错误级别的日志
    if (level == ES_LOG_LEVEL_ERROR) {
        // 发送到远程服务器
        send_error_to_server(log_msg, len);
        
        // 或者触发LED指示
        set_led_state(LED_STATE_BLINK_FAST);
    }
}

// 注册自定义日志输出器
void register_custom_log_sink(void) {
    static es_log_sink_t custom_sink = {
        .name = "custom",
        .sink_func = custom_log_sink,
    };
    
    es_log_add_sink(&custom_sink);
    ES_LOGI("LOG", "Custom log sink registered");
}
```

## 6. 综合应用示例

### 6.1 车载终端应用

```c
// 车载终端主应用
typedef struct {
    bool ignition_on;
    uint16_t vehicle_speed;
    uint16_t engine_rpm;
    float fuel_level;
    gps_position_t gps_pos;
    uint32_t mileage;
} vehicle_status_t;

static vehicle_status_t g_vehicle_status = {0};

// 车辆数据采集任务
es_async_t vehicle_data_task(es_coro_t *coro, void *ctx) {
    static es_can_msg_t can_msg;
    static uint32_t last_update = 0;
    
    es_co_begin;
    
    while (1) {
        // 接收CAN数据
        if (es_can_receive(ES_CAN_PORT_0, &can_msg) == 0) {
            switch (can_msg.id) {
                case 0x0C4:  // 发动机转速和车速
                    g_vehicle_status.engine_rpm = (can_msg.data[0] << 8) | can_msg.data[1];
                    g_vehicle_status.vehicle_speed = (can_msg.data[2] << 8) | can_msg.data[3];
                    break;
                    
                case 0x2C4:  // 燃油液位
                    g_vehicle_status.fuel_level = can_msg.data[0] * 0.4f;
                    break;
                    
                case 0x3C4:  // 点火状态
                    g_vehicle_status.ignition_on = (can_msg.data[0] & 0x01) != 0;
                    break;
            }
        }
        
        // 每秒更新一次里程
        if (es_get_timestamp() - last_update >= 1000) {
            if (g_vehicle_status.vehicle_speed > 0) {
                g_vehicle_status.mileage += g_vehicle_status.vehicle_speed / 3600;  // 简化计算
            }
            last_update = es_get_timestamp();
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// GPS数据处理任务
es_async_t gps_data_task(es_coro_t *coro, void *ctx) {
    static char gps_buffer[256];
    static uint16_t gps_len = 0;
    
    es_co_begin;
    
    while (1) {
        // 从GPS模块读取NMEA数据
        if (read_gps_data(gps_buffer, &gps_len) == 0) {
            if (parse_nmea_data(gps_buffer, &g_vehicle_status.gps_pos) == 0) {
                ES_LOGD("GPS", "Position: %.6f, %.6f", 
                       g_vehicle_status.gps_pos.latitude,
                       g_vehicle_status.gps_pos.longitude);
            }
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// 数据上报任务
es_async_t data_upload_task(es_coro_t *coro, void *ctx) {
    static uint32_t last_upload = 0;
    static uint16_t upload_interval = 30;  // 30秒
    static char json_buffer[512];
    
    es_co_begin;
    
    // 从配置读取上报间隔
    es_cfg_v2_get(USER_REPORT_INTERVAL, &upload_interval, sizeof(upload_interval));
    
    while (1) {
        if (es_get_timestamp() - last_upload >= upload_interval * 1000) {
            // 构造JSON数据
            snprintf(json_buffer, sizeof(json_buffer),
                    "{"
                    "\"timestamp\":%d,"
                    "\"ignition\":%s,"
                    "\"speed\":%d,"
                    "\"rpm\":%d,"
                    "\"fuel\":%.1f,"
                    "\"lat\":%.6f,"
                    "\"lon\":%.6f,"
                    "\"mileage\":%d"
                    "}",
                    es_get_timestamp(),
                    g_vehicle_status.ignition_on ? "true" : "false",
                    g_vehicle_status.vehicle_speed,
                    g_vehicle_status.engine_rpm,
                    g_vehicle_status.fuel_level,
                    g_vehicle_status.gps_pos.latitude,
                    g_vehicle_status.gps_pos.longitude,
                    g_vehicle_status.mileage);
            
            // 上传数据
            if (upload_vehicle_data(json_buffer) == 0) {
                ES_LOGI("UPLOAD", "Vehicle data uploaded successfully");
            } else {
                ES_LOGW("UPLOAD", "Failed to upload vehicle data");
            }
            
            last_upload = es_get_timestamp();
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

// 应用初始化
void vehicle_app_init(void) {
    static es_coro_task_t vehicle_task;
    static es_coro_task_t gps_task;
    static es_coro_task_t upload_task;
    
    // 初始化各个任务
    es_scheduler_task_init(&vehicle_task, "vehicle_data", vehicle_data_task, NULL);
    es_scheduler_task_init(&gps_task, "gps_data", gps_data_task, NULL);
    es_scheduler_task_init(&upload_task, "data_upload", data_upload_task, NULL);
    
    // 添加到调度器
    es_scheduler_task_add(es_scheduler_get_default(), &vehicle_task);
    es_scheduler_task_add(es_scheduler_get_default(), &gps_task);
    es_scheduler_task_add(es_scheduler_get_default(), &upload_task);
    
    ES_LOGI("APP", "Vehicle terminal application initialized");
}
```

这些使用示例展示了 ES MCU Framework 在各种应用场景中的使用方法，为开发者提供了实用的参考代码。通过这些示例，开发者可以快速理解框架的核心概念，并将其应用到实际项目中。
