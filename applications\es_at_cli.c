/**
 * @file es_at_cli.c
 * @brief AT command line interface implementation
 */

#include "es_config.h"
#include "es_at_cli.h"
#include "es_drv_os.h"
#include "es_log.h"
#include "es_drv_uart.h"
#include <string.h>
#include "es_mem.h"
#include "es_scheduler.h"
#include <stdarg.h>  // Add variable argument support

#define TAG "AT_CLI"

// URC queue operation functions
/**
 * @brief Add a URC item to URC queue
 * @param at_cli AT command line interface instance
 * @param handler URC handler function
 * @param data URC data
 * @param len Data length
 * @return Returns 0 on success, negative on failure
 */
static int at_cli_urc_queue_add(es_at_cli_t *at_cli, es_at_cli_urc_handler_t handler, const char *data, int len) {

    //check if urc queue is full
    if(at_cli->urc_queue.is_full) {
        return -1;
    }

    es_at_cli_urc_item_t *item = &at_cli->urc_queue.items[at_cli->urc_queue.write_index];

    memset(item, 0, sizeof(es_at_cli_urc_item_t));
    
    // Allocate data memory
    item->data = es_mem_alloc(len + 1);
    if (item->data == NULL) {
        ES_LOGE(TAG, "Failed to allocate memory for URC data");
        return -1;
    }
    
    memcpy(item->data, data, len);
    item->data[len] = '\0';
    item->len = len;
    item->handler = handler;

    // Increment URC write index
    at_cli->urc_queue.write_index = (at_cli->urc_queue.write_index + 1) % AT_CLI_URC_QUEUE_SIZE;

    // Update queue count
    at_cli->urc_queue.count++;
    
    // Check if queue is full
    at_cli->urc_queue.is_full = at_cli->urc_queue.count >= AT_CLI_URC_QUEUE_SIZE;
    
    return 0;
}

static int at_cli_urc_queue_release(es_at_cli_t *at_cli) {
    if(at_cli->urc_queue.count == 0) {
        return -1;
    }

    es_at_cli_urc_item_t* item = &at_cli->urc_queue.items[at_cli->urc_queue.read_index];

    // Release queue item data memory only if ownership not transferred (data != NULL)
    if (item->data != NULL) {
        es_mem_free_safe((void**)&item->data);
    }

    // Clear queue item
    memset(item, 0, sizeof(es_at_cli_urc_item_t));

    at_cli->urc_queue.read_index = (at_cli->urc_queue.read_index + 1) % AT_CLI_URC_QUEUE_SIZE;

    // Update queue count
    at_cli->urc_queue.count--;

    // Queue is no longer full
    at_cli->urc_queue.is_full = false;

    return 0;
}

/**
 * @brief Clear all items in URC queue
 * @param at_cli AT command line interface instance
 * @return Number of cleared items
 */
static int at_cli_urc_queue_clear(es_at_cli_t *at_cli) {
    int cleared_count = 0;

    if (at_cli == NULL) {
        return 0;
    }

    // Release data of all items in queue
    while (at_cli->urc_queue.count > 0) {
        es_at_cli_urc_item_t* item = &at_cli->urc_queue.items[at_cli->urc_queue.read_index];

        // Safely release data memory
        es_mem_free_safe((void**)&item->data);

        // Clear item content
        memset(item, 0, sizeof(es_at_cli_urc_item_t));

        // Update read index
        at_cli->urc_queue.read_index = (at_cli->urc_queue.read_index + 1) % AT_CLI_URC_QUEUE_SIZE;
        at_cli->urc_queue.count--;
        cleared_count++;
    }

    // Reset queue state
    at_cli->urc_queue.read_index = 0;
    at_cli->urc_queue.write_index = 0;
    at_cli->urc_queue.count = 0;
    at_cli->urc_queue.is_full = false;

    // Clear current processing URC item
    memset(&at_cli->urc_item, 0, sizeof(es_at_cli_urc_item_t));

    return cleared_count;
}

/**
 * @brief Get URC handler function
 * @param buf 接收到的数据缓冲区
 * @param len 数据长度
 * @return URC处理函数，如果没有匹配的处理函数则返回NULL
 */
static es_at_cli_urc_handler_t get_urc_handler(es_at_cli_t *at_cli, const char *buf, int len) {
    for (int i = 0; i < at_cli->urc_descs_size; i++) {
        const es_at_cli_urc_desc_t *desc = &at_cli->urc_descs[i];
        if(len < desc->prefix_len + desc->suffix_len) {
            continue;
        }

        if (strncmp(buf, desc->prefix, desc->prefix_len) == 0 &&
            strncmp(buf + len - desc->suffix_len, desc->suffix, desc->suffix_len) == 0) {
            return desc->handler;
        }
    }
    return NULL;
}

/**
 * @brief URC processing coroutine
 * @param coro Coroutine context
 * @param ctx Context (AT command line interface instance)
 * @return Coroutine return value
 */
static es_async_t es_at_cli_urc_task(es_coro_t *coro, void *ctx) {
    es_at_cli_t *at_cli = (es_at_cli_t *)ctx;

    es_co_begin(coro);

    while (1) {
        // Yield CPU
        es_co_yield;

        // Check if AT CLI instance is valid
        if (at_cli == NULL || at_cli->uart_dev == NULL) {
            es_co_yield;
            continue;
        }

        es_co_wait(at_cli->urc_queue.count > 0);

        // Check queue state again to prevent queue being cleared during wait
        if (at_cli->urc_queue.count == 0) {
            continue;
        }

        // Get URC item from queue
        es_at_cli_urc_item_t *queue_item = &at_cli->urc_queue.items[at_cli->urc_queue.read_index];

        // Transfer ownership of data from queue to processing item (avoid malloc/copy)
        at_cli->urc_item.handler = queue_item->handler;
        at_cli->urc_item.len = queue_item->len;
        at_cli->urc_item.data = queue_item->data;  // Transfer ownership

        // Clear queue item data pointer to prevent double-free
        queue_item->data = NULL;

        // Release queue item (without freeing data since ownership transferred)
        at_cli_urc_queue_release(at_cli);

        // Check validity of URC item
        if (at_cli->urc_item.handler != NULL && at_cli->urc_item.data != NULL) {
            es_co_await(at_cli->urc_item.handler, at_cli, at_cli->urc_item.data, at_cli->urc_item.len);

            // After processing, release data memory of current URC item (if ownership not transferred)
            es_mem_free_safe((void**)&at_cli->urc_item.data);
        } else {
            ES_PRINTF_I(TAG, "Invalid URC item detected, skipping");
        }

        // Clear current processing URC item
        memset(&at_cli->urc_item, 0, sizeof(es_at_cli_urc_item_t));
    }

    es_co_end;
}

/**
 * @brief AT command line interface receive task
 * @param coro Coroutine context
 * @param ctx Context (AT command line interface instance)
 * @return Coroutine return value
 */
static es_async_t es_at_cli_rx_task(es_coro_t *coro, void *ctx)
{
    uint8_t byte;
    es_at_cli_t *at_cli = (es_at_cli_t *)ctx;
    
    es_co_begin(coro);

    while (1) {
        // Yield CPU
        es_co_yield;

        ES_CORO_LOCK_ACQUIRE(&at_cli->rx_lock);

        // Wait for UART data available to read
        es_co_wait(es_uart_available(at_cli->uart_dev, 0) > 0);

        // Read one byte
        es_co_wait(es_uart_read(at_cli->uart_dev, &byte, 1) > 0);
        
        // Add byte to receive buffer
        at_cli->rx_buf[at_cli->rx_buf_len++] = byte;

        ES_CORO_LOCK_RELEASE(&at_cli->rx_lock);

        // Find URC handler function
        at_cli->urc_handler = get_urc_handler(at_cli, (const char*)at_cli->rx_buf, at_cli->rx_buf_len);
        if (at_cli->urc_handler != NULL) {

            if(at_cli_urc_queue_add(at_cli, at_cli->urc_handler, (const char*)at_cli->rx_buf, at_cli->rx_buf_len) < 0) {
                ES_LOGE(TAG, "URC queue is full, data: %.*s", at_cli->rx_buf_len, (char *)at_cli->rx_buf);
            }

            at_cli->urc_handler = NULL;
            // Clear receive buffer
            at_cli->rx_buf_len = 0;
            continue;
        }

        // Handle end marker
        if(at_cli->req.state == ES_AT_CLI_REQ_EXECUTING && at_cli->req.end_mark != NULL) {
            if(strncmp((const char*)at_cli->rx_buf, at_cli->req.end_mark, strlen(at_cli->req.end_mark)) == 0) {
                at_cli->req.state = ES_AT_CLI_REQ_OK;
                at_cli->rx_buf_len = 0;
                continue;
            }
        }
        
        // Check if it's a newline character
        if(byte == '\n' && at_cli->rx_buf_len >= 2 && at_cli->rx_buf[at_cli->rx_buf_len - 2] == '\r') {

            ES_LOG_HEX(TAG, 16, at_cli->rx_buf, at_cli->rx_buf_len);

            if(at_cli->rx_buf_len == 2 || at_cli->req.state != ES_AT_CLI_REQ_EXECUTING) {
                at_cli->rx_buf_len = 0;
                continue;
            }

            do {
                if(at_cli->req.with_rsp) {
                    if(at_cli->rsp_buf_len + at_cli->rx_buf_len >= sizeof(at_cli->rsp_buf)) {
                        ES_LOGE(TAG, "AT response buffer overflow");
                        at_cli->rx_buf_len = 0;
                        at_cli->req.state = ES_AT_CLI_REQ_ERROR;
                        break;
                    }

                    memcpy(at_cli->rsp_buf + at_cli->rsp_buf_len, at_cli->rx_buf, at_cli->rx_buf_len);
                    at_cli->rsp_buf_len += at_cli->rx_buf_len;
                    at_cli->rsp_buf[at_cli->rsp_buf_len - 1] = '\0';
                }
                at_cli->req.line_cnt++;
                if(at_cli->req.line_num == 0) {
                    if (strncmp((const char*)at_cli->rx_buf, "OK", 2) == 0) {
                        at_cli->req.state = ES_AT_CLI_REQ_OK;
                    } else if (strncmp((const char*)at_cli->rx_buf, "ERROR", 5) == 0) {
                        at_cli->req.state = ES_AT_CLI_REQ_ERROR;
                    }
                } else if(at_cli->req.line_cnt >= at_cli->req.line_num) {
                    at_cli->req.state = ES_AT_CLI_REQ_OK;
                }
            } while(0);

            at_cli->rx_buf_len = 0;
        }
        // Check if receive buffer is full
        else if(at_cli->rx_buf_len >= sizeof(at_cli->rx_buf)) {
            ES_LOGE(TAG, "AT response buffer overflow");
            at_cli->rx_buf_len = 0;
        }
    }

    es_co_end;
}

/**
 * @brief Initialize AT command line interface
 * @param at_cli AT command line interface instance
 * @param uart_config UART configuration
 * @param urc_descs URC description array
 * @param urc_descs_size URC description array size
 * @return Returns 0 on success, negative on failure
 */
int es_at_cli_init(es_at_cli_t *at_cli, const es_uart_config_t *uart_config, const es_at_cli_urc_desc_t *urc_descs, int urc_descs_size)
{
    if (at_cli == NULL || uart_config == NULL || urc_descs == NULL || urc_descs_size == 0) {
        return -1;
    }

    // Ensure memory management module is initialized
    es_mem_init();
    
    // Clear at_cli structure
    memset(at_cli, 0, sizeof(es_at_cli_t));

    // Open UART device
    at_cli->uart_dev = es_uart_open(uart_config);
    if (at_cli->uart_dev == NULL) {
        ES_LOGE(TAG, "Failed to open UART device: %s", uart_config->name);
        return -1;
    }

    // Set URC descriptions
    at_cli->urc_descs = urc_descs;
    at_cli->urc_descs_size = urc_descs_size;

    es_coro_lock_init(&at_cli->rx_lock);

    at_cli->rx_task.func = es_at_cli_rx_task;
    at_cli->rx_task.ctx = at_cli;

    at_cli->urc_task.func = es_at_cli_urc_task;
    at_cli->urc_task.ctx = at_cli;

    // Add coroutine tasks
    es_scheduler_task_add(es_scheduler_get_default(), &at_cli->rx_task);
    es_scheduler_task_add(es_scheduler_get_default(), &at_cli->urc_task);

    return 0;
}

/**
 * @brief Deinitialize AT command line interface
 * @param at_cli AT command line interface instance
 * @return Returns 0 on success, negative on failure
 */
int es_at_cli_deinit(es_at_cli_t *at_cli)
{
    if (at_cli == NULL) {
        return -1;
    }

    // Remove tasks from scheduler
    es_scheduler_task_remove(es_scheduler_get_default(), &at_cli->rx_task);
    es_scheduler_task_remove(es_scheduler_get_default(), &at_cli->urc_task);

    // Clean up URC queue
    int cleared_items = at_cli_urc_queue_clear(at_cli);
    if (cleared_items > 0) {
        ES_PRINTF_I(TAG, "Cleared %d URC items from queue", cleared_items);
    }

    // Close UART device
    if (at_cli->uart_dev != NULL) {
        es_uart_close(at_cli->uart_dev);
        at_cli->uart_dev = NULL;
    }

    // Clear structure
    memset(at_cli, 0, sizeof(es_at_cli_t));

    ES_PRINTF_I(TAG, "AT CLI deinitialized successfully");
    return 0;
}

es_async_t es_at_cli_cmd_send(es_coro_t *coro, es_at_cli_t *at_cli, const es_at_cli_req_t* req, const char* fmt, ...) {

     es_co_begin(coro);

	 es_co_wait_timeout(at_cli->req.state == ES_AT_CLI_REQ_IDLE, at_cli->timeout_ms * at_cli->try_times);

    // Clear request structure
    memset(&at_cli->req, 0, sizeof(es_at_cli_req_t));
    
    // Copy request parameters
    at_cli->req.timeout_ms = req->timeout_ms == 0 ? 500 : req->timeout_ms;
    at_cli->req.line_num = req->line_num;
    at_cli->req.with_rsp = req->with_rsp;
    at_cli->req.end_mark = req->end_mark;
    at_cli->req.try_times = 0;
    at_cli->rsp_buf_len = 0;
    at_cli->cmd_tx_buf_len = 0;
    memset(at_cli->cmd_tx_buf, 0, sizeof(at_cli->cmd_tx_buf));
    at_cli->timeout_ms = req->timeout_ms == 0 ? 500 : req->timeout_ms;
    at_cli->try_times = req->try_times == 0 ? 1 : req->try_times;

    // If fmt is not null, use fmt to format command
    if (fmt != NULL) {
        va_list args;
        va_start(args, fmt);
        // Use fixed-size buffer to format string directly
        at_cli->cmd_tx_buf_len = vsnprintf((char*)at_cli->cmd_tx_buf, sizeof(at_cli->cmd_tx_buf) - 1, fmt, args);
        //add terminator
        at_cli->cmd_tx_buf[at_cli->cmd_tx_buf_len] = '\0';
        va_end(args);
    } else {
        // Use cmd from req
        at_cli->cmd_tx_buf_len = req->cmd_len == 0 ? (uint16_t)strlen((char*)req->cmd) : req->cmd_len;
        //add terminator
        at_cli->cmd_tx_buf[at_cli->cmd_tx_buf_len] = '\0';
    }

    ES_LOG_HEX(TAG, 16, req->cmd != NULL ? req->cmd : at_cli->cmd_tx_buf, at_cli->cmd_tx_buf_len);

    for(at_cli->req.try_times = 0; at_cli->req.try_times < at_cli->try_times; at_cli->req.try_times++) {

        // Set start time and send command directly
        at_cli->req.state = ES_AT_CLI_REQ_EXECUTING;
        at_cli->req.start_time_ms = es_os_get_tick_ms();

        // es_uart_write(at_cli->uart_dev, req->cmd != NULL ? req->cmd : at_cli->cmd_tx_buf, at_cli->cmd_tx_buf_len);
        es_co_await(es_uart_coro_write, at_cli->uart_dev, req->cmd != NULL ? req->cmd : at_cli->cmd_tx_buf, at_cli->cmd_tx_buf_len);

        // Wait for request completion
        while (at_cli->req.state == ES_AT_CLI_REQ_EXECUTING) {
            // Check timeout
            if (es_os_check_timeout(at_cli->req.start_time_ms, at_cli->timeout_ms)) {
                at_cli->req.state = ES_AT_CLI_REQ_ERROR;
				ES_LOGE(TAG, "AT command timeout: %*s", at_cli->cmd_tx_buf_len, req->cmd != NULL ? req->cmd : at_cli->cmd_tx_buf);
                break;
            }
            es_co_yield;
        }

        if(at_cli->req.state == ES_AT_CLI_REQ_OK) {
            break;
        }
    }

    int state = at_cli->req.state;

    at_cli->req.state = ES_AT_CLI_REQ_IDLE;

    if(state != ES_AT_CLI_REQ_OK) {
        es_co_err;
    }

    es_co_end;
}

const char *es_at_cli_get_line(const es_at_cli_t *at_cli, int line_num) {
    if (line_num < 0 || line_num >= at_cli->req.line_cnt) {
        return NULL;
    }

    const uint8_t *line = at_cli->rsp_buf;
    for(int i = 0; i < line_num; i++) {
        line += strlen((const char *)line) + 1;
    }
    return (const char *)line;
}


const char* es_at_cli_get_line_by_kw(const es_at_cli_t *at_cli, const char* kw) {
    if(at_cli == NULL || kw == NULL || at_cli->rsp_buf_len == 0) {
        return NULL;
    }

    for(int i = 0; i < at_cli->rsp_buf_len; i++) {
        const char *line = es_at_cli_get_line(at_cli, i);
        if(line == NULL) {
            return NULL;
        }

        if(strstr(line, kw) != NULL) {
            return line;
        }
    }

    return NULL;
}

int es_at_cli_parse_by_kw(const es_at_cli_t *at_cli, const char *keyword, const char *resp_expr, ...) {
    const char *line = es_at_cli_get_line_by_kw(at_cli, keyword);
    if(line == NULL) {
        return -1;
    }

    va_list args;
    va_start(args, resp_expr);
    int ret = vsscanf(line, resp_expr, args);
    va_end(args);
    return ret;
}

