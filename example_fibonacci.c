
        int main() {
            register int n = 10;
            register int a = 0;
            register int b = 1;
            register int i = 2;
            register int temp = 0;
            
            if (n <= 1) {
                return n;
            }
            
            while (i <= n) {
                temp = a + b;
                a = b;
                b = temp;
                i = i + 1;
            }
            
            return b;
        }
        