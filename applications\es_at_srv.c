/**
 * @file es_at_srv.c
 * @brief AT service module implementation
 */

#include "es_at_srv.h"
#include <stdio.h>
#include <stdarg.h>
#include <stddef.h> /* Add stddef.h to get offsetof macro definition */
#include "es_log.h"
#include "es_ringbuffer.h" /* Add ring buffer header file */
#include "es_scheduler.h"
#include "es_config.h"

#define ES_AT_SRV_TAG "AT_SRV"

/* AT service singleton instance */
static es_at_srv_t s_at_srv = {0};

#define X(name, desc, func) extern es_async_t at_srv_cmd_##func##_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type);
DEMO_AT_SRV_ITEMS
#undef X

static const es_at_cmd_item_t s_at_cmd_items[] = {
    #define X(name, desc, func) \
    {name, at_srv_cmd_##func##_handler},
    DEMO_AT_SRV_ITEMS
    #undef X
};

/**
 * @brief Get AT service singleton pointer
 * @return AT service singleton pointer
 */
es_at_srv_t *es_at_srv_get_instance(void)
{
    return &s_at_srv;
}

/**
 * @brief Parse AT command
 * @param buf Data buffer
 * @param len Data length
 * @return Command index: all 0xFF means invalid, high bit 0 means standard command, high bit 1 means user command, low bits are index
 */
static uint32_t es_at_srv_parse_cmd(uint8_t *buf, uint16_t len)
{
    s_at_srv.current_cmd_type = ES_AT_SRV_CMD_EXEC;

    // Clear command context
    memset(&s_at_srv.cmd_ctx, 0, sizeof(es_at_srv_cmd_ctx_t));

     //check if is start with AT+
     if (len > 2 && buf[2] == '+') {
         buf += 3;
         len -= 3;
     }

    // Ensure sufficient length to parse command
    if (len == 0)  {
        return ES_AT_SRV_CMD_INVALID;
    }

    // 解析命令名称
    uint16_t cmd_name_len = 0;
    const char *cmd_name_start = (const char *)buf;

    // 查找命令名称结束位置（等号或问号或结束）
    while (cmd_name_len < len &&
           buf[cmd_name_len] != '=' &&
           buf[cmd_name_len] != '?' &&
           buf[cmd_name_len] != ',')
    {
        cmd_name_len++;
    }

    // 复制基本命令名称（不含参数标记）
    if (cmd_name_len >= ES_AT_SRV_MAX_CMD_NAME) {
        cmd_name_len = ES_AT_SRV_MAX_CMD_NAME - 1;
    }

    // 判断命令类型
    if (cmd_name_len < len) {
        if (buf[cmd_name_len] == '?') {
            s_at_srv.current_cmd_type = ES_AT_SRV_CMD_QUERY; // 查询命令
        }
        else if (buf[cmd_name_len] == '=' && cmd_name_len + 1 < len && buf[cmd_name_len + 1] == '?') {
            s_at_srv.current_cmd_type = ES_AT_SRV_CMD_TEST; // 测试命令
        }
        else if (buf[cmd_name_len] == '=') {
            s_at_srv.current_cmd_type = ES_AT_SRV_CMD_SET; // 设置命令

            // 解析参数
            uint16_t param_start = cmd_name_len + 1;
            uint16_t i = param_start;
            uint8_t param_idx = 0;

            // 解析参数列表
            while (i < len && param_idx < ES_AT_SRV_MAX_PARAMS) {
                uint16_t param_len = 0;
                const uint8_t *param_ptr = buf + i;

                // 计算参数长度
                while (i + param_len < len && buf[i + param_len] != ',') {
                    param_len++;
                }

                // 保存参数信息
                s_at_srv.cmd_ctx.params[param_idx].param = (const char*)param_ptr;
                s_at_srv.cmd_ctx.params[param_idx].len = param_len;
                param_idx++;

                // 跳过逗号
                i += param_len;
                if (i < len && buf[i] == ',') {
                    //replace comma with null
                    buf[i] = '\0';
                    i++;
                }
            }

            s_at_srv.cmd_ctx.param_count = param_idx;
        }
    }

    // 在标准命令列表中查找命令
    for(int i = 0; i < sizeof(s_at_cmd_items) / sizeof(s_at_cmd_items[0]); i++) {
        const es_at_cmd_item_t *item = &s_at_cmd_items[i];
        //maybe same prefix
        if(item->cmd_name[cmd_name_len] != '\0') {
            continue;
        }
        
        if (strncmp(item->cmd_name, cmd_name_start, cmd_name_len) == 0) {
            return ES_AT_SRV_MAKE_STD_CMD_IDX(i);
        }
    }

    // 在用户命令数组中查找命令
    for(int i = 0; i < s_at_srv.user_cmd_count; i++) {
        const es_at_cmd_item_t *item = &s_at_srv.user_cmds[i];
        
        // 检查命令名称长度匹配
        if(item->cmd_name[cmd_name_len] != '\0') {
            continue;
        }
        
        if (strncmp(item->cmd_name, cmd_name_start, cmd_name_len) == 0) {
            return ES_AT_SRV_MAKE_USER_CMD_IDX(i);
        }
    }

    ES_PRINTF_I(ES_AT_SRV_TAG, "Command not found: %.*s", cmd_name_len, cmd_name_start);
    return ES_AT_SRV_CMD_INVALID;
}

/**
 * @brief 处理数据接收
 * @param coro 协程上下文
 * @param srv 服务实例
 * @return 协程返回值
 */
static es_async_t es_at_srv_rx_task(es_coro_t *coro, void* ctx)
{
    uint8_t byte = 0;
    // int read_len = 0;
	  bool is_cmd_end = false;
    static uint32_t cmd_idx = ES_AT_SRV_CMD_INVALID;

    es_co_begin(coro);

    // 初始化接收缓冲区
    memset(s_at_srv.rx_buf, 0, ES_AT_SRV_RX_BUF_SIZE);
    s_at_srv.rx_len = 0;

    while (1) {
        es_co_yield;
        es_co_wait(es_ringbuffer_read(&s_at_srv.data_ringbuf, &byte, 1) == 1);

        // check if buffer is full
        if (s_at_srv.rx_len >= ES_AT_SRV_RX_BUF_SIZE) {
            s_at_srv.rx_len = 0;
        }

        // add byte to rx buffer
        s_at_srv.rx_buf[s_at_srv.rx_len++] = byte;
        
        if (s_at_srv.rx_len >= 2) {
            is_cmd_end = (s_at_srv.rx_buf[s_at_srv.rx_len - 2] == '\r' && 
                         s_at_srv.rx_buf[s_at_srv.rx_len - 1] == '\n');
            //check if is empty line
            if(is_cmd_end && s_at_srv.rx_len == 2) {
                s_at_srv.rx_len = 0;
                continue;
            }
            if(is_cmd_end) {s_at_srv.rx_buf[s_at_srv.rx_len - 2] = '\0'; s_at_srv.rx_len -= 2; }
        }
        if (!is_cmd_end && s_at_srv.rx_len >= 1) {
            is_cmd_end = (s_at_srv.rx_buf[s_at_srv.rx_len - 1] == '\n');
            //check if is empty line
            if(is_cmd_end && s_at_srv.rx_len == 1) {
                s_at_srv.rx_len = 0;
                continue;
            }
            if(is_cmd_end) { s_at_srv.rx_buf[s_at_srv.rx_len - 1] = '\0'; s_at_srv.rx_len -= 1; }
        }

        if (is_cmd_end) {

            // 检查最小有效命令长度和AT前缀
            if (s_at_srv.rx_len < 2 || s_at_srv.rx_buf[0] != 'A' || s_at_srv.rx_buf[1] != 'T') {
                goto clear;
            }

            if(s_at_srv.rx_len == 2) {
                es_at_srv_send_ok();
                goto clear;
            }

            if(s_at_srv.rx_len == 4 && s_at_srv.rx_buf[2] == '&' && s_at_srv.rx_buf[3] == 'L') {
                // 列出固定命令
                for(cmd_idx = 0; cmd_idx < sizeof(s_at_cmd_items) / sizeof(s_at_cmd_items[0]); cmd_idx++) {
                    es_at_srv_fmt_send("%s\r\n", s_at_cmd_items[cmd_idx].cmd_name);
                }
                // 列出用户命令
                for(cmd_idx = 0; cmd_idx < s_at_srv.user_cmd_count; cmd_idx++) {
                    es_at_srv_fmt_send("%s\r\n", s_at_srv.user_cmds[cmd_idx].cmd_name);
                }
                es_at_srv_send_ok();
                goto clear;
            }

            
            // 处理解析结果
            if((cmd_idx = es_at_srv_parse_cmd(s_at_srv.rx_buf, s_at_srv.rx_len)) != ES_AT_SRV_CMD_INVALID) {
                if (ES_AT_SRV_IS_USER_CMD(cmd_idx)) {
                    // 执行用户命令
                    cmd_idx = ES_AT_SRV_GET_CMD_INDEX(cmd_idx);
                    es_co_await(s_at_srv.user_cmds[cmd_idx].handler, &s_at_srv.cmd_ctx, s_at_srv.current_cmd_type);
                } else {
                    // 执行固定数组中的命令
                    cmd_idx = ES_AT_SRV_GET_CMD_INDEX(cmd_idx);
                    es_co_await(s_at_cmd_items[cmd_idx].handler, &s_at_srv.cmd_ctx, s_at_srv.current_cmd_type);
                }
            }
            clear:
            s_at_srv.rx_len = 0;
        }
    }

    es_co_end;
}

int es_at_srv_init(void)
{
    if (s_at_srv.inited)
    {
        return 0;
    }

    // 初始化环形缓冲区
    es_ringbuffer_init(&s_at_srv.data_ringbuf, s_at_srv.ringbuf_data, sizeof(s_at_srv.ringbuf_data));

    es_init_hlist_head(&s_at_srv.sinks);

    // 初始化命令处理相关
    s_at_srv.current_cmd_type = ES_AT_SRV_CMD_EXEC;
    s_at_srv.is_sink_busy = false;
    
    // 初始化用户命令数组
    memset(s_at_srv.user_cmds, 0, sizeof(s_at_srv.user_cmds));
    s_at_srv.user_cmd_count = 0;

    s_at_srv.rx_task.func = es_at_srv_rx_task;
    s_at_srv.rx_task.ctx = &s_at_srv;

    es_scheduler_task_add(es_scheduler_get_default(), &s_at_srv.rx_task);
    s_at_srv.inited = true;

    ES_PRINTF_I(ES_AT_SRV_TAG, "AT service initialized");
    return 0;
}

void es_at_srv_deinit(void)
{
    // 清空缓冲区
    memset(s_at_srv.rx_buf, 0, ES_AT_SRV_RX_BUF_SIZE);
    memset(s_at_srv.tx_buf, 0, ES_AT_SRV_TX_BUF_SIZE);

    ES_PRINTF_I(ES_AT_SRV_TAG, "AT service deinitialized");
}

es_async_t _es_at_srv_send_fmt(es_coro_t *coro, const char *fmt, ...)
{
    es_co_begin(coro);

    va_list args;
    va_start(args, fmt);
    s_at_srv.tx_len = vsnprintf((char *)s_at_srv.tx_buf, ES_AT_SRV_TX_BUF_SIZE, fmt, args);
    va_end(args);

    if (s_at_srv.tx_len > ES_AT_SRV_TX_BUF_SIZE) {
        ES_PRINTF_W(ES_AT_SRV_TAG, "Formatted response too long");
        es_co_exit;
    }

    es_at_srv_send_data((uint8_t *)s_at_srv.tx_buf, s_at_srv.tx_len);

    es_co_end;
}

/**
 * @brief 发送原始数据包
 * @param data 待发送的数据
 * @param len 数据长度
 * @return 协程返回值
 */
es_async_t _es_at_srv_send_data(es_coro_t *coro, const uint8_t *data, uint16_t len)
{
    es_co_begin(coro);
    
    if (!data || len == 0) {
        ES_PRINTF_W(ES_AT_SRV_TAG, "Invalid data or length for sending");
        es_co_exit;
    }

   s_at_srv.is_sink_busy = true;
    // 检查输出处理函数是否设置
    if(s_at_srv.current_sink != NULL) {
        es_co_await(s_at_srv.current_sink->sink_func, data, len);
    }

    s_at_srv.is_sink_busy = false;
    
    es_co_end;
}

es_at_srv_src_type_t es_at_srv_alloc_src_type(void)
{
    return ++s_at_srv.src_type_count;
}

/**
 * @brief 注册输出处理函数
 * @param name 输出处理函数名称
 * @param sink_func 输出处理函数
 * @return 0成功，-1失败
 */
int es_at_srv_add_sink(es_at_srv_sink_t *sink)
{
    if (!sink || !s_at_srv.inited) {
        return -1;
    }

    //check if sink already exists
    struct es_hlist_node *pos = NULL;
    es_hlist_for_each(pos, &s_at_srv.sinks) {
        es_at_srv_sink_t *s = es_hlist_entry(pos, es_at_srv_sink_t, node);
        if (s->src_type == sink->src_type) {
            return -1;
        }
    }
    // 添加到链表
    es_hlist_add_head(&sink->node, &s_at_srv.sinks);
    
    return 0;
}

/**
 * @brief 反注册输出处理函数
 * @param sink 输出处理函数
 * @return 0成功，-1失败
 */
int es_at_srv_remove_sink(es_at_srv_sink_t *sink) 
{
    if (!sink || !s_at_srv.inited) {
        return -1;
    }

    // 从链表中删除
    es_hlist_del(&sink->node, &s_at_srv.sinks);

    return 0;
}


int es_at_srv_recv(es_at_srv_src_type_t src_type, const uint8_t *data, uint16_t len)
{
    if (!data || len == 0 || !s_at_srv.inited) {
        return -1;
    }

    /**
     * 注意，如果当前正在执行AT命令，则不进行sink切换，否则会导致sink coro被切断,结果就是coro执行一半，形成内存泄露
     */
    if(!s_at_srv.is_sink_busy) {
        if(s_at_srv.current_sink == NULL || (s_at_srv.current_sink != NULL && s_at_srv.current_sink->src_type != src_type)) {
            //find sink by src type
            struct es_hlist_node *pos = NULL;
            es_hlist_for_each(pos, &s_at_srv.sinks) {
                es_at_srv_sink_t *s = es_hlist_entry(pos, es_at_srv_sink_t, node);
                if (s->src_type == src_type) {
                    s_at_srv.current_sink = s;
                    break;
                }
            }
        }
    }

    return es_ringbuffer_write_force(&s_at_srv.data_ringbuf, data, len);
}

int es_at_srv_read_data(uint8_t *data, uint16_t len) 
{
    if (!data || len == 0 || !s_at_srv.inited) {
        return -1;
    }

    return es_ringbuffer_read(&s_at_srv.data_ringbuf, data, len);
}

/**
 * @brief 注册用户AT命令
 * @param cmd_item AT命令项指针
 * @return 0成功，-1失败
 */
int es_at_srv_register_user_cmd(const es_at_cmd_item_t *cmd_item)
{
    if (!cmd_item || !cmd_item->cmd_name || !cmd_item->handler || !s_at_srv.inited) {
        return -1;
    }

    // 检查数组是否已满
    if (s_at_srv.user_cmd_count >= ES_AT_SRV_MAX_USER_CMDS) {
        ES_PRINTF_W(ES_AT_SRV_TAG, "User command array is full");
        return -1;
    }

    // 检查命令名长度
    if (strlen(cmd_item->cmd_name) >= ES_AT_SRV_MAX_CMD_NAME) {
        ES_PRINTF_W(ES_AT_SRV_TAG, "Command name too long: %s", cmd_item->cmd_name);
        return -1;
    }

    // 检查命令是否已经存在（固定数组中）
    for(int i = 0; i < sizeof(s_at_cmd_items) / sizeof(s_at_cmd_items[0]); i++) {
        if (strcmp(s_at_cmd_items[i].cmd_name, cmd_item->cmd_name) == 0) {
            ES_PRINTF_W(ES_AT_SRV_TAG, "Command already exists in static array: %s", cmd_item->cmd_name);
            return -1;
        }
    }

    // 检查命令是否已经存在（用户数组中）
    for(int i = 0; i < s_at_srv.user_cmd_count; i++) {
        if (strcmp(s_at_srv.user_cmds[i].cmd_name, cmd_item->cmd_name) == 0) {
            ES_PRINTF_W(ES_AT_SRV_TAG, "Command already exists in user array: %s", cmd_item->cmd_name);
            return -1;
        }
    }

    // 复制命令项到用户数组
    s_at_srv.user_cmds[s_at_srv.user_cmd_count] = *cmd_item;
    s_at_srv.user_cmd_count++;

    ES_PRINTF_I(ES_AT_SRV_TAG, "User command registered: %s", cmd_item->cmd_name);
    return 0;
}

/**
 * @brief 反注册用户AT命令
 * @param cmd_name 命令名称
 * @return 0成功，-1失败
 */
int es_at_srv_unregister_user_cmd(const char *cmd_name)
{
    if (!cmd_name || !s_at_srv.inited) {
        return -1;
    }

    // 查找命令在用户数组中的位置
    int found_idx = -1;
    for(int i = 0; i < s_at_srv.user_cmd_count; i++) {
        if (strcmp(s_at_srv.user_cmds[i].cmd_name, cmd_name) == 0) {
            found_idx = i;
            break;
        }
    }

    if (found_idx == -1) {
        ES_PRINTF_W(ES_AT_SRV_TAG, "Command not found in user array: %s", cmd_name);
        return -1;
    }

    // 将后面的命令向前移动，覆盖要删除的命令
    for(int i = found_idx; i < s_at_srv.user_cmd_count - 1; i++) {
        s_at_srv.user_cmds[i] = s_at_srv.user_cmds[i + 1];
    }

    // 清空最后一个位置
    memset(&s_at_srv.user_cmds[s_at_srv.user_cmd_count - 1], 0, sizeof(es_at_cmd_item_t));
    s_at_srv.user_cmd_count--;

    ES_PRINTF_I(ES_AT_SRV_TAG, "User command unregistered: %s", cmd_name);
    return 0;
}

/**
 * @brief 获取用户命令数量
 * @return 用户命令数量
 */
uint8_t es_at_srv_get_user_cmd_count(void)
{
    return s_at_srv.user_cmd_count;
}
