# AST可视化和优化功能

本文档介绍C语言编译器的AST可视化、优化和VM系统调用支持功能。

## 新增功能

### 1. AST可视化 (ASTVisualizer)

编译器现在可以生成和显示抽象语法树的可视化表示，帮助理解代码结构。

**特性：**
- 树形结构显示
- 缩进层次清晰
- 显示所有节点类型和属性
- 支持所有语言构造

**使用方法：**
```python
compiler = Compiler(show_ast=True)
bytecode = compiler.compile(source_code)

# 或者单独获取AST字符串
ast_string = compiler.get_ast_string()
print(ast_string)
```

### 2. AST优化 (ASTOptimizer)

编译器实现了多种AST级别的优化，可以显著减少生成的字节码大小。

#### 支持的优化类型：

**常量折叠 (Constant Folding)**
- 编译时计算常量表达式
- 示例：`5 + 3` → `8`
- 支持所有算术、位运算和比较运算

**代数简化 (Algebraic Simplification)**
- 应用数学恒等式简化表达式
- 示例：
  - `x + 0` → `x`
  - `x * 1` → `x`
  - `x * 0` → `0`
  - `x & 0` → `0`
  - `x | 0` → `x`

**死代码消除 (Dead Code Elimination)**
- 移除永远不会执行的代码
- 示例：
  - `if (0) { ... }` → 删除整个if语句
  - `while (0) { ... }` → 删除整个循环

**条件分支优化 (Branch Optimization)**
- 常量条件的分支优化
- 示例：
  - `if (1) { A } else { B }` → `A`
  - `if (0) { A } else { B }` → `B`

### 3. VM系统调用支持

编译器现在支持VM系统调用，可以在C代码中直接调用VM提供的系统功能。

#### 支持的系统调用：

| 函数 | 系统调用ID | 参数 | 描述 |
|------|------------|------|------|
| `get_time()` | 0x04 | 无 | 获取当前时间戳 |
| `get_event(id)` | 0x01 | event_id | 获取指定事件的值 |
| `get_io(pin)` | 0x02 | pin_number | 获取指定IO引脚的电平 |
| `get_signal(msg_idx, sig_idx)` | 0x03 | msg_idx, sig_idx | 获取指定信号的值 |
| `get_msg_time(msg_idx)` | 0x05 | msg_idx | 获取指定消息的时间戳 |

**使用示例：**
```c
int main() {
    register int current_time = get_time();
    register int event_status = get_event(42);
    register int pin_level = get_io(5);
    register int signal_value = get_signal(1, 0);
    register int msg_timestamp = get_msg_time(0);
    
    return current_time + event_status + pin_level;
}
```

### 4. 注释支持

编译器现在支持C风格的单行注释：
```c
int main() {
    register int a = 10;  // 这是注释
    // 这也是注释
    return a;
}
```

## 使用示例

### 基本使用

```python
from c_to_vm_compiler import Compiler

# 启用所有功能
compiler = Compiler(
    enable_optimization=True,  # 启用优化
    show_ast=True             # 显示AST
)

c_code = """
int main() {
    register int result = 5 + 3 * 2;  // 常量折叠: 11
    if (1) {                          // 分支优化
        result = result + 1;
    }
    return result;
}
"""

bytecode = compiler.compile(c_code)

# 查看优化报告
optimizations = compiler.get_optimization_report()
for opt in optimizations:
    print(f"优化: {opt}")
```

### 优化效果对比

```python
# 未优化版本
compiler_no_opt = Compiler(enable_optimization=False)
bytecode_no_opt = compiler_no_opt.compile(c_code)

# 优化版本
compiler_opt = Compiler(enable_optimization=True)
bytecode_opt = compiler_opt.compile(c_code)

print(f"未优化: {len(bytecode_no_opt)} bytes")
print(f"优化后: {len(bytecode_opt)} bytes")
print(f"节省: {len(bytecode_no_opt) - len(bytecode_opt)} bytes")
```

## 演示程序

运行以下程序查看各种功能的演示：

```bash
# AST可视化和优化演示
python applications/ast_optimization_demo.py

# 编译器主程序（包含系统调用演示）
python applications/c_to_vm_compiler.py

# 测试优化后的字节码执行
gcc applications/test_optimized_vm.c -o test_optimized_vm
./test_optimized_vm
```

## 优化效果

在实际测试中，优化器可以显著减少字节码大小：

- **常量折叠示例**: 节省 ~30% 字节码
- **死代码消除示例**: 节省 ~50% 字节码  
- **复杂优化示例**: 节省 ~44% 字节码

## API参考

### Compiler类

```python
class Compiler:
    def __init__(self, enable_optimization=True, show_ast=False):
        """
        初始化编译器
        
        Args:
            enable_optimization: 是否启用AST优化
            show_ast: 是否显示AST结构
        """
    
    def compile(self, source: str) -> bytes:
        """编译C代码到VM字节码"""
    
    def get_ast_string(self) -> str:
        """获取原始AST的字符串表示"""
    
    def get_optimized_ast_string(self) -> str:
        """获取优化后AST的字符串表示"""
    
    def get_optimization_report(self) -> List[str]:
        """获取应用的优化列表"""
```

### ASTVisualizer类

```python
class ASTVisualizer:
    def visit(self, node: ASTNode) -> str:
        """访问AST节点并生成可视化字符串"""
```

### ASTOptimizer类

```python
class ASTOptimizer:
    def optimize(self, ast: Program) -> Program:
        """对AST进行优化，返回优化后的AST"""
    
    @property
    def optimizations_applied(self) -> List[str]:
        """获取应用的优化列表"""
```

## 扩展建议

1. **更多优化**:
   - 循环展开
   - 公共子表达式消除
   - 寄存器分配优化

2. **更多系统调用**:
   - 内存操作
   - 定时器控制
   - 中断处理

3. **调试支持**:
   - 源码行号映射
   - 断点支持
   - 变量监视

4. **性能分析**:
   - 指令计数
   - 执行时间分析
   - 内存使用统计

这些新功能使编译器更加强大和实用，为VM提供了完整的开发和调试环境。
