; ****************************************************************
; Scatter-Loading Description File
; ****************************************************************
LR_IROM1 0x00000000 0x00080000  {    ; load region size_region
    ER_IROM1 0x00000000 0x00080000  {  ; load address = execution address
        *.o (RESET, +First)
        *(InRoot$$Sections)
        .ANY (+RO)
        .ANY (+XO)
    }
    RW_IRAM1 0x1FFF8000 0x0002F000  {  ; RW data
        .ANY (+RW +ZI)
    }
}

