#ifndef __ES_DRV_OS_H__
#define __ES_DRV_OS_H__

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Compiler Related Definitions */
#if defined(__ARMCC_VERSION)           /* ARM Compiler */
#define ES_SECTION(x)               __attribute__((section(x)))
#define ES_USED                     __attribute__((used))
#define ALIGN(n)                    __attribute__((aligned(n)))
#define ES_WEAK                     __attribute__((weak))
#define es_inline                   static __inline
/* module compiling */
#ifdef ES_USING_MODULE
#define ES_API                     __declspec(dllimport)
#else
#define ES_API                     __declspec(dllexport)
#endif /* ES_USING_MODULE */
#elif defined (__IAR_SYSTEMS_ICC__)     /* for IAR Compiler */
#define ES_SECTION(x)               @ x
#define ES_USED                     __root
#define PRAGMA(x)                   _Pragma(#x)
#define ALIGN(n)                    PRAGMA(data_alignment=n)
#define ES_WEAK                     __weak
#define es_inline                   static inline
#define ES_API
#elif defined (__GNUC__)                /* GNU GCC Compiler */
#ifndef ES_USING_LIBC
/* the version of GNU GCC must be greater than 4.x */
typedef __builtin_va_list           __gnuc_va_list;
typedef __gnuc_va_list              va_list;
#define va_start(v,l)               __builtin_va_start(v,l)
#define va_end(v)                   __builtin_va_end(v)
#define va_arg(v,l)                 __builtin_va_arg(v,l)
#endif /* ES_USING_LIBC */
#define ES_SECTION(x)               __attribute__((section(x)))
#define ES_USED                     __attribute__((used))
#define ALIGN(n)                    __attribute__((aligned(n)))
#define ES_WEAK                     __attribute__((weak))
#define es_inline                   static __inline
#define ES_API
#elif defined (__ADSPBLACKFIN__)        /* for VisualDSP++ Compiler */
#define ES_SECTION(x)               __attribute__((section(x)))
#define ES_USED                     __attribute__((used))
#define ALIGN(n)                    __attribute__((aligned(n)))
#define ES_WEAK                     __attribute__((weak))
#define es_inline                   static inline
#define ES_API
#elif defined (_MSC_VER)
#define ES_SECTION(x)
#define ES_USED
#define ALIGN(n)                    __declspec(align(n))
#define ES_WEAK
#define es_inline                   static __inline
#define ES_API
#elif defined (__TI_COMPILER_VERSION__)
/* The way that TI compiler set section is different from other(at least
    * GCC and MDK) compilers. See ARM Optimizing C/C++ Compiler 5.9.3 for more
    * details. */
#define ES_SECTION(x)
#define ES_USED
#define PRAGMA(x)                   _Pragma(#x)
#define ALIGN(n)
#define ES_WEAK
#define es_inline                   static inline
#define ES_API
#elif defined (__TASKING__)
#define ES_SECTION(x)               __attribute__((section(x)))
#define ES_USED                     __attribute__((used, protect))
#define PRAGMA(x)                   _Pragma(#x)
#define ALIGN(n)                    __attribute__((__align(n)))
#define ES_WEAK                     __attribute__((weak))
#define es_inline                   static inline
#define ES_API
#else
    #error not supported tool chain
#endif /* __ARMCC_VERSION */


/* Memory barrier macro definitions - multi-platform support */
#if defined(__GNUC__)
    #define ES_MEMORY_BARRIER() __asm__ volatile("" ::: "memory")
#elif defined(_MSC_VER)
    #include <intrin.h>
    #define ES_MEMORY_BARRIER() _ReadWriteBarrier()
#elif defined(__ARMCC_VERSION)
    #define ES_MEMORY_BARRIER() __memory_changed()
#else
    #define ES_MEMORY_BARRIER() do { volatile int dummy = 0; (void)dummy; } while(0)
#endif

/**
 * @brief Initialize operating system related functions
 * @return int 0 indicates success, non-zero indicates failure
 */
int es_os_init(void);

/**
 * @brief Get system timestamp (milliseconds)
 * @return uint32_t Returns milliseconds since system startup
 */
uint32_t es_os_get_tick_ms(void);

/**
 * @brief Get system timestamp (microseconds)
 * @return uint64_t Returns microseconds since system startup
 */
uint64_t es_os_get_tick_us(void);

/**
 * @brief Watchdog feed interface
 */
void es_os_feed_wdt(void);

/**
 * @brief Get current formatted time string
 * @param buf Output buffer
 * @param size Buffer size
 * @return char* Returns pointer to the filled buffer
 */
char* es_os_get_time_str(char* buf, int size);


/**
 * @brief Sleep for a specified number of milliseconds
 * @param ms Number of milliseconds to sleep
 */
void es_os_msleep(uint32_t ms);

/**
 * @brief Sleep for a specified number of microseconds
 * @param us Number of microseconds to sleep
 */
void es_os_us_delay(uint32_t us);

/**
 * @brief Check if the timeout has occurred
 * @param staes_tick Start tick
 * @param timeout_ms Timeout in milliseconds
 * @return int 1 if timeout has occurred, 0 otherwise
 */
int es_os_check_timeout(uint32_t staes_tick, uint32_t timeout_ms);

/**
 * @brief Make secret key
 */
void es_os_make_secret_key(void);

/**
 * @brief Reboot the system
 */
void es_os_reboot(void);


#ifdef __cplusplus
}
#endif

#endif /* __ES_DRV_OS_H__ */ 
