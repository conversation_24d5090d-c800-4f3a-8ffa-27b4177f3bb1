﻿/**
 * @file es_config.h
 * @brief Configuration options header file
 */

#ifndef ES_KCONFIG_H
#define ES_KCONFIG_H


#ifdef _WIN32
#define CONSOLE_UART_NAME "COM1"
#else
#define CONSOLE_UART_NAME "uart3"
#endif


#define CONFIG_CONSOLE_UART { \
    .name = CONSOLE_UART_NAME, \
    .baud_rate = 115200, \
    .data_bits = 8, \
    .stop_bits = ES_UART_STOP_BITS_1, \
    .parity = ES_UART_PARITY_NONE, \
    .flow_control = ES_UART_FLOW_CONTROL_NONE \
}

#endif /* ES_KCONFIG_H */ 
