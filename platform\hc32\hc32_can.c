/**
 * @file hc32_can.c
 * @brief HC32F460 CAN driver implementation with ring buffer support
 */

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "hc32f4xx.h"
#include "hc32_ll_can.h"
#include "hc32_ll_gpio.h"
#include "hc32_ll_fcg.h"
#include "hc32_ll_interrupts.h"
#include "hc32_ll_pwc.h"
#include "hc32_pin.h"
#include "es_ringbuffer.h"
#include "es_ringobj.h"
#include "es_console.h"
#include "es_drv_can.h"

/* GPIO definitions */
#define CAN1_TX_PORT                     (GPIO_PORT_B)
#define CAN1_TX_PIN                      (GPIO_PIN_07)
#define CAN1_TX_PIN_FUNC                 (GPIO_FUNC_50)

#define CAN1_RX_PORT                     (GPIO_PORT_B)
#define CAN1_RX_PIN                      (GPIO_PIN_06)
#define CAN1_RX_PIN_FUNC                 (GPIO_FUNC_51)

#define CAN1_STB_FUNC_ENABLE
#define CAN_STB_PORT                     (GPIO_PORT_E)
#define CAN_STB_PIN                      (GPIO_PIN_01)

/* Interrupt definitions */
#define BSP_CAN1_IRQ_NUM                INT004_IRQn
#define BSP_CAN1_IRQ_PRIO               DDL_IRQ_PRIO_DEFAULT
#define CAN1_INT_SRC                    INT_SRC_CAN_INT

/* Ring buffer definitions */
#define CAN_RX_BUFFER_CAPACITY  128    /* Must be power of 2, number of CAN messages */

/* Filter configuration for accepting all IDs */
#define CAN_FILTER_COUNT        1      /* Use 1 filter for all IDs */

/* Global variables */
static es_ringobj_t s_can_rx_ringbuffer;
static es_can_msg_t can_rx_buffer[CAN_RX_BUFFER_CAPACITY];
static volatile bool can_initialized = false;
static volatile bool can_ptb_tx_complete = false;
static volatile bool can_stb_tx_complete = false;

/* Filter configuration arrays */
static stc_can_filter_config_t can_filters[CAN_FILTER_COUNT];

/**
 * @brief CAN interrupt handler
 */
static void _irq_handler_can1(void)
{
    uint32_t status = CAN_GetStatusValue(CM_CAN);
    stc_can_rx_frame_t rx_frame;
    es_can_msg_t can_msg;
    
    /* Handle receive interrupt */
    if (status & CAN_FLAG_RX) {
        if (CAN_GetRxFrame(CM_CAN, &rx_frame) == LL_OK) {
            /* Convert to application message format */
            can_msg.id = rx_frame.u32ID;
            can_msg.ide = rx_frame.IDE;
            can_msg.rtr = rx_frame.RTR;
            can_msg.dlc = rx_frame.DLC;
            memcpy(can_msg.data, rx_frame.au8Data, 8);
            
            /* Store in ring buffer */
            es_ringobj_put(&s_can_rx_ringbuffer, &can_msg);
        }
        
        /* Clear receive flag */
        CAN_ClearStatus(CM_CAN, CAN_FLAG_RX);
    }
    
    /* Handle receive buffer warning */
    if (status & CAN_FLAG_RX_BUF_WARN) {
        CAN_ClearStatus(CM_CAN, CAN_FLAG_RX_BUF_WARN);
    }
    
    /* Handle receive buffer full */
    if (status & CAN_FLAG_RX_BUF_FULL) {
        CAN_ClearStatus(CM_CAN, CAN_FLAG_RX_BUF_FULL);
    }
    
    /* Handle receive overrun */
    if (status & CAN_FLAG_RX_OVERRUN) {
        CAN_ClearStatus(CM_CAN, CAN_FLAG_RX_OVERRUN);
    }
    
    /* Handle error interrupts */
    if (status & CAN_FLAG_ERR_INT) {
        /* Clear error interrupt flag */
        CAN_ClearStatus(CM_CAN, CAN_FLAG_ERR_INT);
    }
    
    /* Handle bus error */
    if (status & CAN_FLAG_BUS_ERR) {
        CAN_ClearStatus(CM_CAN, CAN_FLAG_BUS_ERR);
    }
    
    /* Handle arbitration lost */
    if (status & CAN_FLAG_ARBITR_LOST) {
        CAN_ClearStatus(CM_CAN, CAN_FLAG_ARBITR_LOST);
    }
    
    /* Handle error passive */
    if (status & CAN_FLAG_ERR_PASSIVE) {
        CAN_ClearStatus(CM_CAN, CAN_FLAG_ERR_PASSIVE);
    }
    
    /* Handle transmission complete - PTB */
    if (status & CAN_FLAG_PTB_TX) {
        CAN_ClearStatus(CM_CAN, CAN_FLAG_PTB_TX);
        can_ptb_tx_complete = true;
    }
    
    /* Handle transmission complete - STB */
    if (status & CAN_FLAG_STB_TX) {
        CAN_ClearStatus(CM_CAN, CAN_FLAG_STB_TX);
        can_stb_tx_complete = true;
    }
}

/**
 * @brief Configure CAN GPIO pins
 */
static void can_gpio_config(void)
{
    /* Configure CAN TX/RX pins */
    GPIO_SetFunc(CAN1_TX_PORT, CAN1_TX_PIN, CAN1_TX_PIN_FUNC);
    GPIO_SetFunc(CAN1_RX_PORT, CAN1_RX_PIN, CAN1_RX_PIN_FUNC);

    GPIO_ResetPins(CAN_STB_PORT, CAN_STB_PIN);
    GPIO_OutputCmd(CAN_STB_PORT, CAN_STB_PIN, ENABLE);
}

/**
 * @brief Configure CAN interrupts
 */
static void can_interrupt_config(void)
{
    struct hc32_irq_config irq_config;
    
    irq_config.irq_num = BSP_CAN1_IRQ_NUM;
    irq_config.int_src = CAN1_INT_SRC;
    irq_config.irq_prio = BSP_CAN1_IRQ_PRIO;
    
    /* Register interrupt handler */
    hc32_install_irq_handler(&irq_config, _irq_handler_can1, true);
}

/**
 * @brief Configure CAN filters to accept all IDs
 */
static void can_filter_config(void)
{
    /* Filter 1: Accept all standard and extended IDs */
    can_filters[0].u32ID = 0x00000000;          /* Base ID */
    can_filters[0].u32IDMask = 0x1FFFFFFF;      /* Mask all bits (accept all) */
    can_filters[0].u32IDType = CAN_ID_STD_EXT;  /* Accept both standard and extended IDs */
}

/**
 * @brief Initialize CAN module
 * @return 0 on success, -1 on failure
 */
int es_can_init(void)
{
    stc_can_init_t can_init;
    int32_t ret;
    
    if (can_initialized) {
        return 0; /* Already initialized */
    }

    /* Enable CAN peripheral clock */
    FCG_Fcg1PeriphClockCmd(FCG1_PERIPH_CAN, ENABLE); 


    /* Initialize ring buffer for receive */
    ret = es_ringobj_init(&s_can_rx_ringbuffer, can_rx_buffer, sizeof(es_can_msg_t), CAN_RX_BUFFER_CAPACITY);
    if (ret != 0) {
        return -1;
    }
    
    /* Configure GPIO pins */
    can_gpio_config();
    
    /* Configure filters to accept all IDs */
    can_filter_config();
    
    /* Configure interrupts */
    can_interrupt_config();

    /* Initialize CAN configuration structure */
    ret = CAN_StructInit(&can_init);
    if (ret != LL_OK) {
        return -1;
    }
    
    /* Configure bit timing - direct values for 500K baud */
    can_init.stcBitCfg.u32Prescaler = 4;
    can_init.stcBitCfg.u32TimeSeg1 = 3;
    can_init.stcBitCfg.u32TimeSeg2 = 1;
    can_init.stcBitCfg.u32SJW = 1;
    
    /* Configure basic parameters */
    can_init.u8WorkMode = CAN_WORK_MD_NORMAL;
    can_init.u8PTBSingleShotTx = CAN_PTB_SINGLESHOT_TX_DISABLE;
    can_init.u8STBSingleShotTx = CAN_STB_SINGLESHOT_TX_DISABLE;
    can_init.u8STBPrioMode = CAN_STB_PRIO_MD_DISABLE;
    can_init.u8RxWarnLimit = 8;     /* Receive buffer warning limit */
    can_init.u8ErrorWarnLimit = 7;  /* Error warning limit */
    can_init.u8RxAllFrame = CAN_RX_ALL_FRAME_DISABLE;
    can_init.u8RxOvfMode = 0x40U;  /* CAN_RX_OVF_DISCARD_NEW */
    can_init.u8SelfAck = CAN_SELF_ACK_DISABLE;
    
    /* Configure acceptance filters to receive all IDs - 这是关键！*/
    can_init.u16FilterSelect = CAN_FILTER1;  /* Enable filter 1 only (CAN_FILTER1) */
    can_init.pstcFilter = can_filters;       /* Point to filter configuration array */
    can_init.pstcCanTtc = NULL;             /* No TTCAN */
    
    /* Initialize CAN */
    ret = CAN_Init(CM_CAN, &can_init);
    if (ret != LL_OK) {
        return -1;
    }

    
    /* Enable CAN interrupts */
    CAN_IntCmd(CM_CAN, 
               CAN_INT_RX | 
               CAN_INT_RX_BUF_WARN | 
               CAN_INT_RX_BUF_FULL | 
               CAN_INT_RX_OVERRUN |
               CAN_INT_ERR_INT |
               CAN_INT_BUS_ERR |
               CAN_INT_ARBITR_LOST |
               CAN_INT_ERR_PASSIVE |
               CAN_INT_PTB_TX |
               CAN_INT_STB_TX,
               ENABLE);
    
    can_initialized = true;
    
    return 0;
}

/**
 * @brief Read CAN message from receive buffer
 * @param msg Pointer to es_can_msg_t structure to store the received message
 * @return Number of messages read (0 or 1), -1 on error
 */
int es_can_read(es_can_msg_t *msg)
{
    if (!can_initialized || !msg) {
        return -1;
    }
    
    /* Try to read a message from ring buffer */
    if (es_ringobj_get(&s_can_rx_ringbuffer, msg)) {
        return 1; /* One message read */
    }
    
    return 0; /* No message available */
}

/**
 * @brief Write CAN message to transmit buffer
 * @param msg Pointer to es_can_msg_t structure containing the message to send
 * @return 0 on success, -1 on failure
 */
int es_can_write(const es_can_msg_t *msg)
{
    stc_can_tx_frame_t tx_frame;
    int32_t ret;
    
    if (!can_initialized || !msg) {
        return -1;
    }
    
    /* Validate parameters */
    if (msg->dlc > 8) {
        return -1;
    }
    
    if (msg->ide > 1 || msg->rtr > 1) {
        return -1;
    }
    
    if (msg->ide == 0 && msg->id > 0x7FF) {
        return -1; /* Standard ID range: 0-0x7FF */
    }
    
    if (msg->ide == 1 && msg->id > 0x1FFFFFFF) {
        return -1; /* Extended ID range: 0-0x1FFFFFFF */
    }
    
    /* Prepare transmit frame */
    tx_frame.u32ID = msg->id;
    tx_frame.IDE = msg->ide;
    tx_frame.RTR = msg->rtr;
    tx_frame.DLC = msg->dlc;
    tx_frame.FDF = 0; /* Classical CAN frame */
    tx_frame.BRS = 0; /* No bit rate switching */
    
    /* Copy data if it's a data frame */
    if (msg->rtr == 0) {
        memcpy(tx_frame.au8Data, msg->data, msg->dlc);
    } else {
        memset(tx_frame.au8Data, 0, 8);
    }
    
    /* Fill PTB (Primary Transmit Buffer) */
    ret = CAN_FillTxFrame(CM_CAN, CAN_TX_BUF_PTB, &tx_frame);
    if (ret != LL_OK) {
        return -1;
    }
    
    /* Start transmission */
    CAN_StartTx(CM_CAN, 0x10U);  /* CAN_TX_REQ_PTB */
    
    return 0;
}

es_async_t es_can_coro_write(es_coro_t *coro, uint32_t id, const uint8_t *data, uint16_t len) 
{
    
    static uint32_t offset = 0;
    static uint8_t dlc = 0;
    static uint8_t running = 0;
    es_co_begin(coro);

    if(!can_initialized) {
        es_co_exit;
    }

    //wait offset is 0
    es_co_wait_timeout(running == 0, 2000);

    offset = 0;
    running = 1;
    while(offset < len) {
        stc_can_tx_frame_t frame = {0};
        dlc = len - offset > 8 ? 8 : len - offset;
        frame.u32ID = id;
        frame.DLC = dlc;
        frame.IDE = id > 0x7FF ? 1 : 0;

        memcpy(frame.au8Data, data + offset, dlc);

        CAN_FillTxFrame(CM_CAN, CAN_TX_BUF_PTB, &frame);
        CAN_StartTx(CM_CAN, CAN_TX_REQ_PTB);  /* CAN_TX_REQ_PTB */
        can_ptb_tx_complete = false;
        es_co_wait_timeout(can_ptb_tx_complete, 50);

        offset += dlc;
    }
    running = 0;
    es_co_end;
}

/**
 * @brief Check if CAN receive buffer has data available
 * @return Number of available messages, -1 on error
 */
int es_can_available(void)
{
    if (!can_initialized) {
        return -1;
    }
    
    /* Get number of messages in buffer */
    return es_ringobj_count(&s_can_rx_ringbuffer);
}

/**
 * @brief Get CAN status information
 * @return CAN status flags, 0 if not initialized
 */
uint32_t es_can_get_status(void)
{
    if (!can_initialized) {
        return 0;
    }
    
    return CAN_GetStatusValue(CM_CAN);
}

/**
 * @brief Get CAN error information
 * @param error_info Pointer to store error information
 * @return 0 on success, -1 on failure
 */
int es_can_get_error_info(es_can_error_info_t *error_info)
{
    stc_can_error_info_t hc32_error_info;
    
    if (!can_initialized || !error_info) {
        return -1;
    }
    
    /* Get error information from CAN peripheral */
    int32_t ret = CAN_GetErrorInfo(CM_CAN, &hc32_error_info);
    if (ret != LL_OK) {
        return -1;
    }
    
    /* Map HC32 specific structure to generic structure */
    error_info->arbitr_lost_pos = hc32_error_info.u8ArbitrLostPos;
    error_info->error_type = hc32_error_info.u8ErrorType;
    error_info->rx_error_count = hc32_error_info.u8RxErrorCount;
    error_info->tx_error_count = hc32_error_info.u8TxErrorCount;
    
    return 0;
}

 
