#!/usr/bin/env python3
"""
C语言子集到VM字节码编译器

支持的C语言特性：
- 基本数据类型：int (32位)
- 变量声明和赋值
- 算术运算：+, -, *, /, %
- 位运算：&, |, ^, ~, <<, >>
- 比较运算：==, !=, <, <=, >, >=
- 控制流：if-else, while
- 表达式和语句
- 寄存器变量（使用register关键字）

示例代码：
```c
int main() {
    register int a = 10;
    register int b = 20;
    int result = a + b * 2;
    if (result > 40) {
        result = result - 10;
    }
    return result;
}
```
"""

import re
import struct
from typing import List, Dict, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass

# VM操作码定义
class VMOpcode:
    NOP = 0x00
    PUSH8 = 0x01
    PUSH16 = 0x02
    PUSH32 = 0x03
    POP = 0x04
    DUP = 0x05
    SWAP = 0x06
    ADD = 0x10
    SUB = 0x11
    MUL = 0x12
    DIV = 0x13
    MOD = 0x14
    AND = 0x15
    OR = 0x16
    XOR = 0x17
    NOT = 0x18
    SHL = 0x19
    SHR = 0x1A
    EQ = 0x20
    NE = 0x21
    LT = 0x22
    LE = 0x23
    GT = 0x24
    GE = 0x25
    JMP = 0x30
    JZ = 0x31
    JNZ = 0x32
    CALL = 0x40
    RET = 0x41
    STORE_REG = 0x42
    LOAD_REG = 0x43
    HALT = 0xFF

# 词法分析器
class TokenType(Enum):
    # 字面量
    NUMBER = "NUMBER"
    IDENTIFIER = "IDENTIFIER"
    
    # 关键字
    INT = "int"
    REGISTER = "register"
    IF = "if"
    ELSE = "else"
    WHILE = "while"
    RETURN = "return"
    
    # 运算符
    PLUS = "+"
    MINUS = "-"
    MULTIPLY = "*"
    DIVIDE = "/"
    MODULO = "%"
    
    # 位运算
    BIT_AND = "&"
    BIT_OR = "|"
    BIT_XOR = "^"
    BIT_NOT = "~"
    LSHIFT = "<<"
    RSHIFT = ">>"
    
    # 比较运算
    EQ = "=="
    NE = "!="
    LT = "<"
    LE = "<="
    GT = ">"
    GE = ">="
    
    # 赋值
    ASSIGN = "="
    
    # 分隔符
    SEMICOLON = ";"
    COMMA = ","
    LPAREN = "("
    RPAREN = ")"
    LBRACE = "{"
    RBRACE = "}"
    
    # 特殊
    EOF = "EOF"

@dataclass
class Token:
    type: TokenType
    value: str
    line: int
    column: int

class Lexer:
    def __init__(self, source: str):
        self.source = source
        self.pos = 0
        self.line = 1
        self.column = 1
        self.tokens = []
        
        # 关键字映射
        self.keywords = {
            'int': TokenType.INT,
            'register': TokenType.REGISTER,
            'if': TokenType.IF,
            'else': TokenType.ELSE,
            'while': TokenType.WHILE,
            'return': TokenType.RETURN,
        }
        
        # 双字符运算符
        self.double_ops = {
            '<<': TokenType.LSHIFT,
            '>>': TokenType.RSHIFT,
            '==': TokenType.EQ,
            '!=': TokenType.NE,
            '<=': TokenType.LE,
            '>=': TokenType.GE,
        }
        
        # 单字符运算符
        self.single_ops = {
            '+': TokenType.PLUS,
            '-': TokenType.MINUS,
            '*': TokenType.MULTIPLY,
            '/': TokenType.DIVIDE,
            '%': TokenType.MODULO,
            '&': TokenType.BIT_AND,
            '|': TokenType.BIT_OR,
            '^': TokenType.BIT_XOR,
            '~': TokenType.BIT_NOT,
            '<': TokenType.LT,
            '>': TokenType.GT,
            '=': TokenType.ASSIGN,
            ';': TokenType.SEMICOLON,
            ',': TokenType.COMMA,
            '(': TokenType.LPAREN,
            ')': TokenType.RPAREN,
            '{': TokenType.LBRACE,
            '}': TokenType.RBRACE,
        }
    
    def current_char(self) -> Optional[str]:
        if self.pos >= len(self.source):
            return None
        return self.source[self.pos]
    
    def peek_char(self, offset: int = 1) -> Optional[str]:
        peek_pos = self.pos + offset
        if peek_pos >= len(self.source):
            return None
        return self.source[peek_pos]
    
    def advance(self):
        if self.pos < len(self.source) and self.source[self.pos] == '\n':
            self.line += 1
            self.column = 1
        else:
            self.column += 1
        self.pos += 1
    
    def skip_whitespace(self):
        while self.current_char() and self.current_char().isspace():
            self.advance()

    def skip_comment(self):
        """跳过单行注释 //"""
        if self.current_char() == '/' and self.peek_char() == '/':
            # 跳过整行
            while self.current_char() and self.current_char() != '\n':
                self.advance()
            return True
        return False
    
    def read_number(self) -> str:
        start_pos = self.pos

        # 检查是否是十六进制数字 (0x...)
        if self.current_char() == '0' and self.peek_char() and self.peek_char().lower() == 'x':
            self.advance()  # skip '0'
            self.advance()  # skip 'x'
            # 读取十六进制数字
            while self.current_char() and self.current_char().lower() in '0123456789abcdef':
                self.advance()
        else:
            # 读取十进制数字
            while self.current_char() and self.current_char().isdigit():
                self.advance()

        return self.source[start_pos:self.pos]
    
    def read_identifier(self) -> str:
        start_pos = self.pos
        while self.current_char() and (self.current_char().isalnum() or self.current_char() == '_'):
            self.advance()
        return self.source[start_pos:self.pos]
    
    def tokenize(self) -> List[Token]:
        while self.current_char():
            self.skip_whitespace()

            if not self.current_char():
                break

            # 跳过注释
            if self.skip_comment():
                continue

            line, column = self.line, self.column
            
            # 数字
            if self.current_char().isdigit():
                value = self.read_number()
                self.tokens.append(Token(TokenType.NUMBER, value, line, column))
                continue
            
            # 标识符和关键字
            if self.current_char().isalpha() or self.current_char() == '_':
                value = self.read_identifier()
                token_type = self.keywords.get(value, TokenType.IDENTIFIER)
                self.tokens.append(Token(token_type, value, line, column))
                continue
            
            # 双字符运算符
            if self.peek_char():
                double_op = self.current_char() + self.peek_char()
                if double_op in self.double_ops:
                    self.advance()
                    self.advance()
                    self.tokens.append(Token(self.double_ops[double_op], double_op, line, column))
                    continue
            
            # 单字符运算符
            if self.current_char() in self.single_ops:
                char = self.current_char()
                self.advance()
                self.tokens.append(Token(self.single_ops[char], char, line, column))
                continue
            
            # 未知字符
            raise SyntaxError(f"Unexpected character '{self.current_char()}' at line {self.line}, column {self.column}")
        
        self.tokens.append(Token(TokenType.EOF, "", self.line, self.column))
        return self.tokens

# AST节点定义
class ASTNode:
    pass

@dataclass
class Program(ASTNode):
    functions: List['Function']

@dataclass
class Function(ASTNode):
    name: str
    return_type: str
    parameters: List['Parameter']
    body: 'Block'

@dataclass
class Parameter(ASTNode):
    type: str
    name: str

@dataclass
class Block(ASTNode):
    statements: List['Statement']

class Statement(ASTNode):
    pass

@dataclass
class VarDeclaration(Statement):
    type: str
    name: str
    is_register: bool = False
    initializer: Optional['Expression'] = None

@dataclass
class Assignment(Statement):
    target: str
    value: 'Expression'

@dataclass
class IfStatement(Statement):
    condition: 'Expression'
    then_stmt: Statement
    else_stmt: Optional[Statement] = None

@dataclass
class WhileStatement(Statement):
    condition: 'Expression'
    body: Statement

@dataclass
class ReturnStatement(Statement):
    value: Optional['Expression'] = None

@dataclass
class ExpressionStatement(Statement):
    expression: 'Expression'

class Expression(ASTNode):
    pass

@dataclass
class BinaryOp(Expression):
    left: Expression
    operator: TokenType
    right: Expression

@dataclass
class UnaryOp(Expression):
    operator: TokenType
    operand: Expression

@dataclass
class Number(Expression):
    value: int

@dataclass
class Identifier(Expression):
    name: str

@dataclass
class FunctionCall(Expression):
    name: str
    arguments: List[Expression]

# 语法分析器
class Parser:
    def __init__(self, tokens: List[Token]):
        self.tokens = tokens
        self.pos = 0

    def current_token(self) -> Token:
        if self.pos >= len(self.tokens):
            return self.tokens[-1]  # EOF token
        return self.tokens[self.pos]

    def peek_token(self, offset: int = 1) -> Token:
        peek_pos = self.pos + offset
        if peek_pos >= len(self.tokens):
            return self.tokens[-1]  # EOF token
        return self.tokens[peek_pos]

    def advance(self):
        if self.pos < len(self.tokens) - 1:
            self.pos += 1

    def expect(self, token_type: TokenType) -> Token:
        token = self.current_token()
        if token.type != token_type:
            raise SyntaxError(f"Expected {token_type}, got {token.type} at line {token.line}")
        self.advance()
        return token

    def match(self, *token_types: TokenType) -> bool:
        return self.current_token().type in token_types

    def parse(self) -> Program:
        functions = []
        while not self.match(TokenType.EOF):
            functions.append(self.parse_function())
        return Program(functions)

    def parse_function(self) -> Function:
        # int function_name() { ... }
        return_type = self.expect(TokenType.INT).value
        name = self.expect(TokenType.IDENTIFIER).value
        self.expect(TokenType.LPAREN)

        # 暂时不支持参数
        parameters = []

        self.expect(TokenType.RPAREN)
        body = self.parse_block()

        return Function(name, return_type, parameters, body)

    def parse_block(self) -> Block:
        self.expect(TokenType.LBRACE)
        statements = []

        while not self.match(TokenType.RBRACE, TokenType.EOF):
            statements.append(self.parse_statement())

        self.expect(TokenType.RBRACE)
        return Block(statements)

    def parse_statement(self) -> Statement:
        if self.match(TokenType.INT, TokenType.REGISTER):
            return self.parse_var_declaration()
        elif self.match(TokenType.IF):
            return self.parse_if_statement()
        elif self.match(TokenType.WHILE):
            return self.parse_while_statement()
        elif self.match(TokenType.RETURN):
            return self.parse_return_statement()
        elif self.match(TokenType.LBRACE):
            return self.parse_block()
        else:
            # 可能是赋值或表达式语句
            if self.current_token().type == TokenType.IDENTIFIER and self.peek_token().type == TokenType.ASSIGN:
                return self.parse_assignment()
            else:
                return self.parse_expression_statement()

    def parse_var_declaration(self) -> VarDeclaration:
        is_register = False
        if self.match(TokenType.REGISTER):
            is_register = True
            self.advance()

        var_type = self.expect(TokenType.INT).value
        name = self.expect(TokenType.IDENTIFIER).value

        initializer = None
        if self.match(TokenType.ASSIGN):
            self.advance()
            initializer = self.parse_expression()

        self.expect(TokenType.SEMICOLON)
        return VarDeclaration(var_type, name, is_register, initializer)

    def parse_assignment(self) -> Assignment:
        target = self.expect(TokenType.IDENTIFIER).value
        self.expect(TokenType.ASSIGN)
        value = self.parse_expression()
        self.expect(TokenType.SEMICOLON)
        return Assignment(target, value)

    def parse_if_statement(self) -> IfStatement:
        self.expect(TokenType.IF)
        self.expect(TokenType.LPAREN)
        condition = self.parse_expression()
        self.expect(TokenType.RPAREN)
        then_stmt = self.parse_statement()

        else_stmt = None
        if self.match(TokenType.ELSE):
            self.advance()
            else_stmt = self.parse_statement()

        return IfStatement(condition, then_stmt, else_stmt)

    def parse_while_statement(self) -> WhileStatement:
        self.expect(TokenType.WHILE)
        self.expect(TokenType.LPAREN)
        condition = self.parse_expression()
        self.expect(TokenType.RPAREN)
        body = self.parse_statement()
        return WhileStatement(condition, body)

    def parse_return_statement(self) -> ReturnStatement:
        self.expect(TokenType.RETURN)
        value = None
        if not self.match(TokenType.SEMICOLON):
            value = self.parse_expression()
        self.expect(TokenType.SEMICOLON)
        return ReturnStatement(value)

    def parse_expression_statement(self) -> ExpressionStatement:
        expr = self.parse_expression()
        self.expect(TokenType.SEMICOLON)
        return ExpressionStatement(expr)

    # 表达式解析 - 运算符优先级递归下降
    def parse_expression(self) -> Expression:
        return self.parse_logical_or()

    def parse_logical_or(self) -> Expression:
        expr = self.parse_logical_xor()
        while self.match(TokenType.BIT_OR):
            op = self.current_token().type
            self.advance()
            right = self.parse_logical_xor()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_logical_xor(self) -> Expression:
        expr = self.parse_logical_and()
        while self.match(TokenType.BIT_XOR):
            op = self.current_token().type
            self.advance()
            right = self.parse_logical_and()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_logical_and(self) -> Expression:
        expr = self.parse_equality()
        while self.match(TokenType.BIT_AND):
            op = self.current_token().type
            self.advance()
            right = self.parse_equality()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_equality(self) -> Expression:
        expr = self.parse_relational()
        while self.match(TokenType.EQ, TokenType.NE):
            op = self.current_token().type
            self.advance()
            right = self.parse_relational()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_relational(self) -> Expression:
        expr = self.parse_shift()
        while self.match(TokenType.LT, TokenType.LE, TokenType.GT, TokenType.GE):
            op = self.current_token().type
            self.advance()
            right = self.parse_shift()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_shift(self) -> Expression:
        expr = self.parse_additive()
        while self.match(TokenType.LSHIFT, TokenType.RSHIFT):
            op = self.current_token().type
            self.advance()
            right = self.parse_additive()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_additive(self) -> Expression:
        expr = self.parse_multiplicative()
        while self.match(TokenType.PLUS, TokenType.MINUS):
            op = self.current_token().type
            self.advance()
            right = self.parse_multiplicative()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_multiplicative(self) -> Expression:
        expr = self.parse_unary()
        while self.match(TokenType.MULTIPLY, TokenType.DIVIDE, TokenType.MODULO):
            op = self.current_token().type
            self.advance()
            right = self.parse_unary()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_unary(self) -> Expression:
        if self.match(TokenType.MINUS, TokenType.BIT_NOT):
            op = self.current_token().type
            self.advance()
            operand = self.parse_unary()
            return UnaryOp(op, operand)
        return self.parse_primary()

    def parse_primary(self) -> Expression:
        if self.match(TokenType.NUMBER):
            value = int(self.current_token().value, 0)  # 支持十六进制
            self.advance()
            return Number(value)

        if self.match(TokenType.IDENTIFIER):
            name = self.current_token().value
            self.advance()

            # 检查是否是函数调用
            if self.match(TokenType.LPAREN):
                self.advance()
                arguments = []

                # 解析参数列表
                if not self.match(TokenType.RPAREN):
                    arguments.append(self.parse_expression())
                    while self.match(TokenType.COMMA):
                        self.advance()
                        arguments.append(self.parse_expression())

                self.expect(TokenType.RPAREN)
                return FunctionCall(name, arguments)
            else:
                return Identifier(name)

        if self.match(TokenType.LPAREN):
            self.advance()
            expr = self.parse_expression()
            self.expect(TokenType.RPAREN)
            return expr

        token = self.current_token()
        raise SyntaxError(f"Unexpected token {token.type} at line {token.line}")

# AST可视化器
class ASTVisualizer:
    def __init__(self):
        self.indent_level = 0
        self.output = []

    def visit(self, node: ASTNode) -> str:
        """访问AST节点并生成可视化字符串"""
        self.output = []
        self._visit_node(node)
        return '\n'.join(self.output)

    def _visit_node(self, node: ASTNode):
        indent = "  " * self.indent_level

        if isinstance(node, Program):
            self.output.append(f"{indent}Program")
            self.indent_level += 1
            for func in node.functions:
                self._visit_node(func)
            self.indent_level -= 1

        elif isinstance(node, Function):
            self.output.append(f"{indent}Function: {node.name} -> {node.return_type}")
            self.indent_level += 1
            if node.parameters:
                self.output.append(f"{indent}  Parameters:")
                self.indent_level += 1
                for param in node.parameters:
                    self._visit_node(param)
                self.indent_level -= 1
            self.output.append(f"{indent}  Body:")
            self.indent_level += 1
            self._visit_node(node.body)
            self.indent_level -= 1
            self.indent_level -= 1

        elif isinstance(node, Parameter):
            self.output.append(f"{indent}Parameter: {node.type} {node.name}")

        elif isinstance(node, Block):
            self.output.append(f"{indent}Block")
            self.indent_level += 1
            for stmt in node.statements:
                self._visit_node(stmt)
            self.indent_level -= 1

        elif isinstance(node, VarDeclaration):
            reg_str = "register " if node.is_register else ""
            self.output.append(f"{indent}VarDeclaration: {reg_str}{node.type} {node.name}")
            if node.initializer:
                self.indent_level += 1
                self.output.append(f"{indent}  Initializer:")
                self.indent_level += 1
                self._visit_node(node.initializer)
                self.indent_level -= 2

        elif isinstance(node, Assignment):
            self.output.append(f"{indent}Assignment: {node.target}")
            self.indent_level += 1
            self.output.append(f"{indent}  Value:")
            self.indent_level += 1
            self._visit_node(node.value)
            self.indent_level -= 2

        elif isinstance(node, IfStatement):
            self.output.append(f"{indent}IfStatement")
            self.indent_level += 1
            self.output.append(f"{indent}  Condition:")
            self.indent_level += 1
            self._visit_node(node.condition)
            self.indent_level -= 1
            self.output.append(f"{indent}  Then:")
            self.indent_level += 1
            self._visit_node(node.then_stmt)
            self.indent_level -= 1
            if node.else_stmt:
                self.output.append(f"{indent}  Else:")
                self.indent_level += 1
                self._visit_node(node.else_stmt)
                self.indent_level -= 1
            self.indent_level -= 1

        elif isinstance(node, WhileStatement):
            self.output.append(f"{indent}WhileStatement")
            self.indent_level += 1
            self.output.append(f"{indent}  Condition:")
            self.indent_level += 1
            self._visit_node(node.condition)
            self.indent_level -= 1
            self.output.append(f"{indent}  Body:")
            self.indent_level += 1
            self._visit_node(node.body)
            self.indent_level -= 1
            self.indent_level -= 1

        elif isinstance(node, ReturnStatement):
            self.output.append(f"{indent}ReturnStatement")
            if node.value:
                self.indent_level += 1
                self.output.append(f"{indent}  Value:")
                self.indent_level += 1
                self._visit_node(node.value)
                self.indent_level -= 2

        elif isinstance(node, ExpressionStatement):
            self.output.append(f"{indent}ExpressionStatement")
            self.indent_level += 1
            self._visit_node(node.expression)
            self.indent_level -= 1

        elif isinstance(node, BinaryOp):
            self.output.append(f"{indent}BinaryOp: {node.operator.value}")
            self.indent_level += 1
            self.output.append(f"{indent}  Left:")
            self.indent_level += 1
            self._visit_node(node.left)
            self.indent_level -= 1
            self.output.append(f"{indent}  Right:")
            self.indent_level += 1
            self._visit_node(node.right)
            self.indent_level -= 1
            self.indent_level -= 1

        elif isinstance(node, UnaryOp):
            self.output.append(f"{indent}UnaryOp: {node.operator.value}")
            self.indent_level += 1
            self.output.append(f"{indent}  Operand:")
            self.indent_level += 1
            self._visit_node(node.operand)
            self.indent_level -= 1
            self.indent_level -= 1

        elif isinstance(node, FunctionCall):
            self.output.append(f"{indent}FunctionCall: {node.name}")
            if node.arguments:
                self.indent_level += 1
                self.output.append(f"{indent}  Arguments:")
                self.indent_level += 1
                for arg in node.arguments:
                    self._visit_node(arg)
                self.indent_level -= 2

        elif isinstance(node, Number):
            self.output.append(f"{indent}Number: {node.value}")

        elif isinstance(node, Identifier):
            self.output.append(f"{indent}Identifier: {node.name}")

        else:
            self.output.append(f"{indent}Unknown node: {type(node).__name__}")

# AST优化器
class ASTOptimizer:
    def __init__(self):
        self.optimizations_applied = []

    def optimize(self, ast: Program) -> Program:
        """对AST进行优化"""
        self.optimizations_applied = []
        optimized_ast = self._optimize_program(ast)
        return optimized_ast

    def _optimize_program(self, program: Program) -> Program:
        optimized_functions = []
        for func in program.functions:
            optimized_functions.append(self._optimize_function(func))
        return Program(optimized_functions)

    def _optimize_function(self, func: Function) -> Function:
        optimized_body = self._optimize_block(func.body)
        return Function(func.name, func.return_type, func.parameters, optimized_body)

    def _optimize_block(self, block: Block) -> Block:
        optimized_statements = []
        for stmt in block.statements:
            optimized_stmt = self._optimize_statement(stmt)
            if optimized_stmt:  # 过滤掉被优化掉的语句
                optimized_statements.append(optimized_stmt)
        return Block(optimized_statements)

    def _optimize_statement(self, stmt: Statement) -> Optional[Statement]:
        if isinstance(stmt, VarDeclaration):
            if stmt.initializer:
                optimized_init = self._optimize_expression(stmt.initializer)
                return VarDeclaration(stmt.type, stmt.name, stmt.is_register, optimized_init)
            return stmt

        elif isinstance(stmt, Assignment):
            optimized_value = self._optimize_expression(stmt.value)
            return Assignment(stmt.target, optimized_value)

        elif isinstance(stmt, IfStatement):
            optimized_condition = self._optimize_expression(stmt.condition)

            # 常量折叠：如果条件是常量，可以直接选择分支
            if isinstance(optimized_condition, Number):
                if optimized_condition.value != 0:
                    # 条件为真，只保留then分支
                    self.optimizations_applied.append("Constant folding: if(true) -> then branch")
                    return self._optimize_statement(stmt.then_stmt)
                else:
                    # 条件为假，只保留else分支（如果有）
                    self.optimizations_applied.append("Constant folding: if(false) -> else branch")
                    if stmt.else_stmt:
                        return self._optimize_statement(stmt.else_stmt)
                    else:
                        return None  # 删除整个if语句

            optimized_then = self._optimize_statement(stmt.then_stmt)
            optimized_else = None
            if stmt.else_stmt:
                optimized_else = self._optimize_statement(stmt.else_stmt)

            return IfStatement(optimized_condition, optimized_then, optimized_else)

        elif isinstance(stmt, WhileStatement):
            optimized_condition = self._optimize_expression(stmt.condition)

            # 常量折叠：如果条件是常量0，删除整个循环
            if isinstance(optimized_condition, Number) and optimized_condition.value == 0:
                self.optimizations_applied.append("Dead code elimination: while(false) removed")
                return None

            optimized_body = self._optimize_statement(stmt.body)
            return WhileStatement(optimized_condition, optimized_body)

        elif isinstance(stmt, ReturnStatement):
            if stmt.value:
                optimized_value = self._optimize_expression(stmt.value)
                return ReturnStatement(optimized_value)
            return stmt

        elif isinstance(stmt, ExpressionStatement):
            optimized_expr = self._optimize_expression(stmt.expression)
            return ExpressionStatement(optimized_expr)

        elif isinstance(stmt, Block):
            return self._optimize_block(stmt)

        return stmt

    def _optimize_expression(self, expr: Expression) -> Expression:
        if isinstance(expr, BinaryOp):
            left = self._optimize_expression(expr.left)
            right = self._optimize_expression(expr.right)

            # 常量折叠
            if isinstance(left, Number) and isinstance(right, Number):
                result = self._evaluate_binary_op(left.value, expr.operator, right.value)
                if result is not None:
                    self.optimizations_applied.append(f"Constant folding: {left.value} {expr.operator.value} {right.value} = {result}")
                    return Number(result)

            # 代数简化
            simplified = self._algebraic_simplification(left, expr.operator, right)
            if simplified:
                return simplified

            return BinaryOp(left, expr.operator, right)

        elif isinstance(expr, UnaryOp):
            operand = self._optimize_expression(expr.operand)

            # 常量折叠
            if isinstance(operand, Number):
                if expr.operator == TokenType.MINUS:
                    result = -operand.value
                    self.optimizations_applied.append(f"Constant folding: -{operand.value} = {result}")
                    return Number(result)
                elif expr.operator == TokenType.BIT_NOT:
                    result = ~operand.value & 0xFFFFFFFF  # 32位掩码
                    self.optimizations_applied.append(f"Constant folding: ~{operand.value} = {result}")
                    return Number(result)

            return UnaryOp(expr.operator, operand)

        elif isinstance(expr, FunctionCall):
            optimized_args = []
            for arg in expr.arguments:
                optimized_args.append(self._optimize_expression(arg))
            return FunctionCall(expr.name, optimized_args)

        return expr

    def _evaluate_binary_op(self, left: int, op: TokenType, right: int) -> Optional[int]:
        """计算二元运算的常量结果"""
        try:
            if op == TokenType.PLUS:
                return left + right
            elif op == TokenType.MINUS:
                return left - right
            elif op == TokenType.MULTIPLY:
                return left * right
            elif op == TokenType.DIVIDE:
                return left // right if right != 0 else None
            elif op == TokenType.MODULO:
                return left % right if right != 0 else None
            elif op == TokenType.BIT_AND:
                return left & right
            elif op == TokenType.BIT_OR:
                return left | right
            elif op == TokenType.BIT_XOR:
                return left ^ right
            elif op == TokenType.LSHIFT:
                return (left << right) & 0xFFFFFFFF
            elif op == TokenType.RSHIFT:
                return left >> right
            elif op == TokenType.EQ:
                return 1 if left == right else 0
            elif op == TokenType.NE:
                return 1 if left != right else 0
            elif op == TokenType.LT:
                return 1 if left < right else 0
            elif op == TokenType.LE:
                return 1 if left <= right else 0
            elif op == TokenType.GT:
                return 1 if left > right else 0
            elif op == TokenType.GE:
                return 1 if left >= right else 0
        except (ZeroDivisionError, OverflowError):
            return None
        return None

    def _algebraic_simplification(self, left: Expression, op: TokenType, right: Expression) -> Optional[Expression]:
        """代数简化"""
        # x + 0 = x, 0 + x = x
        if op == TokenType.PLUS:
            if isinstance(right, Number) and right.value == 0:
                self.optimizations_applied.append("Algebraic simplification: x + 0 = x")
                return left
            if isinstance(left, Number) and left.value == 0:
                self.optimizations_applied.append("Algebraic simplification: 0 + x = x")
                return right

        # x - 0 = x
        elif op == TokenType.MINUS:
            if isinstance(right, Number) and right.value == 0:
                self.optimizations_applied.append("Algebraic simplification: x - 0 = x")
                return left

        # x * 1 = x, 1 * x = x
        elif op == TokenType.MULTIPLY:
            if isinstance(right, Number) and right.value == 1:
                self.optimizations_applied.append("Algebraic simplification: x * 1 = x")
                return left
            if isinstance(left, Number) and left.value == 1:
                self.optimizations_applied.append("Algebraic simplification: 1 * x = x")
                return right
            # x * 0 = 0, 0 * x = 0
            if isinstance(right, Number) and right.value == 0:
                self.optimizations_applied.append("Algebraic simplification: x * 0 = 0")
                return Number(0)
            if isinstance(left, Number) and left.value == 0:
                self.optimizations_applied.append("Algebraic simplification: 0 * x = 0")
                return Number(0)

        # x / 1 = x
        elif op == TokenType.DIVIDE:
            if isinstance(right, Number) and right.value == 1:
                self.optimizations_applied.append("Algebraic simplification: x / 1 = x")
                return left

        # x & 0 = 0, x | 0 = x, x ^ 0 = x
        elif op == TokenType.BIT_AND:
            if isinstance(right, Number) and right.value == 0:
                self.optimizations_applied.append("Algebraic simplification: x & 0 = 0")
                return Number(0)
            if isinstance(left, Number) and left.value == 0:
                self.optimizations_applied.append("Algebraic simplification: 0 & x = 0")
                return Number(0)

        elif op == TokenType.BIT_OR:
            if isinstance(right, Number) and right.value == 0:
                self.optimizations_applied.append("Algebraic simplification: x | 0 = x")
                return left
            if isinstance(left, Number) and left.value == 0:
                self.optimizations_applied.append("Algebraic simplification: 0 | x = x")
                return right

        elif op == TokenType.BIT_XOR:
            if isinstance(right, Number) and right.value == 0:
                self.optimizations_applied.append("Algebraic simplification: x ^ 0 = x")
                return left
            if isinstance(left, Number) and left.value == 0:
                self.optimizations_applied.append("Algebraic simplification: 0 ^ x = x")
                return right

        return None

# VM系统调用定义
VM_SYSCALLS = {
    'get_event': 0x01,      # VM_SYSCALL_GET_EVENT
    'get_io': 0x02,         # VM_SYSCALL_GET_IO
    'get_signal': 0x03,     # VM_SYSCALL_GET_SIGNAL
    'get_time': 0x04,       # VM_SYSCALL_GET_TIME
    'get_msg_time': 0x05,   # VM_SYSCALL_GET_MSG_TIME
}

# 代码生成器
class CodeGenerator:
    def __init__(self):
        self.bytecode = bytearray()
        self.variables = {}  # 变量名 -> 寄存器ID或栈位置
        self.register_vars = {}  # 寄存器变量名 -> 寄存器ID
        self.next_register = 0
        self.labels = {}  # 标签名 -> 地址
        self.pending_jumps = []  # (地址, 标签名) 待回填的跳转

    def emit_byte(self, byte: int):
        self.bytecode.append(byte & 0xFF)

    def emit_u16(self, value: int):
        self.bytecode.extend(struct.pack('<H', value & 0xFFFF))

    def emit_u32(self, value: int):
        self.bytecode.extend(struct.pack('<I', value & 0xFFFFFFFF))

    def get_current_address(self) -> int:
        return len(self.bytecode)

    def allocate_register(self, var_name: str) -> int:
        if self.next_register >= 8:
            raise RuntimeError(f"Too many register variables (max 8)")
        reg_id = self.next_register
        self.next_register += 1
        self.register_vars[var_name] = reg_id
        return reg_id

    def emit_push_value(self, value: int):
        """根据值的大小选择合适的PUSH指令"""
        if 0 <= value <= 255:
            self.emit_byte(VMOpcode.PUSH8)
            self.emit_byte(value)
        elif 0 <= value <= 65535:
            self.emit_byte(VMOpcode.PUSH16)
            self.emit_u16(value)
        else:
            self.emit_byte(VMOpcode.PUSH32)
            self.emit_u32(value)

    def generate(self, program: Program) -> bytes:
        for function in program.functions:
            self.generate_function(function)

        # 添加HALT指令
        self.emit_byte(VMOpcode.HALT)

        # 回填跳转地址
        self.resolve_jumps()

        return bytes(self.bytecode)

    def generate_function(self, func: Function):
        # 生成函数体
        self.generate_block(func.body)

    def generate_block(self, block: Block):
        for stmt in block.statements:
            self.generate_statement(stmt)

    def generate_statement(self, stmt: Statement):
        if isinstance(stmt, VarDeclaration):
            self.generate_var_declaration(stmt)
        elif isinstance(stmt, Assignment):
            self.generate_assignment(stmt)
        elif isinstance(stmt, IfStatement):
            self.generate_if_statement(stmt)
        elif isinstance(stmt, WhileStatement):
            self.generate_while_statement(stmt)
        elif isinstance(stmt, ReturnStatement):
            self.generate_return_statement(stmt)
        elif isinstance(stmt, ExpressionStatement):
            self.generate_expression(stmt.expression)
            self.emit_byte(VMOpcode.POP)  # 丢弃表达式结果
        elif isinstance(stmt, Block):
            self.generate_block(stmt)

    def generate_var_declaration(self, decl: VarDeclaration):
        if decl.is_register:
            # 寄存器变量
            reg_id = self.allocate_register(decl.name)
            if decl.initializer:
                self.generate_expression(decl.initializer)
                self.emit_byte(VMOpcode.STORE_REG)
                self.emit_byte(reg_id)
            else:
                # 初始化为0
                self.emit_push_value(0)
                self.emit_byte(VMOpcode.STORE_REG)
                self.emit_byte(reg_id)
        else:
            # 普通变量（暂时不支持，因为VM没有内存管理）
            raise NotImplementedError("Non-register variables not supported yet")

    def generate_assignment(self, assign: Assignment):
        self.generate_expression(assign.value)

        if assign.target in self.register_vars:
            # 寄存器变量
            reg_id = self.register_vars[assign.target]
            self.emit_byte(VMOpcode.STORE_REG)
            self.emit_byte(reg_id)
        else:
            raise RuntimeError(f"Unknown variable: {assign.target}")

    def generate_if_statement(self, if_stmt: IfStatement):
        # 生成条件表达式
        self.generate_expression(if_stmt.condition)

        # JZ到else分支或结束
        jz_addr = self.get_current_address()
        self.emit_byte(VMOpcode.JZ)
        self.emit_u16(0)  # 占位符，稍后回填

        # 生成then分支
        self.generate_statement(if_stmt.then_stmt)

        if if_stmt.else_stmt:
            # 跳过else分支
            jmp_addr = self.get_current_address()
            self.emit_byte(VMOpcode.JMP)
            self.emit_u16(0)  # 占位符

            # 回填JZ地址到else分支
            else_addr = self.get_current_address()
            self.patch_jump(jz_addr + 1, else_addr)

            # 生成else分支
            self.generate_statement(if_stmt.else_stmt)

            # 回填JMP地址到结束
            end_addr = self.get_current_address()
            self.patch_jump(jmp_addr + 1, end_addr)
        else:
            # 回填JZ地址到结束
            end_addr = self.get_current_address()
            self.patch_jump(jz_addr + 1, end_addr)

    def generate_while_statement(self, while_stmt: WhileStatement):
        # 循环开始地址
        loop_start = self.get_current_address()

        # 生成条件表达式
        self.generate_expression(while_stmt.condition)

        # JZ到循环结束
        jz_addr = self.get_current_address()
        self.emit_byte(VMOpcode.JZ)
        self.emit_u16(0)  # 占位符

        # 生成循环体
        self.generate_statement(while_stmt.body)

        # 跳回循环开始
        self.emit_byte(VMOpcode.JMP)
        self.emit_u16(loop_start)

        # 回填JZ地址到循环结束
        loop_end = self.get_current_address()
        self.patch_jump(jz_addr + 1, loop_end)

    def generate_return_statement(self, ret_stmt: ReturnStatement):
        if ret_stmt.value:
            self.generate_expression(ret_stmt.value)
        else:
            self.emit_push_value(0)
        self.emit_byte(VMOpcode.RET)

    def generate_expression(self, expr: Expression):
        if isinstance(expr, Number):
            self.emit_push_value(expr.value)
        elif isinstance(expr, Identifier):
            if expr.name in self.register_vars:
                reg_id = self.register_vars[expr.name]
                self.emit_byte(VMOpcode.LOAD_REG)
                self.emit_byte(reg_id)
            else:
                raise RuntimeError(f"Unknown variable: {expr.name}")
        elif isinstance(expr, BinaryOp):
            self.generate_binary_op(expr)
        elif isinstance(expr, UnaryOp):
            self.generate_unary_op(expr)
        elif isinstance(expr, FunctionCall):
            self.generate_function_call(expr)

    def generate_binary_op(self, expr: BinaryOp):
        # 生成左右操作数
        self.generate_expression(expr.left)
        self.generate_expression(expr.right)

        # 生成操作符指令
        op_map = {
            TokenType.PLUS: VMOpcode.ADD,
            TokenType.MINUS: VMOpcode.SUB,
            TokenType.MULTIPLY: VMOpcode.MUL,
            TokenType.DIVIDE: VMOpcode.DIV,
            TokenType.MODULO: VMOpcode.MOD,
            TokenType.BIT_AND: VMOpcode.AND,
            TokenType.BIT_OR: VMOpcode.OR,
            TokenType.BIT_XOR: VMOpcode.XOR,
            TokenType.LSHIFT: VMOpcode.SHL,
            TokenType.RSHIFT: VMOpcode.SHR,
            TokenType.EQ: VMOpcode.EQ,
            TokenType.NE: VMOpcode.NE,
            TokenType.LT: VMOpcode.LT,
            TokenType.LE: VMOpcode.LE,
            TokenType.GT: VMOpcode.GT,
            TokenType.GE: VMOpcode.GE,
        }

        if expr.operator in op_map:
            self.emit_byte(op_map[expr.operator])
        else:
            raise RuntimeError(f"Unsupported binary operator: {expr.operator}")

    def generate_unary_op(self, expr: UnaryOp):
        self.generate_expression(expr.operand)

        if expr.operator == TokenType.MINUS:
            # 负号：0 - operand
            self.emit_push_value(0)
            self.emit_byte(VMOpcode.SWAP)
            self.emit_byte(VMOpcode.SUB)
        elif expr.operator == TokenType.BIT_NOT:
            self.emit_byte(VMOpcode.NOT)
        else:
            raise RuntimeError(f"Unsupported unary operator: {expr.operator}")

    def generate_function_call(self, call: FunctionCall):
        """生成函数调用代码"""
        if call.name in VM_SYSCALLS:
            # VM系统调用
            syscall_id = VM_SYSCALLS[call.name]

            # 生成参数（按相反顺序压栈，因为VM从栈顶弹出）
            for arg in reversed(call.arguments):
                self.generate_expression(arg)

            # 生成CALL指令
            self.emit_byte(VMOpcode.CALL)
            self.emit_byte(syscall_id)

        else:
            raise RuntimeError(f"Unknown function: {call.name}")

    def patch_jump(self, addr: int, target: int):
        """回填跳转地址"""
        target_bytes = struct.pack('<H', target & 0xFFFF)
        self.bytecode[addr] = target_bytes[0]
        self.bytecode[addr + 1] = target_bytes[1]

    def resolve_jumps(self):
        """解析所有待回填的跳转"""
        for addr, label in self.pending_jumps:
            if label in self.labels:
                self.patch_jump(addr, self.labels[label])
            else:
                raise RuntimeError(f"Undefined label: {label}")

# 编译器主类
class Compiler:
    def __init__(self, enable_optimization=True, show_ast=False):
        self.lexer = None
        self.parser = None
        self.generator = None
        self.optimizer = ASTOptimizer() if enable_optimization else None
        self.visualizer = ASTVisualizer()
        self.enable_optimization = enable_optimization
        self.show_ast = show_ast
        self.ast = None
        self.optimized_ast = None

    def compile(self, source: str) -> bytes:
        """编译C代码到VM字节码"""
        # 词法分析
        self.lexer = Lexer(source)
        tokens = self.lexer.tokenize()

        # 语法分析
        self.parser = Parser(tokens)
        self.ast = self.parser.parse()

        if self.show_ast:
            print("=== 原始AST ===")
            print(self.visualizer.visit(self.ast))
            print()

        # AST优化
        if self.enable_optimization:
            self.optimized_ast = self.optimizer.optimize(self.ast)

            if self.show_ast:
                print("=== 优化后AST ===")
                print(self.visualizer.visit(self.optimized_ast))
                print()

            if self.optimizer.optimizations_applied:
                print("=== 应用的优化 ===")
                for opt in self.optimizer.optimizations_applied:
                    print(f"  • {opt}")
                print()

            ast_to_use = self.optimized_ast
        else:
            ast_to_use = self.ast

        # 代码生成
        self.generator = CodeGenerator()
        bytecode = self.generator.generate(ast_to_use)

        return bytecode

    def compile_file(self, filename: str) -> bytes:
        """编译C文件到VM字节码"""
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        return self.compile(source)

    def save_bytecode(self, bytecode: bytes, filename: str):
        """保存字节码到文件"""
        with open(filename, 'wb') as f:
            f.write(bytecode)

    def print_bytecode_hex(self, bytecode: bytes):
        """以十六进制格式打印字节码"""
        print("Generated bytecode:")
        for i in range(0, len(bytecode), 16):
            chunk = bytecode[i:i+16]
            hex_str = ' '.join(f'{b:02X}' for b in chunk)
            print(f"{i:04X}: {hex_str}")

    def get_ast_string(self) -> str:
        """获取AST的字符串表示"""
        if self.ast:
            return self.visualizer.visit(self.ast)
        return "No AST available"

    def get_optimized_ast_string(self) -> str:
        """获取优化后AST的字符串表示"""
        if self.optimized_ast:
            return self.visualizer.visit(self.optimized_ast)
        return "No optimized AST available"

    def get_optimization_report(self) -> List[str]:
        """获取优化报告"""
        if self.optimizer:
            return self.optimizer.optimizations_applied.copy()
        return []

# 示例和测试
def main():
    # 示例C代码 - 包含优化机会
    c_code = """
    int main() {
        register int a = 10;
        register int b = 20;
        register int result = a + b * 2;
        register int optimizable = 5 + 3 * 2;  // 常量折叠
        register int zero_mult = optimizable * 0;  // 代数简化

        if (1) {  // 常量条件
            result = result - 10;
        }

        if (0) {  // 死代码
            result = 999;
        }

        // VM系统调用示例
        register int current_time = get_time();
        register int event_value = get_event(1);

        return result + current_time + event_value;
    }
    """

    try:
        print("🚀 C语言编译器 - AST可视化和优化演示")
        print("=" * 60)

        # 启用AST显示和优化
        compiler = Compiler(enable_optimization=True, show_ast=True)
        bytecode = compiler.compile(c_code)

        print("=== 编译结果 ===")
        print("Compilation successful!")
        compiler.print_bytecode_hex(bytecode)

        # 保存到文件
        compiler.save_bytecode(bytecode, "optimized_program.bin")
        print(f"\nBytecode saved to optimized_program.bin ({len(bytecode)} bytes)")

        # 对比未优化版本
        print("\n" + "=" * 60)
        print("🔍 优化对比")

        compiler_no_opt = Compiler(enable_optimization=False, show_ast=False)
        bytecode_no_opt = compiler_no_opt.compile(c_code)

        print(f"未优化字节码长度: {len(bytecode_no_opt)} bytes")
        print(f"优化后字节码长度: {len(bytecode)} bytes")
        print(f"节省空间: {len(bytecode_no_opt) - len(bytecode)} bytes ({((len(bytecode_no_opt) - len(bytecode)) / len(bytecode_no_opt) * 100):.1f}%)")

    except Exception as e:
        print(f"Compilation error: {e}")
        import traceback
        traceback.print_exc()

def test_syscalls():
    """测试VM系统调用"""
    print("\n🔧 VM系统调用测试")
    print("=" * 30)

    c_code = """
    int main() {
        register int time = get_time();
        register int event = get_event(42);
        register int io_level = get_io(5);
        register int signal = get_signal(1, 2);
        register int msg_time = get_msg_time(0);

        return time + event + io_level + signal + msg_time;
    }
    """

    try:
        compiler = Compiler(enable_optimization=True, show_ast=True)
        bytecode = compiler.compile(c_code)

        print("VM系统调用编译成功!")
        compiler.print_bytecode_hex(bytecode)

    except Exception as e:
        print(f"编译错误: {e}")

if __name__ == "__main__":
    main()
    test_syscalls()
