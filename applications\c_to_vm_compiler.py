#!/usr/bin/env python3
"""
C语言子集到VM字节码编译器

支持的C语言特性：
- 基本数据类型：int (32位)
- 变量声明和赋值
- 算术运算：+, -, *, /, %
- 位运算：&, |, ^, ~, <<, >>
- 比较运算：==, !=, <, <=, >, >=
- 控制流：if-else, while
- 表达式和语句
- 寄存器变量（使用register关键字）

示例代码：
```c
int main() {
    register int a = 10;
    register int b = 20;
    int result = a + b * 2;
    if (result > 40) {
        result = result - 10;
    }
    return result;
}
```
"""

import re
import struct
from typing import List, Dict, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass

# VM操作码定义
class VMOpcode:
    NOP = 0x00
    PUSH8 = 0x01
    PUSH16 = 0x02
    PUSH32 = 0x03
    POP = 0x04
    DUP = 0x05
    SWAP = 0x06
    ADD = 0x10
    SUB = 0x11
    MUL = 0x12
    DIV = 0x13
    MOD = 0x14
    AND = 0x15
    OR = 0x16
    XOR = 0x17
    NOT = 0x18
    SHL = 0x19
    SHR = 0x1A
    EQ = 0x20
    NE = 0x21
    LT = 0x22
    LE = 0x23
    GT = 0x24
    GE = 0x25
    JMP = 0x30
    JZ = 0x31
    JNZ = 0x32
    CALL = 0x40
    RET = 0x41
    STORE_REG = 0x42
    LOAD_REG = 0x43
    HALT = 0xFF

# 词法分析器
class TokenType(Enum):
    # 字面量
    NUMBER = "NUMBER"
    IDENTIFIER = "IDENTIFIER"
    
    # 关键字
    INT = "int"
    REGISTER = "register"
    IF = "if"
    ELSE = "else"
    WHILE = "while"
    RETURN = "return"
    
    # 运算符
    PLUS = "+"
    MINUS = "-"
    MULTIPLY = "*"
    DIVIDE = "/"
    MODULO = "%"
    
    # 位运算
    BIT_AND = "&"
    BIT_OR = "|"
    BIT_XOR = "^"
    BIT_NOT = "~"
    LSHIFT = "<<"
    RSHIFT = ">>"
    
    # 比较运算
    EQ = "=="
    NE = "!="
    LT = "<"
    LE = "<="
    GT = ">"
    GE = ">="
    
    # 赋值
    ASSIGN = "="
    
    # 分隔符
    SEMICOLON = ";"
    COMMA = ","
    LPAREN = "("
    RPAREN = ")"
    LBRACE = "{"
    RBRACE = "}"
    
    # 特殊
    EOF = "EOF"

@dataclass
class Token:
    type: TokenType
    value: str
    line: int
    column: int

class Lexer:
    def __init__(self, source: str):
        self.source = source
        self.pos = 0
        self.line = 1
        self.column = 1
        self.tokens = []
        
        # 关键字映射
        self.keywords = {
            'int': TokenType.INT,
            'register': TokenType.REGISTER,
            'if': TokenType.IF,
            'else': TokenType.ELSE,
            'while': TokenType.WHILE,
            'return': TokenType.RETURN,
        }
        
        # 双字符运算符
        self.double_ops = {
            '<<': TokenType.LSHIFT,
            '>>': TokenType.RSHIFT,
            '==': TokenType.EQ,
            '!=': TokenType.NE,
            '<=': TokenType.LE,
            '>=': TokenType.GE,
        }
        
        # 单字符运算符
        self.single_ops = {
            '+': TokenType.PLUS,
            '-': TokenType.MINUS,
            '*': TokenType.MULTIPLY,
            '/': TokenType.DIVIDE,
            '%': TokenType.MODULO,
            '&': TokenType.BIT_AND,
            '|': TokenType.BIT_OR,
            '^': TokenType.BIT_XOR,
            '~': TokenType.BIT_NOT,
            '<': TokenType.LT,
            '>': TokenType.GT,
            '=': TokenType.ASSIGN,
            ';': TokenType.SEMICOLON,
            ',': TokenType.COMMA,
            '(': TokenType.LPAREN,
            ')': TokenType.RPAREN,
            '{': TokenType.LBRACE,
            '}': TokenType.RBRACE,
        }
    
    def current_char(self) -> Optional[str]:
        if self.pos >= len(self.source):
            return None
        return self.source[self.pos]
    
    def peek_char(self, offset: int = 1) -> Optional[str]:
        peek_pos = self.pos + offset
        if peek_pos >= len(self.source):
            return None
        return self.source[peek_pos]
    
    def advance(self):
        if self.pos < len(self.source) and self.source[self.pos] == '\n':
            self.line += 1
            self.column = 1
        else:
            self.column += 1
        self.pos += 1
    
    def skip_whitespace(self):
        while self.current_char() and self.current_char().isspace():
            self.advance()
    
    def read_number(self) -> str:
        start_pos = self.pos

        # 检查是否是十六进制数字 (0x...)
        if self.current_char() == '0' and self.peek_char() and self.peek_char().lower() == 'x':
            self.advance()  # skip '0'
            self.advance()  # skip 'x'
            # 读取十六进制数字
            while self.current_char() and self.current_char().lower() in '0123456789abcdef':
                self.advance()
        else:
            # 读取十进制数字
            while self.current_char() and self.current_char().isdigit():
                self.advance()

        return self.source[start_pos:self.pos]
    
    def read_identifier(self) -> str:
        start_pos = self.pos
        while self.current_char() and (self.current_char().isalnum() or self.current_char() == '_'):
            self.advance()
        return self.source[start_pos:self.pos]
    
    def tokenize(self) -> List[Token]:
        while self.current_char():
            self.skip_whitespace()
            
            if not self.current_char():
                break
            
            line, column = self.line, self.column
            
            # 数字
            if self.current_char().isdigit():
                value = self.read_number()
                self.tokens.append(Token(TokenType.NUMBER, value, line, column))
                continue
            
            # 标识符和关键字
            if self.current_char().isalpha() or self.current_char() == '_':
                value = self.read_identifier()
                token_type = self.keywords.get(value, TokenType.IDENTIFIER)
                self.tokens.append(Token(token_type, value, line, column))
                continue
            
            # 双字符运算符
            if self.peek_char():
                double_op = self.current_char() + self.peek_char()
                if double_op in self.double_ops:
                    self.advance()
                    self.advance()
                    self.tokens.append(Token(self.double_ops[double_op], double_op, line, column))
                    continue
            
            # 单字符运算符
            if self.current_char() in self.single_ops:
                char = self.current_char()
                self.advance()
                self.tokens.append(Token(self.single_ops[char], char, line, column))
                continue
            
            # 未知字符
            raise SyntaxError(f"Unexpected character '{self.current_char()}' at line {self.line}, column {self.column}")
        
        self.tokens.append(Token(TokenType.EOF, "", self.line, self.column))
        return self.tokens

# AST节点定义
class ASTNode:
    pass

@dataclass
class Program(ASTNode):
    functions: List['Function']

@dataclass
class Function(ASTNode):
    name: str
    return_type: str
    parameters: List['Parameter']
    body: 'Block'

@dataclass
class Parameter(ASTNode):
    type: str
    name: str

@dataclass
class Block(ASTNode):
    statements: List['Statement']

class Statement(ASTNode):
    pass

@dataclass
class VarDeclaration(Statement):
    type: str
    name: str
    is_register: bool = False
    initializer: Optional['Expression'] = None

@dataclass
class Assignment(Statement):
    target: str
    value: 'Expression'

@dataclass
class IfStatement(Statement):
    condition: 'Expression'
    then_stmt: Statement
    else_stmt: Optional[Statement] = None

@dataclass
class WhileStatement(Statement):
    condition: 'Expression'
    body: Statement

@dataclass
class ReturnStatement(Statement):
    value: Optional['Expression'] = None

@dataclass
class ExpressionStatement(Statement):
    expression: 'Expression'

class Expression(ASTNode):
    pass

@dataclass
class BinaryOp(Expression):
    left: Expression
    operator: TokenType
    right: Expression

@dataclass
class UnaryOp(Expression):
    operator: TokenType
    operand: Expression

@dataclass
class Number(Expression):
    value: int

@dataclass
class Identifier(Expression):
    name: str

# 语法分析器
class Parser:
    def __init__(self, tokens: List[Token]):
        self.tokens = tokens
        self.pos = 0

    def current_token(self) -> Token:
        if self.pos >= len(self.tokens):
            return self.tokens[-1]  # EOF token
        return self.tokens[self.pos]

    def peek_token(self, offset: int = 1) -> Token:
        peek_pos = self.pos + offset
        if peek_pos >= len(self.tokens):
            return self.tokens[-1]  # EOF token
        return self.tokens[peek_pos]

    def advance(self):
        if self.pos < len(self.tokens) - 1:
            self.pos += 1

    def expect(self, token_type: TokenType) -> Token:
        token = self.current_token()
        if token.type != token_type:
            raise SyntaxError(f"Expected {token_type}, got {token.type} at line {token.line}")
        self.advance()
        return token

    def match(self, *token_types: TokenType) -> bool:
        return self.current_token().type in token_types

    def parse(self) -> Program:
        functions = []
        while not self.match(TokenType.EOF):
            functions.append(self.parse_function())
        return Program(functions)

    def parse_function(self) -> Function:
        # int function_name() { ... }
        return_type = self.expect(TokenType.INT).value
        name = self.expect(TokenType.IDENTIFIER).value
        self.expect(TokenType.LPAREN)

        # 暂时不支持参数
        parameters = []

        self.expect(TokenType.RPAREN)
        body = self.parse_block()

        return Function(name, return_type, parameters, body)

    def parse_block(self) -> Block:
        self.expect(TokenType.LBRACE)
        statements = []

        while not self.match(TokenType.RBRACE, TokenType.EOF):
            statements.append(self.parse_statement())

        self.expect(TokenType.RBRACE)
        return Block(statements)

    def parse_statement(self) -> Statement:
        if self.match(TokenType.INT, TokenType.REGISTER):
            return self.parse_var_declaration()
        elif self.match(TokenType.IF):
            return self.parse_if_statement()
        elif self.match(TokenType.WHILE):
            return self.parse_while_statement()
        elif self.match(TokenType.RETURN):
            return self.parse_return_statement()
        elif self.match(TokenType.LBRACE):
            return self.parse_block()
        else:
            # 可能是赋值或表达式语句
            if self.current_token().type == TokenType.IDENTIFIER and self.peek_token().type == TokenType.ASSIGN:
                return self.parse_assignment()
            else:
                return self.parse_expression_statement()

    def parse_var_declaration(self) -> VarDeclaration:
        is_register = False
        if self.match(TokenType.REGISTER):
            is_register = True
            self.advance()

        var_type = self.expect(TokenType.INT).value
        name = self.expect(TokenType.IDENTIFIER).value

        initializer = None
        if self.match(TokenType.ASSIGN):
            self.advance()
            initializer = self.parse_expression()

        self.expect(TokenType.SEMICOLON)
        return VarDeclaration(var_type, name, is_register, initializer)

    def parse_assignment(self) -> Assignment:
        target = self.expect(TokenType.IDENTIFIER).value
        self.expect(TokenType.ASSIGN)
        value = self.parse_expression()
        self.expect(TokenType.SEMICOLON)
        return Assignment(target, value)

    def parse_if_statement(self) -> IfStatement:
        self.expect(TokenType.IF)
        self.expect(TokenType.LPAREN)
        condition = self.parse_expression()
        self.expect(TokenType.RPAREN)
        then_stmt = self.parse_statement()

        else_stmt = None
        if self.match(TokenType.ELSE):
            self.advance()
            else_stmt = self.parse_statement()

        return IfStatement(condition, then_stmt, else_stmt)

    def parse_while_statement(self) -> WhileStatement:
        self.expect(TokenType.WHILE)
        self.expect(TokenType.LPAREN)
        condition = self.parse_expression()
        self.expect(TokenType.RPAREN)
        body = self.parse_statement()
        return WhileStatement(condition, body)

    def parse_return_statement(self) -> ReturnStatement:
        self.expect(TokenType.RETURN)
        value = None
        if not self.match(TokenType.SEMICOLON):
            value = self.parse_expression()
        self.expect(TokenType.SEMICOLON)
        return ReturnStatement(value)

    def parse_expression_statement(self) -> ExpressionStatement:
        expr = self.parse_expression()
        self.expect(TokenType.SEMICOLON)
        return ExpressionStatement(expr)

    # 表达式解析 - 运算符优先级递归下降
    def parse_expression(self) -> Expression:
        return self.parse_logical_or()

    def parse_logical_or(self) -> Expression:
        expr = self.parse_logical_xor()
        while self.match(TokenType.BIT_OR):
            op = self.current_token().type
            self.advance()
            right = self.parse_logical_xor()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_logical_xor(self) -> Expression:
        expr = self.parse_logical_and()
        while self.match(TokenType.BIT_XOR):
            op = self.current_token().type
            self.advance()
            right = self.parse_logical_and()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_logical_and(self) -> Expression:
        expr = self.parse_equality()
        while self.match(TokenType.BIT_AND):
            op = self.current_token().type
            self.advance()
            right = self.parse_equality()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_equality(self) -> Expression:
        expr = self.parse_relational()
        while self.match(TokenType.EQ, TokenType.NE):
            op = self.current_token().type
            self.advance()
            right = self.parse_relational()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_relational(self) -> Expression:
        expr = self.parse_shift()
        while self.match(TokenType.LT, TokenType.LE, TokenType.GT, TokenType.GE):
            op = self.current_token().type
            self.advance()
            right = self.parse_shift()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_shift(self) -> Expression:
        expr = self.parse_additive()
        while self.match(TokenType.LSHIFT, TokenType.RSHIFT):
            op = self.current_token().type
            self.advance()
            right = self.parse_additive()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_additive(self) -> Expression:
        expr = self.parse_multiplicative()
        while self.match(TokenType.PLUS, TokenType.MINUS):
            op = self.current_token().type
            self.advance()
            right = self.parse_multiplicative()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_multiplicative(self) -> Expression:
        expr = self.parse_unary()
        while self.match(TokenType.MULTIPLY, TokenType.DIVIDE, TokenType.MODULO):
            op = self.current_token().type
            self.advance()
            right = self.parse_unary()
            expr = BinaryOp(expr, op, right)
        return expr

    def parse_unary(self) -> Expression:
        if self.match(TokenType.MINUS, TokenType.BIT_NOT):
            op = self.current_token().type
            self.advance()
            operand = self.parse_unary()
            return UnaryOp(op, operand)
        return self.parse_primary()

    def parse_primary(self) -> Expression:
        if self.match(TokenType.NUMBER):
            value = int(self.current_token().value, 0)  # 支持十六进制
            self.advance()
            return Number(value)

        if self.match(TokenType.IDENTIFIER):
            name = self.current_token().value
            self.advance()
            return Identifier(name)

        if self.match(TokenType.LPAREN):
            self.advance()
            expr = self.parse_expression()
            self.expect(TokenType.RPAREN)
            return expr

        token = self.current_token()
        raise SyntaxError(f"Unexpected token {token.type} at line {token.line}")

# 代码生成器
class CodeGenerator:
    def __init__(self):
        self.bytecode = bytearray()
        self.variables = {}  # 变量名 -> 寄存器ID或栈位置
        self.register_vars = {}  # 寄存器变量名 -> 寄存器ID
        self.next_register = 0
        self.labels = {}  # 标签名 -> 地址
        self.pending_jumps = []  # (地址, 标签名) 待回填的跳转

    def emit_byte(self, byte: int):
        self.bytecode.append(byte & 0xFF)

    def emit_u16(self, value: int):
        self.bytecode.extend(struct.pack('<H', value & 0xFFFF))

    def emit_u32(self, value: int):
        self.bytecode.extend(struct.pack('<I', value & 0xFFFFFFFF))

    def get_current_address(self) -> int:
        return len(self.bytecode)

    def allocate_register(self, var_name: str) -> int:
        if self.next_register >= 8:
            raise RuntimeError(f"Too many register variables (max 8)")
        reg_id = self.next_register
        self.next_register += 1
        self.register_vars[var_name] = reg_id
        return reg_id

    def emit_push_value(self, value: int):
        """根据值的大小选择合适的PUSH指令"""
        if 0 <= value <= 255:
            self.emit_byte(VMOpcode.PUSH8)
            self.emit_byte(value)
        elif 0 <= value <= 65535:
            self.emit_byte(VMOpcode.PUSH16)
            self.emit_u16(value)
        else:
            self.emit_byte(VMOpcode.PUSH32)
            self.emit_u32(value)

    def generate(self, program: Program) -> bytes:
        for function in program.functions:
            self.generate_function(function)

        # 添加HALT指令
        self.emit_byte(VMOpcode.HALT)

        # 回填跳转地址
        self.resolve_jumps()

        return bytes(self.bytecode)

    def generate_function(self, func: Function):
        # 生成函数体
        self.generate_block(func.body)

    def generate_block(self, block: Block):
        for stmt in block.statements:
            self.generate_statement(stmt)

    def generate_statement(self, stmt: Statement):
        if isinstance(stmt, VarDeclaration):
            self.generate_var_declaration(stmt)
        elif isinstance(stmt, Assignment):
            self.generate_assignment(stmt)
        elif isinstance(stmt, IfStatement):
            self.generate_if_statement(stmt)
        elif isinstance(stmt, WhileStatement):
            self.generate_while_statement(stmt)
        elif isinstance(stmt, ReturnStatement):
            self.generate_return_statement(stmt)
        elif isinstance(stmt, ExpressionStatement):
            self.generate_expression(stmt.expression)
            self.emit_byte(VMOpcode.POP)  # 丢弃表达式结果
        elif isinstance(stmt, Block):
            self.generate_block(stmt)

    def generate_var_declaration(self, decl: VarDeclaration):
        if decl.is_register:
            # 寄存器变量
            reg_id = self.allocate_register(decl.name)
            if decl.initializer:
                self.generate_expression(decl.initializer)
                self.emit_byte(VMOpcode.STORE_REG)
                self.emit_byte(reg_id)
            else:
                # 初始化为0
                self.emit_push_value(0)
                self.emit_byte(VMOpcode.STORE_REG)
                self.emit_byte(reg_id)
        else:
            # 普通变量（暂时不支持，因为VM没有内存管理）
            raise NotImplementedError("Non-register variables not supported yet")

    def generate_assignment(self, assign: Assignment):
        self.generate_expression(assign.value)

        if assign.target in self.register_vars:
            # 寄存器变量
            reg_id = self.register_vars[assign.target]
            self.emit_byte(VMOpcode.STORE_REG)
            self.emit_byte(reg_id)
        else:
            raise RuntimeError(f"Unknown variable: {assign.target}")

    def generate_if_statement(self, if_stmt: IfStatement):
        # 生成条件表达式
        self.generate_expression(if_stmt.condition)

        # JZ到else分支或结束
        jz_addr = self.get_current_address()
        self.emit_byte(VMOpcode.JZ)
        self.emit_u16(0)  # 占位符，稍后回填

        # 生成then分支
        self.generate_statement(if_stmt.then_stmt)

        if if_stmt.else_stmt:
            # 跳过else分支
            jmp_addr = self.get_current_address()
            self.emit_byte(VMOpcode.JMP)
            self.emit_u16(0)  # 占位符

            # 回填JZ地址到else分支
            else_addr = self.get_current_address()
            self.patch_jump(jz_addr + 1, else_addr)

            # 生成else分支
            self.generate_statement(if_stmt.else_stmt)

            # 回填JMP地址到结束
            end_addr = self.get_current_address()
            self.patch_jump(jmp_addr + 1, end_addr)
        else:
            # 回填JZ地址到结束
            end_addr = self.get_current_address()
            self.patch_jump(jz_addr + 1, end_addr)

    def generate_while_statement(self, while_stmt: WhileStatement):
        # 循环开始地址
        loop_start = self.get_current_address()

        # 生成条件表达式
        self.generate_expression(while_stmt.condition)

        # JZ到循环结束
        jz_addr = self.get_current_address()
        self.emit_byte(VMOpcode.JZ)
        self.emit_u16(0)  # 占位符

        # 生成循环体
        self.generate_statement(while_stmt.body)

        # 跳回循环开始
        self.emit_byte(VMOpcode.JMP)
        self.emit_u16(loop_start)

        # 回填JZ地址到循环结束
        loop_end = self.get_current_address()
        self.patch_jump(jz_addr + 1, loop_end)

    def generate_return_statement(self, ret_stmt: ReturnStatement):
        if ret_stmt.value:
            self.generate_expression(ret_stmt.value)
        else:
            self.emit_push_value(0)
        self.emit_byte(VMOpcode.RET)

    def generate_expression(self, expr: Expression):
        if isinstance(expr, Number):
            self.emit_push_value(expr.value)
        elif isinstance(expr, Identifier):
            if expr.name in self.register_vars:
                reg_id = self.register_vars[expr.name]
                self.emit_byte(VMOpcode.LOAD_REG)
                self.emit_byte(reg_id)
            else:
                raise RuntimeError(f"Unknown variable: {expr.name}")
        elif isinstance(expr, BinaryOp):
            self.generate_binary_op(expr)
        elif isinstance(expr, UnaryOp):
            self.generate_unary_op(expr)

    def generate_binary_op(self, expr: BinaryOp):
        # 生成左右操作数
        self.generate_expression(expr.left)
        self.generate_expression(expr.right)

        # 生成操作符指令
        op_map = {
            TokenType.PLUS: VMOpcode.ADD,
            TokenType.MINUS: VMOpcode.SUB,
            TokenType.MULTIPLY: VMOpcode.MUL,
            TokenType.DIVIDE: VMOpcode.DIV,
            TokenType.MODULO: VMOpcode.MOD,
            TokenType.BIT_AND: VMOpcode.AND,
            TokenType.BIT_OR: VMOpcode.OR,
            TokenType.BIT_XOR: VMOpcode.XOR,
            TokenType.LSHIFT: VMOpcode.SHL,
            TokenType.RSHIFT: VMOpcode.SHR,
            TokenType.EQ: VMOpcode.EQ,
            TokenType.NE: VMOpcode.NE,
            TokenType.LT: VMOpcode.LT,
            TokenType.LE: VMOpcode.LE,
            TokenType.GT: VMOpcode.GT,
            TokenType.GE: VMOpcode.GE,
        }

        if expr.operator in op_map:
            self.emit_byte(op_map[expr.operator])
        else:
            raise RuntimeError(f"Unsupported binary operator: {expr.operator}")

    def generate_unary_op(self, expr: UnaryOp):
        self.generate_expression(expr.operand)

        if expr.operator == TokenType.MINUS:
            # 负号：0 - operand
            self.emit_push_value(0)
            self.emit_byte(VMOpcode.SWAP)
            self.emit_byte(VMOpcode.SUB)
        elif expr.operator == TokenType.BIT_NOT:
            self.emit_byte(VMOpcode.NOT)
        else:
            raise RuntimeError(f"Unsupported unary operator: {expr.operator}")

    def patch_jump(self, addr: int, target: int):
        """回填跳转地址"""
        target_bytes = struct.pack('<H', target & 0xFFFF)
        self.bytecode[addr] = target_bytes[0]
        self.bytecode[addr + 1] = target_bytes[1]

    def resolve_jumps(self):
        """解析所有待回填的跳转"""
        for addr, label in self.pending_jumps:
            if label in self.labels:
                self.patch_jump(addr, self.labels[label])
            else:
                raise RuntimeError(f"Undefined label: {label}")

# 编译器主类
class Compiler:
    def __init__(self):
        self.lexer = None
        self.parser = None
        self.generator = None

    def compile(self, source: str) -> bytes:
        """编译C代码到VM字节码"""
        # 词法分析
        self.lexer = Lexer(source)
        tokens = self.lexer.tokenize()

        # 语法分析
        self.parser = Parser(tokens)
        ast = self.parser.parse()

        # 代码生成
        self.generator = CodeGenerator()
        bytecode = self.generator.generate(ast)

        return bytecode

    def compile_file(self, filename: str) -> bytes:
        """编译C文件到VM字节码"""
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        return self.compile(source)

    def save_bytecode(self, bytecode: bytes, filename: str):
        """保存字节码到文件"""
        with open(filename, 'wb') as f:
            f.write(bytecode)

    def print_bytecode_hex(self, bytecode: bytes):
        """以十六进制格式打印字节码"""
        print("Generated bytecode:")
        for i in range(0, len(bytecode), 16):
            chunk = bytecode[i:i+16]
            hex_str = ' '.join(f'{b:02X}' for b in chunk)
            print(f"{i:04X}: {hex_str}")

# 示例和测试
def main():
    # 示例C代码
    c_code = """
    int main() {
        register int a = 10;
        register int b = 20;
        register int result = a + b * 2;
        if (result > 40) {
            result = result - 10;
        }
        return result;
    }
    """

    try:
        compiler = Compiler()
        bytecode = compiler.compile(c_code)

        print("Compilation successful!")
        compiler.print_bytecode_hex(bytecode)

        # 保存到文件
        compiler.save_bytecode(bytecode, "test_program.bin")
        print("\nBytecode saved to test_program.bin")

    except Exception as e:
        print(f"Compilation error: {e}")

if __name__ == "__main__":
    main()
