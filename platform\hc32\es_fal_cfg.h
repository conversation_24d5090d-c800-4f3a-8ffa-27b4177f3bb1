﻿/**
 * @file es_fal_cfg.h
 * @brief ES Flash抽象层配置文件
 */
#ifndef ES_FAL_CFG_H
#define ES_FAL_CFG_H

#include "es_flash.h"

#ifdef __cplusplus
extern "C" {
#endif

extern const es_flash_dev_t hc32_flash_dev;
extern const es_flash_dev_t w25qxx_flash;

#define ES_FAL_FLASH_DEV_TABLE { &hc32_flash_dev, &w25qxx_flash }

#define ES_FAL_PARTITION_TABLE  \
{ \
    { "store", &w25qxx_flash, 0, 3* 8 * 1024 }, \
    { "download", &w25qxx_flash, 3* 8 * 1024, 1024 * 1024 }, \
    { "log", &w25qxx_flash, 3* 8 * 1024 + 1024 * 1024, 320 * 1024 }, \
    { "blind", &w25qxx_flash, 3* 8 * 1024 + 1024 * 1024 + 320 * 1024, 512 * 1024 }, \
}

#ifdef __cplusplus
}
#endif

#endif /* ES_FAL_CFG_H */
