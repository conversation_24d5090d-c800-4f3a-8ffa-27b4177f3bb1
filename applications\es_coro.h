/**
 * @file es_coro.h
 * @brief Stackless coroutine implementation
 *
 * Stackless coroutine implementation based on async.h, using <PERSON>'s Device technique,
 * providing lightweight asynchronous programming support for embedded systems.
 *
 * <AUTHOR> @date
 * @version 1.0
 */

#ifndef __ES_CORO_H__
#define __ES_CORO_H__


#include <stdint.h>
#include <stdbool.h>
#include "es_mem.h"
#include "es_drv_os.h"


#ifdef __cplusplus
extern "C" {
#endif


/**
 * @brief Coroutine return value enumeration
 */
typedef enum {
    ES_ASYNC_DONE = 0,   /**< Coroutine completed */
    ES_ASYNC_YIELD,      /**< Coroutine yielded CPU */
    ES_ASYNC_WAIT,       /**< Coroutine waiting for condition */
    ES_ASYNC_ERROR,      /**< Coroutine error */
} es_async_t;

/**
 * @brief Invalid coroutine ID value
 */
#define ES_CORO_INVALID_ID ((1 << 10)-1)

/**
 * @brief Coroutine type enumeration
 */
typedef enum {
    ES_CORO_TYPE_BASIC = 0,   /**< Basic coroutine type */
    ES_CORO_TYPE_TIMEOUT,     /**< Coroutine type with timeout */
} es_coro_type_t;

/**
 * @brief Maximum number of child coroutines
 */
#define ES_CORO_MAX_CHILDREN 64

/**
 * @brief Coroutine context structure - using bit fields to further optimize size
 */
typedef struct {
    uint16_t line;      /**< Current execution line number, 0 indicates initial or completed state */
    uint16_t enabled:1;   /**< Whether enabled, for scheduler use */
    uint16_t child_id:10;   /**< Child coroutine ID */
    uint16_t cnt:5;   /**< 5-bit counter, for other purposes */
} es_coro_t;

/**
 * @brief Coroutine context with timeout
 */
typedef struct {
    es_coro_t coro;         /**< Base coroutine context */
    uint32_t start_time; /**< Start time */
} es_coro_timeout_t;


/**
 * @brief Allocate a child coroutine
 * @param parent Parent coroutine
 * @return Child coroutine ID, 0 indicates allocation failure
 */
uint16_t es_coro_alloc(es_coro_t *parent);

/**
 * @brief Free a child coroutine
 * @param parent Parent coroutine
 */
void es_coro_free(es_coro_t *parent);

/**
 * @brief Get coroutine pointer by parent
 * @param parent Parent coroutine
 * @return Coroutine pointer, NULL indicates invalid ID
 */
es_coro_t* es_coro_get(es_coro_t *parent);

/**
 * @brief Check if coroutine has timed out
 * @param coro Coroutine context with timeout
 * @param timeout Timeout duration
 * @return Returns true if timed out, false otherwise
 */
bool es_coro_timeout_check(es_coro_timeout_t *coro, uint32_t timeout);

/**
 * @brief Initialize coroutine
 * @param coro Coroutine context
 */
void es_coro_init(es_coro_t *coro);

/**
 * @brief Check if coroutine is completed
 * @param coro Coroutine context
 * @return Returns true if coroutine is completed, false otherwise
 */
bool es_coro_is_done(es_coro_t *coro);

/**
 * @brief Initialize coroutine with timeout
 * @param coro Coroutine context with timeout
 */
void es_coro_timeout_init(es_coro_timeout_t *coro);

/**
 * @brief Begin coroutine definition
 * @param coro Coroutine context pointer
 */
#define es_co_begin(coro) \
    es_coro_t *const __coro = (coro); \
    es_async_t __err = ES_ASYNC_DONE; \
    (void)__err; \
    switch((__coro)->line) { default:

#define es_co_err  do{ (__coro)->line = 0;  return ES_ASYNC_ERROR; }while(0)

/**
 * @brief End coroutine definition
 */
#define es_co_end  (__coro)->line = 0;  return ES_ASYNC_DONE; }

/**
 * @brief Exit coroutine
 */
#define es_co_exit  do{ (__coro)->line = 0; return ES_ASYNC_DONE; }while(0)

/**
 * @brief Combine coroutine exit and error handling, simplify operations
 */
#define es_co_eee(...) es_co_exit; err: do{__VA_ARGS__; es_co_err;}while(0); es_co_end

/**
 * @brief Yield CPU control
 */
#define es_co_yield do{(__coro)->line = __LINE__;return ES_ASYNC_YIELD;case __LINE__: ;} while(0)

/**
 * @brief Wait for condition to be satisfied
 * @param condition Condition to wait for
 */
#define es_co_wait(condition) do{(__coro)->line = __LINE__; case __LINE__: if(!(condition)) return ES_ASYNC_WAIT;} while(0)

/**
 * @brief Wait for condition to be satisfied (inverse)
 * @param condition Condition to wait for
 */
#define es_co_wait_while(condition) es_co_wait(!(condition))

/**
 * @brief Execute child coroutine using dynamically allocated coroutine context
 * @param child_func Child coroutine function
 * @param ... Other parameters for child coroutine function
 */
#define es_co_await(child_func, ...) \
    do { \
        if(es_coro_alloc((__coro)) == ES_CORO_INVALID_ID) { \
            __err = ES_ASYNC_ERROR; \
        } \
        else { \
            (__coro)->line = __LINE__; \
            case __LINE__: { \
                __err = child_func(es_coro_get((__coro)), ##__VA_ARGS__); \
                if (__err == ES_ASYNC_YIELD || __err == ES_ASYNC_WAIT) { \
                    return __err; \
                } \
                es_coro_free((__coro)); \
            } \
        } \
    } while(0)


    
/**
 * @brief Execute child coroutine using dynamically allocated coroutine context (with error label)
 * @param label Error jump label
 * @param child_func Child coroutine function
 * @param ... Other parameters for child coroutine function
 */
#define es_co_await_ex(label, child_func, ...) \
    do {es_co_await(child_func, ##__VA_ARGS__); if(__err != ES_ASYNC_DONE) { goto label; } }while(0)

/**
 * @brief Wait until timeout or condition is satisfied
 * @param condition Condition to wait for
 * @param timeout Timeout duration
 */
#define es_co_wait_timeout(condition, timeout) \
    do { \
        (__coro)->line = __LINE__; \
        ((es_coro_timeout_t *)__coro)->start_time = es_os_get_tick_ms(); \
        case __LINE__: { \
            if(!(condition)) { \
                if(es_coro_timeout_check((es_coro_timeout_t *)__coro, timeout)) { \
                    break; \
                } \
                return ES_ASYNC_WAIT; \
            } \
        } \
    } while(0)


/**
 * @brief Wait until timeout or condition is satisfied with error label
 * @param label Timeout jump label
 * @param condition Condition to wait for
 * @param timeout Timeout duration
 */
#define es_co_wait_timeout_ex(label, condition, timeout) \
    do { \
        (__coro)->line = __LINE__; \
        ((es_coro_timeout_t *)__coro)->start_time = es_os_get_tick_ms(); \
        case __LINE__: { \
            if(!(condition)) { \
                if(es_coro_timeout_check((es_coro_timeout_t *)__coro, timeout)) { \
                    goto label; \
                } \
                return ES_ASYNC_WAIT; \
            } \
        } \
    } while(0)



/**
 * @brief Sleep for specified time
 * @param ms Sleep time (milliseconds)
 */
#define es_co_sleep(ms) \
    do { \
        (__coro)->line = __LINE__; \
        ((es_coro_timeout_t *)__coro)->start_time = es_os_get_tick_ms(); \
        case __LINE__: { \
            if(!es_coro_timeout_check((es_coro_timeout_t *)__coro, ms)) { \
                return ES_ASYNC_WAIT; \
            } \
        } \
    } while(0)


#define es_co_forever for((__coro)->cnt = 0; ; (__coro)->cnt++)

/**
 * @brief Coroutine semaphore structure
 */
typedef struct {
    int16_t count;        /**< Semaphore count */
    int16_t max_count;    /**< Semaphore maximum count */
} es_coro_sem_t;

/**
 * @brief Coroutine lock structure
 */
typedef struct {
    int8_t locked;        /**< Lock state: 0-unlocked, 1-locked */
} es_coro_lock_t;

/**
 * @brief Initialize coroutine lock
 * @param lock Lock structure pointer
 */
void es_coro_lock_init(es_coro_lock_t *lock);

/**
 * @brief Try to acquire lock
 * @param lock Lock structure pointer
 * @return Returns true if acquisition successful, false otherwise
 */
bool es_coro_lock_acquire(es_coro_lock_t *lock);

/**
 * @brief Release lock
 * @param lock Lock structure pointer
 */
void es_coro_lock_release(es_coro_lock_t *lock);

/**
 * @brief Coroutine wait to acquire lock
 * @param lock Lock structure pointer
 */
#define ES_CORO_LOCK_ACQUIRE(lock) es_co_wait(es_coro_lock_acquire(lock))

/**
 * @brief Coroutine release lock
 * @param lock Lock structure pointer
 */
#define ES_CORO_LOCK_RELEASE(lock) es_coro_lock_release(lock);


/**
 * @brief Initialize coroutine semaphore
 * @param sem Semaphore structure pointer
 * @param init_count Initial count value
 * @param max_count Maximum count value
 */
void es_coro_sem_init(es_coro_sem_t *sem, int16_t init_count, int16_t max_count);

/**
 * @brief Acquire semaphore
 * @param sem Semaphore structure pointer
 * @return Returns true if acquisition successful, false otherwise
 */
bool es_coro_sem_take(es_coro_sem_t *sem);

/**
 * @brief Release semaphore
 * @param sem Semaphore structure pointer
 * @return Returns true if release successful, false if maximum count reached
 */
bool es_coro_sem_give(es_coro_sem_t *sem);

/**
 * @brief Coroutine wait for semaphore
 * @param sem Semaphore structure pointer
 */
#define ES_CORO_SEM_TAKE(sem) es_co_wait(es_coro_sem_take(sem))

/**
 * @brief Coroutine release semaphore
 * @param sem Semaphore structure pointer
 */
#define ES_CORO_SEM_GIVE(sem) es_coro_sem_give(sem);

/**
 * @brief Coroutine wait for semaphore (with timeout)
 * @param sem Semaphore structure pointer
 * @param timeout Timeout duration (milliseconds)
 */
#define ES_CORO_SEM_TAKE_TIMEOUT(sem, timeout) es_co_wait_timeout(es_coro_sem_take(sem), timeout)


/**
 * @brief Monitor long-running coroutines
 * @param timeout_ms Timeout duration (milliseconds)
 * @return Whether any coroutine has timed out
 */
bool es_coro_monitor_timeout(uint32_t timeout_ms);

#ifdef __cplusplus
}
#endif

#endif /* __ES_CORO_H__ */ 


