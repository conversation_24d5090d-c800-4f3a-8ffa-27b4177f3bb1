#include <errno.h>
#include <stdint.h>
#include <stddef.h>
#include "hc32_ll.h"



#undef errno
static int errno;

struct stat {
    uint32_t st_size;  // 示例字段
    uint16_t st_mode;
};

char *__env[1] = { 0 };
char **environ = __env;

int _write(int file, char *ptr, int len)
{
//    int i;
    errno = -1;
    return -1;
}

void *_sbrk(ptrdiff_t incr)
{
    /* 不支持动态内存分配 */
    errno = ENOMEM;
    return (void *)-1;
}

int _close(int file)
{
    return -1;
}

int _fstat(int file, struct stat *st)
{
    //st->st_mode = S_IFCHR;
    return 0;
}

int _isatty(int file)
{
    return 1;
}

int _lseek(int file, int ptr, int dir)
{
    return 0;
}

int _read(int file, char *ptr, int len)
{
    return 0;
}

int _kill(int pid, int sig)
{
    errno = EINVAL;
    return -1;
}

int _getpid(void)
{
    return 1;
}

void _exit(int status)
{
    _kill(status, -1);
    while (1) {}
}

int _fork(void)
{
    errno = -1;
    return -1;
}

int _wait(int *status)
{
    errno = -1;
    return -1;
}

//int _stat(char *file, struct stat *st)
//{
////    st->st_mode = 0;
//    return 0;
//}

int _link(char *old, char *new)
{
    errno = -1;
    return -1;
}

int _unlink(char *name)
{
    errno = -1;
    return -1;
}

int _execve(char *name, char **argv, char **env)
{
    errno = ENOMEM;
    return -1;
}
