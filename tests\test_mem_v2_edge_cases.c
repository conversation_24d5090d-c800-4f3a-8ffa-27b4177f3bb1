/**
 * @file test_mem_v2_edge_cases.c
 * @brief 内存管理器V2边界情况测试
 * <AUTHOR> Assistant
 * @date 2025/1/21
 */

#include <stdio.h>
#include <string.h>
#include <assert.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdbool.h>
#include <stdlib.h>
#include "../applications/es_mem_v2.h"

// 简单的日志实现用于测试
void es_log_write(int level, const char *tag, bool to_flash, const char *fmt, ...) {
    va_list args;
    va_start(args, fmt);
    printf("[%s] ", tag);
    vprintf(fmt, args);
    printf("\n");
    va_end(args);
}

static void test_max_allocation_with_header(void) {
    printf("\n=== Testing Maximum Allocation with Header Overhead ===\n");
    
    // 测试最大分配大小
    printf("Testing ES_MEM_V2_MAX_ALLOC (%d bytes) allocation...\n", ES_MEM_V2_MAX_ALLOC);
    void *ptr = es_mem_v2_alloc(ES_MEM_V2_MAX_ALLOC);
    
    if (ptr) {
        printf("✓ Successfully allocated %d bytes\n", ES_MEM_V2_MAX_ALLOC);
        
        // 验证可以写入数据
        memset(ptr, 0xAA, ES_MEM_V2_MAX_ALLOC);
        printf("✓ Successfully wrote test pattern to allocated memory\n");
        
        // 验证数据完整性
        uint8_t *data = (uint8_t*)ptr;
        bool data_ok = true;
        for (int i = 0; i < ES_MEM_V2_MAX_ALLOC; i++) {
            if (data[i] != 0xAA) {
                data_ok = false;
                break;
            }
        }
        printf("✓ Data integrity check: %s\n", data_ok ? "PASSED" : "FAILED");
        
        es_mem_v2_free(ptr);
        printf("✓ Successfully freed maximum allocation\n");
    } else {
        printf("✗ Failed to allocate %d bytes (this might be expected if pool is too small)\n", 
               ES_MEM_V2_MAX_ALLOC);
    }
}

static void test_allocation_near_limits(void) {
    printf("\n=== Testing Allocations Near Limits ===\n");
    
    // 测试接近最大分配的大小
    size_t test_sizes[] = {
        ES_MEM_V2_MAX_ALLOC - 1,
        ES_MEM_V2_MAX_ALLOC,
        ES_MEM_V2_MAX_ALLOC + 1
    };
    
    for (int i = 0; i < 3; i++) {
        size_t size = test_sizes[i];
        printf("Testing allocation of %zu bytes...\n", size);
        
        void *ptr = es_mem_v2_alloc(size);
        if (ptr) {
            printf("✓ Successfully allocated %zu bytes at %p\n", size, ptr);
            es_mem_v2_free(ptr);
        } else {
            printf("✗ Failed to allocate %zu bytes (expected for size > MAX_ALLOC)\n", size);
        }
    }
}

static void test_pool_exhaustion(void) {
    printf("\n=== Testing Pool Exhaustion ===\n");
    
    // 计算理论上可以分配的最大块数
    // 每个块需要：header + aligned_data
    size_t block_size = 100;
    size_t aligned_block_size = ((block_size + ES_MEM_V2_ALIGN_SIZE - 1) & ~(ES_MEM_V2_ALIGN_SIZE - 1));
    size_t total_per_block = ES_MEM_V2_HEADER_SIZE + aligned_block_size;
    size_t max_blocks = (ES_MEM_V2_POOL_SIZE - ES_MEM_V2_HEADER_SIZE) / total_per_block;
    
    printf("Block size: %zu, aligned: %zu, total per block: %zu\n", 
           block_size, aligned_block_size, total_per_block);
    printf("Theoretical max blocks: %zu\n", max_blocks);
    
    void **ptrs = malloc(max_blocks * sizeof(void*));
    if (!ptrs) {
        printf("✗ Failed to allocate pointer array\n");
        return;
    }
    
    size_t allocated_count = 0;
    
    // 尝试分配直到失败
    for (size_t i = 0; i < max_blocks + 10; i++) {
        ptrs[i] = es_mem_v2_alloc(block_size);
        if (ptrs[i]) {
            allocated_count++;
        } else {
            printf("Allocation failed at block %zu\n", i);
            break;
        }
    }
    
    printf("✓ Successfully allocated %zu blocks of %zu bytes each\n", 
           allocated_count, block_size);
    printf("Total allocated: %zu bytes\n", allocated_count * total_per_block);
    
    // 验证再次分配会失败
    void *extra_ptr = es_mem_v2_alloc(block_size);
    if (!extra_ptr) {
        printf("✓ Additional allocation correctly failed (pool exhausted)\n");
    } else {
        printf("✗ Unexpected: additional allocation succeeded\n");
        es_mem_v2_free(extra_ptr);
    }
    
    // 释放所有分配的内存
    for (size_t i = 0; i < allocated_count; i++) {
        es_mem_v2_free(ptrs[i]);
    }
    
    free(ptrs);
    printf("✓ All memory freed\n");
}

static void test_fragmentation_scenario(void) {
    printf("\n=== Testing Fragmentation Scenario ===\n");
    
    // 分配多个不同大小的块
    void *ptrs[10];
    size_t sizes[] = {64, 128, 256, 512, 100, 200, 300, 150, 80, 400};
    
    printf("Allocating blocks with different sizes...\n");
    for (int i = 0; i < 10; i++) {
        ptrs[i] = es_mem_v2_alloc(sizes[i]);
        if (ptrs[i]) {
            printf("  Block %d: %zu bytes at %p\n", i, sizes[i], ptrs[i]);
        } else {
            printf("  Block %d: allocation failed\n", i);
        }
    }
    
    // 释放一些块创建碎片
    printf("\nCreating fragmentation by freeing alternate blocks...\n");
    for (int i = 1; i < 10; i += 2) {
        if (ptrs[i]) {
            es_mem_v2_free(ptrs[i]);
            ptrs[i] = NULL;
            printf("  Freed block %d\n", i);
        }
    }
    
    // 尝试分配一个中等大小的块
    printf("\nTrying to allocate 150 bytes in fragmented pool...\n");
    void *new_ptr = es_mem_v2_alloc(150);
    if (new_ptr) {
        printf("✓ Successfully allocated 150 bytes at %p\n", new_ptr);
        es_mem_v2_free(new_ptr);
    } else {
        printf("✗ Failed to allocate 150 bytes (fragmentation issue)\n");
    }
    
    // 清理剩余内存
    for (int i = 0; i < 10; i += 2) {
        if (ptrs[i]) {
            es_mem_v2_free(ptrs[i]);
        }
    }
    
    printf("✓ Fragmentation test completed\n");
}

static void test_alignment_edge_cases(void) {
    printf("\n=== Testing Alignment Edge Cases ===\n");
    
    // 测试各种大小的对齐
    size_t test_sizes[] = {1, 2, 3, 4, 5, 7, 8, 9, 15, 16, 17, 31, 32, 33};
    int count = sizeof(test_sizes) / sizeof(test_sizes[0]);
    
    for (int i = 0; i < count; i++) {
        size_t size = test_sizes[i];
        void *ptr = es_mem_v2_alloc(size);
        
        if (ptr) {
            uintptr_t addr = (uintptr_t)ptr;
            bool aligned = (addr % ES_MEM_V2_ALIGN_SIZE) == 0;
            size_t expected_aligned = ((size + ES_MEM_V2_ALIGN_SIZE - 1) & ~(ES_MEM_V2_ALIGN_SIZE - 1));
            
            printf("Size %2zu -> aligned to %2zu, ptr=%p, 4-byte aligned: %s\n", 
                   size, expected_aligned, ptr, aligned ? "✓" : "✗");
            
            if (!aligned) {
                printf("✗ ALIGNMENT ERROR for size %zu\n", size);
            }
            
            es_mem_v2_free(ptr);
        } else {
            printf("✗ Failed to allocate %zu bytes\n", size);
        }
    }
}

int main(void) {
    printf("=== Memory Allocator V2 Edge Cases Test Suite ===\n");
    printf("Pool size: %d bytes, Max alloc: %d bytes, Header size: %d bytes\n",
           ES_MEM_V2_POOL_SIZE, ES_MEM_V2_MAX_ALLOC, ES_MEM_V2_HEADER_SIZE);
    
    // 初始化
    int ret = es_mem_v2_init();
    assert(ret == 0);
    printf("✓ Memory allocator initialized\n");
    
    // 运行边界测试
    test_max_allocation_with_header();
    test_allocation_near_limits();
    test_pool_exhaustion();
    test_fragmentation_scenario();
    test_alignment_edge_cases();
    
    // 最终检查
    printf("\n=== Final Status ===\n");
    es_mem_v2_dump_stats();
    
    int leaks = es_mem_v2_check_leaks();
    if (leaks == 0) {
        printf("✓ No memory leaks detected\n");
    } else {
        printf("⚠ %d memory leaks detected\n", leaks);
    }
    
    // 反初始化
    ret = es_mem_v2_deinit();
    assert(ret == 0);
    printf("✓ Memory allocator deinitialized\n");
    
    printf("\n=== Edge Cases Tests Completed! ===\n");
    return 0;
}
