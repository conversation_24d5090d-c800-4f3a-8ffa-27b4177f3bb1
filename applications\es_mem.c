/**
 * @file es_mem_v2.c
 * @brief Memory Management Module V2 Implementation - Next Fit Strategy
 * <AUTHOR> Assistant
 * @date 2025/1/21
 * @copyright Copyright (c) 2025
 */

#include "es_mem.h"
#include "es_log.h"
#include "es_at_srv.h"
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <stdlib.h>

#define TAG "MEM_V2"

// Memory pool structure
typedef struct {
    uint8_t pool[ES_MEM_V2_POOL_SIZE];     // Memory pool array
    uint32_t next_fit_offset;              // Next Fit search start offset
    bool initialized;                      // Initialization flag
    es_mem_v2_stats_t stats;              // Statistics information
} es_mem_v2_pool_t;

// Global memory pool instance
static es_mem_v2_pool_t s_mem_pool = {0};


// Get actual size (bit field stores size/4)
static inline uint32_t get_actual_size(uint32_t size_field) {
    // 14-bit field can store values 0-16383, representing sizes 0-65532 bytes
    // But our pool is only 10KB, so anything > 10KB indicates corruption
    uint32_t actual_size = size_field * 4;
    if (actual_size > ES_MEM_V2_POOL_SIZE) {
        ES_PRINTF_I(TAG, "WARNING: Detected corrupted size field: %u (raw: %u)", actual_size, size_field);
        return 0; // Return 0 to indicate corruption
    }
    return actual_size;
}

// Set size field (store actual size/4)
static inline uint32_t set_size_field(uint32_t actual_size) {
    if (actual_size > ES_MEM_V2_POOL_SIZE) {
        ES_PRINTF_I(TAG, "ERROR: Attempting to set invalid size: %u", actual_size);
        return 0;
    }
    uint32_t size_field = actual_size / 4;
    // 14-bit field can only store values 0-16383
    if (size_field > 16383) {
        ES_PRINTF_I(TAG, "ERROR: Size field overflow: %u (max: 16383)", size_field);
        return 0;
    }
    return size_field;
}

// Inline function: get header pointer
static inline es_mem_v2_header_t* get_header(void *ptr) {
    return (es_mem_v2_header_t*)((uint8_t*)ptr - ES_MEM_V2_HEADER_SIZE);
}

// Inline function: get data pointer
static inline void* get_data_ptr(es_mem_v2_header_t *header) {
    return (uint8_t*)header + ES_MEM_V2_HEADER_SIZE;
}

// Inline function: check if pointer is within pool
static inline bool is_valid_ptr(void *ptr) {
    uint8_t *p = (uint8_t*)ptr;
    return (p >= s_mem_pool.pool + ES_MEM_V2_HEADER_SIZE &&
            p < s_mem_pool.pool + ES_MEM_V2_POOL_SIZE);
}

// Validate header integrity
static inline bool is_header_valid(es_mem_v2_header_t *header) {
    if (!header) return false;

    // Check if header is within pool bounds
    uint8_t *h = (uint8_t*)header;
    if (h < s_mem_pool.pool || h >= s_mem_pool.pool + ES_MEM_V2_POOL_SIZE - ES_MEM_V2_HEADER_SIZE) {
        return false;
    }

    // Check size fields
    uint32_t current_size = get_actual_size(header->current_size);
    uint32_t prev_size = get_actual_size(header->prev_size);

    if (current_size == 0 || current_size > ES_MEM_V2_POOL_SIZE) {
        return false;
    }

    if (prev_size > ES_MEM_V2_POOL_SIZE) {
        return false;
    }

    return true;
}


// Get next block
static es_mem_v2_header_t* get_next_block(es_mem_v2_header_t *header) {
    uint32_t actual_size = get_actual_size(header->current_size);
    uint8_t *next = (uint8_t*)header + ES_MEM_V2_HEADER_SIZE + actual_size;
    if (next >= s_mem_pool.pool + ES_MEM_V2_POOL_SIZE) {
        return NULL;
    }
    return (es_mem_v2_header_t*)next;
}

// Get previous block
static es_mem_v2_header_t* get_prev_block(es_mem_v2_header_t *header) {
    if (header->prev_size == 0) {
        return NULL;
    }
    uint32_t actual_prev_size = get_actual_size(header->prev_size);
    uint8_t *prev = (uint8_t*)header - ES_MEM_V2_HEADER_SIZE - actual_prev_size;
    if (prev < s_mem_pool.pool) {
        return NULL;
    }
    return (es_mem_v2_header_t*)prev;
}

// Merge free blocks
static void merge_free_blocks(es_mem_v2_header_t *header) {
    if (!header || header->allocated) {
        return;
    }

    // Backward merge - merge all subsequent free blocks as much as possible
    es_mem_v2_header_t *next = get_next_block(header);
    while (next && !next->allocated) {
        uint32_t current_actual = get_actual_size(header->current_size);
        uint32_t next_actual = get_actual_size(next->current_size);
        uint32_t new_size = current_actual + ES_MEM_V2_HEADER_SIZE + next_actual;
        header->current_size = set_size_field(new_size);

        // Update prev_size of the next next block
        es_mem_v2_header_t *next_next = get_next_block(next);
        if (next_next) {
            next_next->prev_size = header->current_size;
        }

        next = get_next_block(header);
    }

    // Forward merge - merge all preceding free blocks as much as possible
    es_mem_v2_header_t *prev = get_prev_block(header);
    while (prev && !prev->allocated) {
        uint32_t prev_actual = get_actual_size(prev->current_size);
        uint32_t current_actual = get_actual_size(header->current_size);
        uint32_t new_size = prev_actual + ES_MEM_V2_HEADER_SIZE + current_actual;
        prev->current_size = set_size_field(new_size);

        // Update current header to point to merged block
        header = prev;

        // Continue checking previous block
        prev = get_prev_block(header);
    }

    // Update prev_size of the next block of final merged block
    es_mem_v2_header_t *next_block = get_next_block(header);
    if (next_block) {
        next_block->prev_size = header->current_size;
    }
}

int es_mem_v2_init(void) {
    if (s_mem_pool.initialized) {
        ES_PRINTF_I(TAG, "Memory pool already initialized");
        return 0;
    }
    
    // Clear memory pool
    memset(&s_mem_pool, 0, sizeof(es_mem_v2_pool_t));

    // Initialize first free block
    es_mem_v2_header_t *first_header = (es_mem_v2_header_t*)s_mem_pool.pool;
    uint32_t first_block_size = ES_MEM_V2_POOL_SIZE - ES_MEM_V2_HEADER_SIZE;
    first_header->current_size = set_size_field(first_block_size);
    first_header->prev_size = 0;
    first_header->allocated = 0;
    first_header->reserved = 0;

    // Initialize statistics
    s_mem_pool.stats.total_size = ES_MEM_V2_POOL_SIZE;
    s_mem_pool.stats.free_size = ES_MEM_V2_POOL_SIZE - ES_MEM_V2_HEADER_SIZE;
    s_mem_pool.stats.largest_free = s_mem_pool.stats.free_size;
    
    s_mem_pool.next_fit_offset = 0;
    s_mem_pool.initialized = true;
    
    ES_PRINTF_I(TAG, "Memory pool V2 initialized: size=%d bytes, max_alloc=%d bytes", 
               ES_MEM_V2_POOL_SIZE, ES_MEM_V2_MAX_ALLOC);
    
    return 0;
}

int es_mem_v2_deinit(void) {
    if (!s_mem_pool.initialized) {
        return -1;
    }
    
    // Check for memory leaks
    int leaks = es_mem_v2_check_leaks();
    if (leaks > 0) {
        ES_PRINTF_I(TAG, "Warning: %d memory leaks detected during deinit", leaks);
    }
    
    memset(&s_mem_pool, 0, sizeof(es_mem_v2_pool_t));
    
    ES_PRINTF_I(TAG, "Memory pool V2 deinitialized");
    return 0;
}

void* es_mem_v2_alloc(size_t size) {
    // Check parameter validity, considering metadata header overhead
    if (!s_mem_pool.initialized || size == 0 ||
        size > ES_MEM_V2_MAX_ALLOC ||
        (size + ES_MEM_V2_HEADER_SIZE) > ES_MEM_V2_POOL_SIZE) {
        s_mem_pool.stats.alloc_fail_count++;
        return NULL;
    }

    // 4-byte alignment
    size_t aligned_size = ES_MEM_V2_ALIGN(size);

    // Next Fit search: start from last allocation position
    uint8_t *search_start = s_mem_pool.pool + s_mem_pool.next_fit_offset;
    es_mem_v2_header_t *header = (es_mem_v2_header_t*)search_start;

    // Two-round search: first round from next_fit_offset to end, second round from beginning to next_fit_offset
    for (int round = 0; round < 2; round++) {
        while ((uint8_t*)header < s_mem_pool.pool + ES_MEM_V2_POOL_SIZE) {
            // Check if it's a valid free block
            uint32_t current_actual_size = get_actual_size(header->current_size);
            if (!header->allocated && current_actual_size >= aligned_size) {
                // Found suitable block
                uint32_t remaining = current_actual_size - aligned_size;
                
                if (remaining >= ES_MEM_V2_HEADER_SIZE + ES_MEM_V2_ALIGN_SIZE) {
                    // Split block
                    es_mem_v2_header_t *new_header = (es_mem_v2_header_t*)
                        ((uint8_t*)header + ES_MEM_V2_HEADER_SIZE + aligned_size);

                    uint32_t new_block_size = remaining - ES_MEM_V2_HEADER_SIZE;
                    new_header->current_size = set_size_field(new_block_size);
                    new_header->prev_size = set_size_field(aligned_size);
                    new_header->allocated = 0;
                    new_header->reserved = 0;

                    header->current_size = set_size_field(aligned_size);

                    // Update prev_size of next block
                    es_mem_v2_header_t *next = get_next_block(new_header);
                    if (next) {
                        next->prev_size = new_header->current_size;
                    }
                }

                header->allocated = 1;

                // Validate header after modification
                if (!is_header_valid(header)) {
                    ES_PRINTF_I(TAG, "ERROR: Header corruption detected after allocation");
                    return NULL;
                }

                // Update Next Fit position
                s_mem_pool.next_fit_offset = (uint8_t*)header - s_mem_pool.pool;

                // Update statistics
                s_mem_pool.stats.alloc_count++;
                s_mem_pool.stats.active_blocks++;
                s_mem_pool.stats.used_size += ES_MEM_V2_HEADER_SIZE + aligned_size;
                s_mem_pool.stats.free_size -= ES_MEM_V2_HEADER_SIZE + aligned_size;

                void *data_ptr = get_data_ptr(header);
                ES_PRINTF_I(TAG, "Allocated %zu bytes (aligned %zu) at %p", size, aligned_size, data_ptr);

                return data_ptr;
            }
            
            // Move to next block
            if (header->current_size == 0) {
                break; // Avoid infinite loop
            }
            header = get_next_block(header);
            if (!header) {
                break;
            }
        }

        // Second round: search from beginning to next_fit_offset
        if (round == 0) {
            header = (es_mem_v2_header_t*)s_mem_pool.pool;
            search_start = s_mem_pool.pool + s_mem_pool.next_fit_offset;
        }
    }
    
    s_mem_pool.stats.alloc_fail_count++;
    ES_PRINTF_I(TAG, "Allocation failed for size %zu", size);
    return NULL;
}

void* es_mem_v2_calloc(size_t num, size_t size) {
    size_t total_size = num * size;
    void *ptr = es_mem_v2_alloc(total_size);
    if (ptr) {
        memset(ptr, 0, total_size);
    }
    return ptr;
}

void es_mem_v2_free(void *ptr) {
    if (!ptr || !is_valid_ptr(ptr)) {
        ES_PRINTF_I(TAG, "Invalid pointer for free: %p", ptr);
        return;
    }

    es_mem_v2_header_t *header = get_header(ptr);

    // Validate header before freeing
    if (!is_header_valid(header)) {
        ES_PRINTF_I(TAG, "ERROR: Header corruption detected before free: %p", ptr);
        return;
    }

    if (!header->allocated) {
        ES_PRINTF_I(TAG, "Double free detected: %p", ptr);
        return;
    }

    header->allocated = 0;
    
    // Update statistics
    uint32_t actual_size = get_actual_size(header->current_size);
    s_mem_pool.stats.free_count++;
    s_mem_pool.stats.active_blocks--;
    s_mem_pool.stats.used_size -= ES_MEM_V2_HEADER_SIZE + actual_size;
    s_mem_pool.stats.free_size += ES_MEM_V2_HEADER_SIZE + actual_size;

    // Merge adjacent free blocks
    merge_free_blocks(header);
    
    ES_PRINTF_I(TAG, "Freed memory at %p", ptr);
}

void es_mem_v2_free_safe(void **ptr) {
    if (ptr && *ptr) {
        es_mem_v2_free(*ptr);
        *ptr = NULL;
    }
}

int es_mem_v2_get_stats(es_mem_v2_stats_t *stats) {
    if (!stats || !s_mem_pool.initialized) {
        return -1;
    }

    *stats = s_mem_pool.stats;
    return 0;
}

void* es_mem_v2_realloc(void *ptr, size_t size) {
    if (!ptr) {
        return es_mem_v2_alloc(size);
    }

    if (size == 0) {
        es_mem_v2_free(ptr);
        return NULL;
    }

    if (!is_valid_ptr(ptr)) {
        return NULL;
    }

    es_mem_v2_header_t *header = get_header(ptr);
    if (!header->allocated) {
        return NULL;
    }

    size_t aligned_size = ES_MEM_V2_ALIGN(size);
    uint32_t current_actual_size = get_actual_size(header->current_size);

    // If new size is less than or equal to current size, return directly
    if (aligned_size <= current_actual_size) {
        return ptr;
    }

    // Allocate new memory
    void *new_ptr = es_mem_v2_alloc(size);
    if (!new_ptr) {
        return NULL;
    }

    // Copy data
    memcpy(new_ptr, ptr, current_actual_size);

    // Free old memory
    es_mem_v2_free(ptr);

    return new_ptr;
}

void es_mem_v2_dump_stats(void) {
    if (!s_mem_pool.initialized) {
        ES_PRINTF_I(TAG, "Memory pool not initialized");
        return;
    }

    ES_PRINTF_I(TAG, "=== Memory V2 Statistics ===");
    ES_PRINTF_I(TAG, "Total size: %u bytes", s_mem_pool.stats.total_size);
    ES_PRINTF_I(TAG, "Used size: %u bytes", s_mem_pool.stats.used_size);
    ES_PRINTF_I(TAG, "Free size: %u bytes", s_mem_pool.stats.free_size);
    ES_PRINTF_I(TAG, "Largest free: %u bytes", s_mem_pool.stats.largest_free);
    ES_PRINTF_I(TAG, "Active blocks: %u", s_mem_pool.stats.active_blocks);
    ES_PRINTF_I(TAG, "Alloc count: %u", s_mem_pool.stats.alloc_count);
    ES_PRINTF_I(TAG, "Free count: %u", s_mem_pool.stats.free_count);
    ES_PRINTF_I(TAG, "Alloc fail count: %u", s_mem_pool.stats.alloc_fail_count);
    ES_PRINTF_I(TAG, "Next fit offset: %u", s_mem_pool.next_fit_offset);
}

void es_mem_v2_dump_pools(void) {
    if (!s_mem_pool.initialized) {
        ES_PRINTF_I(TAG, "Memory pool not initialized");
        return;
    }

    ES_PRINTF_I(TAG, "=== Memory V2 Pool Status ===");

    es_mem_v2_header_t *header = (es_mem_v2_header_t*)s_mem_pool.pool;
    int block_count = 0;
    uint32_t largest_free = 0;

    while ((uint8_t*)header < s_mem_pool.pool + ES_MEM_V2_POOL_SIZE && header->current_size > 0) {
        uint32_t actual_current = get_actual_size(header->current_size);
        uint32_t actual_prev = get_actual_size(header->prev_size);

        ES_PRINTF_I(TAG, "Block %d: offset=%u, size=%u, prev_size=%u, allocated=%s",
                   block_count,
                   (uint32_t)((uint8_t*)header - s_mem_pool.pool),
                   actual_current,
                   actual_prev,
                   header->allocated ? "YES" : "NO");

        if (!header->allocated && actual_current > largest_free) {
            largest_free = actual_current;
        }

        header = get_next_block(header);
        if (!header) {
            break;
        }
        block_count++;

        // Prevent infinite loop
        if (block_count > 100) {
            ES_PRINTF_I(TAG, "Too many blocks, stopping dump");
            break;
        }
    }

    // Update largest free block statistics
    s_mem_pool.stats.largest_free = largest_free;

    ES_PRINTF_I(TAG, "Total blocks: %d, largest free: %u", block_count, largest_free);


}

int es_mem_v2_get_block_info(void *ptr, es_mem_v2_block_info_t *info) {
    if (!ptr || !info || !is_valid_ptr(ptr)) {
        return -1;
    }

    es_mem_v2_header_t *header = get_header(ptr);
    if (!header->allocated) {
        return -1;
    }

    uint32_t actual_size = get_actual_size(header->current_size);
    info->ptr = ptr;
    info->size = actual_size;
    info->actual_size = ES_MEM_V2_HEADER_SIZE + actual_size;
    info->allocated = header->allocated;

    return 0;
}

int es_mem_v2_verify_integrity(void) {
    if (!s_mem_pool.initialized) {
        return -1;
    }

    es_mem_v2_header_t *header = (es_mem_v2_header_t*)s_mem_pool.pool;
    es_mem_v2_header_t *prev = NULL;
    int block_count = 0;

    while ((uint8_t*)header < s_mem_pool.pool + ES_MEM_V2_POOL_SIZE && header->current_size > 0) {
        // Check prev_size consistency
        if (block_count == 0) {
            // First block should have prev_size = 0
            if (header->prev_size != 0) {
                ES_PRINTF_I(TAG, "Integrity error: first block prev_size should be 0, got %u",
                           get_actual_size(header->prev_size));
                return -1;
            }
        } else {
            // Non-first blocks should have prev_size matching previous block's current_size
            if (prev && header->prev_size != prev->current_size) {
                ES_PRINTF_I(TAG, "Integrity error: prev_size mismatch at block %d, expected %u, got %u",
                           block_count, get_actual_size(prev->current_size), get_actual_size(header->prev_size));
                return -1;
            }
        }

        // Check block size reasonableness
        uint32_t actual_size = get_actual_size(header->current_size);
        if (actual_size == 0 || actual_size > ES_MEM_V2_POOL_SIZE) {
            ES_PRINTF_I(TAG, "Integrity error: invalid block size %u at block %d", actual_size, block_count);
            ES_PRINTF_I(TAG, "  Raw header data: current_size=%u, prev_size=%u, allocated=%u",
                       header->current_size, header->prev_size, header->allocated);
            ES_PRINTF_I(TAG, "  Header address: %p, Pool start: %p", header, s_mem_pool.pool);

            // Dump raw header bytes for debugging
            uint8_t *raw_header = (uint8_t*)header;
            ES_PRINTF_I(TAG, "  Raw bytes: %02X %02X %02X %02X",
                       raw_header[0], raw_header[1], raw_header[2], raw_header[3]);
            return -1;
        }

        // Check if block extends beyond pool boundary
        uint8_t *block_end = (uint8_t*)header + ES_MEM_V2_HEADER_SIZE + actual_size;
        if (block_end > s_mem_pool.pool + ES_MEM_V2_POOL_SIZE) {
            ES_PRINTF_I(TAG, "Integrity error: block %d extends beyond pool boundary", block_count);
            return -1;
        }

        // Check header alignment
        if (((uintptr_t)header & (ES_MEM_V2_ALIGN_SIZE - 1)) != 0) {
            ES_PRINTF_I(TAG, "Integrity error: block %d header not aligned", block_count);
            return -1;
        }

        prev = header;
        header = get_next_block(header);
        if (!header) {
            break;
        }
        block_count++;

        // Prevent infinite loop
        if (block_count > 1000) {
            ES_PRINTF_I(TAG, "Integrity error: too many blocks");
            return -1;
        }
    }

    return 0;
}

int es_mem_v2_check_leaks(void) {
    if (!s_mem_pool.initialized) {
        return -1;
    }

    es_mem_v2_header_t *header = (es_mem_v2_header_t*)s_mem_pool.pool;
    int leak_count = 0;
    int block_count = 0;

    while ((uint8_t*)header < s_mem_pool.pool + ES_MEM_V2_POOL_SIZE && header->current_size > 0) {
        if (header->allocated) {
            leak_count++;
            uint32_t actual_size = get_actual_size(header->current_size);
            ES_PRINTF_I(TAG, "Memory leak detected: block %d, size=%u, offset=%u",
                       block_count, actual_size,
                       (uint32_t)((uint8_t*)header - s_mem_pool.pool));
        }

        header = get_next_block(header);
        if (!header) {
            break;
        }
        block_count++;

        // Prevent infinite loop
        if (block_count > 1000) {
            break;
        }
    }

    if (leak_count > 0) {
        ES_PRINTF_I(TAG, "Total memory leaks: %d", leak_count);
    }

    return leak_count;
}

int es_mem_v2_defragment(void) {
    if (!s_mem_pool.initialized) {
        return -1;
    }

    es_mem_v2_header_t *header = (es_mem_v2_header_t*)s_mem_pool.pool;
    int merged_count = 0;

    while ((uint8_t*)header < s_mem_pool.pool + ES_MEM_V2_POOL_SIZE && header->current_size > 0) {
        if (!header->allocated) {
            es_mem_v2_header_t *next = get_next_block(header);
            if (next && !next->allocated) {
                // Merge adjacent free blocks
                uint32_t current_actual = get_actual_size(header->current_size);
                uint32_t next_actual = get_actual_size(next->current_size);
                uint32_t new_size = current_actual + ES_MEM_V2_HEADER_SIZE + next_actual;
                header->current_size = set_size_field(new_size);

                // Update prev_size of next next block
                es_mem_v2_header_t *next_next = get_next_block(header);
                if (next_next) {
                    next_next->prev_size = header->current_size;
                }

                merged_count++;
                continue; // Continue checking current block
            }
        }

        header = get_next_block(header);
        if (!header) {
            break;
        }
    }

    ES_PRINTF_I(TAG, "Defragmentation completed: merged %d blocks", merged_count);
    return merged_count;
}

#ifdef ES_MEM_V2_DEBUG
void* es_mem_v2_alloc_debug(size_t size, const char *file, int line) {
    void *ptr = es_mem_v2_alloc(size);
    if (ptr) {
        ES_PRINTF_I(TAG, "DEBUG: Allocated %zu bytes at %p (%s:%d)", size, ptr, file, line);
    } else {
        ES_PRINTF_I(TAG, "DEBUG: Allocation failed for %zu bytes (%s:%d)", size, file, line);
    }
    return ptr;
}

void* es_mem_v2_calloc_debug(size_t num, size_t size, const char *file, int line) {
    void *ptr = es_mem_v2_calloc(num, size);
    if (ptr) {
        ES_PRINTF_I(TAG, "DEBUG: Callocated %zu*%zu bytes at %p (%s:%d)", num, size, ptr, file, line);
    } else {
        ES_PRINTF_I(TAG, "DEBUG: Callocation failed for %zu*%zu bytes (%s:%d)", num, size, file, line);
    }
    return ptr;
}

void* es_mem_v2_realloc_debug(void *ptr, size_t size, const char *file, int line) {
    void *new_ptr = es_mem_v2_realloc(ptr, size);
    ES_PRINTF_I(TAG, "DEBUG: Reallocated %p to %zu bytes, new ptr=%p (%s:%d)", ptr, size, new_ptr, file, line);
    return new_ptr;
}
#endif

// =============================================================================
// AT Command Implementation for Memory Testing
// =============================================================================

// Memory stress test related variables
static uint32_t s_stress_test_count = 0;
static uint32_t s_stress_test_size = 0;
static bool s_stress_test_running = false;

/**
 * @brief AT+MEM command handler - Memory stress testing
 *
 * Functions:
 * - Query memory status: AT+MEM?
 * - Memory stress test: AT+MEM=STRESS,<count>,<size>
 * - Show memory statistics: AT+MEM=STATS
 * - Show memory pool status: AT+MEM=DUMP
 * - Memory integrity check: AT+MEM=CHECK
 * - Memory defragmentation: AT+MEM=DEFRAG
 * - Memory leak check: AT+MEM=LEAKS
 *
 * @param coro Coroutine context
 * @param ctx Command context
 * @param cmd_type Command type
 * @return Coroutine return value
 */
es_async_t at_srv_cmd_mem_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    char *endptr = NULL;

    es_co_begin(coro);

    if (cmd_type == ES_AT_SRV_CMD_QUERY) {
        // Query memory status
        es_mem_v2_stats_t stats;
        if (es_mem_v2_get_stats(&stats) == 0) {
            es_at_srv_fmt_send("+MEM: total=%u,used=%u,free=%u,blocks=%u\r\n",
                               stats.total_size, stats.used_size,
                               stats.free_size, stats.active_blocks);
            es_at_srv_send_ok();
        } else {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
        }
    } else if (cmd_type == ES_AT_SRV_CMD_SET) {
        if (ctx->param_count < 1) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_PARAM_MISSING);
            es_co_exit;
        }

        // Parse operation type
        if (strncmp(ctx->params[0].param, "STRESS", 6) == 0) {
            // Memory stress test: AT+MEM=STRESS,<count>,<size>
            if (ctx->param_count < 3) {
                es_at_srv_send_error_code(ES_AT_SRV_ERR_PARAM_MISSING);
                es_co_exit;
            }

            if (s_stress_test_running) {
                es_at_srv_fmt_send("+MEM: ERROR,stress test already running\r\n");
                es_at_srv_send_error();
                es_co_exit;
            }

            uint32_t count = strtoul(ctx->params[1].param, &endptr, 10);
            if (endptr == ctx->params[1].param || count == 0) {
                es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
                es_co_exit;
            }

            uint32_t size = strtoul(ctx->params[2].param, &endptr, 10);
            if (endptr == ctx->params[2].param || size == 0) {
                es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
                es_co_exit;
            }

            s_stress_test_running = true;
            s_stress_test_count = count;
            s_stress_test_size = size;

            ES_PRINTF_I(TAG, "Starting memory stress test: count=%u, size=%u", count, size);

            uint32_t success_count = 0;
            uint32_t fail_count = 0;
            uint32_t total_allocated = 0;

            // Execute stress test
            for (uint32_t i = 0; i < count; i++) {
                void *ptr = es_mem_v2_alloc(size);
                if (ptr) {
                    success_count++;
                    total_allocated += size;

                    // Write test data - use safer mode
                    // Avoid writing data that might be mistaken for valid headers
                    uint8_t *data = (uint8_t*)ptr;
                    for (uint32_t j = 0; j < size; j++) {
                        data[j] = (uint8_t)((i + j) & 0xFF);
                    }

                    // Verify written data
                    bool data_valid = true;
                    for (uint32_t j = 0; j < size; j++) {
                        if (data[j] != (uint8_t)((i + j) & 0xFF)) {
                            ES_PRINTF_I(TAG, "Data corruption detected at iteration %u, offset %u", i, j);
                            data_valid = false;
                            break;
                        }
                    }

                    if (!data_valid) {
                        fail_count++;
                        es_mem_v2_free(ptr);
                        break;
                    }

                    // Free immediately to test allocation/deallocation performance
                    es_mem_v2_free(ptr);
                } else {
                    fail_count++;
                }

                // Check memory integrity every 100 operations
                if ((i + 1) % 100 == 0) {
                    if (es_mem_v2_verify_integrity() != 0) {
                        ES_PRINTF_I(TAG, "Memory integrity check failed at iteration %u", i + 1);
                        break;
                    }
                }
            }

            s_stress_test_running = false;

            es_at_srv_fmt_send("+MEM: STRESS,count=%u,size=%u,success=%u,fail=%u,total_bytes=%u\r\n",
                               count, size, success_count, fail_count, total_allocated);
            es_at_srv_send_ok();

        } else if (strncmp(ctx->params[0].param, "STATS", 5) == 0) {
            // Show memory statistics: AT+MEM=STATS
            es_mem_v2_dump_stats();
            es_at_srv_send_ok();
        } else if (strncmp(ctx->params[0].param, "DUMP", 4) == 0) {
            // Show memory pool status: AT+MEM=DUMP
            es_mem_v2_dump_pools();
            es_at_srv_send_ok();
        } else if (strncmp(ctx->params[0].param, "CHECK", 5) == 0) {
            // Memory integrity check: AT+MEM=CHECK
            int result = es_mem_v2_verify_integrity();
            if (result == 0) {
                es_at_srv_fmt_send("+MEM: CHECK,OK\r\n");
                es_at_srv_send_ok();
            } else {
                es_at_srv_fmt_send("+MEM: CHECK,FAILED\r\n");
                es_at_srv_send_error();
            }
        } else if (strncmp(ctx->params[0].param, "DEFRAG", 6) == 0) {
            // Memory defragmentation: AT+MEM=DEFRAG
            int merged = es_mem_v2_defragment();
            es_at_srv_fmt_send("+MEM: DEFRAG,merged=%d\r\n", merged);
            es_at_srv_send_ok();
        } else if (strncmp(ctx->params[0].param, "LEAKS", 5) == 0) {
            // Memory leak check: AT+MEM=LEAKS
            int leaks = es_mem_v2_check_leaks();
            es_at_srv_fmt_send("+MEM: LEAKS,count=%d\r\n", leaks);
            es_at_srv_send_ok();
        } else {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
        }
    } else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_NOT_SUPPORTED);
    }

    es_co_end;
}
