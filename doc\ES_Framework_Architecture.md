# ES MCU Framework 架构文档

## 1. 项目概述

ES MCU Framework 是一个专为 HC32F460 微控制器设计的企业级嵌入式系统开发框架。该框架基于 RT-Thread 实时操作系统，采用模块化设计，提供了完整的协程调度、通信协议栈、存储管理等核心功能。

### 1.1 核心特性

- **模块化架构**: 清晰的分层设计，各功能模块相互独立
- **协程调度系统**: 基于无栈协程的轻量级任务调度
- **通信协议栈**: 支持多种通信协议和自动帧解析
- **安全存储系统**: 双扇区安全存储，支持参数分类管理
- **日志文件系统**: 完整的日志记录和查询功能
- **加密安全模块**: 集成多种加密算法
- **电源管理**: 低功耗设计，支持多种唤醒方式

### 1.2 支持平台

- **硬件平台**: HC32F460 系列微控制器
- **仿真平台**: Win32 平台（用于开发和测试）
- **操作系统**: RT-Thread 实时操作系统

## 2. 系统架构

### 2.1 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Applications)                     │
├─────────────────────────────────────────────────────────────┤
│  协程调度  │  通信协议  │  配置管理  │  日志系统  │  业务逻辑  │
├─────────────────────────────────────────────────────────────┤
│                    框架层 (Framework)                       │
├─────────────────────────────────────────────────────────────┤
│  内存管理  │  存储系统  │  加密模块  │  电源管理  │  工具模块  │
├─────────────────────────────────────────────────────────────┤
│                  平台抽象层 (Platform)                       │
├─────────────────────────────────────────────────────────────┤
│    HC32F460    │    Win32     │    驱动接口    │    OS接口    │
├─────────────────────────────────────────────────────────────┤
│                   硬件层 (Hardware)                         │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块组织

#### 2.2.1 核心模块 (Core Modules)
- **es.h/es.c**: 系统核心，负责系统初始化和模块协调
- **es_scheduler**: 协程调度器，提供轻量级任务调度
- **es_coro**: 无栈协程实现，基于 Duff's Device 技术

#### 2.2.2 通信模块 (Communication Modules)
- **es_frame**: 通信协议框架，支持多种协议格式
- **es_isotp**: ISO-TP 协议实现，用于车载诊断
- **es_uds**: UDS 诊断服务实现
- **es_at_srv**: AT 命令服务，提供串口命令接口
- **es_console**: 控制台接口

#### 2.2.3 存储模块 (Storage Modules)
- **es_cfg_v2**: 配置管理系统 V2，支持参数分类和版本控制
- **es_log**: 日志系统，支持多级日志和远程查询
- **es_flash**: Flash 存储管理
- **ringfs**: 环形文件系统

#### 2.2.4 安全模块 (Security Modules)
- **es_aes**: AES 加密算法实现
- **es_des3**: 3DES 加密算法实现
- **es_md5**: MD5 哈希算法实现
- **es_crc**: CRC 校验算法实现

#### 2.2.5 工具模块 (Utility Modules)
- **es_mem**: 内存管理，提供内存池和统计功能
- **es_time**: 时间管理和时间戳服务
- **es_utils**: 通用工具函数
- **es_button**: 按键处理
- **es_pm**: 电源管理

### 2.3 模块依赖关系

```mermaid
graph TD
    A[es.h 系统核心] --> B[es_scheduler 调度器]
    A --> C[es_mem 内存管理]
    A --> D[es_flash Flash存储]
    A --> E[es_log 日志系统]
    A --> F[es_cfg_v2 配置管理]
    
    B --> G[es_coro 协程]
    E --> H[es_log_fs 日志文件系统]
    E --> I[es_log_v2_fs 日志V2文件系统]
    F --> D
    
    J[es_frame 协议框架] --> K[es_isotp ISO-TP]
    J --> L[es_uds UDS诊断]
    
    M[es_at_srv AT服务] --> N[es_console 控制台]
    
    O[安全模块] --> P[es_aes AES加密]
    O --> Q[es_des3 3DES加密]
    O --> R[es_md5 MD5哈希]
    O --> S[es_crc CRC校验]
    
    T[平台层] --> U[HC32F460平台]
    T --> V[Win32平台]
```

## 3. 初始化流程

### 3.1 系统启动序列

```c
int es_init(void)
{
    es_os_init();           // 1. 操作系统接口初始化
    es_mem_init();          // 2. 内存管理初始化
    es_console_init();      // 3. 控制台初始化
    es_flash_init();        // 4. Flash存储初始化
    es_log_init();          // 5. 日志系统初始化
    es_cfg_v2_init();       // 6. 配置管理初始化
    es_log_v2_init();       // 7. 日志V2初始化
    es_frame_v2_init();     // 8. 协议框架初始化
    es_can_init();          // 9. CAN总线初始化
    es_veh_init();          // 10. 车辆相关初始化
    es_at_srv_init();       // 11. AT服务初始化
    es_pm_init();           // 12. 电源管理初始化
    es_ota_init();          // 13. OTA升级初始化
    
    return 0;
}
```

### 3.2 初始化依赖关系

1. **基础层**: OS接口 → 内存管理 → 控制台
2. **存储层**: Flash存储 → 日志系统 → 配置管理
3. **通信层**: 协议框架 → CAN总线 → AT服务
4. **应用层**: 车辆模块 → 电源管理 → OTA升级

## 4. 内存布局

### 4.1 Flash 分区

```
Flash 布局 (HC32F460):
┌─────────────────┬─────────────┬─────────────────┐
│   Bootloader    │ Application │   Data Storage  │
│   (0x00000000)  │(0x00008000) │  (0x00070000)   │
├─────────────────┼─────────────┼─────────────────┤
│      32KB       │    416KB    │      96KB       │
└─────────────────┴─────────────┴─────────────────┘

Data Storage 分区:
┌─────────────────┬─────────────────┬─────────────────┐
│  Config Sector  │   Log Sector    │  Backup Sector  │
│   (32KB x 2)    │   (32KB x 2)    │     (32KB)      │
└─────────────────┴─────────────────┴─────────────────┘
```

### 4.2 RAM 使用

```
RAM 布局:
┌─────────────────┬─────────────────┬─────────────────┐
│   System Stack │   Heap Memory   │   Static Data   │
│     (8KB)       │     (32KB)      │     (24KB)      │
└─────────────────┴─────────────────┴─────────────────┘
```

## 5. 设计原则

### 5.1 模块化设计
- 每个模块都有清晰的接口定义
- 模块间通过标准接口通信
- 支持模块的独立测试和替换

### 5.2 资源优化
- 使用内存池减少内存碎片
- 协程调度减少栈内存使用
- 环形缓冲区提高存储效率

### 5.3 可靠性设计
- 双扇区存储确保数据安全
- CRC校验保证数据完整性
- 看门狗机制防止系统死锁

### 5.4 可扩展性
- 支持新协议的动态注册
- 配置参数支持版本升级
- 模块化架构便于功能扩展

## 6. 性能指标

| 指标 | 数值 | 说明 |
|------|------|------|
| ROM 占用 | < 256KB | 包含所有核心模块 |
| RAM 占用 | < 64KB | 运行时内存使用 |
| 协程数量 | 64个 | 最大支持协程任务数 |
| 配置参数 | 256个 | 最大配置参数数量 |
| 日志条目 | 1000+ | 环形日志存储容量 |
| 启动时间 | < 500ms | 系统完整启动时间 |

## 7. 开发规范

### 7.1 命名规范
- 模块前缀: `es_`
- 函数命名: `es_module_function_name`
- 宏定义: `ES_MODULE_MACRO_NAME`
- 类型定义: `es_module_type_t`

### 7.2 错误处理
- 统一的错误码定义
- 错误信息记录到日志
- 关键错误触发系统重启

### 7.3 资源管理
- 动态内存使用内存池
- 及时释放不再使用的资源
- 避免内存泄漏和野指针

这个架构文档为 ES MCU Framework 的整体设计提供了全面的概述，为开发者理解和使用框架提供了重要参考。
