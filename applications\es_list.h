#ifndef __ES_LIST_H__
#define __ES_LIST_H__

#include <stddef.h>

/**
 * container_of - Get structure pointer from member pointer
 * @ptr: Structure member pointer
 * @type: Structure type
 * @member: Member name
 */
#define container_of(ptr, type, member) \
    ((type *)((char *)(ptr) - offsetof(type, member)))

/**
 * Doubly linked list node structure
 */
struct es_list_head {
    struct es_list_head *next;
    struct es_list_head *prev;
};

/**
 * Singly linked list node structure (optimized version)
 */
struct es_hlist_node {
    struct es_hlist_node *next;  /* Keep only one next pointer */
};

/**
 * Singly linked list head structure
 */
struct es_hlist_head {
    struct es_hlist_node *first;
};

/* Initialize list */
#define ES_LIST_HEAD_INIT(name) { &(name), &(name) }
#define ES_HLIST_HEAD_INIT { .first = NULL }

#define ES_LIST_HEAD(name) \
    struct es_list_head name = ES_LIST_HEAD_INIT(name)
#define ES_HLIST_HEAD(name) \
    struct es_hlist_head name = ES_HLIST_HEAD_INIT

/* Doubly linked list operations */
static inline void es_init_list_head(struct es_list_head *list)
{
    list->next = list;
    list->prev = list;
}

static inline void __es_list_add(struct es_list_head *new,
                            struct es_list_head *prev,
                            struct es_list_head *next)
{
    next->prev = new;
    new->next = next;
    new->prev = prev;
    prev->next = new;
}

static inline void es_list_add(struct es_list_head *new, struct es_list_head *head)
{
    __es_list_add(new, head, head->next);
}

static inline void es_list_add_tail(struct es_list_head *new, struct es_list_head *head)
{
    __es_list_add(new, head->prev, head);
}

static inline void __es_list_del(struct es_list_head *prev, struct es_list_head *next)
{
    next->prev = prev;
    prev->next = next;
}

static inline void es_list_del(struct es_list_head *entry)
{
    __es_list_del(entry->prev, entry->next);
    entry->next = NULL;
    entry->prev = NULL;
}

/* Singly linked list operations */
static inline void es_init_hlist_head(struct es_hlist_head *h)
{
    h->first = NULL;
}

static inline void es_init_hlist_node(struct es_hlist_node *h)
{
    h->next = NULL;
}

static inline void es_hlist_add_head(struct es_hlist_node *n, struct es_hlist_head *h)
{
    n->next = h->first;
    h->first = n;
}

static inline void es_hlist_add_after(struct es_hlist_node *n, struct es_hlist_node *after)
{
    n->next = after->next;
    after->next = n;
}

static inline void es_hlist_add_before(struct es_hlist_node *n, struct es_hlist_node *before)
{
    n->next = before;
    before->next = n;
}

static inline void es_hlist_add_tail(struct es_hlist_node *n, struct es_hlist_head *h)
{
    struct es_hlist_node *pos;
    
    for (pos = h->first; pos && pos->next; pos = pos->next);
    if (pos) {
        pos->next = n;
    } else {
        h->first = n;
    }
    n->next = NULL;
}

static inline void es_hlist_del_head(struct es_hlist_head *h)
{
    if (h->first)
        h->first = h->first->next;
}

static inline struct es_hlist_node *es_hlist_find_prev(struct es_hlist_head *h, struct es_hlist_node *n)
{
    struct es_hlist_node *pos;
    
    if (!h->first || h->first == n)
        return NULL;
        
    for (pos = h->first; pos->next != NULL && pos->next != n; pos = pos->next);
    
    return (pos->next == n) ? pos : NULL;
}

static inline void es_hlist_del(struct es_hlist_node *n, struct es_hlist_head *h)
{
    if (h->first == n) {
        h->first = n->next;
    } else {
        struct es_hlist_node *prev = es_hlist_find_prev(h, n);
        if (prev)
            prev->next = n->next;
    }
    n->next = NULL;
}

/* Traversal macros */
#define es_list_for_each(pos, head) \
    for (pos = (head)->next; pos != (head); pos = pos->next)

#define es_list_for_each_safe(pos, n, head) \
    for (pos = (head)->next, n = pos->next; pos != (head); \
        pos = n, n = pos->next)

#define es_list_entry(ptr, type, member) \
    container_of(ptr, type, member)

#define es_hlist_for_each(pos, head) \
    for (pos = (head)->first; pos; pos = pos->next)

#define es_hlist_for_each_safe(pos, n, head) \
    for (pos = (head)->first, n = pos ? pos->next : NULL; pos; \
         pos = n, n = pos ? pos->next : NULL)

#define es_hlist_entry(ptr, type, member) \
    container_of(ptr, type, member)

/**
 * Check if node is in the list
 */
static inline int es_hlist_node_in_list(struct es_hlist_node *n, struct es_hlist_head *h)
{
    struct es_hlist_node *pos;
    
    for (pos = h->first; pos; pos = pos->next) {
        if (pos == n)
            return 1;
    }
    
    return 0;
}

#endif /* __ES_LIST_H__ */ 

