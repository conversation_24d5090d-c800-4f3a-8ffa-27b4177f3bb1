/**
 * @file es_md5.h
 * @brief MD5 hash function implementation
 */
#ifndef __ES_MD5_H__
#define __ES_MD5_H__

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* MD5 constant definitions */
#define MD5_HASH_SIZE      16  /* MD5 hash result size is 16 bytes */
#define MD5_BLOCK_SIZE     64  /* MD5 processing block size is 64 bytes */

/**
 * @brief MD5 context structure
 */
typedef struct {
    uint32_t state[4];      /* State (ABCD) */
    uint32_t count[2];      /* Bit count (low 32 bits, high 32 bits) */
    uint8_t buffer[64];     /* Input buffer */
} es_md5_ctx_t;

/**
 * @brief Initialize MD5 context
 * 
 * @param ctx MD5 context pointer
 */
void es_md5_init(es_md5_ctx_t *ctx);

/**
 * @brief Add data to MD5 context for hashing
 * 
 * @param ctx MD5 context pointer
 * @param data Input data
 * @param length Data length
 */
void es_md5_update(es_md5_ctx_t *ctx, const void *data, size_t length);

/**
 * @brief Finalize MD5 hash calculation and output result
 * 
 * @param ctx MD5 context pointer
 * @param digest Output 16-byte MD5 hash value
 */
void es_md5_final(es_md5_ctx_t *ctx, uint8_t digest[16]);

/**
 * @brief Calculate MD5 hash of data (one-time calculation)
 * 
 * @param data Input data
 * @param length Data length
 * @param digest Output 16-byte MD5 hash value
 */
void es_md5(const void *data, size_t length, uint8_t digest[16]);

#ifdef __cplusplus
}
#endif

#endif /* __ES_MD5_H__ */ 
