// --- START OF MODIFIED hc32_lsm6dsl.c ---

#include "es_lsm6dsl.h"
#include "ev_hc32f460_lqfp100_v2_lsm6dsl.h" // Assuming this defines stc_lsm6dsl_axis_t
#include "es_scheduler.h"
#include "es_log.h"
#include "es_drv_os.h" // For es_os_get_tick_ms
#include <string.h>
#include <stdint.h>
#include <math.h>
#include <stdbool.h>

#define TAG "LSM6DSL"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// --- Physical Constants and Conversion Factors ---
#define LSM6DSL_ACCEL_SENSITIVITY_MG_LSB  (0.061f)   // mg per LSB. Example for +/-2g. ADJUST IF NEEDED!
#define LSM6DSL_GYRO_SENSITIVITY_MDPS_LSB (8.75f)    // mdps per LSB. Example for +/-250dps. ADJUST IF NEEDED!
#define GRAVITY_STANDARD                  (9.80665f) // m/s^2

#define ACCEL_RAW_TO_MS2(raw_val) ((float)(raw_val) * LSM6DSL_ACCEL_SENSITIVITY_MG_LSB / 1000.0f * GRAVITY_STANDARD)
#define GYRO_RAW_TO_RADS(raw_val) ((float)(raw_val) * LSM6DSL_GYRO_SENSITIVITY_MDPS_LSB / 1000.0f * (M_PI / 180.0f))

// --- Algorithm Constants ---
#define GRAVITY_MAGNITUDE_EXPECTED      GRAVITY_STANDARD
#define GRAVITY_MAGNITUDE_TOLERANCE     1.5f
#define GYRO_STILL_THRESHOLD            0.05f  // rad/s
#define ACCEL_MOTION_THRESHOLD          1.5f   // m/s^2

#define CALIBRATION_SAMPLES_REQUIRED    50
#define FALL_ANGLE_THRESHOLD_RAD        (M_PI / 4.0f) // 45 degrees
#define FALL_DETECTION_SAMPLES          5  // 需要连续检测到倾倒的次数
#define FALL_RECOVERY_SAMPLES           10 // 需要连续检测到恢复的次数

// Kalman Filter Tuning Parameters
#define KF_Q_ANGLE                      0.001f
#define KF_Q_GYRO_BIAS                  0.0003f
#define KF_R_MEASURE                    0.03f

#define SENSOR_READ_INTERVAL_MS         (20) // Target interval in ms (50Hz) - CRITICAL, ADJUST AS NEEDED

// --- Vector Math Library (using float) ---
typedef struct {
    float x, y, z;
} Vector3D_f;

float vec_dot_f(Vector3D_f v1, Vector3D_f v2) { return v1.x * v2.x + v1.y * v2.y + v1.z * v2.z; }
float vec_magnitude_f(Vector3D_f v) { return sqrtf(v.x * v.x + v.y * v.y + v.z * v.z); }
Vector3D_f vec_normalize_f(Vector3D_f v) {
    float mag = vec_magnitude_f(v);
    if (fabsf(mag) < 0.000001f) return (Vector3D_f){0.0f, 0.0f, 0.0f};
    return (Vector3D_f){v.x / mag, v.y / mag, v.z / mag};
}
Vector3D_f vec_subtract_f(Vector3D_f v1, Vector3D_f v2) { return (Vector3D_f){v1.x - v2.x, v1.y - v2.y, v1.z - v2.z}; }
Vector3D_f vec_scale_f(Vector3D_f v, float scalar) { return (Vector3D_f){v.x * scalar, v.y * scalar, v.z * scalar}; }
Vector3D_f vec_add_f(Vector3D_f v1, Vector3D_f v2) { return (Vector3D_f){v1.x + v2.x, v1.y + v2.y, v1.z + v2.z}; }

float angle_between_vectors_f(Vector3D_f v1, Vector3D_f v2) {
    Vector3D_f v1_norm = vec_normalize_f(v1);
    Vector3D_f v2_norm = vec_normalize_f(v2);
    float dot_product = vec_dot_f(v1_norm, v2_norm);
    if (dot_product > 1.0f) dot_product = 1.0f;
    if (dot_product < -1.0f) dot_product = -1.0f;
    return acosf(dot_product);
}

// --- Kalman Filter Structure and Functions (using float) ---
typedef struct {
    float angle; float bias; float P[2][2];
    float Q_angle; float Q_gyro_bias; float R_measure;
} KalmanState_f;

void kalman_init_f(KalmanState_f* kf, float Q_angle_val, float Q_gyro_bias_val, float R_measure_val) {
    kf->Q_angle = Q_angle_val; kf->Q_gyro_bias = Q_gyro_bias_val; kf->R_measure = R_measure_val;
    kf->angle = 0.0f; kf->bias = 0.0f;
    kf->P[0][0] = 1.0f; kf->P[0][1] = 0.0f; kf->P[1][0] = 0.0f; kf->P[1][1] = 1.0f;
}

float kalman_update_f(KalmanState_f* kf, float gyro_rate, float accel_angle, float dt) {
    kf->angle += dt * (gyro_rate - kf->bias);
    float P00_temp = kf->P[0][0];
    kf->P[0][0] += dt * (dt * kf->P[1][1] - kf->P[0][1] - kf->P[1][0] + kf->Q_angle);
    kf->P[0][1] -= dt * kf->P[1][1]; kf->P[1][0] -= dt * kf->P[1][1];
    kf->P[1][1] += kf->Q_gyro_bias * dt;
    float y = accel_angle - kf->angle;
    float S = kf->P[0][0] + kf->R_measure;
    if (fabsf(S) < 0.000001f) S = (S >= 0 ? 0.000001f : -0.000001f);
    float K[2]; K[0] = kf->P[0][0] / S; K[1] = kf->P[1][0] / S;
    kf->angle += K[0] * y; kf->bias += K[1] * y;
    P00_temp = kf->P[0][0]; float P01_temp_update = kf->P[0][1];
    kf->P[0][0] -= K[0] * P00_temp; kf->P[0][1] -= K[0] * P01_temp_update;
    kf->P[1][0] -= K[1] * P00_temp; kf->P[1][1] -= K[1] * P01_temp_update;
    return kf->angle;
}

// --- IMU Processor State ---
typedef enum {
    CAL_STATE_STARTUP, CAL_STATE_GRAVITY_PENDING, CAL_STATE_GRAVITY_SAMPLING,
    CAL_STATE_GRAVITY_DONE, CAL_STATE_FORWARD_PENDING, CAL_STATE_FORWARD_SAMPLING,
    CAL_STATE_ALL_DONE
} CalibrationState_t;

typedef struct {
    Vector3D_f accel_phys_units; Vector3D_f gyro_phys_units;
    KalmanState_f kf_roll; KalmanState_f kf_pitch;
    float current_roll_kf; float current_pitch_kf;
    Vector3D_f filtered_gravity_from_kf; Vector3D_f filtered_gyro_bias_corrected;
    Vector3D_f learned_gravity_imu; Vector3D_f learned_forward_imu; Vector3D_f learned_up_imu;
    CalibrationState_t cal_state;
    uint16_t gravity_samples_collected; Vector3D_f gravity_sum_imu;
    uint16_t forward_samples_collected; Vector3D_f forward_sum_imu;
    uint16_t startup_settle_count;
    bool is_fallen;
    uint8_t fall_detection_count;    // 连续检测到倾倒的次数
    uint8_t fall_recovery_count;     // 连续检测到恢复的次数
    bool is_fall_detected;           // 最终的倾倒状态
} IMUProcessorState_t;

// --- Main Data Context for LSM6DSL ---
typedef struct {
    stc_lsm6dsl_axis_t raw_lsm_accel; stc_lsm6dsl_axis_t raw_lsm_gyro;
    IMUProcessorState_t imu_proc;
} lsm6dsl_data_ctx_t;

static lsm6dsl_data_ctx_t sensor_data_ctx;
static es_coro_task_t sensor_task;

// --- IMU Processor Initialization and Update Functions ---
void imu_processor_state_init(IMUProcessorState_t* proc_state) {
    memset(proc_state, 0, sizeof(IMUProcessorState_t)); // Clear all members
    kalman_init_f(&proc_state->kf_roll, KF_Q_ANGLE, KF_Q_GYRO_BIAS, KF_R_MEASURE);
    kalman_init_f(&proc_state->kf_pitch, KF_Q_ANGLE, KF_Q_GYRO_BIAS, KF_R_MEASURE);
    proc_state->filtered_gravity_from_kf = (Vector3D_f){0,0,GRAVITY_MAGNITUDE_EXPECTED}; // Initial assumption
    proc_state->cal_state = CAL_STATE_STARTUP;
    proc_state->is_fall_detected = false;
    proc_state->fall_detection_count = 0;
    proc_state->fall_recovery_count = 0;
}

void imu_processor_state_update(IMUProcessorState_t* proc_state, Vector3D_f current_accel_phys_units, Vector3D_f current_gyro_phys_units, float dt) {
    // This is a secondary check. Primary dt validation should be in the calling task.
    if (dt <= 0.00001f) { // dt is in seconds. 0.00001s = 10 microseconds.
        // ES_LOGW(TAG, "PROC_DT_BAD: dt (%d us) too small. Using default %dms.",
        //         (int)(dt*1000000.0f), (int)SENSOR_READ_INTERVAL_MS);
        dt = (float)SENSOR_READ_INTERVAL_MS / 1000.0f;
        // Skipping update is an option, but might stall calibration. Using default dt is often safer.
    }

    proc_state->accel_phys_units = current_accel_phys_units;
    proc_state->gyro_phys_units = current_gyro_phys_units;
    proc_state->is_fallen = false;

    float roll_accel = atan2f(proc_state->accel_phys_units.y, proc_state->accel_phys_units.z);
    float pitch_accel_denominator_sq = proc_state->accel_phys_units.y * proc_state->accel_phys_units.y + 
                                     proc_state->accel_phys_units.z * proc_state->accel_phys_units.z;
    float pitch_accel_denominator = sqrtf(pitch_accel_denominator_sq);
    if (fabsf(pitch_accel_denominator) < 0.001f) {
         pitch_accel_denominator = (pitch_accel_denominator >= 0 ? 0.001f : -0.001f) ;
    }
    float pitch_accel = atan2f(-proc_state->accel_phys_units.x, pitch_accel_denominator);

    proc_state->current_roll_kf = kalman_update_f(&proc_state->kf_roll, proc_state->gyro_phys_units.x, roll_accel, dt);
    proc_state->current_pitch_kf = kalman_update_f(&proc_state->kf_pitch, proc_state->gyro_phys_units.y, pitch_accel, dt);

    proc_state->filtered_gyro_bias_corrected.x = proc_state->gyro_phys_units.x - proc_state->kf_roll.bias;
    proc_state->filtered_gyro_bias_corrected.y = proc_state->gyro_phys_units.y - proc_state->kf_pitch.bias;
    proc_state->filtered_gyro_bias_corrected.z = proc_state->gyro_phys_units.z;

    float sr = sinf(proc_state->current_roll_kf); float cr = cosf(proc_state->current_roll_kf);
    float sp = sinf(proc_state->current_pitch_kf); float cp = cosf(proc_state->current_pitch_kf);

    proc_state->filtered_gravity_from_kf.x = -sp * GRAVITY_MAGNITUDE_EXPECTED;
    proc_state->filtered_gravity_from_kf.y = sr * cp * GRAVITY_MAGNITUDE_EXPECTED;
    proc_state->filtered_gravity_from_kf.z = cr * cp * GRAVITY_MAGNITUDE_EXPECTED;

    float gyro_mag_bias_corrected = vec_magnitude_f(proc_state->filtered_gyro_bias_corrected);

    switch (proc_state->cal_state) {
        case CAL_STATE_STARTUP:
            proc_state->startup_settle_count++;
            if (proc_state->startup_settle_count > (2000 / SENSOR_READ_INTERVAL_MS)) { 
                proc_state->cal_state = CAL_STATE_GRAVITY_PENDING;
                ES_LOGI(TAG, "KF settled. Pending gravity cal.");
            }
            break;
        case CAL_STATE_GRAVITY_PENDING: case CAL_STATE_GRAVITY_SAMPLING: {
            float accel_mag_phys = vec_magnitude_f(proc_state->accel_phys_units);
            if (gyro_mag_bias_corrected < GYRO_STILL_THRESHOLD &&
                fabsf(accel_mag_phys - GRAVITY_MAGNITUDE_EXPECTED) < GRAVITY_MAGNITUDE_TOLERANCE * 1.5f) {
                if (proc_state->cal_state == CAL_STATE_GRAVITY_PENDING) {
                    proc_state->cal_state = CAL_STATE_GRAVITY_SAMPLING;
                    proc_state->gravity_samples_collected = 0; proc_state->gravity_sum_imu = (Vector3D_f){0,0,0};
                    ES_LOGI(TAG, "Starting gravity sampling.");
                }
                proc_state->gravity_sum_imu = vec_add_f(proc_state->gravity_sum_imu, proc_state->filtered_gravity_from_kf);
                proc_state->gravity_samples_collected++;
                if (proc_state->gravity_samples_collected >= CALIBRATION_SAMPLES_REQUIRED) {
                    proc_state->learned_gravity_imu = vec_normalize_f(vec_scale_f(proc_state->gravity_sum_imu, 1.0f / proc_state->gravity_samples_collected));
                    proc_state->learned_up_imu = vec_scale_f(proc_state->learned_gravity_imu, -1.0f);
                    proc_state->cal_state = CAL_STATE_GRAVITY_DONE;
                    ES_LOGI(TAG, "Gravity cal DONE. Gx:%d Gy:%d Gz:%d (x100)",
                           (int)(proc_state->learned_gravity_imu.x*100), (int)(proc_state->learned_gravity_imu.y*100), (int)(proc_state->learned_gravity_imu.z*100));
                }
            }
        } break;
        case CAL_STATE_GRAVITY_DONE:
            proc_state->cal_state = CAL_STATE_FORWARD_PENDING;
            ES_LOGI(TAG, "Gravity done. Pending forward cal.");
            break;
        case CAL_STATE_FORWARD_PENDING: case CAL_STATE_FORWARD_SAMPLING: {
            Vector3D_f linear_accel_imu = vec_subtract_f(proc_state->accel_phys_units, proc_state->filtered_gravity_from_kf);
            float linear_accel_mag = vec_magnitude_f(linear_accel_imu);
            if (gyro_mag_bias_corrected < GYRO_STILL_THRESHOLD * 2.0f && linear_accel_mag > ACCEL_MOTION_THRESHOLD) {
                if (proc_state->cal_state == CAL_STATE_FORWARD_PENDING) {
                    proc_state->cal_state = CAL_STATE_FORWARD_SAMPLING;
                    proc_state->forward_samples_collected = 0; proc_state->forward_sum_imu = (Vector3D_f){0,0,0};
                    ES_LOGI(TAG, "Starting forward dir sampling.");
                }
                Vector3D_f corrected_linear_accel = vec_subtract_f(linear_accel_imu,
                                                vec_scale_f(proc_state->learned_gravity_imu, vec_dot_f(linear_accel_imu, proc_state->learned_gravity_imu)));
                proc_state->forward_sum_imu = vec_add_f(proc_state->forward_sum_imu, vec_normalize_f(corrected_linear_accel));
                proc_state->forward_samples_collected++;
                if (proc_state->forward_samples_collected >= CALIBRATION_SAMPLES_REQUIRED) {
                    proc_state->learned_forward_imu = vec_normalize_f(vec_scale_f(proc_state->forward_sum_imu, 1.0f / proc_state->forward_samples_collected));
                    proc_state->learned_forward_imu = vec_normalize_f(vec_subtract_f(proc_state->learned_forward_imu,
                                                vec_scale_f(proc_state->learned_gravity_imu, vec_dot_f(proc_state->learned_forward_imu, proc_state->learned_gravity_imu))));
                    proc_state->cal_state = CAL_STATE_ALL_DONE;
                    ES_LOGI(TAG, "Forward cal DONE. Fx:%d Fy:%d Fz:%d (x100)",
                           (int)(proc_state->learned_forward_imu.x*100), (int)(proc_state->learned_forward_imu.y*100), (int)(proc_state->learned_forward_imu.z*100));
                }
            }
        } break;
        case CAL_STATE_ALL_DONE: break;
    }

    if (proc_state->cal_state >= CAL_STATE_GRAVITY_DONE) {
        float angle = angle_between_vectors_f(vec_normalize_f(proc_state->filtered_gravity_from_kf), proc_state->learned_gravity_imu);
        
        // 检查加速度大小是否在合理范围内
        float accel_mag = vec_magnitude_f(proc_state->accel_phys_units);
        bool accel_valid = (accel_mag > (GRAVITY_MAGNITUDE_EXPECTED - GRAVITY_MAGNITUDE_TOLERANCE) && 
                           accel_mag < (GRAVITY_MAGNITUDE_EXPECTED + GRAVITY_MAGNITUDE_TOLERANCE));
        
        // 检查陀螺仪是否稳定
        float gyro_mag = vec_magnitude_f(proc_state->filtered_gyro_bias_corrected);
        bool gyro_stable = (gyro_mag < GYRO_STILL_THRESHOLD * 2.0f);
        
        if (angle > FALL_ANGLE_THRESHOLD_RAD && accel_valid && gyro_stable) {
            proc_state->fall_detection_count++;
            proc_state->fall_recovery_count = 0;
            
            if (proc_state->fall_detection_count >= FALL_DETECTION_SAMPLES) {
                proc_state->is_fall_detected = true;
                proc_state->is_fallen = true;
                ES_LOGW(TAG, "!!! FALL DETECTED !!! Angle_x100: %d, Accel: %d, Gyro: %d", 
                        (int)(angle * 100.0f), (int)(accel_mag * 100.0f), (int)(gyro_mag * 1000.0f));
            }
        } else {
            proc_state->fall_detection_count = 0;
            
            if (proc_state->is_fall_detected) {
                proc_state->fall_recovery_count++;
                
                if (proc_state->fall_recovery_count >= FALL_RECOVERY_SAMPLES) {
                    proc_state->is_fall_detected = false;
                    proc_state->is_fallen = false;
                    ES_LOGI(TAG, "Fall recovery detected. Angle_x100: %d", (int)(angle * 100.0f));
                }
            }
        }
    }
}

// --- Sensor Read Task (Coroutine) ---
static es_async_t lsm6dsl_sensor_read_task(es_coro_t *coro, void *ctx)
{
    lsm6dsl_data_ctx_t *data_ctx = (lsm6dsl_data_ctx_t *)ctx;
    
    static uint32_t current_loop_entry_tick_ms = 0;
    static uint32_t prev_loop_entry_tick_ms = 0;
    // Initialize dt_s to a sensible default. It will be recalculated after the first wait.
    float dt_s = (float)SENSOR_READ_INTERVAL_MS / 1000.0f; 

    static uint32_t last_main_log_ms = 0;

    es_co_begin(coro);
    
    prev_loop_entry_tick_ms = es_os_get_tick_ms(); // Initialize for the very first dt calculation

    while (1) {
        es_co_yield;
        last_main_log_ms = es_os_get_tick_ms();
//         // Timestamp for es_co_wait should be taken just before the wait
//         uint32_t wait_timeout_start_ms = es_os_get_tick_ms(); 

//         current_loop_entry_tick_ms = es_os_get_tick_ms(); // Effective start of this iteration's work

//         if (prev_loop_entry_tick_ms != 0) { // Avoid using uninitialized prev_loop_entry_tick_ms
//             dt_s = (float)(current_loop_entry_tick_ms - prev_loop_entry_tick_ms) / 1000.0f;
//         } else {
//             // This case implies es_os_get_tick_ms() returned 0 when prev_loop_entry_tick_ms was initialized.
//             // Use default dt. The next iteration will use the calculated dt.
//             dt_s = (float)SENSOR_READ_INTERVAL_MS / 1000.0f;
//         }
//         uint32_t temp_prev_for_log = prev_loop_entry_tick_ms; // Store for logging
//         prev_loop_entry_tick_ms = current_loop_entry_tick_ms; // Prepare for *next* iteration's dt calculation

//         // --- Debug log for dt calculation (BEFORE safety check) ---
//         static uint32_t last_dt_debug_log_ms = 0;
//         if (current_loop_entry_tick_ms - last_dt_debug_log_ms > 200) { // Log roughly every 200ms
//             last_dt_debug_log_ms = current_loop_entry_tick_ms;
//             ES_LOGD(TAG, "DT_RAW: cur_ms=%u, prev_ms=%u, dt_calc_us=%d",
//                     (unsigned int)current_loop_entry_tick_ms,
//                     (unsigned int)temp_prev_for_log, 
//                     (int)(dt_s * 1000000.0f)); // Log dt in microseconds
//         }

//         // --- dt_s safety check (Primary check in task) ---
//         if (dt_s <= 0.00001f ) { // 10 microseconds
//             // ES_LOGW(TAG, "TASK_DT_SML: dt_s raw %dus too small/0. Using default %ums.",
//             //         (int)(dt_s * 1000000.0f), (unsigned int)SENSOR_READ_INTERVAL_MS);
//             dt_s = (float)SENSOR_READ_INTERVAL_MS / 1000.0f;
//         } else if (dt_s > ((float)SENSOR_READ_INTERVAL_MS * 10.0f / 1000.0f)) { // Allow up to 10x expected interval
//             //  ES_LOGW(TAG, "TASK_DT_LRG: dt_s raw %dms too large (>10x). Using default %ums.",
//             //         (int)(dt_s * 1000.0f), (unsigned int)SENSOR_READ_INTERVAL_MS);
//             dt_s = (float)SENSOR_READ_INTERVAL_MS / 1000.0f;
//         }

//         // --- Read Raw Sensor Data ---
// //        int bsp_err = 0;
        es_co_await_ex(err, BSP_LSM6DSL_Co_ReadAccel, &data_ctx->raw_lsm_accel);
// //        if(bsp_err != 0) { ES_LOGE(TAG, "Fail Accel Read, err:%d", bsp_err); /* Error handling */ }
        es_co_await_ex(err, BSP_LSM6DSL_Co_ReadGyro, &data_ctx->raw_lsm_gyro);
// //        if(bsp_err != 0) { ES_LOGE(TAG, "Fail Gyro Read, err:%d", bsp_err); /* Error handling */ }

        ES_PRINTF_I(TAG,"Accel: %d %d %d, Gyro: %d %d %d", data_ctx->raw_lsm_accel.x, data_ctx->raw_lsm_accel.y, data_ctx->raw_lsm_accel.z, data_ctx->raw_lsm_gyro.x, data_ctx->raw_lsm_gyro.y, data_ctx->raw_lsm_gyro.z);

//         // --- Convert Raw LSB Data to Physical Units ---
//         Vector3D_f accel_phys_units, gyro_phys_units;
//         accel_phys_units.x = ACCEL_RAW_TO_MS2(data_ctx->raw_lsm_accel.x);
//         accel_phys_units.y = ACCEL_RAW_TO_MS2(data_ctx->raw_lsm_accel.y);
//         accel_phys_units.z = ACCEL_RAW_TO_MS2(data_ctx->raw_lsm_accel.z);
//         gyro_phys_units.x = GYRO_RAW_TO_RADS(data_ctx->raw_lsm_gyro.x);
//         gyro_phys_units.y = GYRO_RAW_TO_RADS(data_ctx->raw_lsm_gyro.y);
//         gyro_phys_units.z = GYRO_RAW_TO_RADS(data_ctx->raw_lsm_gyro.z);
        
//         // --- Update IMU Processor State ---
//         imu_processor_state_update(&data_ctx->imu_proc, accel_phys_units, gyro_phys_units, dt_s);

//         // --- Logging (with integers) ---
//         static uint32_t last_main_log_ms = 0;
//         if (current_loop_entry_tick_ms - last_main_log_ms > 1000) { // Log approx every 1 second
//             last_main_log_ms = current_loop_entry_tick_ms;
//             ES_LOGD(TAG, "R:%d P:%d Gxb:%d Gyb:%d St:%d Fall:%d",
//                 (int)(data_ctx->imu_proc.current_roll_kf * 180.0f/M_PI * 100.0f),
//                 (int)(data_ctx->imu_proc.current_pitch_kf * 180.0f/M_PI * 100.0f),
//                 (int)(data_ctx->imu_proc.kf_roll.bias * 1000.0f * 100.0f), 
//                 (int)(data_ctx->imu_proc.kf_pitch.bias * 1000.0f * 100.0f),
//                 (int)data_ctx->imu_proc.cal_state, (int)data_ctx->imu_proc.is_fallen);
//             if(data_ctx->imu_proc.cal_state == CAL_STATE_ALL_DONE){
//                  ES_LOGD(TAG, "LearnedG(x%d y%d z%d) F(x%d y%d z%d) (all x100)",
//                     (int)(data_ctx->imu_proc.learned_gravity_imu.x*100), (int)(data_ctx->imu_proc.learned_gravity_imu.y*100), (int)(data_ctx->imu_proc.learned_gravity_imu.z*100),
//                     (int)(data_ctx->imu_proc.learned_forward_imu.x*100), (int)(data_ctx->imu_proc.learned_forward_imu.y*100), (int)(data_ctx->imu_proc.learned_forward_imu.z*100));
//             }
//         }
//         bsp_err:
//         es_co_wait(es_os_check_timeout(wait_timeout_start_ms, SENSOR_READ_INTERVAL_MS)); 
err:
        es_co_wait(es_os_check_timeout(last_main_log_ms, 1000));
    
        
    }
    es_co_end;
}

// // --- Initialization Function ---
int es_lsm6dsl_init(void) {
    int ret = 0;
    BSP_LSM6DSL_Init();
    memset(&sensor_data_ctx, 0, sizeof(lsm6dsl_data_ctx_t));
    imu_processor_state_init(&sensor_data_ctx.imu_proc);
    sensor_task.func = lsm6dsl_sensor_read_task;
    sensor_task.ctx = &sensor_data_ctx;
    ret = es_scheduler_task_add(es_scheduler_get_default(), &sensor_task);
    if (ret != 0) {
        ES_LOGE(TAG, "Fail add LSM6DSL task, err:%d", ret); return -1;
    }
    ES_LOGI(TAG, "LSM6DSL orientation initialized. Task interval: %ums.", (unsigned int)SENSOR_READ_INTERVAL_MS);
    ES_LOGW(TAG, "VERIFY LSM6DSL SENSITIVITY & TASK INTERVAL! Check DT_RAW logs.");
    return 0;
}

// --- Public API functions ---
bool es_lsm6dsl_is_vehicle_fallen(void) { return sensor_data_ctx.imu_proc.is_fallen; }
Vector3D_f es_lsm6dsl_get_learned_gravity(void) {
    return (sensor_data_ctx.imu_proc.cal_state >= CAL_STATE_GRAVITY_DONE) ? \
           sensor_data_ctx.imu_proc.learned_gravity_imu : (Vector3D_f){0,0,0};
}
Vector3D_f es_lsm6dsl_get_learned_forward(void) {
    return (sensor_data_ctx.imu_proc.cal_state == CAL_STATE_ALL_DONE) ? \
           sensor_data_ctx.imu_proc.learned_forward_imu : (Vector3D_f){0,0,0};
}
CalibrationState_t es_lsm6dsl_get_calibration_state(void){ return sensor_data_ctx.imu_proc.cal_state; }
void es_lsm6dsl_get_orientation_angles_deg_scaled(int* roll_deg_x100, int* pitch_deg_x100) {
    if (roll_deg_x100) *roll_deg_x100 = (int)(sensor_data_ctx.imu_proc.current_roll_kf * 180.0f / M_PI * 100.0f);
    if (pitch_deg_x100) *pitch_deg_x100 = (int)(sensor_data_ctx.imu_proc.current_pitch_kf * 180.0f / M_PI * 100.0f);
}
// --- END OF MODIFIED hc32_lsm6dsl.c ---
