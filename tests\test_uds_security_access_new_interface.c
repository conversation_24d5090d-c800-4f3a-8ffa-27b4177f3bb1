/**
 * @file test_uds_security_access_new_interface.c
 * @brief Test for the new UDS security access interface with callback
 * <AUTHOR> MCU Team
 * @date 2024
 */

#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

// Test result tracking
static int tests_run = 0;
static int tests_passed = 0;

#define TEST_ASSERT_EQUAL(expected, actual, message) \
    do { \
        tests_run++; \
        if ((expected) == (actual)) { \
            tests_passed++; \
            printf("✓ %s\n", message); \
        } else { \
            printf("✗ %s (expected: %d, actual: %d)\n", message, (int)(expected), (int)(actual)); \
        } \
    } while(0)

// Mock callback that generates a simple key
static uint16_t test_key_callback(uint8_t level, const uint8_t *seed, uint16_t seed_length,
                                 uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
    printf("Key callback called: level=%d, seed_len=%d, max_key_len=%d\n", 
           level, seed_length, max_key_length);
    
    if (!seed || seed_length == 0 || !key_buffer || max_key_length < 4) {
        return 0;
    }
    
    // Generate a simple 4-byte key
    key_buffer[0] = seed[0] ^ 0xAA;
    key_buffer[1] = (seed_length > 1) ? (seed[1] ^ 0xBB) : 0xBB;
    key_buffer[2] = (seed_length > 2) ? (seed[2] ^ 0xCC) : 0xCC;
    key_buffer[3] = (seed_length > 3) ? (seed[3] ^ 0xDD) : 0xDD;
    
    printf("Generated key: %02X %02X %02X %02X\n", 
           key_buffer[0], key_buffer[1], key_buffer[2], key_buffer[3]);
    
    return 4;
}

// Mock callback that fails
static uint16_t failing_callback(uint8_t level, const uint8_t *seed, uint16_t seed_length,
                                uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
    printf("Failing callback called - returning 0\n");
    return 0;
}

/**
 * @brief Test callback interface
 */
static void test_callback_interface(void)
{
    printf("\n--- Testing Callback Interface ---\n");
    
    // Test data
    uint8_t seed[] = {0x12, 0x34, 0x56, 0x78};
    uint8_t key_buffer[16];
    
    // Test successful callback
    uint16_t key_len = test_key_callback(1, seed, sizeof(seed), key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(4, key_len, "Callback should return 4-byte key");
    
    // Verify key generation
    uint8_t expected_key[] = {0x12 ^ 0xAA, 0x34 ^ 0xBB, 0x56 ^ 0xCC, 0x78 ^ 0xDD};
    bool key_correct = (memcmp(key_buffer, expected_key, 4) == 0);
    TEST_ASSERT_EQUAL(1, key_correct, "Generated key should match expected pattern");
    
    // Test failing callback
    key_len = failing_callback(1, seed, sizeof(seed), key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(0, key_len, "Failing callback should return 0");
}

/**
 * @brief Test parameter validation
 */
static void test_parameter_validation(void)
{
    printf("\n--- Testing Parameter Validation ---\n");
    
    uint8_t seed[] = {0x12, 0x34, 0x56, 0x78};
    uint8_t key_buffer[16];
    uint16_t key_len;
    
    // Test NULL seed
    key_len = test_key_callback(1, NULL, 4, key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(0, key_len, "Should fail with NULL seed");
    
    // Test zero seed length
    key_len = test_key_callback(1, seed, 0, key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(0, key_len, "Should fail with zero seed length");
    
    // Test NULL key buffer
    key_len = test_key_callback(1, seed, sizeof(seed), NULL, 16, NULL);
    TEST_ASSERT_EQUAL(0, key_len, "Should fail with NULL key buffer");
    
    // Test insufficient buffer size
    key_len = test_key_callback(1, seed, sizeof(seed), key_buffer, 2, NULL);
    TEST_ASSERT_EQUAL(0, key_len, "Should fail with insufficient buffer size");
}

/**
 * @brief Test direct buffer usage (simulating the new implementation)
 */
static void test_direct_buffer_usage(void)
{
    printf("\n--- Testing Direct Buffer Usage ---\n");

    // Simulate UDS request buffer (like UDS_REQUEST_BUFFER(conn))
    uint8_t tx_buffer[64];

    // Test seed request phase (odd level)
    tx_buffer[0] = 0x27;  // Security Access SID
    tx_buffer[1] = 0x01;  // Odd level for seed request

    printf("Seed request: %02X %02X\n", tx_buffer[0], tx_buffer[1]);

    // Test key sending phase (even level = odd + 1)
    tx_buffer[0] = 0x27;  // Security Access SID
    tx_buffer[1] = 0x02;  // Even level for key sending (1 + 1)

    // Simulate seed data
    uint8_t seed[] = {0xAB, 0xCD, 0xEF, 0x01};

    // Call callback to generate key directly into tx_buffer[2]
    uint16_t max_key_length = sizeof(tx_buffer) - 2;
    uint16_t key_length = test_key_callback(1, seed, sizeof(seed),
                                           &tx_buffer[2], max_key_length, NULL);

    TEST_ASSERT_EQUAL(4, key_length, "Should generate 4-byte key");

    // Verify the complete request buffer
    TEST_ASSERT_EQUAL(0x27, tx_buffer[0], "SID should be correct");
    TEST_ASSERT_EQUAL(0x02, tx_buffer[1], "Level should be correct (odd + 1)");

    // Verify key was written directly to buffer
    uint8_t expected_key[] = {0xAB ^ 0xAA, 0xCD ^ 0xBB, 0xEF ^ 0xCC, 0x01 ^ 0xDD};
    bool key_correct = (memcmp(&tx_buffer[2], expected_key, 4) == 0);
    TEST_ASSERT_EQUAL(1, key_correct, "Key should be written directly to tx buffer");

    printf("Complete key request: ");
    for (int i = 0; i < 2 + key_length; i++) {
        printf("%02X ", tx_buffer[i]);
    }
    printf("\n");
}

/**
 * @brief Test with different seed lengths
 */
static void test_different_seed_lengths(void)
{
    printf("\n--- Testing Different Seed Lengths ---\n");

    uint8_t key_buffer[16];
    uint16_t key_len;

    // Test with 1-byte seed
    uint8_t seed1[] = {0x12};
    key_len = test_key_callback(1, seed1, sizeof(seed1), key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(4, key_len, "Should handle 1-byte seed");

    // Test with 8-byte seed
    uint8_t seed8[] = {0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0};
    key_len = test_key_callback(1, seed8, sizeof(seed8), key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(4, key_len, "Should handle 8-byte seed");

    // Test with large seed
    uint8_t seed_large[32];
    memset(seed_large, 0x55, sizeof(seed_large));
    key_len = test_key_callback(1, seed_large, sizeof(seed_large), key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(4, key_len, "Should handle large seed");
}

/**
 * @brief Test level validation (odd/even)
 */
static void test_level_validation(void)
{
    printf("\n--- Testing Level Validation ---\n");

    // Test valid odd levels
    printf("Valid odd levels: 1, 3, 5, 7, etc. should be accepted\n");
    printf("Level 1: %s\n", (1 & 0x01) ? "✓ Valid (odd)" : "✗ Invalid (even)");
    printf("Level 3: %s\n", (3 & 0x01) ? "✓ Valid (odd)" : "✗ Invalid (even)");
    printf("Level 5: %s\n", (5 & 0x01) ? "✓ Valid (odd)" : "✗ Invalid (even)");

    // Test invalid even levels
    printf("Invalid even levels: 2, 4, 6, 8, etc. should be rejected\n");
    printf("Level 2: %s\n", (2 & 0x01) ? "✗ Should be rejected" : "✓ Correctly rejected (even)");
    printf("Level 4: %s\n", (4 & 0x01) ? "✗ Should be rejected" : "✓ Correctly rejected (even)");
    printf("Level 6: %s\n", (6 & 0x01) ? "✗ Should be rejected" : "✓ Correctly rejected (even)");

    // Test level + 1 calculation
    uint8_t odd_levels[] = {1, 3, 5, 7, 9};
    for (int i = 0; i < sizeof(odd_levels); i++) {
        uint8_t seed_level = odd_levels[i];
        uint8_t key_level = seed_level + 1;
        printf("Seed level %d -> Key level %d (%s)\n",
               seed_level, key_level,
               ((key_level & 0x01) == 0) ? "✓ Even" : "✗ Should be even");
        TEST_ASSERT_EQUAL(0, key_level & 0x01, "Key level should be even");
    }
}

/**
 * @brief Main test function
 */
int main(void)
{
    printf("=== UDS Security Access New Interface Tests ===\n");
    printf("Testing the new callback-based security access implementation\n");
    printf("Key features:\n");
    printf("- Callback generates key from ECU seed\n");
    printf("- Direct buffer usage (no static variables)\n");
    printf("- Automatic seed request and key sending\n\n");
    
    // Run tests
    test_callback_interface();
    test_parameter_validation();
    test_direct_buffer_usage();
    test_different_seed_lengths();
    test_level_validation();
    
    // Print results
    printf("\n=== Test Results ===\n");
    printf("Tests run: %d\n", tests_run);
    printf("Tests passed: %d\n", tests_passed);
    printf("Tests failed: %d\n", tests_run - tests_passed);
    
    if (tests_passed == tests_run) {
        printf("✓ All tests passed!\n");
        printf("\nThe new UDS security access interface is working correctly:\n");
        printf("- Callbacks receive seed from ECU and generate keys\n");
        printf("- Keys are written directly to tx buffer (no extra memory)\n");
        printf("- Proper parameter validation is in place\n");
        return 0;
    } else {
        printf("✗ Some tests failed!\n");
        return 1;
    }
}
