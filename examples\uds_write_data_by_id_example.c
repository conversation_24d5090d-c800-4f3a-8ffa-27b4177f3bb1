/**
 * @file uds_write_data_by_id_example.c
 * @brief Example demonstrating the UDS Write Data By Identifier service
 * <AUTHOR> MCU Team
 * @date 2025-01-20
 */

#include "es_uds.h"
#include "es_printf.h"
#include "es_coroutine.h"

static const char *TAG = "UDS_WRITE_EXAMPLE";

/**
 * @brief Example demonstrating basic write data by identifier usage
 */
static es_async_t write_data_example(es_coro_t *coro) {
    es_co_begin(coro);
    
    // Initialize UDS connection
    static es_uds_connection_t conn;
    es_uds_error_t result = es_uds_connection_init(&conn, 0x7E0, 0x7E8);
    if (result != ES_UDS_OK) {
        ES_PRINTF_I(TAG, "Failed to initialize UDS connection");
        es_co_err;
    }
    
    ES_PRINTF_I(TAG, "=== UDS Write Data By Identifier Example ===");
    
    // Example 1: Write configuration data
    uint8_t config_data[] = {0x01, 0x02, 0x03, 0x04};
    uint16_t config_did = 0xF190;  // Example DID for configuration
    
    ES_PRINTF_I(TAG, "Writing configuration data to DID 0x%04X", config_did);
    es_co_await_ex(err, es_uds_write_data_by_identifier, &conn, config_did, config_data, sizeof(config_data));
    
    // Verify response DID
    uint16_t response_did = UDS_WRITE_DATA_BY_ID_DID(&conn);
    if (response_did == config_did) {
        ES_PRINTF_I(TAG, "Write successful - DID 0x%04X confirmed", response_did);
    } else {
        ES_PRINTF_I(TAG, "Warning: Response DID 0x%04X doesn't match request DID 0x%04X", 
                    response_did, config_did);
    }
    
    // Example 2: Write calibration parameters
    uint8_t calibration_data[] = {0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0};
    uint16_t calibration_did = 0xF1A0;  // Example DID for calibration
    
    ES_PRINTF_I(TAG, "Writing calibration data to DID 0x%04X", calibration_did);
    es_co_await_ex(err, es_uds_write_data_by_identifier, &conn, calibration_did, 
                   calibration_data, sizeof(calibration_data));
    
    response_did = UDS_WRITE_DATA_BY_ID_DID(&conn);
    ES_PRINTF_I(TAG, "Calibration write successful - DID 0x%04X", response_did);
    
    // Example 3: Write single byte parameter
    uint8_t single_byte = 0xFF;
    uint16_t param_did = 0xF1B0;  // Example DID for single parameter
    
    ES_PRINTF_I(TAG, "Writing single byte parameter to DID 0x%04X", param_did);
    es_co_await_ex(err, es_uds_write_data_by_identifier, &conn, param_did, &single_byte, 1);
    
    response_did = UDS_WRITE_DATA_BY_ID_DID(&conn);
    ES_PRINTF_I(TAG, "Parameter write successful - DID 0x%04X", response_did);
    
    ES_PRINTF_I(TAG, "=== All write operations completed successfully ===");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Write data by identifier operation failed");
    );
}

/**
 * @brief Example demonstrating write followed by read verification
 */
static es_async_t write_and_verify_example(es_coro_t *coro) {
    es_co_begin(coro);
    
    // Initialize UDS connection
    static es_uds_connection_t conn;
    es_uds_error_t result = es_uds_connection_init(&conn, 0x7E0, 0x7E8);
    if (result != ES_UDS_OK) {
        ES_PRINTF_I(TAG, "Failed to initialize UDS connection");
        es_co_err;
    }
    
    ES_PRINTF_I(TAG, "=== Write and Verify Example ===");
    
    // Test data
    uint8_t test_data[] = {0xAA, 0xBB, 0xCC, 0xDD};
    uint16_t test_did = 0xF1C0;  // Example DID for test data
    
    // Write data
    ES_PRINTF_I(TAG, "Writing test data to DID 0x%04X", test_did);
    es_co_await_ex(err, es_uds_write_data_by_identifier, &conn, test_did, test_data, sizeof(test_data));
    
    uint16_t write_response_did = UDS_WRITE_DATA_BY_ID_DID(&conn);
    ES_PRINTF_I(TAG, "Write completed - response DID 0x%04X", write_response_did);
    
    // Read back to verify
    ES_PRINTF_I(TAG, "Reading back data from DID 0x%04X for verification", test_did);
    es_co_await_ex(err, es_uds_read_data_by_identifier, &conn, test_did);
    
    uint16_t read_response_did = UDS_READ_DATA_BY_ID_DID(&conn);
    uint8_t *read_data = UDS_READ_DATA_BY_ID_DATA(&conn);
    uint16_t read_length = UDS_READ_DATA_BY_ID_LENGTH(&conn);
    
    ES_PRINTF_I(TAG, "Read completed - DID 0x%04X, length %d", read_response_did, read_length);
    
    // Verify data
    if (read_length == sizeof(test_data)) {
        bool data_match = true;
        for (int i = 0; i < sizeof(test_data); i++) {
            if (read_data[i] != test_data[i]) {
                data_match = false;
                break;
            }
        }
        
        if (data_match) {
            ES_PRINTF_I(TAG, "✓ Data verification successful - write/read data match");
        } else {
            ES_PRINTF_I(TAG, "✗ Data verification failed - write/read data mismatch");
        }
    } else {
        ES_PRINTF_I(TAG, "✗ Data verification failed - length mismatch (expected %d, got %d)", 
                    sizeof(test_data), read_length);
    }
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Write and verify operation failed");
    );
}

/**
 * @brief Main example function
 */
es_async_t uds_write_data_by_id_example_main(es_coro_t *coro) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "Starting UDS Write Data By Identifier examples");
    
    // Run basic write example
    es_co_await_ex(err, write_data_example);
    
    // Run write and verify example
    es_co_await_ex(err, write_and_verify_example);
    
    ES_PRINTF_I(TAG, "All UDS Write Data By Identifier examples completed");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "UDS Write Data By Identifier examples failed");
    );
}
