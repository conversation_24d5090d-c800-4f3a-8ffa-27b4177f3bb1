//
// Created by 67376 on 2022/12/30.
//

#ifndef CMAKELISTS_TXT_XFRAME_H
#define CMAKELISTS_TXT_XFRAME_H

#include <stdint.h>
#include "es_coro.h"


#ifdef __cplusplus
extern "C" {
#endif

#define META_DATA    (1 << 0) // Data frame
#define META_DEV_REQ (1 << 1) // Device request frame
#define META_DEV_RSP (1 << 2) // Device response frame
#define META_SRV_REQ (1 << 3) // Server request frame
#define META_SRV_RSP (1 << 4) // Server response frame
#define META_CONNECT (1 << 8) // Connect frame
#define META_CONNACK (1 << 9) // Connect acknowledgment frame


typedef uint32_t x32;
typedef uint16_t x16;
typedef uint8_t x8;
typedef int32_t i32;
typedef int16_t i16;
typedef int8_t i8;
typedef uint32_t x24;
typedef uint32_t c_time;

typedef int (*c_encode)(uint8_t *data, int size, int pos);

#define STR_NO_EXPAND(A)    #A
#define STR(A)              STR_NO_EXPAND(A)

#define CAT_NO_EXPAND(A, B) A##B
#define CAT(A, B)           CAT_NO_EXPAND(A, B)

#define TSP_FRAME_HEAD \
    X(head, 0x00, 0x00, M(x16, start) M(x16, len) M(s_arr(15), imei) M(x8, type) M(x16, seq) M(x16, effect))


#define BLE_FRAME_HEAD \
    X(ble_head, 0x00, 0x00, M(x16, start) M(x8, len)  M(x8, crypto) M(x8, seq))

#define TSP_RX_FRAMES \
	X(connack,                                  0x02,      (META_CONNACK),                		M(x8,result) M(x32,timestamp)		)\
	X(srv_rsp_upgrade_data,                     0xf001,    (META_SRV_RSP),                		M(x8,type) M(x32,byte0) M(x32,offset)		)\
	X(srv_rsp_verify,                           0xf003,    (META_SRV_RSP),                		M(x8,type) M(c_str(64),filename) M(x32,file_size)		)\
    X(srv_rsp_agps,                             0xf004,    (META_SRV_RSP),                      M(x8, data_mode) M(x8, cur_pack_num) M(x8, total_pack_num) M(x16, cur_pack_len) M(c_str(960),star_data))\
    X(srv_utc_time_req,                         0xf005,    (META_SRV_RSP),                		M(x32,timestamp)		)\
    X(srv_rsp_weather_info,                     0xf006,    (META_SRV_RSP),                      M(x32, city_code) M(x8, weather_code) M(x8, temp))\
	X(srv_req_upgrade_request,                  0xf100,    (META_SRV_REQ),                		M(x8,type) M(c_str(128),addr) M(c_str(64),filename)	)\
	X(srv_req_set_param,                        0xf102,    (META_SRV_REQ),                		)\
	X(srv_req_get_param,                        0xf103,    (META_SRV_REQ),                		)\
    X(srv_req_del_mac,                          0xf114,    (META_SRV_REQ),                      M(x8, mac_num) M(x8, mac_length) M(c_str(64),mac_data)  )\
	X(srv_req_find_car,                         0xf115,    (META_SRV_REQ),                		M(x8,byte0)		)\
	X(srv_req_ctl_lock,                         0xf113,    (META_SRV_REQ),                		M(x8,value) M(x8,cnt)		)\
    X(srv_req_manual_defence_switch,            0xf120,    (META_SRV_REQ),                		M(x8,defence_switch)		)\
    X(srv_req_blemac_dismatch,                  0xf114,    (META_SRV_REQ),                      M(x8,mac_num) M(x8,mac_length) M(c_str(13), mac_data))\
	X(srv_req_check_upgrade_condition,          0xf126,    (META_SRV_REQ),                		M(x8,type) M(x32,task_id)		)\
	X(srv_req_upgrade_device,                   0xf127,    (META_SRV_REQ),                		M(x8,type) M(x32,crc)		)\
    X(srv_req_seat_lock,                        0xf117,    (META_SRV_REQ),                      M(x8,control) M(x8,cnt))\
    X(srv_req_defence_set,                      0xf121,    (META_SRV_REQ),                      M(x8,sense) M(x8,volumn))\
    X(srv_req_seal_control,                     0xf128,    (META_SRV_REQ),                      M(x8,seal_control) M(x8,seal_time))\
    X(srv_req_seal_state,                       0xf129,    (META_SRV_REQ),                      M(x8,seal_state))\
	X(srv_rsp_common,                           0xFFFF,    (META_SRV_RSP),                		)\

#define TSP_TX_FRAMES \
    X(connect,                                  0x01,      (META_CONNECT),                		M(x16,proto) M(x16,mcu_hardver) M(c_str(64),mcu_softver) M(c_str(64),boot_softver) M(c_str(64),mdm_softver) M(x32,time)		)\
    X(engine_on,                                0x0001,    (META_DATA),        		            M(x8,type) M(c_time,time) M(gps_t,gps)		)\
    X(engine_off,                               0x0002,    (META_DATA),        		            M(x8,type) M(c_time,time) M(gps_t,gps)		)\
    X(theft_start,                              0x0003,    (META_DATA),                   		M(c_time,time) M(gps_t,gps)		)\
    X(theft_end,                                0x0004,    (META_DATA),                   		M(c_time,time) M(gps_t,gps)		)\
    X(diagnostic_data_stream,                   0x0009,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x8,general_data_validity) M(c_arr(33),general_data) M(x8,private_data_validity) M(c_arr(125),private_data)		)\
    X(lock_sync,                                0x000C,    (META_DATA),                   		M(c_time,time) M(x8,status)		)\
    X(vehicle_fault_light,                      0x000D,    (META_DATA),                   		M(c_time,time) M(x16,status) M(gps_t,gps)		)\
    X(dtc,                                      0x000E,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x8,version) M(x8,num) M(c_arr(96),data)		)\
    X(real_time,                                0x0010,    (META_DATA),                   		M(c_time,time) M(x8,car_state) M(x8,dev_state) M(x8,gsm_signal) M(x8,gps_signal) M(x32,battery) M(x8, gps_num) M(x8, charge_state))\
    X(realtime_vehicle_condition,               0x0012,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(c_arr(255),data)		)\
    X(rapid_acceleration,                       0x0100,    (META_DATA),                   		M(c_time,time) M(x8,pre_speed) M(x8,post_speed) M(x8,max_acceleration_value) M(gps_t,gps)		)\
    X(rapid_deceleration,                       0x0101,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x8,pre_speed) M(x8,post_speed) M(x8,max_deceleration_value)		)\
    X(rapid_turn,                               0x0102,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x8,max_turn_angle)		)\
    X(brake,                                    0x0110,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x8,brake_time)		)\
    X(fatigue_driving,                          0x0104,    (META_DATA),                   		M(c_time,time) M(gps_t,gps)		)\
    X(over_speed,                               0x0105,    (META_DATA),                   		M(c_time,time) M(x8,max_speed) M(x32,start_time) M(x32,end_time) M(gps_t,gps)		)\
    X(location,                                 0x0200,    (META_DATA),                   		M(x8, cnt) M(gps_t, gps) M(x16, slope_angle)		)\
    X(lbs,                                      0x0201,    (META_DEV_REQ),                   	M(c_time,time) M(x32,cellid) M(x32,lac) M(x32,mccmnc) M(x8,rssi) M(x8,network_type)		)\
    X(sleep,                                    0x0402,    (META_DATA),                   		M(c_time,time) M(x16,voltage) M(x8,voltage_source) M(gps_t,gps) M(x8,mode)		)\
    X(wakeup,                                   0x0403,    (META_DATA),                   		M(c_time,time) M(x16,voltage) M(x8,voltage_source) M(x8,wake_type) M(gps_t,gps)		)\
    X(gps_long_time_no_fix,                     0x0404,    (META_DATA),                   		M(c_time,time)		)\
    X(reset,                                    0x0405,    (META_DATA),                   		M(c_time,time) M(x8,power_on_type)		)\
    X(upgrade_status,                           0x0406,    (META_DATA),                   		M(x8,type) M(c_time,time) M(x8,status)		)\
    X(device_info,                              0x040C,    (META_DEV_REQ),                   	M(x32,time) M(c_str(8),phone) M(c_str(21),iccid) M(c_str(16),imsi) M(x8,gsm_signal) M(x8,gps_signal) M(c_str(32),bver) M(c_str(32),bname)		)\
    X(ecu_info_report,                          0x0408,    (META_DATA),                   		M(x32,time) M(x8,type)  M(x32,flash) M(c_str(64),ecu_hard) M(c_str(64),ecu_soft) M(c_str(64),ecu_vin)		)\
    X(sk,                                       0x0412,    (META_DATA),                   		M(c_time,time) M(c_str(7),sk_number)		)\
    X(log_report,                               0x0414,    (META_DATA),                   		M(c_time,time) M(x8,switch_state) M(x32,error_flag) M(c_arr(5),rssi) M(x16, reserved) M(x16, reserved2)       )\
    X(log_data_report,                          0x0415,    (META_DATA),                   		M(c_time,time) M(f_arr(300),data)     )\
    X(long_link_sw,                             0x0416,    (META_DATA),                   		M(c_time,time) M(x8,sw)     )\
    X(ecu_write_percentage,                     0x0417,    (META_DATA),                   		M(c_time,time) M(x8,type) M(x8,result)	)\
    X(low_voltage,                              0x0501,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x8,voltage)		)\
    X(shake,                                    0x0502,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x16,reserved)		)\
    X(collision,                                0x0505,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x8,value)		)\
    X(device_pull_out,                          0x0507,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x8,engine_status)		)\
    X(rollover,                                 0x0509,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(x16,value)		)\
    X(sensor_learn_state,                       0x0510,    (META_DATA),                   		M(c_time,time) M(x8,status)		)\
    X(long_on_unlock,                           0x0511,    (META_DATA),                   		M(c_time,time) M(gps_t,gps)	)\
    X(self_check,                               0x050a,    (META_DATA),                   		M(c_time,time) M(gps_t,gps) M(c_arr(4),result)		)\
    X(device_bat_low,                           0x050b,    (META_DATA),                         M(c_time,time) M(gps_t,gps) M(x8,bat_vol) )\
    X(acc_sw,                                   0x050c,    (META_DATA),                         M(c_time,time) M(x8,status) )\
    X(charge_connector,                         0x050d,    (META_DATA),                         M(c_time,time) M(x8,status) )\
    X(ibox_inner_bat_disconnect,                0x050e,    (META_DATA),                         M(c_time,time))\
    X(gsm_long_time_no_link,                    0x050f,    (META_DATA),                         M(c_time,time))\
    X(large_bat_alarm,                          0x0512,    (META_DATA),                         M(c_time,time) M(gps_t,gps) M(x8, bms_num) M(x8, bms_sn))\
    X(large_bat_lowpower_alarm,                 0x0514,    (META_DATA),                         M(c_time,time) M(gps_t,gps) M(x8, h_l_bms) M(x8, current_soc)     )\
    X(large_bat_charge_full,                    0x0515,    (META_DATA),                         M(c_time,time) M(gps_t,gps) M(x8, bms_type)     )\
    X(veh_info,                                 0x060d,    (META_DATA),                         M(c_time,time) M(x8, soc_h) M(x8, soh_h) M(x8, soc_l) M(x8, soh_l) M(x16, estimate_time) M(x8,ebs_recycle) M(x16,estimate_mileage) M(x32,total_mileage) M(x16,batt_h_vol) M(x16,batt_l_vol) )\
    X(high_frequency,                           0x0610,    (META_DATA),                         M(c_time,time) M(x32,total_mileage) M(x16,front_tire_pressure) M(x8,front_tire_temp) M(x16,rear_tire_pressure) M(x8,rear_tire_temp) M(x8,veh_defence) M(x8,lock_state) M(x8,abs_info) M(x8,smart_sw) M(x8,est_mileage) M(x16,avg_ec) M(x8, p_ready) M(x32, subtotal_mileage_a) M(x32, subtotal_mileage_b) M(x8, vcu_system_state))\
    X(high_frequency_body_data,                 0x061a,    (META_DEV_REQ),                      M(c_arr(128),data) )\
    X(veh_bms_data_h,                           0x0611,    (META_DATA),                         M(c_time,time) M(x8,soc_avg) M(x8,parallel_status) M(x16,total_bms_vol) M(x16,total_bms_cur) M(x16,total_bms_charge_rest_time) M(x16,total_bms_capacity) M(x16,obc_input_value) M(x16,obc_ntc_temp) M(x8,bms_pack_num) M(x8,bms_pos_01) M(x16,bms_vol_01) M(x16,bms_cur_01) M(x8,bms_dc_mos_state_01)\
                                                                                                M(x8,bms_c_mos_state_01) M(x16,soc_percent_01) M(x16,soh_percent_01) M(x16,bms_circle_time_01) M(x16,bms_charge_rest_time_01) M(x16,bms_core_min_vol_01) M(x16,bms_core_max_vol_01) M(x8,bms_core_max_temp_01) M(x8,bms_core_min_temp_01) M(x8,bms_mos_max_temp_01) M(x8,bms_env_temp_01) M(x8,bms_disconnect_01)\
                                                                                                M(x8,bms_pos_02) M(x16,bms_vol_02) M(x16,bms_cur_02) M(x8,bms_dc_mos_state_02) M(x8,bms_c_mos_state_02) M(x16,soc_percent_02) M(x16,soh_percent_02) M(x16,bms_circle_time_02) M(x16,bms_charge_rest_time_02) M(x16,bms_core_min_vol_02) M(x16,bms_core_max_vol_02) M(x8,bms_core_max_temp_02) M(x8,bms_core_min_temp_02) M(x8,bms_mos_max_temp_02) M(x8,bms_env_temp_02) M(x8,bms_disconnect_02)\
                                                                                                M(x8,bms_pos_03) M(x16,bms_vol_03) M(x16,bms_cur_03) M(x8,bms_dc_mos_state_03) M(x8,bms_c_mos_state_03) M(x16,soc_percent_03) M(x16,soh_percent_03) M(x16,bms_circle_time_03) M(x16,bms_charge_rest_time_03) M(x16,bms_core_min_vol_03) M(x16,bms_core_max_vol_03) M(x8,bms_core_max_temp_03) M(x8,bms_core_min_temp_03) M(x8,bms_mos_max_temp_03) M(x8,bms_env_temp_03) M(x8,bms_disconnect_03) M(x8, discharge_pack_num) M(x8, obc_ota_state) M(x8, acc_all_state))\
    X(veh_bms_data_l,                           0x0612,    (META_DATA),                         M(c_time,time) M(x8,bms_num) M(x8, bms_pos_01) M(x8,bms_year_01) M(x8,bms_mon_01) M(x8,bms_day_01) M(x8,bms_01_sn0) M(x8,bms_01_sn1) M(x8,bms_01_sn2) M(x8,bms_01_sn3) M(x8, bms_pos_02) M(x8,bms_year_02) M(x8,bms_mon_02) M(x8,bms_day_02) M(x8,bms_02_sn0) M(x8,bms_02_sn1) M(x8,bms_02_sn2) M(x8,bms_02_sn3) M(x8, bms_pos_03) M(x8,bms_year_03) M(x8,bms_mon_03) M(x8,bms_day_03)\
                                                                                                M(x8,bms_03_sn0) M(x8,bms_03_sn1) M(x8,bms_03_sn2) M(x8,bms_03_sn3))\
    X(fault_code_pcu,                           0x0613,    (META_DATA),                         M(c_time,time) M(x8,fault1) M(x8,fault2) M(x8,fault3)		)\
    X(fault_code_vcu,                           0x0614,    (META_DATA),                         M(c_time,time) M(x8,fault1) M(x8,fault2) M(x8,fault3)		)\
    X(fault_code_obc,                           0x0615,    (META_DATA),                         M(c_time,time) M(x8,fault1) M(x8,fault2) M(x8,fault3)		)\
    X(fault_code_bms,                           0x0616,    (META_DATA),                         M(c_time,time) M(x8,fault1) M(x8,fault2) M(x8,fault3) M(x8,fault4) M(x8,fault5) M(x8,fault6) M(x8,fault7) M(x8,fault8) M(x8,bms_sn)		)\
    X(fault_code_meter,                         0x0619,    (META_DATA),                         M(c_time,time) M(x8,fault1)		)\
    X(low_frequency,                            0x0705,    (META_DATA),                         M(c_time,time)  M(x16,front_wheel_rotation) M(x16,rear_wheel_speed) M(x16,mil_time)		)\
    X(fault_code_ecu,                           0x0702,    (META_DATA),                         M(c_time,time) M(x8,count) M(c_arr(128),fault_data)			)\
    X(fault_code_abs,                           0x0703,    (META_DATA),                         M(c_time,time) M(x8,count) M(c_arr(128),fault_data)		)\
    X(small_battery_alarm,                      0x0513,    (META_DATA),                         M(c_time,time) M(gps_t,gps) M(x8,state)		)\
    X(dev_req_upgrade_data,                     0xf001,    (META_DEV_REQ),                		M(x8,type) M(x32,time) M(c_str(64),filename) M(x32,offset) M(x16,len) 		)\
    X(dev_req_verify,                           0xf003,    (META_DEV_REQ),                		M(x8,type) M(x32,time) M(c_str(64),filename) 		)\
    X(utc_time_req,                             0xf005,    (META_DEV_REQ),                		M(x8,type) )\
    X(dev_req_weather_info,                     0xf006,    (META_DEV_REQ),                      M(c_time,time) M(gps_t,gps))\
    X(dev_rsp_upgrade_request,                  0xf100,    (META_DEV_RSP),                		M(x8,status)	)\
    X(dev_rsp_set_param,                        0xf102,    (META_DEV_RSP),                		M(c_encode,encode)		)\
    X(dev_rsp_get_param,                        0xf103,    (META_DEV_RSP),                		M(c_encode,encode)		)\
    X(dev_req_agps,                             0xf004,    (META_DEV_REQ),                      M(x8, data_mode) M(x8, data_type))\
    X(dev_rsp_ctl_lock,                         0xf113,    (META_DEV_RSP),                		M(x8,result)		)\
    X(dev_rsp_blemac_dismatch,                  0xf114,    (META_DEV_RSP),                      M(x8,result))\
    X(dev_rsp_find_car,                         0xf115,    (META_DEV_RSP),                		M(x8,result)		)\
    X(dev_rsp_manual_defence_switch,            0xf120,    (META_DEV_RSP),                		M(x8,result)		)\
    X(dev_rsp_defence_set,                      0xf121,    (META_DEV_RSP),                      M(x8,result) )\
    X(dev_rsp_check_upgrade_condition,          0xf126,    (META_DEV_RSP),                		M(x32 ,time) M(x8,type) M(x8,cnum) M(x8,c1) M(x8,c2) M(x8,c3) M(x32,task_id)		)\
    X(dev_rsp_upgrade_device,                   0xf127,    (META_DEV_RSP),                		M(x8,type) M(x8,result)		)\
    X(dev_rsp_seal_control,                     0xf128,    (META_DEV_RSP),                      M(x8,result) )\
    X(dev_rsp_seal_state,                       0xf129,    (META_DEV_RSP),                      M(x8,result))\
    X(dev_rsp_seat_lock,                        0xf117,    (META_DEV_RSP),                		M(x8,result))\

#define BLE_RX_FRAMES \
    X(app_req_auth,                      0x01fe,    (META_SRV_REQ),                	     M(s_arr(6),time) M(s_arr(32),token) M(x8,mobileSystem))\
    X(app_req_ctrl,                      0x03fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,value))\
    X(app_req_rssi_range_switch,         0x04fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,InductionStatus))\
    X(app_req_rssi_range,                0x07fe,    (META_SRV_REQ),                      M(s_arr(6),time) M(x16, min) M(x16, max)) \
    X(app_req_find_car,                  0x08fe,    (META_SRV_REQ),                		 M(s_arr(6),time)) \
    X(app_req_add_nfc,                   0x0bfe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,add_control)) \
    X(app_req_del_nfc,                   0x0cfe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,del_control) M(s_arr(4),delete_card_uuid) M(s_arr(6),delete_card_key)) \
    X(app_req_read_nfc,                  0x0efe,    (META_SRV_REQ),                		 M(s_arr(6),time)) \
    X(app_req_set_oil_defend,            0x0ffe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,DefendStatus)) \
    X(app_req_set_oil_sound,             0x11fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,sound)) \
    X(app_req_set_oil_trunk_lock,        0x12fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_nfc_switch,            0x13fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_seat_lock,             0x14fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_mute_switch,           0x15fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_middle_lock,           0x16fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_emergency_mode,        0x17fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_user_param,            0x01fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control) M(x8,speed) M(x8,maxTorque)) \
    X(app_req_set_booster_param,         0x02fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control) M(x8,speed)) \
    X(app_req_set_delayed_headlights,    0x03fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control) M(x8,delayTime)) \
    X(app_req_set_charging_power,        0x04fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,chargingPower)) \
    X(app_req_set_auto_shift_to_p,       0x05fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control) M(x8,delayTime)) \
    X(app_req_phone_call,                0x05fe,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,type)) \
    X(app_req_set_chord_horn,            0x06fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,soundSource) M(x8,volume)) \
    X(app_req_set_ambient_light,         0x07fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,lightingEffects) M(x8,gradual) M(x8,r) M(x8,g) M(x8,b)) \
    X(app_req_set_reverse_control,       0x08fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control) M(x8,reverseSpeed)) \
    X(app_req_set_intelligent_switch,    0x63fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control) M(x8,controlType)) \
    X(app_req_defence_set,               0x09fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,sense) M(x8,volumn)) \
    X(app_req_set_auto_steering_return,  0x0afd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_ebs_set,               0x0bfd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_e_save_mode,           0x0cfd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_dyn_mode,              0x0dfd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_sport_mode,            0x0efd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_lost_mode,             0x0ffd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_tcs_switch,            0x10fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_side_stand_switch,     0x11fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,control)) \
    X(app_req_set_battery_param,         0x12fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,type) M(x8,capacity)) \
    X(app_req_sync_param,                0x64fd,    (META_SRV_REQ),                		 ) \
    X(app_req_set_default,               0x65fd,    (META_SRV_REQ),                		 M(s_arr(6),time) M(x8,code) M(x8,codeSub)) \

#define BLE_TX_FRAMES \
    X(ble_rsp_add_nfc,                   0x0b01,    (META_DEV_RSP),                		M(x8,result) M(s_arr(4),uuid) M(s_arr(6),key))\
    X(ble_rsp_read_nfc,                  0x0e01,    (META_DEV_RSP),                		M(x8,result) M(c_arr(60),data))\
    X(ble_sync_param,                    0x64fd,    (META_DEV_REQ),                		M(c_arr(128),data))\
    X(ble_sync_acc,                      0x66fd,    (META_DEV_REQ),                		M(c_arr(16),data))\
    X(ble_rsp_common,                    0xFFFF,    (META_DEV_RSP),                     M(x16, cmd) M(x8, result))

#pragma pack(1)

/**
 * @brief GPS structure definition
 */
typedef struct gps_t {
    /**
     * @brief Timing time
     */
    uint32_t time;

    /**
     * @brief Positioning source 0-GPS 1-GSM
     */
    uint8_t src;

    /**
     * @brief Data validity 0x0-real-time 0x1-latest
     */
    uint8_t valid;

    /**
     * @brief East/West longitude 0x0-East 0x1-West
     */
    uint8_t f_lng;

    /**
     * @brief North/South latitude 0x0-South 0x1-North
     */
    uint8_t f_lat;

    /**
     * @brief Number of positioning satellites
     */
    uint8_t satellites;

    /**
     * @brief Altitude value
     */
    int16_t height;

    /**
     * @brief Longitude value
     */
    uint32_t lon;

    /**
     * @brief Latitude value
     */
    uint32_t lat;

    /**
     * @brief Speed value
     */
    uint16_t speed;

    /**
     * @brief Direction value
     */
    uint16_t direction;

    /**
     * @brief pdod
     */
    uint16_t pdod;

    /**
     * @brief hdop
     */
    uint16_t hdop;

    /**
     * @brief vdop
     */
    uint16_t vdop;

    /**
     * Ignition status
     */
    uint8_t ign_status;

    /**
     * @brief hacc
     */
    uint16_t hacc;

    uint8_t is_turn_point;

    /**
     * GPS, GSM signal strength
     */
    uint8_t signal_strength;
} gps_t;

#define c_arr(S)        \
    struct {            \
        uint8_t size;   \
        uint8_t arr[S]; \
    }
#define c_str(S)     \
    struct {         \
        char str[S]; \
    }
#define s_arr(S)     \
    struct {         \
        uint8_t arr[S]; \
    }
#define f_arr(S)        \
    struct {            \
        uint16_t cnt;   \
        uint8_t arr[S]; \
    }
#define M(L, R) L R;

#define X(S, CMD, META, ...)          \
    typedef struct frame_tx_##S##_t { \
        __VA_ARGS__                   \
    } frame_tx_##S##_t;
TSP_TX_FRAMES
TSP_FRAME_HEAD
#undef X

#define X(S, CMD, META, ...)          \
    typedef struct ble_tx_##S##_t { \
        __VA_ARGS__                   \
    } ble_tx_##S##_t;
BLE_TX_FRAMES
BLE_FRAME_HEAD
#undef X

#define X(S, CMD, META, ...)          \
    typedef struct frame_rx_##S##_t { \
        __VA_ARGS__ uint8_t xxxdump;  \
    } frame_rx_##S##_t;
TSP_RX_FRAMES
TSP_FRAME_HEAD
#undef X

#define X(S, CMD, META, ...)          \
    typedef struct ble_rx_##S##_t { \
        __VA_ARGS__ uint8_t xxxdump;  \
    } ble_rx_##S##_t;
BLE_RX_FRAMES
BLE_FRAME_HEAD
#undef X

#undef M
#undef c_arr
#undef c_str
#undef s_arr
#undef f_arr

/**
 * @brief TX frame type definition
 */
enum frame_tx_type_e {
    frame_tx_type_min = 0,
#define X(name, id, meta, ...) frame_tx_type_##name,
    TSP_TX_FRAMES
#undef X
        frame_tx_type_max
};

enum ble_tx_type_e {
    ble_tx_type_min = 0,
#define X(name, id, meta, ...) ble_tx_type_##name,
    BLE_TX_FRAMES
#undef X
    ble_tx_type_max
};

/**
 * @brief RX frame type definition
 */
enum frame_rx_type_e {
    frame_rx_type_min = 0,
#define X(name, id, meta, ...) frame_rx_type_##name,
    TSP_RX_FRAMES
#undef X
        frame_rx_type_max
};

enum ble_rx_type_e {
    ble_rx_type_min = 0,
#define X(name, id, meta, ...) ble_rx_type_##name,
    BLE_RX_FRAMES
#undef X
    ble_rx_type_max
};

/**
 * @brief Frame header definition
 */
struct frame_head_t {
    /**
     * @brief Frame type
     */
    uint8_t type;

    /**
     * @brief Frame sequence number
     */
    uint16_t seq;
};

/**
 * @brief TX frame definition
 */
typedef struct frame_tx_t {
    struct frame_head_t head;
    union {
#define X(name, id, meta, ...) struct frame_tx_##name##_t name;
        TSP_TX_FRAMES
#undef X
    } value;
} frame_tx_t;

typedef struct ble_tx_t {
    struct frame_head_t head;
    union {
#define X(name, id, meta, ...) struct ble_tx_##name##_t name;
        BLE_TX_FRAMES
#undef X
    } value;
} ble_tx_t;


/**
 * @brief RX frame definition
 */
typedef struct frame_rx_t {
    struct frame_head_t head;
    union {
#define X(name, id, meta, ...) struct frame_rx_##name##_t name;
        TSP_RX_FRAMES
#undef X
    } value;
} frame_rx_t;

typedef struct ble_rx_t {
    struct frame_head_t head;
    union {
#define X(name, id, meta, ...) struct ble_rx_##name##_t name;
        BLE_RX_FRAMES
#undef X
    } value;
} ble_rx_t;

#pragma pack()

/**
 * @brief Frame information definition
 */
typedef struct frame_info_t {
    /**
     * @brief Frame description
     */
    const char *desc;

    /**
     * @brief Frame size
     */
    uint16_t size;

    /**
     * @brief Frame metadata
     */
    uint16_t meta;

    /**
     * @brief Frame command
     */
    uint16_t cmd;

    /**
     * @brief Frame encode/decode function union
     */
    union {
        /**
         * @brief Frame encoding function pointer
         * @param buf Buffer
         * @param size Buffer size
         * @param pos Current position
         * @param o Frame pointer
         */
        int (*encode)(unsigned char *buf, int size, int pos, const void *o);

        /**
         * @brief Frame decoding function pointer
         * @param buf Buffer
         * @param size Buffer size
         * @param pos Current position
         * @param o Frame pointer
         */
        int (*decode)(const unsigned char *buf, int size, int pos, void *o);
    } codec;
} frame_info_t;

/**
 * @brief Get frame information
 * @param type Frame type
 * @return Returns frame information pointer on success, NULL on failure
 */
const frame_info_t *frame_tx_get_info(uint8_t type);

/**
 * @brief Get BLE frame information
 * @param type Frame type
 * @return Returns frame information pointer on success, NULL on failure
 */
const frame_info_t *ble_tx_get_info(uint8_t type);


/**
 * @brief Get BLE frame information
 * @param type Frame type
 * @return Returns frame information pointer on success, NULL on failure
 */
const frame_info_t *ble_rx_get_info(uint8_t type);

/**
 * @brief Get frame size
 * @param type Frame type
 * @return Returns frame size on success, 0 on failure
 */
int frame_tx_get_size(uint8_t type);

/**
 * @brief Get BLE frame size
 * @param type Frame type
 * @return Returns frame size on success, 0 on failure
 */
int ble_tx_get_size(uint8_t type);

/**
 * @brief Encode frame
 * @param frame Frame pointer
 * @param data Encoded data
 * @param len Data length
 * @return Returns encoded data length on success, -1 on failure
 */
int frame_tx_encode(const frame_tx_t *frame, const uint8_t* device_id, uint8_t *data, int len);

/**
 * @brief Encode BLE frame
 * @param frame Frame pointer
 * @param data Encoded data
 * @param len Data length
 * @return Returns encoded data length on success, -1 on failure
 */
int ble_tx_encode(const ble_tx_t *frame, uint8_t *data, int len);

/**
 * @brief Decode frame
 * @param payload Frame data
 * @param size Frame data length
 * @return Returns frame data length on success, -1 on failure
 */
es_async_t frame_rx_decode(es_coro_t *coro, frame_tx_t *tx, const uint8_t *payload, int size);

/**
 * @brief Decode BLE frame
 * @param coro Coroutine pointer
 * @param tx Frame pointer
 * @param payload Frame data
 * @param size Frame data length
 * @return Returns frame data length on success, -1 on failure
 */
es_async_t ble_rx_decode(es_coro_t *coro, ble_tx_t *tx, const uint8_t *payload, int size);



#ifdef __cplusplus
}
#endif

#endif // CMAKELISTS_TXT_XFRAME_H
