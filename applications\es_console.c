/**
 * @file es_console.c
 * @brief Console output module implementation
 * @date 2024/10/1
 */

#include <stdio.h>
#include <string.h>
#include "es_console.h"
#include "es_list.h"
#include "es_drv_uart.h"
#include "es_coro.h"
#include "es_config.h"
#include "es_scheduler.h"
#include "es_at_srv.h"
#include "es_log.h"
#include <stdint.h>
#include <stdbool.h>



/**
 * @brief Console context structure
 */
typedef struct {
    bool inited;                         /* Console initialization state */
    es_uart_dev_t uart_dev;             /* UART device handle */
    es_at_srv_sink_t at_srv_sink;       /* AT service sink */
    es_coro_task_t rx_task;             /* Console receive task */
    es_log_sink_t log_sink;             /* Console log sink */
} es_console_ctx_t;


/* Console context singleton instance */
static es_console_ctx_t s_console_ctx = {0};

static es_async_t es_at_srv_console_sink_func(es_coro_t *coro, const uint8_t *data, uint16_t len)
{
    es_co_begin(coro);

    if (!data || len == 0 || !s_console_ctx.inited)
    {
        es_co_exit;
    }

    es_co_await(es_uart_coro_write, s_console_ctx.uart_dev, data, len);

    es_co_end;
}
/**
 * @brief UART console log sink callback function
 * @param level Log level
 * @param log_msg Log message string
 */
static void log_console_sink_func(es_log_level_t level, bool to_flash, const char *log_msg, uint32_t len)
{
    /* Just print to UART, level is already handled in es_log.c */
    (void)level;
    es_console_send_data((uint8_t *)log_msg, (int)len);
}

/**
 * @brief Coroutine for receiving data and dispatching to processing coroutines
 * @param coro 协程上下文
 * @param ctx 协程上下文
 * @return 协程返回值
 */
static es_async_t es_console_rx_task_func(es_coro_t *coro, void *ctx)
{
    uint8_t byte;

    es_co_begin(coro);

    while (1) {
        es_co_yield;

        /* Check if data is available */
        es_co_wait(es_uart_available(s_console_ctx.uart_dev, 0) > 0);

        /* Read only one byte at a time */
        es_co_wait(es_uart_read(s_console_ctx.uart_dev, &byte, 1) > 0);

        es_at_srv_recv(s_console_ctx.at_srv_sink.src_type, &byte, 1);  
    }

    es_co_end;
}

/**
 * @brief Initialize console module
 * @return 0 on success, non-zero on failure
 */
int es_console_init(void)
{
    if (s_console_ctx.inited) {
        return 0;
    }

    //init log
    es_log_init();
    es_at_srv_init();

    int ret = 0;
    es_uart_config_t uart_config = CONFIG_CONSOLE_UART;
    
    /* Open UART device */
    s_console_ctx.uart_dev = es_uart_open(&uart_config);
    if (s_console_ctx.uart_dev == NULL) {
        return -1;
    }

    s_console_ctx.at_srv_sink.src_type = es_at_srv_alloc_src_type();
    s_console_ctx.at_srv_sink.sink_func = es_at_srv_console_sink_func;

    s_console_ctx.log_sink.name = "console";
    s_console_ctx.log_sink.sink_func = log_console_sink_func;

    es_at_srv_add_sink(&s_console_ctx.at_srv_sink);
    es_log_add_sink(&s_console_ctx.log_sink);

    s_console_ctx.rx_task = (es_coro_task_t) {
        .name = "console_rx",
        .func = es_console_rx_task_func,
        .ctx = NULL
    };
    
    /* Add receive task to default scheduler */
    ret = es_scheduler_task_add(es_scheduler_get_default(), &s_console_ctx.rx_task);
    if (ret < 0) {
        es_uart_close(s_console_ctx.uart_dev);
        s_console_ctx.uart_dev = NULL;
        return -1;
    }

    s_console_ctx.inited = true;

    return ret;
}

/**
 * @brief Deinitialize console module
 */
void es_console_deinit(void)
{
    /* If UART console is initialized, release it */
    if (s_console_ctx.uart_dev != NULL) {
        es_uart_close(s_console_ctx.uart_dev);
        s_console_ctx.uart_dev = NULL;
    }

    s_console_ctx.inited = false;
}
/**
 * @brief Send data to console
 * @param data Data to send
 * @param len Data length
 */
void es_console_send_data(const uint8_t *data, int len)
{
    if (data && len > 0 && s_console_ctx.inited) {
        #ifdef _WIN32
        printf("%s", (char *)data);
        #endif
        /* If UART is initialized, also send to UART */
        if (s_console_ctx.uart_dev != NULL) {

            es_uart_write(s_console_ctx.uart_dev, data, len);
        }
    }
}
