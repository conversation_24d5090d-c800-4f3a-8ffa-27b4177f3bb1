/**
 * @file es_console.h
 * @brief Console output module
 * @date 2024/10/1
 */

#ifndef __ES_CONSOLE_H__
#define __ES_CONSOLE_H__

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif


/**
 * @brief Initialize console module
 * @return 0 on success, non-zero on failure
 */
int es_console_init(void);

/**
 * @brief Deinitialize console module
 */
void es_console_deinit(void);

/**
 * @brief Send data to console
 * @param data Data to send
 * @param len Data length
 */
void es_console_send_data(const uint8_t *data, int len);


#ifdef __cplusplus
}
#endif

#endif /* __ES_CONSOLE_H__ */ 
