#include "es_time.h"
#include "es_drv_os.h"
#include <string.h> // For memcpy and other operations
#include <stdio.h>  // For snprintf operations

// Define timezone offset (UTC+8)
#define TIMEZONE_OFFSET (0 )

/* seconds per day */
#define SPD 24*60*60

/* days per month -- nonleap! */
static const short __spm[13] =
{
    0,
    (31),
    (31 + 28),
    (31 + 28 + 31),
    (31 + 28 + 31 + 30),
    (31 + 28 + 31 + 30 + 31),
    (31 + 28 + 31 + 30 + 31 + 30),
    (31 + 28 + 31 + 30 + 31 + 30 + 31),
    (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31),
    (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31 + 30),
    (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31 + 30 + 31),
    (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31 + 30 + 31 + 30),
    (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31 + 30 + 31 + 30 + 31),
};

static int __isleap(int year)
{
    /* every fourth year is a leap year except for century years that are
     * not divisible by 400. */
    /*  return (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)); */
    return (!(year % 4) && ((year % 100) || !(year % 400)));
}

uint32_t es_timegm(struct es_tm * const t)
{
    uint32_t day;
    uint32_t i;
    uint32_t years;

    if(t == NULL)
    {
        return (uint32_t)-1;
    }

    years = (uint32_t)t->tm_year - 70;
    if (t->tm_sec > 60)         /* seconds after the minute - [0, 60] including leap second */
    {
        t->tm_min += t->tm_sec / 60;
        t->tm_sec %= 60;
    }
    if (t->tm_min >= 60)        /* minutes after the hour - [0, 59] */
    {
        t->tm_hour += t->tm_min / 60;
        t->tm_min %= 60;
    }
    if (t->tm_hour >= 24)       /* hours since midnight - [0, 23] */
    {
        t->tm_mday += t->tm_hour / 24;
        t->tm_hour %= 24;
    }
    if (t->tm_mon >= 12)        /* months since January - [0, 11] */
    {
        t->tm_year += t->tm_mon / 12;
        t->tm_mon %= 12;
    }
    while (t->tm_mday > __spm[1 + t->tm_mon])
    {
        if (t->tm_mon == 1 && __isleap(t->tm_year + 1900))
        {
            --t->tm_mday;
        }
        t->tm_mday -= __spm[t->tm_mon];
        ++t->tm_mon;
        if (t->tm_mon > 11)
        {
            t->tm_mon = 0;
            ++t->tm_year;
        }
    }

    if (t->tm_year < 70)
    {
        return (uint32_t)-1;
    }

    /* Days since 1970 is 365 * number of years + number of leap years since 1970 */
    day = years * 365 + (years + 1) / 4;

    /* After 2100 we have to substract 3 leap years for every 400 years
     This is not intuitive. Most mktime implementations do not support
     dates after 2059, anyway, so we might leave this out for it's
     bloat. */
    if (years >= 131)
    {
        years -= 131;
        years /= 100;
        day -= (years >> 2) * 3 + 1;
        if ((years &= 3) == 3)
            years--;
        day -= years;
    }

    day += t->tm_yday = __spm[t->tm_mon] + t->tm_mday - 1 +
                        (__isleap(t->tm_year + 1900) & (t->tm_mon > 1));

    /* day is now the number of days since 'Jan 1 1970' */
    i = 7;
    t->tm_wday = (day + 4) % i; /* Sunday=0, Monday=1, ..., Saturday=6 */

    i = 24;
    day *= i;
    i = 60;
    return ((day + t->tm_hour) * i + t->tm_min) * i + t->tm_sec;
}


// Convert timestamp to tm structure (optimized version)
struct es_tm * es_gmtime_r(const uint32_t *timep, struct es_tm *r) {
    uint32_t i;
    uint32_t work = *timep % (SPD);

    if(timep == NULL || r == NULL)
    {
        return NULL;
    }

    memset(r, 0, sizeof(struct es_tm));

    r->tm_sec = work % 60;
    work /= 60;
    r->tm_min = work % 60;
    r->tm_hour = work / 60;
    work = *timep / (SPD);
    r->tm_wday = (4 + work) % 7;
    for (i = 1970;; ++i)
    {
        uint32_t k = __isleap(i) ? 366 : 365;
        if (work >= k)
            work -= k;
        else
            break;
    }
    r->tm_year = i - 1900;
    r->tm_yday = work;

    r->tm_mday = 1;
    if (__isleap(i) && (work > 58))
    {
        if (work == 59)
            r->tm_mday = 2; /* 29.2. */
        work -= 1;
    }

    for (i = 11; i >= 0 && ((int)__spm[i] > (int)work); --i);

    r->tm_mon = i;
    r->tm_mday += work - __spm[i];

    return r;
}


uint32_t es_mktime( struct es_tm * const tm) {
       uint32_t timestamp;

    timestamp = es_timegm(tm);
    timestamp = timestamp - 3600 * TIMEZONE_OFFSET;
    return timestamp;
}

// Convert timestamp to string (optimized buffer operations)
void es_timestamp_to_str(uint32_t timestamp, char buf[20]) {
    uint32_t timestamp_tmp = timestamp;
    
    // Add timezone offset (using macro definition)
    timestamp_tmp += TIMEZONE_OFFSET;
    
    struct es_tm tm;
    es_gmtime_r(&timestamp_tmp, &tm);
    
    // Use snprintf to format date-time string (YYYY-MM-DD HH:MM:SS)
    snprintf(buf, 20, "%04d-%02d-%02d %02d:%02d:%02d", 
             tm.tm_year + 1900, 
             tm.tm_mon + 1, 
             tm.tm_mday, 
             tm.tm_hour, 
             tm.tm_min, 
             tm.tm_sec);
}

/**
 * @brief Check if timeout occurred
 * @param start_tick Starting tick
 * @param timeout_ms Timeout duration (milliseconds)
 * @return int 1 indicates timeout, 0 indicates no timeout
 */
int es_os_check_timeout(uint32_t start_tick, uint32_t timeout_ms)
{
    uint32_t current_tick = es_os_get_tick_ms();
    
    /* Handle counter wraparound situation */
    if (current_tick >= start_tick) {
        return (current_tick - start_tick) >= timeout_ms ? 1 : 0;
    } else {
        /* Current tick is less than start tick, indicating wraparound occurred */
        return ((UINT32_MAX - start_tick) + current_tick + 1) >= timeout_ms ? 1 : 0;
    }
}
