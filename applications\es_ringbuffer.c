﻿/**
 * @file es_ringbuffer.c
 * @brief Generic ring buffer module implementation
 * @date 2024/10/1
 */

#include "es_ringbuffer.h"
#include "es_drv_os.h"

/**
 * @brief Check if a number is a power of 2
 */
static inline bool is_power_of_two(uint32_t n)
{
    return (n != 0) && ((n & (n - 1)) == 0);
}

/**
 * @brief Round up to the nearest power of 2
 */
static inline uint32_t round_up_to_power_of_two(uint32_t n)
{
    if (n == 0) return 1;
    
    n--;
    n |= n >> 1;
    n |= n >> 2;
    n |= n >> 4;
    n |= n >> 8;
    n |= n >> 16;
    
    return n + 1;
}

/**
 * @brief Initialize ring buffer
 */
int es_ringbuffer_init(es_ringbuffer_t *rb, uint8_t *buffer, uint32_t size)
{
    if (rb == NULL || buffer == NULL || size == 0) {
        return -1;
    }
    
    // Ensure size is a power of 2
    if (!is_power_of_two(size)) {
        return -1;
    }
    
    rb->buffer = buffer;
    rb->size = size;
    rb->mask = size - 1;  // Calculate mask
    rb->read_index = 0;
    rb->write_index = 0;
    rb->data_size = 0;
    rb->is_full = false;
    
    memset(buffer, 0, size);
    
    return 0;
}

/**
 * @brief Reset ring buffer state
 */
int es_ringbuffer_reset(es_ringbuffer_t *rb)
{
    if (rb == NULL) {
        return -1;
    }
    
    rb->read_index = 0;
    rb->write_index = 0;
    rb->data_size = 0;
    rb->is_full = false;
    
    if (rb->buffer) {
        memset(rb->buffer, 0, rb->size);
    }
    
    return 0;
}

/**
 * @brief Write data to ring buffer
 * @note Interrupt-safe version: uses memory barriers to ensure operation order
 */
uint32_t es_ringbuffer_write(es_ringbuffer_t *rb, const uint8_t *data, uint32_t len)
{
    uint32_t i;
    uint32_t count = 0;

    if (rb == NULL || data == NULL || len == 0) {
        return 0;
    }

    /* Read current write index */
    uint16_t current_write_index = rb->write_index;
    uint16_t current_read_index = rb->read_index;

    /* Calculate available space - use index difference calculation, avoid dependency on data_size */
    uint32_t used_space = (current_write_index - current_read_index) & rb->mask;
    if (rb->is_full) {
        used_space = rb->size;
    }
    uint32_t available_space = rb->size - used_space;

    if (len > available_space) {
        len = available_space;
    }

    if (len == 0) {
        return 0;
    }

    /* Write data */
    for (i = 0; i < len; i++) {
        rb->buffer[current_write_index] = data[i];
        current_write_index = (current_write_index + 1) & rb->mask;
        count++;
    }

    /* Memory barrier: ensure data write completion before updating pointer */
    ES_MEMORY_BARRIER();

    /* Update write index */
    rb->write_index = current_write_index;

    /* Memory barrier: ensure write_index update before updating other fields */
    ES_MEMORY_BARRIER();

    /* Update data size and full flag */
    rb->data_size += count;
    if (rb->data_size >= rb->size) {
        rb->is_full = true;
    }

    return count;
}

/**
 * @brief Read data from ring buffer
 * @note Interrupt-safe version: uses memory barriers to ensure operation order
 */
uint32_t es_ringbuffer_read(es_ringbuffer_t *rb, uint8_t *data, uint32_t len)
{
    uint32_t i;
    uint32_t count = 0;

    if (rb == NULL || data == NULL || len == 0) {
        return 0;
    }

    /* Read current indices */
    uint16_t current_read_index = rb->read_index;
    uint16_t current_write_index = rb->write_index;

    /* Check if buffer is empty - use index comparison, avoid dependency on data_size */
    if (current_read_index == current_write_index && !rb->is_full) {
        return 0;  /* Buffer is empty */
    }

    /* Calculate available data to read */
    uint32_t available_data;
    if (rb->is_full) {
        available_data = rb->size;
    } else {
        available_data = (current_write_index - current_read_index) & rb->mask;
    }

    if (len > available_data) {
        len = available_data;
    }

    /* Read data */
    for (i = 0; i < len; i++) {
        data[i] = rb->buffer[current_read_index];
        current_read_index = (current_read_index + 1) & rb->mask;
        count++;
    }

    /* Memory barrier: ensure data read completion before updating pointer */
    ES_MEMORY_BARRIER();

    /* Update read index */
    rb->read_index = current_read_index;

    /* Memory barrier: ensure read_index update before updating other fields */
    ES_MEMORY_BARRIER();

    /* Update data size and full flag */
    rb->data_size -= count;
    rb->is_full = false;

    return count;
}

/**
 * @brief Peek at ring buffer data without removing it
 */
uint32_t es_ringbuffer_peek(es_ringbuffer_t *rb, uint8_t *data, uint32_t len)
{
    uint32_t i;
    uint32_t count = 0;
    uint32_t read_index;
    
    if (rb == NULL || data == NULL || len == 0) {
        return 0;
    }
    
    /* Buffer is empty, cannot peek */
    if (rb->data_size == 0) {
        return 0;
    }
    
    /* Calculate maximum data that can be peeked */
    if (len > rb->data_size) {
        len = rb->data_size;
    }
    
    /* Temporarily save read index for peeking data */
    read_index = rb->read_index;
    
    /* Peek data */
    for (i = 0; i < len; i++) {
        data[i] = rb->buffer[read_index];
        read_index = (read_index + 1) & rb->mask;  // Use mask instead of modulo
        count++;
    }
    
    return count;
}

/**
 * @brief Get available data amount in ring buffer
 */
uint32_t es_ringbuffer_available(es_ringbuffer_t *rb)
{
    if (rb == NULL) {
        return 0;
    }
    
    return rb->data_size;
}

/**
 * @brief Get free space size in ring buffer
 */
uint32_t es_ringbuffer_free_space(es_ringbuffer_t *rb)
{
    if (rb == NULL) {
        return 0;
    }
    
    return rb->size - rb->data_size;
}

/**
 * @brief Check if ring buffer is empty
 */
bool es_ringbuffer_is_empty(es_ringbuffer_t *rb)
{
    if (rb == NULL) {
        return true;
    }
    
    return (rb->data_size == 0);
}

/**
 * @brief Check if ring buffer is full
 */
bool es_ringbuffer_is_full(es_ringbuffer_t *rb)
{
    if (rb == NULL) {
        return false;
    }
    
    return !!rb->is_full;
}

/**
 * @brief Skip specified length of data in ring buffer
 */
uint32_t es_ringbuffer_skip(es_ringbuffer_t *rb, uint32_t len)
{
    uint32_t i;
    uint32_t count = 0;
    
    if (rb == NULL || len == 0) {
        return 0;
    }
    
    /* Buffer is empty, cannot skip */
    if (rb->data_size == 0) {
        return 0;
    }
    
    /* Calculate maximum data that can be skipped */
    if (len > rb->data_size) {
        len = rb->data_size;
    }
    
    /* Skip data */
    for (i = 0; i < len; i++) {
        rb->read_index = (rb->read_index + 1) & rb->mask;  // Use mask instead of modulo
        count++;
    }
    
    /* Update data size and full flag */
    rb->data_size -= count;
    rb->is_full = false;
    
    return count;
}

/**
 * @brief Force write data to ring buffer, overwrite old data if space is insufficient
 */
uint32_t es_ringbuffer_write_force(es_ringbuffer_t *rb, const uint8_t *data, uint32_t len)
{
    uint32_t i;
    uint32_t count = 0;
    
    if (rb == NULL || data == NULL || len == 0) {
        return 0;
    }
    
    /* If data length to write exceeds buffer size, keep only the last rb->size bytes */
    if (len > rb->size) {
        data += (len - rb->size);
        len = rb->size;
    }
    
    /* Write data, overwrite if buffer is full */
    for (i = 0; i < len; i++) {
        rb->buffer[rb->write_index] = data[i];
        rb->write_index = (rb->write_index + 1) & rb->mask;  // Use mask instead of modulo
        
        if (rb->is_full) {
            /* Buffer is full, move read pointer */
            rb->read_index = (rb->read_index + 1) & rb->mask;  // Use mask instead of modulo
        } else {
            /* Buffer not full, increase data size */
            rb->data_size++;
            if (rb->data_size >= rb->size) {
                rb->is_full = true;
            }
        }
        count++;
    }
    
    return count;
}

