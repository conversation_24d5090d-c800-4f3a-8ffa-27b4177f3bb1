﻿/**
 * @file es_at_def.h
 * @brief AT命令定义头文件
 * <AUTHOR>
 * @date 2025/1/14
 * @copyright Copyright (c) 2025
 */

#ifndef ES_AT_DEF_H
#define ES_AT_DEF_H

#ifdef __cplusplus
extern "C" {
#endif

#define DEMO_AT_SRV_ITEMS \
    X("VERSION", "AT+VERSION?", version) \
    X("IMEI", "AT+IMEI?", imei) \
    X("SN", "AT+SN?|AT+SN=<value>", sn) \
    X("RST", "AT+RST", rst) \
    X("PM", "AT+PM=<event>|AT+PM?", pm) \
    X("VOLT", "AT+VOLT?", volt) \
    X("OTASTART", "AT+OTASTART=<len>,<crc32>[,<max_block>]", ota_start) \
    X("ACC", "AT+ACC=<state>", acc) \
    X("HTTP", "AT+HTTP=<url>", http) \
    X("FRAME", "AT+FRAME=<type>", frame) \
    X("LOGV2", "AT+LOGV2=<max>,[<keyword>,...]|AT+LOGV2?", logv2) \
    X("LOGV2SEEK", "AT+LOGV2SEEK=<pos>,<offset>|AT+LOGV2SEEK?", logv2seek) \
    X("LOGV2CLR", "AT+LOGV2CLR", logv2clr) \
    X("LOGV2TEST", "AT+LOGV2TEST=<count>,<interval_ms>", logv2test) \
    X("CFG2RST", "AT+CFG2RST(reset all cfg)", cfg2rst) \
    X("CFG2FACTORY", "AT+CFG2FACTORY(reset factory cfg)", cfg2factory) \
    X("CFG2USER", "AT+CFG2USER(reset user cfg)", cfg2user) \
    X("CFG2", "AT+CFG2=<id>[,<value>]|AT+CFG2?(query all cfg)", cfg2) \
    X("CFG2COMPACT", "AT+CFG2COMPACT", cfg2compact) \
    X("MEM", "AT+MEM?|AT+MEM=<operation>[,<params>]", mem) \


#ifdef __cplusplus
}
#endif

#endif /* ES_AT_DEF_H */
