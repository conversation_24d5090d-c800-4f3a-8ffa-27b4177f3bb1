﻿/**
 * @file win_flash.c
 * @brief Windows平台模拟Flash驱动实现
 */
#include <stdio.h>
#include <string.h>
#include <windows.h>
#include <stdbool.h>
#include "es_flash.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Windows模拟Flash设备实例 */
static struct {
    HANDLE hFile;              /* 存储文件句柄 */
    bool is_initialized;       /* 初始化标志 */
    uint8_t *cache;            /* 扇区缓存 */
} g_win_flash;

/* 内部函数声明 */
static int win_flash_dev_init(void);
static int win_flash_dev_deinit(void);
static int win_flash_dev_read(uint32_t addr, uint8_t *data, uint32_t len);
static int win_flash_dev_write(uint32_t addr, const uint8_t *data, uint32_t len);
static int win_flash_dev_erase_sector(uint32_t sector_addr);

const es_flash_dev_t file_flash_dev = {
    .name = "flash",
    .start_addr = 0,
    .size = 1024 * 1024 * 4,
    .sector_size = 4096,
    .write_gran = 1,
    .init = win_flash_dev_init,
    .read = win_flash_dev_read,
    .write = win_flash_dev_write,
    .erase_sector = win_flash_dev_erase_sector,
};


int win_flash_dev_clean(void)
{
    // 删除flash目录及其中的所有文件
    if (RemoveDirectoryA("flash")) {
        // 创建空的flash目录
        if (CreateDirectoryA("flash", NULL)) {
            return 0;
        }
    }
    return -1;
}

/* Flash设备操作函数实现 */
static int win_flash_dev_init(void)
{
    DWORD fileSize;
    HANDLE hFile;
    bool needInit = false;

    if (g_win_flash.is_initialized) {
        return 0;
    }

    /* 确保flash目录存在 */
    DWORD attrs = GetFileAttributesA("flash");
    if (attrs == INVALID_FILE_ATTRIBUTES || !(attrs & FILE_ATTRIBUTE_DIRECTORY)) {
        /* 目录不存在，创建它 */
        if (!CreateDirectoryA("flash", NULL) && GetLastError() != ERROR_ALREADY_EXISTS) {
            return -1;
        }
    }

    /* 构建完整的文件路径 */
    char filePath[MAX_PATH] = "flash/";
    strcat(filePath, file_flash_dev.name);

    /* 检查文件是否存在 */
    hFile = CreateFileA(filePath,
                       GENERIC_READ,
                       FILE_SHARE_READ,
                       NULL,
                       OPEN_EXISTING,
                       FILE_ATTRIBUTE_NORMAL,
                       NULL);

    if (hFile == INVALID_HANDLE_VALUE) {
        needInit = true;
    } else {
        fileSize = GetFileSize(hFile, NULL);
        CloseHandle(hFile);
        if (fileSize != file_flash_dev.size) {
            needInit = true;
        }
    }

    /* 打开或创建存储文件 */
    g_win_flash.hFile = CreateFileA(filePath,
                                   GENERIC_READ | GENERIC_WRITE,
                                   0,
                                   NULL,
                                   OPEN_ALWAYS,
                                   FILE_ATTRIBUTE_NORMAL,
                                   NULL);
    if (g_win_flash.hFile == INVALID_HANDLE_VALUE) {
        return -1;
    }

    /* 初始化存储文件 */
    if (needInit) {
        uint8_t *buffer = NULL;
        DWORD bytesWritten = 0;
        DWORD bufferSize = 65536; // 64KB缓冲区
        
        // 分配缓冲区内存
        buffer = (uint8_t *)malloc(bufferSize);
        if (buffer == NULL) {
            CloseHandle(g_win_flash.hFile);
            g_win_flash.hFile = INVALID_HANDLE_VALUE;
            return -1;
        }
        
        // 填充缓冲区为0xFF
        memset(buffer, 0xFF, bufferSize);
        
        // 设置文件指针到开始位置
        SetFilePointer(g_win_flash.hFile, 0, NULL, FILE_BEGIN);
        
        // 循环写入直到达到文件大小
        uint32_t remaining = file_flash_dev.size;
        while (remaining > 0) {
            DWORD toWrite = (remaining > bufferSize) ? bufferSize : remaining;
            if (!WriteFile(g_win_flash.hFile, buffer, toWrite, &bytesWritten, NULL) || bytesWritten != toWrite) {
                free(buffer);
                CloseHandle(g_win_flash.hFile);
                g_win_flash.hFile = INVALID_HANDLE_VALUE;
                return -1;
            }
            remaining -= bytesWritten;
        }
        
        free(buffer);
        FlushFileBuffers(g_win_flash.hFile);
    }

    /* 分配扇区缓存 */
    g_win_flash.cache = (uint8_t *)malloc(file_flash_dev.sector_size);
    if (g_win_flash.cache == NULL) {
        CloseHandle(g_win_flash.hFile);
        g_win_flash.hFile = INVALID_HANDLE_VALUE;
        return -1;
    }

    g_win_flash.is_initialized = true;
    return 0;
}

static int win_flash_dev_deinit(void)
{
    if (!g_win_flash.is_initialized) {
        return -1;
    }

    if (g_win_flash.hFile != INVALID_HANDLE_VALUE) {
        CloseHandle(g_win_flash.hFile);
        g_win_flash.hFile = INVALID_HANDLE_VALUE;
    }

    if (g_win_flash.cache != NULL) {
        free(g_win_flash.cache);
        g_win_flash.cache = NULL;
    }

    g_win_flash.is_initialized = false;
    return 0;
}

static int win_flash_dev_read(uint32_t addr, uint8_t *data, uint32_t len)
{
    DWORD bytesRead;
    
    if (!g_win_flash.is_initialized || data == NULL) {
        return -1;
    }

    if (addr + len > file_flash_dev.size) {
        return -1;
    }

    // 设置文件指针
    if (SetFilePointer(g_win_flash.hFile, addr, NULL, FILE_BEGIN) == INVALID_SET_FILE_POINTER) {
        return -1;
    }

    // 读取数据
    if (!ReadFile(g_win_flash.hFile, data, len, &bytesRead, NULL) || bytesRead != len) {
        return -1;
    }

    return len;
}

static int win_flash_dev_write(uint32_t addr, const uint8_t *data, uint32_t len)
{
    DWORD bytesRead, bytesWritten;
    uint8_t *current_data = NULL;
    uint8_t *new_data = NULL;
    
    if (!g_win_flash.is_initialized || data == NULL) {
        return -1;
    }

    if (addr + len > file_flash_dev.size) {
        return -1;
    }

    // 分配缓冲区读取当前数据
    current_data = (uint8_t *)malloc(len);
    new_data = (uint8_t *)malloc(len);
    if (current_data == NULL || new_data == NULL) {
        if (current_data) free(current_data);
        if (new_data) free(new_data);
        return -1;
    }

    // 设置文件指针并读取当前数据
    if (SetFilePointer(g_win_flash.hFile, addr, NULL, FILE_BEGIN) == INVALID_SET_FILE_POINTER) {
        free(current_data);
        free(new_data);
        return -1;
    }

    if (!ReadFile(g_win_flash.hFile, current_data, len, &bytesRead, NULL) || bytesRead != len) {
        free(current_data);
        free(new_data);
        return -1;
    }

    // 模拟Flash写入特性：只能从1变为0，不能从0变为1
    for (uint32_t i = 0; i < len; i++) {
        // 对每个位进行检查和操作
        new_data[i] = current_data[i] & data[i];  // 只有当前位为1且要写入0时，才能变为0
        
        // 检查是否有非法操作（试图将0变为1）
        if ((current_data[i] & (~data[i])) != 0) {
            // 存在试图将0变为1的操作，这在真实Flash中是不允许的
            // 在调试模式下可以打印警告信息
            #ifdef DEBUG
            printf("Warning: Attempt to write 1 to 0 at address 0x%08X, byte offset %d\n", addr + i, i);
            #endif
        }
    }

    // 重新设置文件指针到写入位置
    if (SetFilePointer(g_win_flash.hFile, addr, NULL, FILE_BEGIN) == INVALID_SET_FILE_POINTER) {
        free(current_data);
        free(new_data);
        return -1;
    }

    // 写入处理后的数据
    if (!WriteFile(g_win_flash.hFile, new_data, len, &bytesWritten, NULL) || bytesWritten != len) {
        free(current_data);
        free(new_data);
        return -1;
    }

    FlushFileBuffers(g_win_flash.hFile);
    
    free(current_data);
    free(new_data);
    return len;
}

static int win_flash_dev_erase_sector(uint32_t sector_addr)
{
    DWORD bytesWritten;
    
    if (!g_win_flash.is_initialized) {
        return -1;
    }

    if (sector_addr >= file_flash_dev.size) {
        return -1;
    }

    /* 擦除扇区(填充0xFF) */
    memset(g_win_flash.cache, 0xFF, file_flash_dev.sector_size);

    // 设置文件指针
    if (SetFilePointer(g_win_flash.hFile, sector_addr, NULL, FILE_BEGIN) == INVALID_SET_FILE_POINTER) {
        return -1;
    }

    // 写入擦除数据
    if (!WriteFile(g_win_flash.hFile, g_win_flash.cache, file_flash_dev.sector_size, &bytesWritten, NULL) || 
        bytesWritten != file_flash_dev.sector_size) {
        return -1;
    }

    FlushFileBuffers(g_win_flash.hFile);
    return 0;
}

#ifdef __cplusplus
}
#endif 