/**
 * @file es_pm.c
 * @brief Power management module implementation, based on generic state machine
 */

#include "es_pm.h"
#include "es_log.h"
#include "es_scheduler.h"
#include "es_list.h"
#include "es_drv_os.h"
#include "es_utils.h"
#include <string.h>
#include <stdlib.h>
#include "es_at_srv.h"
#include "es_drv_adc.h"

#define TAG                  "PM"

// Power management context



es_pm_ctx_t g_pm_ctx = {0};

// State name array for logging
static const char *s_pm_state_names[] = {
    [ES_PM_STATE_ON] = "ON",
    [ES_PM_STATE_OFF] = "OFF",
    [ES_PM_STATE_SLEEP_READY] = "SLEEP_READY",
    [ES_PM_STATE_SLEEP] = "SLEEP",
};

// Voltage related definitions
#define VOLTAGE_SAMPLE_COUNT    5       // Voltage sampling count
#define VOLTAGE_UPDATE_INTERVAL 1000    // Voltage update interval in ms

// Voltage sampling structure
typedef struct {
    uint16_t samples[VOLTAGE_SAMPLE_COUNT];  // Voltage sample value array
    uint8_t sample_index;                    // Current sampling index
    uint16_t voltage_mv;                     // Average voltage value in mV
    uint32_t last_update_time;               // Last update time
} es_pm_voltage_t;

static es_pm_voltage_t s_pm_voltage = {0};
static es_pm_voltage_t s_pm_main_voltage = {0};   // Main power supply voltage
static es_pm_voltage_t s_pm_battery_voltage = {0}; // Battery voltage

// ADC conversion factors - adjust according to actual circuit
#define ADC_MAINPWR_FACTOR    11.0f   // Main power ADC conversion factor (voltage divider ratio)
#define ADC_BATTERY_FACTOR    3.3f    // Battery ADC conversion factor (voltage divider ratio)
#define ADC_VREF_MV          3300     // ADC reference voltage in mV

static void es_pm_check_acc_on_reboot(void)
{
    // Add fast ON state switching detection variables
    static uint8_t on_count = 0;                   ///< ON state counter
    static uint32_t first_on_time = 0;             ///< Time of first ON state
    // Add ON state switching detection logic
    uint32_t current_time = es_os_get_tick_ms();
    on_count++;
    
    // If entering ON state for the first time, record time
    if (on_count == 1) {
        first_on_time = current_time;
    } 
    // Check if 3 ON state switches occurred within 1 second
    else if (on_count >= 3) {
        // Check if within 1 second
        if (es_os_check_timeout(first_on_time, 1000) == 0) {
            // Condition met: 3 consecutive ON states within 1 second, trigger reboot
            ES_LOGI(TAG, "Detected 3 ON events within 1 second, rebooting...");
            // Delay briefly to ensure log output
            // es_os_us_delay(500000); // Delay 500ms
            es_os_reboot();
        } else {
            // Over 1 second, reset counter and update first time
            on_count = 0;
        }
    }
}

/**
 * @brief ON state event handler function
 *
 * @param coro Coroutine context
 * @param ctx User context
 * @param state Current state
 * @param event Event
 * @param next_state Next state pointer
 * @return es_async_t Coroutine return value, contains next state, ES_PM_STATE_NONE means no state transition
 */
static es_async_t es_pm_state_on_handler(es_coro_t *coro, void *ctx, uint32_t state, uint32_t event)
{
    es_co_begin(coro);

    //check enter and exit
    if (event & ES_PM_EVENT_ENTRY) {
        ES_LOGI(TAG, "Enter ON state");
        
        es_pm_check_acc_on_reboot();
        
        es_co_exit;
    }
    if (event & ES_PM_EVENT_EXIT) {
        ES_LOGI(TAG, "Exit ON state");
        es_co_exit;
    }
    
    if (event & ES_PM_EVENT_OFF) {
        // ON state received OFF event, transition to OFF state
        g_pm_ctx.next_state = ES_PM_STATE_OFF;
        es_co_exit;
    }
    
    es_co_end;
}

/**
 * @brief OFF state event handler function
 *
 * @param coro Coroutine context
 * @param ctx User context
 * @param state Current state
 * @param event Event
 * @return es_async_t Coroutine return value, contains next state, ES_PM_STATE_NONE means no state transition
 */
static es_async_t es_pm_state_off_handler(es_coro_t *coro, void *ctx, uint32_t state, uint32_t event)
{
  
    es_co_begin(coro);

    //check enter and exit
    if (event & ES_PM_EVENT_ENTRY) {
        ES_LOGI(TAG, "Enter OFF state");
        es_co_exit;
    }
    if (event & ES_PM_EVENT_EXIT) {
        ES_LOGI(TAG, "Exit OFF state");
        es_co_exit;
    }
    
    if (event & ES_PM_EVENT_ON) {
		// OFF state received ON event, transition to ON state
		g_pm_ctx.next_state = ES_PM_STATE_ON;
		es_co_exit;
    }

    if (es_os_check_timeout(g_pm_ctx.enter_state_tick_ms, 5*1000)) {
        g_pm_ctx.next_state = ES_PM_STATE_SLEEP_READY;
        es_co_exit;
    }
    
    es_co_end;
}


static es_async_t es_pm_state_sleep_ready_handler(es_coro_t *coro, void *ctx, uint32_t state, uint32_t event)
{
    es_co_begin(coro);

    if (event & ES_PM_EVENT_ENTRY) {
        ES_LOGI(TAG, "Enter SLEEP_READY state");
        es_co_exit;
    }
    if (event & ES_PM_EVENT_EXIT) {
        ES_LOGI(TAG, "Exit SLEEP_READY state");
        es_co_exit;
    }

    if (event & ES_PM_EVENT_ON) {
        g_pm_ctx.next_state = ES_PM_STATE_ON;
        es_co_exit;
    }

    if (es_os_check_timeout(g_pm_ctx.enter_state_tick_ms, 5*1000)) {
        g_pm_ctx.next_state = ES_PM_STATE_SLEEP;
        es_co_exit;
    }
    
    es_co_end;
}

/**
 * @brief SLEEP state event handler function
 *
 * @param coro Coroutine context
 * @param ctx User context
 * @param state Current state
 * @param event Event
 * @return es_async_t Coroutine return value, contains next state, ES_PM_STATE_NONE means no state transition
 */
static es_async_t es_pm_state_sleep_handler(es_coro_t *coro, void *ctx, uint32_t state, uint32_t event)
{
    es_co_begin(coro);

    //check enter and exit
    if (event & ES_PM_EVENT_ENTRY) {
        ES_LOGI(TAG, "Enter SLEEP state");
        g_pm_ctx.lpm_dev_mask |= ES_PM_LPM_DEV_PM;
        //wait all lpm dev ready or acc on
        es_co_wait_timeout(g_pm_ctx.lpm_dev_mask == ES_PM_LPM_DEV_ALL || es_read_acc_state(), 20*1000);
        if(g_pm_ctx.lpm_dev_mask != ES_PM_LPM_DEV_ALL) {
            //timeout, maybe error, reboot, and try again
            ES_LOGE(TAG, "LPM dev timeout, reboot: %08x", g_pm_ctx.lpm_dev_mask);
            // es_os_reboot();
        }
        
        if(es_read_acc_state()) {
            g_pm_ctx.next_state = ES_PM_STATE_ON;
        } else {
            es_pm_enter_lpm_stop(0);
        }
        g_pm_ctx.lpm_dev_mask = 0;
        es_co_exit;
    }
    if (event & ES_PM_EVENT_EXIT) {
        ES_LOGI(TAG, "Exit SLEEP state");
        es_co_exit;
    }

    
    // Ignition event has highest priority, immediately exit sleep state
    if (event & ES_PM_EVENT_ON) {
        g_pm_ctx.next_state = ES_PM_STATE_ON;
        es_co_exit;
    }
    
    es_co_end;
}

typedef es_async_t (*es_pm_event_handler_t)(es_coro_t *coro, void *state_machine, uint32_t state, uint32_t event);
static const es_pm_event_handler_t s_pm_state_handlers[] = {
	[ES_PM_STATE_ON] = es_pm_state_on_handler,
	[ES_PM_STATE_OFF] = es_pm_state_off_handler,
    [ES_PM_STATE_SLEEP_READY] = es_pm_state_sleep_ready_handler,
	[ES_PM_STATE_SLEEP] = es_pm_state_sleep_handler,
};




/**
 * @brief Get filtered ACC state
 *
 * @return uint8_t Filtered ACC state, 1 means ACC on, 0 means ACC off
 */
static uint8_t es_pm_get_filtered_acc_state(void)
{ 
    static uint8_t s_acc_history = 0;  // Use bits to store history state and filtered state
    // Use generic filtering algorithm to process ACC state
    s_acc_history = es_utils_digital_filter(s_acc_history, es_read_acc_state(), 5);  
    return (s_acc_history & DIGITAL_FILTER_RESULT_MASK);
}

/**
 * @brief Read main power supply raw voltage value
 * 
 * @return uint16_t Raw voltage value in mV
 */
static uint16_t es_pm_read_main_voltage_raw(void)
{
    return es_adc_read_main_voltage(PM_MAINPWR_FACTOR);
}

/**
 * @brief Read battery raw voltage value
 * 
 * @return uint16_t Raw voltage value in mV
 */
static uint16_t es_pm_read_battery_voltage_raw(void)
{
    return es_adc_read_battery_voltage(PM_BATTERY_FACTOR);
}

/**
 * @brief Read raw voltage value
 * 
 * @return uint16_t Raw voltage value in mV
 */
static uint16_t es_pm_read_voltage_raw(void)
{
    // Use main power supply voltage as default voltage
    return es_pm_read_main_voltage_raw();
}

/**
 * @brief Update voltage sampling
 */
static void es_pm_update_voltage(void)
{
    uint32_t current_time = es_os_get_tick_ms();
    
    // Check if update interval is reached
    if (es_os_check_timeout(s_pm_voltage.last_update_time, VOLTAGE_UPDATE_INTERVAL)) {
        // Get new voltage sample
        uint16_t new_sample = es_pm_read_voltage_raw();
        
        // Update sample array
        s_pm_voltage.samples[s_pm_voltage.sample_index] = new_sample;
        s_pm_voltage.sample_index = (s_pm_voltage.sample_index + 1) % VOLTAGE_SAMPLE_COUNT;
        
        // Calculate average voltage
        uint32_t sum = 0;
        for (int i = 0; i < VOLTAGE_SAMPLE_COUNT; i++) {
            sum += s_pm_voltage.samples[i];
        }
        s_pm_voltage.voltage_mv = sum / VOLTAGE_SAMPLE_COUNT;
        
        // Update timestamp
        s_pm_voltage.last_update_time = current_time;
        
        // Update main power supply voltage
        new_sample = es_pm_read_main_voltage_raw();
        s_pm_main_voltage.samples[s_pm_main_voltage.sample_index] = new_sample;
        s_pm_main_voltage.sample_index = (s_pm_main_voltage.sample_index + 1) % VOLTAGE_SAMPLE_COUNT;
        
        sum = 0;
        for (int i = 0; i < VOLTAGE_SAMPLE_COUNT; i++) {
            sum += s_pm_main_voltage.samples[i];
        }
        s_pm_main_voltage.voltage_mv = sum / VOLTAGE_SAMPLE_COUNT;
        s_pm_main_voltage.last_update_time = current_time;
        
        // Update battery voltage
        new_sample = es_pm_read_battery_voltage_raw();
        s_pm_battery_voltage.samples[s_pm_battery_voltage.sample_index] = new_sample;
        s_pm_battery_voltage.sample_index = (s_pm_battery_voltage.sample_index + 1) % VOLTAGE_SAMPLE_COUNT;
        
        sum = 0;
        for (int i = 0; i < VOLTAGE_SAMPLE_COUNT; i++) {
            sum += s_pm_battery_voltage.samples[i];
        }
        s_pm_battery_voltage.voltage_mv = sum / VOLTAGE_SAMPLE_COUNT;
        s_pm_battery_voltage.last_update_time = current_time;
    }
}

/**
 * @brief Get current voltage value
 * 
 * @return uint16_t Current voltage value in mV
 */
uint16_t es_pm_get_voltage(void)
{
    return s_pm_voltage.voltage_mv;
}

/**
 * @brief Get main power supply voltage
 * 
 * @return uint16_t Main power supply voltage value in mV
 */
uint16_t es_pm_get_main_voltage(void)
{
    return s_pm_main_voltage.voltage_mv;
}

/**
 * @brief Get battery voltage
 * 
 * @return uint16_t Battery voltage value in mV
 */
uint16_t es_pm_get_battery_voltage(void)
{
    return s_pm_battery_voltage.voltage_mv;
}

/**
 * @brief State machine coroutine main task
 */
static es_async_t es_pm_sm_task(es_coro_t *coro, void *ctx)
{
    es_pm_ctx_t *pm_ctx = (es_pm_ctx_t *)ctx;
    
    es_co_begin(coro);
    
    // State machine main loop
    while (1) {
        es_co_yield;

        static bool is_first_run = true;
        static uint32_t running_event = 0;
        static uint32_t last_tick_ms = 0;
        
        // Get current accumulated events
        running_event = pm_ctx->pending_event;
        last_tick_ms = es_os_get_tick_ms();

        //check if is acc on
        if(es_pm_get_filtered_acc_state()) {
            running_event |= ES_PM_EVENT_ON;
        } else {
            running_event |= ES_PM_EVENT_OFF;
        }

        // Update voltage sampling
        es_pm_update_voltage();

        running_event |= ES_PM_EVENT_TICK;
        pm_ctx->pending_event = ES_PM_EVENT_NONE;
        
        pm_ctx->next_state = ES_PM_STATE_NONE;

        if(is_first_run) {
            g_pm_ctx.cur_state = es_pm_get_filtered_acc_state() ? ES_PM_STATE_ON : ES_PM_STATE_OFF;
            running_event = ES_PM_EVENT_ENTRY;
            is_first_run = false;
        }

        es_co_await(s_pm_state_handlers[pm_ctx->cur_state], ctx, pm_ctx->cur_state, running_event);

        if(pm_ctx->next_state != ES_PM_STATE_NONE) {
            // Output state transition log
            ES_LOGI(TAG, "State transition: %s -> %s",  s_pm_state_names[pm_ctx->cur_state],  s_pm_state_names[pm_ctx->next_state]);
        
            // Trigger exit current state event
            es_co_await(s_pm_state_handlers[pm_ctx->cur_state], pm_ctx, pm_ctx->cur_state, ES_PM_EVENT_EXIT);
            
            // Update state
            pm_ctx->cur_state = pm_ctx->next_state;
            
            // Trigger enter new state event
            g_pm_ctx.enter_state_tick_ms = es_os_get_tick_ms();
            es_co_await(s_pm_state_handlers[pm_ctx->next_state], pm_ctx, pm_ctx->next_state, ES_PM_EVENT_ENTRY);
        }

        // Wait for cycle time
        es_co_wait(es_os_check_timeout(last_tick_ms, 1));
    }
    
    es_co_end;
}

/**
 * @brief Power management module initialization
 */
int es_pm_init(void)
{
    g_pm_ctx.pm_task.func = es_pm_sm_task;
    g_pm_ctx.pm_task.ctx = &g_pm_ctx;

    g_pm_ctx.enter_state_tick_ms = es_os_get_tick_ms();
    
    // Initialize voltage sampling array
    for (int i = 0; i < VOLTAGE_SAMPLE_COUNT; i++) {
        s_pm_voltage.samples[i] = es_pm_read_voltage_raw();
    }
    s_pm_voltage.voltage_mv = s_pm_voltage.samples[0]; // Initial value
    s_pm_voltage.last_update_time = es_os_get_tick_ms();
    
    // Initialize ADC sampling
    es_pm_adc_init();
    
    es_scheduler_task_add(es_scheduler_get_default(), &g_pm_ctx.pm_task);
    
    ES_LOGI(TAG, "Power management initialized");
    
    return 0;
}

void es_pm_set_task_enable(bool en)
{
    g_pm_ctx.pm_task.coro.enabled = en;
}

/**
 * @brief Trigger power management event
 * 
 * @param event Event type
 */
void es_pm_event_trigger(uint32_t event)
{
    g_pm_ctx.pending_event |= event;
}

/**
 * @brief Handle AT+PM command - power management control
 * 
 * Functions:
 * - Query current power status: AT+PM?
 * - Send event: AT+PM=<event_type>
 *   event_type: 1=ON, 2=OFF, 4=TIMEOUT
 * 
 * @param coro Coroutine context
 * @param ctx Command context
 * @param cmd_type Command type
 * @return Coroutine return value
 */
es_async_t at_srv_cmd_pm_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    int event_type = 0;
    char *endptr = NULL;
    
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_QUERY) { 
        // Send status response
        es_at_srv_fmt_send("+PM: %s\r\n",  s_pm_state_names[g_pm_ctx.cur_state]);
        es_at_srv_send_ok();
    }
    else if (cmd_type == ES_AT_SRV_CMD_SET) {
        // Set command - send event
        if (ctx->param_count < 1) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_PARAM_MISSING);
            es_co_exit;
        }
        
        // Parse event type parameter
        if (ctx->params[0].len > 0) {
            // Convert string parameter to integer
            event_type = strtol(ctx->params[0].param, &endptr, 10);
            if (endptr == ctx->params[0].param) {
                // Conversion failed
                es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
                es_co_exit;
            }
        } else {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_co_exit;
        }
        
        // Trigger power management event
        g_pm_ctx.pending_event |= (1 << (event_type));
        es_at_srv_send_ok();
    }
    else if (cmd_type == ES_AT_SRV_CMD_TEST) {
        // Test command
        es_at_srv_fmt_send("AT+PM=<EVENT> (EVENT: 3=ON, 4=OFF, 5=SLEEP)\r\n");
        es_at_srv_send_ok();
    }
    else {
        // Unsupported command type
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
    }
    
    es_co_end;
}

/**
 * @brief Handle AT+VOLT command - voltage query
 * 
 * Functions:
 * - Query current voltage: AT+VOLT?
 * - Query main power voltage: AT+VOLT=MAIN
 * - Query battery voltage: AT+VOLT=BAT
 * 
 * @param coro Coroutine context
 * @param ctx Command context
 * @param cmd_type Command type
 * @return Coroutine return value
 */
es_async_t at_srv_cmd_volt_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_QUERY) { 
        // Send voltage response - default return main power voltage
        es_at_srv_fmt_send("+VOLT: %u.%03u V\r\n", 
                           s_pm_main_voltage.voltage_mv / 1000, 
                           s_pm_main_voltage.voltage_mv % 1000);
        es_at_srv_send_ok();
    }
    else if (cmd_type == ES_AT_SRV_CMD_SET) {
        if (ctx->param_count >= 1) {
            if (strncmp(ctx->params[0].param, "MAIN", ctx->params[0].len) == 0) {
                // Query main power voltage
                es_at_srv_fmt_send("+VOLT: MAIN %u.%03u V\r\n", 
                                   s_pm_main_voltage.voltage_mv / 1000, 
                                   s_pm_main_voltage.voltage_mv % 1000);
                es_at_srv_send_ok();
            }
            else if (strncmp(ctx->params[0].param, "BAT", ctx->params[0].len) == 0) {
                // Query battery voltage
                es_at_srv_fmt_send("+VOLT: BAT %u.%03u V\r\n", 
                                   s_pm_battery_voltage.voltage_mv / 1000, 
                                   s_pm_battery_voltage.voltage_mv % 1000);
                es_at_srv_send_ok();
            }
            else {
                es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            }
        }
        else {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_PARAM_MISSING);
        }
    }
    else if (cmd_type == ES_AT_SRV_CMD_TEST) {
        // Test command
        es_at_srv_fmt_send("AT+VOLT?\r\n");
        es_at_srv_fmt_send("AT+VOLT=MAIN\r\n");
        es_at_srv_fmt_send("AT+VOLT=BAT\r\n");
        es_at_srv_send_ok();
    }
    else {
        // Unsupported command type
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
    }
    
    es_co_end;
}

static volatile uint8_t s_acc_state = 0xff; //Used to simulate ACC state
#ifdef WIN32
int es_read_acc_state(void)
{
    return s_acc_state;
}
#endif
es_async_t at_srv_cmd_acc_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type) 
{
    char *endptr = NULL;
    es_co_begin(coro);

    if (cmd_type == ES_AT_SRV_CMD_QUERY) {
        //read the pin of acc
        es_at_srv_fmt_send("+ACC: %d\r\n", es_read_acc_state());
        es_at_srv_send_ok();
    }
    else if (cmd_type == ES_AT_SRV_CMD_SET) {
        //set the pin of acc
        int state = strtol(ctx->params[0].param, &endptr, 10);
        if (endptr == ctx->params[0].param) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_co_exit;
        }
        s_acc_state = state;
        es_at_srv_send_ok();
    }
    else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
    }

    es_co_end;
}



/**
 * @brief Initialize ADC sampling
 * 
 * @return int 0: success, other values: failure
 */
int es_pm_adc_init(void)
{
    int ret = es_adc_init();
    
    if (ret != 0) {
        ES_LOGI(TAG, "ADC init failed");
        return ret;
    }
    
    // Initialize voltage sampling array
    for (int i = 0; i < VOLTAGE_SAMPLE_COUNT; i++) {
        s_pm_main_voltage.samples[i] = es_pm_read_main_voltage_raw();
        s_pm_battery_voltage.samples[i] = es_pm_read_battery_voltage_raw();
    }
    
    s_pm_main_voltage.voltage_mv = s_pm_main_voltage.samples[0];
    s_pm_battery_voltage.voltage_mv = s_pm_battery_voltage.samples[0];
    
    s_pm_main_voltage.last_update_time = es_os_get_tick_ms();
    s_pm_battery_voltage.last_update_time = es_os_get_tick_ms();
    
    ES_LOGI(TAG, "PM ADC initialized, main voltage: %umV, battery voltage: %umV", 
            s_pm_main_voltage.voltage_mv, s_pm_battery_voltage.voltage_mv);
    
    return 0;
}

