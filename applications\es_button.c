#include "es_button.h"
#include "es_coro.h"
#include "es_drv_os.h"
#include "es_scheduler.h"
#include <string.h>

/* Button static array */
static es_button_t button_pool[ES_BUTTON_MAX_COUNT] = {0};

/* Module initialization flag */
static uint8_t button_module_initialized = 0;

static es_coro_task_t button_task = {0};

static void es_button_state_machine(es_button_t *button);

static es_async_t es_button_process_task(es_coro_t *coro, void *ctx)
{
    es_co_begin(coro);
    static uint8_t i = 0;

    while (1) {
        // Yield CPU control, allow other tasks to run
        es_co_yield;
        
        for (i = 0; i < ES_BUTTON_MAX_COUNT; i++) {
            es_button_t *button = &button_pool[i];
            if (button->is_used && button->is_active) {
                es_button_state_machine(button);
            }
            es_co_yield;
        }
    }

    es_co_end;
}

/**
 * @brief Initialize button module
 */
int es_button_init(void)
{
    if (button_module_initialized) {
        return 0;
    }
    
    /* Initialize GPIO module */
    if (es_pin_init() != 0) {
        return -1;
    }
    
    button_task.name = "button_task";
    button_task.func = es_button_process_task;
    button_task.ctx = NULL;
    
    es_scheduler_task_add(es_scheduler_get_default(), &button_task);
    
    button_module_initialized = 1;
    
    return 0;
}

/**
 * @brief Get current timestamp (milliseconds)
 */
uint32_t es_button_get_tick(void)
{
    return es_os_get_tick_ms();
}

/**
 * @brief Read button level
 */
static uint8_t es_button_read_pin(es_button_t *button)
{
    int pin_value = es_pin_read(button->pin);
    if (pin_value < 0) {
        return !button->active_level; /* Read failed, return inactive state */
    }
    return (uint8_t)pin_value;
}

/**
 * @brief Check if button is pressed
 */
static uint8_t es_button_is_pressed(es_button_t *button)
{
    uint8_t pin_level = es_button_read_pin(button);
    return (pin_level == button->active_level) ? 1 : 0;
}

/**
 * @brief Trigger button event
 */
static void es_button_trigger_event(es_button_t *button, es_button_event_t event)
{
    if (button->callback) {
        button->callback(button->pin, event, button->user_data);
    }
}

/**
 * @brief Button state machine processing
 */
static void es_button_state_machine(es_button_t *button)
{
    uint32_t current_time = es_button_get_tick();
    uint8_t is_pressed = es_button_is_pressed(button);
    uint32_t press_duration = 0;
    uint32_t release_duration = 0;
    
    if (button->press_time > 0) {
        press_duration = current_time - button->press_time;
    }
    
    if (button->release_time > 0) {
        release_duration = current_time - button->release_time;
    }
    
    switch (button->state) {
        case ES_BUTTON_STATE_IDLE:
            if (is_pressed) {
                button->press_time = current_time;
                button->state = ES_BUTTON_STATE_PRESSED;
                es_button_trigger_event(button, ES_BUTTON_EVENT_PRESS);
            }
            break;
            
        case ES_BUTTON_STATE_PRESSED:
            if (!is_pressed) {
                /* Button released */
                button->release_time = current_time;
                if (press_duration >= button->debounce_time) {
                    button->state = ES_BUTTON_STATE_WAIT_DOUBLE;
                    es_button_trigger_event(button, ES_BUTTON_EVENT_RELEASE);
                } else {
                    /* Released within debounce time, ignore */
                    button->state = ES_BUTTON_STATE_IDLE;
                    button->press_time = 0;
                }
            } else if (press_duration >= button->long_press_time) {
                /* Long press triggered */
                button->state = ES_BUTTON_STATE_LONG_PRESSED;
                es_button_trigger_event(button, ES_BUTTON_EVENT_LONG_PRESS);
            }
            break;
            
        case ES_BUTTON_STATE_WAIT_DOUBLE:
            if (is_pressed) {
                /* Second press detected */
                button->press_time = current_time;
                button->state = ES_BUTTON_STATE_WAIT_RELEASE;
            } else if (release_duration >= button->double_click_time) {
                /* Double click timeout, confirm as single click */
                button->state = ES_BUTTON_STATE_IDLE;
                button->press_time = 0;
                button->release_time = 0;
                es_button_trigger_event(button, ES_BUTTON_EVENT_SINGLE_CLICK);
            }
            break;
            
        case ES_BUTTON_STATE_WAIT_RELEASE:
            if (!is_pressed) {
                /* Second release */
                button->release_time = current_time;
                if (press_duration >= button->debounce_time) {
                    /* Confirm double click */
                    button->state = ES_BUTTON_STATE_IDLE;
                    button->press_time = 0;
                    button->release_time = 0;
                    es_button_trigger_event(button, ES_BUTTON_EVENT_RELEASE);
                    es_button_trigger_event(button, ES_BUTTON_EVENT_DOUBLE_CLICK);
                } else {
                    /* Released within debounce time, return to wait double click state */
                    button->state = ES_BUTTON_STATE_WAIT_DOUBLE;
                    button->press_time = 0;
                }
            } else if (press_duration >= button->long_press_time) {
                /* Second press can also become long press */
                button->state = ES_BUTTON_STATE_LONG_PRESSED;
                es_button_trigger_event(button, ES_BUTTON_EVENT_LONG_PRESS);
            }
            break;
            
        case ES_BUTTON_STATE_LONG_PRESSED:
            if (!is_pressed) {
                /* Long press released */
                button->state = ES_BUTTON_STATE_IDLE;
                button->press_time = 0;
                button->release_time = 0;
                es_button_trigger_event(button, ES_BUTTON_EVENT_RELEASE);
                es_button_trigger_event(button, ES_BUTTON_EVENT_LONG_RELEASE);
            }
            break;
            
        default:
            button->state = ES_BUTTON_STATE_IDLE;
            button->press_time = 0;
            button->release_time = 0;
            break;
    }
}

/**
 * @brief Create button
 */
es_button_t *es_button_create(const es_button_config_t *config)
{
    if (!config || !button_module_initialized) {
        return NULL;
    }
    
    /* Find free button slot */
    es_button_t *button = NULL;
    for (int i = 0; i < ES_BUTTON_MAX_COUNT; i++) {
        if (!button_pool[i].is_used) {
            button = &button_pool[i];
            break;
        }
    }
    
    if (!button) {
        return NULL; /* No free slots */
    }
    
    /* Initialize button structure */
    memset(button, 0, sizeof(es_button_t));
    
    /* Copy configuration parameters */
    button->pin = config->pin;
    button->active_level = config->active_level;
    button->callback = config->callback;
    button->user_data = config->user_data;
    
    /* Set default values */
    button->debounce_time = (config->debounce_time == 0) ? 
                           ES_BUTTON_DEFAULT_DEBOUNCE_TIME : config->debounce_time;
    button->double_click_time = (config->double_click_time == 0) ? 
                               ES_BUTTON_DEFAULT_DOUBLE_CLICK_TIME : config->double_click_time;
    button->long_press_time = (config->long_press_time == 0) ? 
                             ES_BUTTON_DEFAULT_LONG_PRESS_TIME : config->long_press_time;
    
    button->state = ES_BUTTON_STATE_IDLE;
    button->is_active = 0;
    button->is_used = 1;
    
    /* Configure GPIO */
    if (button->active_level == PIN_LOW) {
        es_pin_mode(button->pin, PIN_MODE_INPUT_PULLUP);
    } else {
        es_pin_mode(button->pin, PIN_MODE_INPUT_PULLDOWN);
    }
    
    return button;
}

/**
 * @brief Delete button
 */
int es_button_delete(es_button_t *button)
{
    if (!button || !button_module_initialized) {
        return -1;
    }
    
    /* Check if button is in pool */
    if (button < button_pool || button >= button_pool + ES_BUTTON_MAX_COUNT) {
        return -1; /* Invalid button pointer */
    }
    
    /* Disable and release button */
    button->is_active = 0;
    button->is_used = 0;
    memset(button, 0, sizeof(es_button_t));
    
    return 0;
}

/**
 * @brief Enable button
 */
int es_button_enable(es_button_t *button)
{
    if (!button) {
        return -1;
    }
    
    button->is_active = 1;
    button->state = ES_BUTTON_STATE_IDLE;
    button->press_time = 0;
    button->release_time = 0;
    
    return 0;
}

/**
 * @brief Disable button
 */
int es_button_disable(es_button_t *button)
{
    if (!button) {
        return -1;
    }
    
    button->is_active = 0;
    button->state = ES_BUTTON_STATE_IDLE;
    button->press_time = 0;
    button->release_time = 0;
    
    return 0;
}
