/**
 * @file es_aes.c
 * @brief AES encryption/decryption implementation
 */
#include "es_aes.h"
#include <string.h>

/* AES Constants */
#define AES_BLOCKLEN 16 // Block length in bytes - AES is 128b block only
#define AES_KEYLEN 16   // Key length in bytes
#define AES_keyExpSize 176

/* AES Data Structures */
typedef uint8_t state_t[4][4];

// The number of columns comprising a state in AES. This is a constant in AES. Value=4
#define Nb 4
#define Nk 4        // The number of 32 bit words in a key.
#define Nr 10       // The number of rounds in AES Cipher.

// j<PERSON><PERSON>@github points out that declaring Multiply as a function
// reduces code size considerably with the Keil ARM compiler.
#define MULTIPLY_AS_A_FUNCTION 0

// The lookup-tables are marked const so they can be placed in read-only storage instead of RAM
// The numbers below can be computed dynamically trading ROM for RAM - 
// This can be useful in (embedded) bootloader applications, where ROM is often limited.
static const uint8_t sbox[256] = {
  //0     1    2      3     4    5     6     7      8    9     A      B    C     D     E     F
  0x63, 0x7c, 0x77, 0x7b, 0xf2, 0x6b, 0x6f, 0xc5, 0x30, 0x01, 0x67, 0x2b, 0xfe, 0xd7, 0xab, 0x76,
  0xca, 0x82, 0xc9, 0x7d, 0xfa, 0x59, 0x47, 0xf0, 0xad, 0xd4, 0xa2, 0xaf, 0x9c, 0xa4, 0x72, 0xc0,
  0xb7, 0xfd, 0x93, 0x26, 0x36, 0x3f, 0xf7, 0xcc, 0x34, 0xa5, 0xe5, 0xf1, 0x71, 0xd8, 0x31, 0x15,
  0x04, 0xc7, 0x23, 0xc3, 0x18, 0x96, 0x05, 0x9a, 0x07, 0x12, 0x80, 0xe2, 0xeb, 0x27, 0xb2, 0x75,
  0x09, 0x83, 0x2c, 0x1a, 0x1b, 0x6e, 0x5a, 0xa0, 0x52, 0x3b, 0xd6, 0xb3, 0x29, 0xe3, 0x2f, 0x84,
  0x53, 0xd1, 0x00, 0xed, 0x20, 0xfc, 0xb1, 0x5b, 0x6a, 0xcb, 0xbe, 0x39, 0x4a, 0x4c, 0x58, 0xcf,
  0xd0, 0xef, 0xaa, 0xfb, 0x43, 0x4d, 0x33, 0x85, 0x45, 0xf9, 0x02, 0x7f, 0x50, 0x3c, 0x9f, 0xa8,
  0x51, 0xa3, 0x40, 0x8f, 0x92, 0x9d, 0x38, 0xf5, 0xbc, 0xb6, 0xda, 0x21, 0x10, 0xff, 0xf3, 0xd2,
  0xcd, 0x0c, 0x13, 0xec, 0x5f, 0x97, 0x44, 0x17, 0xc4, 0xa7, 0x7e, 0x3d, 0x64, 0x5d, 0x19, 0x73,
  0x60, 0x81, 0x4f, 0xdc, 0x22, 0x2a, 0x90, 0x88, 0x46, 0xee, 0xb8, 0x14, 0xde, 0x5e, 0x0b, 0xdb,
  0xe0, 0x32, 0x3a, 0x0a, 0x49, 0x06, 0x24, 0x5c, 0xc2, 0xd3, 0xac, 0x62, 0x91, 0x95, 0xe4, 0x79,
  0xe7, 0xc8, 0x37, 0x6d, 0x8d, 0xd5, 0x4e, 0xa9, 0x6c, 0x56, 0xf4, 0xea, 0x65, 0x7a, 0xae, 0x08,
  0xba, 0x78, 0x25, 0x2e, 0x1c, 0xa6, 0xb4, 0xc6, 0xe8, 0xdd, 0x74, 0x1f, 0x4b, 0xbd, 0x8b, 0x8a,
  0x70, 0x3e, 0xb5, 0x66, 0x48, 0x03, 0xf6, 0x0e, 0x61, 0x35, 0x57, 0xb9, 0x86, 0xc1, 0x1d, 0x9e,
  0xe1, 0xf8, 0x98, 0x11, 0x69, 0xd9, 0x8e, 0x94, 0x9b, 0x1e, 0x87, 0xe9, 0xce, 0x55, 0x28, 0xdf,
  0x8c, 0xa1, 0x89, 0x0d, 0xbf, 0xe6, 0x42, 0x68, 0x41, 0x99, 0x2d, 0x0f, 0xb0, 0x54, 0xbb, 0x16 };

static const uint8_t rsbox[256] = {
  0x52, 0x09, 0x6a, 0xd5, 0x30, 0x36, 0xa5, 0x38, 0xbf, 0x40, 0xa3, 0x9e, 0x81, 0xf3, 0xd7, 0xfb,
  0x7c, 0xe3, 0x39, 0x82, 0x9b, 0x2f, 0xff, 0x87, 0x34, 0x8e, 0x43, 0x44, 0xc4, 0xde, 0xe9, 0xcb,
  0x54, 0x7b, 0x94, 0x32, 0xa6, 0xc2, 0x23, 0x3d, 0xee, 0x4c, 0x95, 0x0b, 0x42, 0xfa, 0xc3, 0x4e,
  0x08, 0x2e, 0xa1, 0x66, 0x28, 0xd9, 0x24, 0xb2, 0x76, 0x5b, 0xa2, 0x49, 0x6d, 0x8b, 0xd1, 0x25,
  0x72, 0xf8, 0xf6, 0x64, 0x86, 0x68, 0x98, 0x16, 0xd4, 0xa4, 0x5c, 0xcc, 0x5d, 0x65, 0xb6, 0x92,
  0x6c, 0x70, 0x48, 0x50, 0xfd, 0xed, 0xb9, 0xda, 0x5e, 0x15, 0x46, 0x57, 0xa7, 0x8d, 0x9d, 0x84,
  0x90, 0xd8, 0xab, 0x00, 0x8c, 0xbc, 0xd3, 0x0a, 0xf7, 0xe4, 0x58, 0x05, 0xb8, 0xb3, 0x45, 0x06,
  0xd0, 0x2c, 0x1e, 0x8f, 0xca, 0x3f, 0x0f, 0x02, 0xc1, 0xaf, 0xbd, 0x03, 0x01, 0x13, 0x8a, 0x6b,
  0x3a, 0x91, 0x11, 0x41, 0x4f, 0x67, 0xdc, 0xea, 0x97, 0xf2, 0xcf, 0xce, 0xf0, 0xb4, 0xe6, 0x73,
  0x96, 0xac, 0x74, 0x22, 0xe7, 0xad, 0x35, 0x85, 0xe2, 0xf9, 0x37, 0xe8, 0x1c, 0x75, 0xdf, 0x6e,
  0x47, 0xf1, 0x1a, 0x71, 0x1d, 0x29, 0xc5, 0x89, 0x6f, 0xb7, 0x62, 0x0e, 0xaa, 0x18, 0xbe, 0x1b,
  0xfc, 0x56, 0x3e, 0x4b, 0xc6, 0xd2, 0x79, 0x20, 0x9a, 0xdb, 0xc0, 0xfe, 0x78, 0xcd, 0x5a, 0xf4,
  0x1f, 0xdd, 0xa8, 0x33, 0x88, 0x07, 0xc7, 0x31, 0xb1, 0x12, 0x10, 0x59, 0x27, 0x80, 0xec, 0x5f,
  0x60, 0x51, 0x7f, 0xa9, 0x19, 0xb5, 0x4a, 0x0d, 0x2d, 0xe5, 0x7a, 0x9f, 0x93, 0xc9, 0x9c, 0xef,
  0xa0, 0xe0, 0x3b, 0x4d, 0xae, 0x2a, 0xf5, 0xb0, 0xc8, 0xeb, 0xbb, 0x3c, 0x83, 0x53, 0x99, 0x61,
  0x17, 0x2b, 0x04, 0x7e, 0xba, 0x77, 0xd6, 0x26, 0xe1, 0x69, 0x14, 0x63, 0x55, 0x21, 0x0c, 0x7d };

// The round constant word array, Rcon[i], contains the values given by 
// x to the power (i-1) being powers of x (x is denoted as {02}) in the field GF(2^8)
static const uint8_t Rcon[11] = {
  0x8d, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36 };

// Get S-box value
#define get_sbox_value(num) (sbox[(num)])
#define get_sbox_invert(num) (rsbox[(num)])

// Key expansion function
static void aes_key_expansion(uint8_t* round_key, const uint8_t* key)
{
  unsigned i, j, k;
  uint8_t tempa[4]; // Used for the column/row operations
  
  // The first round key is the key itself.
  for (i = 0; i < Nk; ++i)
  {
    round_key[(i * 4) + 0] = key[(i * 4) + 0];
    round_key[(i * 4) + 1] = key[(i * 4) + 1];
    round_key[(i * 4) + 2] = key[(i * 4) + 2];
    round_key[(i * 4) + 3] = key[(i * 4) + 3];
  }

  // All other round keys are found from the previous round keys.
  for (i = Nk; i < Nb * (Nr + 1); ++i)
  {
    {
      k = (i - 1) * 4;
      tempa[0]=round_key[k + 0];
      tempa[1]=round_key[k + 1];
      tempa[2]=round_key[k + 2];
      tempa[3]=round_key[k + 3];
    }

    if (i % Nk == 0)
    {
      // This function shifts the 4 bytes in a word to the left once.
      // [a0,a1,a2,a3] becomes [a1,a2,a3,a0]

      // Function RotWord()
      {
        const uint8_t u8tmp = tempa[0];
        tempa[0] = tempa[1];
        tempa[1] = tempa[2];
        tempa[2] = tempa[3];
        tempa[3] = u8tmp;
      }

      // SubWord() is a function that takes a four-byte input word and 
      // applies the S-box to each of the four bytes to produce an output word.

      // Function Subword()
      {
        tempa[0] = get_sbox_value(tempa[0]);
        tempa[1] = get_sbox_value(tempa[1]);
        tempa[2] = get_sbox_value(tempa[2]);
        tempa[3] = get_sbox_value(tempa[3]);
      }

      tempa[0] = tempa[0] ^ Rcon[i/Nk];
    }
    j = i * 4; k=(i - Nk) * 4;
    round_key[j + 0] = round_key[k + 0] ^ tempa[0];
    round_key[j + 1] = round_key[k + 1] ^ tempa[1];
    round_key[j + 2] = round_key[k + 2] ^ tempa[2];
    round_key[j + 3] = round_key[k + 3] ^ tempa[3];
  }
}

// Add round key transformation
static void aes_add_round_key(uint8_t round, state_t* state, const uint8_t* round_key)
{
  uint8_t i,j;
  for (i = 0; i < 4; ++i)
  {
    for (j = 0; j < 4; ++j)
    {
      (*state)[i][j] ^= round_key[(round * Nb * 4) + (i * Nb) + j];
    }
  }
}

// Substitute bytes transformation
static void aes_sub_bytes(state_t* state)
{
  uint8_t i, j;
  for (i = 0; i < 4; ++i)
  {
    for (j = 0; j < 4; ++j)
    {
      (*state)[j][i] = get_sbox_value((*state)[j][i]);
    }
  }
}

// Shift rows transformation
static void aes_shift_rows(state_t* state)
{
  uint8_t temp;

  // Rotate first row 1 columns to left  
  temp           = (*state)[0][1];
  (*state)[0][1] = (*state)[1][1];
  (*state)[1][1] = (*state)[2][1];
  (*state)[2][1] = (*state)[3][1];
  (*state)[3][1] = temp;

  // Rotate second row 2 columns to left  
  temp           = (*state)[0][2];
  (*state)[0][2] = (*state)[2][2];
  (*state)[2][2] = temp;

  temp           = (*state)[1][2];
  (*state)[1][2] = (*state)[3][2];
  (*state)[3][2] = temp;

  // Rotate third row 3 columns to left
  temp           = (*state)[0][3];
  (*state)[0][3] = (*state)[3][3];
  (*state)[3][3] = (*state)[2][3];
  (*state)[2][3] = (*state)[1][3];
  (*state)[1][3] = temp;
}

// Multiplication in GF(2^8)
#if MULTIPLY_AS_A_FUNCTION
static uint8_t aes_multiply(uint8_t x, uint8_t y)
{
  return (((y & 1) * x) ^
       ((y>>1 & 1) * aes_xtime(x)) ^
       ((y>>2 & 1) * aes_xtime(aes_xtime(x))) ^
       ((y>>3 & 1) * aes_xtime(aes_xtime(aes_xtime(x)))) ^
       ((y>>4 & 1) * aes_xtime(aes_xtime(aes_xtime(aes_xtime(x)))))); /* this last call to xtime() can be omitted */
}
#else
#define aes_multiply(x, y)                               \
      (  ((y & 1) * x) ^                              \
      ((y>>1 & 1) * aes_xtime(x)) ^                   \
      ((y>>2 & 1) * aes_xtime(aes_xtime(x))) ^        \
      ((y>>3 & 1) * aes_xtime(aes_xtime(aes_xtime(x)))) ^ \
      ((y>>4 & 1) * aes_xtime(aes_xtime(aes_xtime(aes_xtime(x))))))
#endif

// Helper function for finite field multiplication
static uint8_t aes_xtime(uint8_t x)
{
  return ((x<<1) ^ (((x>>7) & 1) * 0x1b));
}

// Column mixing
static void aes_mix_columns(state_t* state)
{
  uint8_t i;
  uint8_t tmp, tm, t;
  for (i = 0; i < 4; ++i)
  {  
    t   = (*state)[i][0];
    tmp = (*state)[i][0] ^ (*state)[i][1] ^ (*state)[i][2] ^ (*state)[i][3];
    tm  = (*state)[i][0] ^ (*state)[i][1]; tm = aes_xtime(tm); (*state)[i][0] ^= tm ^ tmp;
    tm  = (*state)[i][1] ^ (*state)[i][2]; tm = aes_xtime(tm); (*state)[i][1] ^= tm ^ tmp;
    tm  = (*state)[i][2] ^ (*state)[i][3]; tm = aes_xtime(tm); (*state)[i][2] ^= tm ^ tmp;
    tm  = (*state)[i][3] ^ t;              tm = aes_xtime(tm); (*state)[i][3] ^= tm ^ tmp;
  }
}

// InvMixColumns transformation
static void aes_inv_mix_columns(state_t* state)
{
  int i;
  uint8_t a, b, c, d;
  for (i = 0; i < 4; ++i)
  { 
    a = (*state)[i][0];
    b = (*state)[i][1];
    c = (*state)[i][2];
    d = (*state)[i][3];

    (*state)[i][0] = aes_multiply(a, 0x0e) ^ aes_multiply(b, 0x0b) ^ aes_multiply(c, 0x0d) ^ aes_multiply(d, 0x09);
    (*state)[i][1] = aes_multiply(a, 0x09) ^ aes_multiply(b, 0x0e) ^ aes_multiply(c, 0x0b) ^ aes_multiply(d, 0x0d);
    (*state)[i][2] = aes_multiply(a, 0x0d) ^ aes_multiply(b, 0x09) ^ aes_multiply(c, 0x0e) ^ aes_multiply(d, 0x0b);
    (*state)[i][3] = aes_multiply(a, 0x0b) ^ aes_multiply(b, 0x0d) ^ aes_multiply(c, 0x09) ^ aes_multiply(d, 0x0e);
  }
}

// InvSubstitute bytes transformation
static void aes_inv_sub_bytes(state_t* state)
{
  uint8_t i, j;
  for (i = 0; i < 4; ++i)
  {
    for (j = 0; j < 4; ++j)
    {
      (*state)[j][i] = get_sbox_invert((*state)[j][i]);
    }
  }
}

// InvShiftRows transformation
static void aes_inv_shift_rows(state_t* state)
{
  uint8_t temp;

  // Rotate first row 1 columns to right  
  temp = (*state)[3][1];
  (*state)[3][1] = (*state)[2][1];
  (*state)[2][1] = (*state)[1][1];
  (*state)[1][1] = (*state)[0][1];
  (*state)[0][1] = temp;

  // Rotate second row 2 columns to right 
  temp = (*state)[0][2];
  (*state)[0][2] = (*state)[2][2];
  (*state)[2][2] = temp;

  temp = (*state)[1][2];
  (*state)[1][2] = (*state)[3][2];
  (*state)[3][2] = temp;

  // Rotate third row 3 columns to right
  temp = (*state)[0][3];
  (*state)[0][3] = (*state)[1][3];
  (*state)[1][3] = (*state)[2][3];
  (*state)[2][3] = (*state)[3][3];
  (*state)[3][3] = temp;
}

// Encryption function
static void aes_cipher(state_t* state, const uint8_t* round_key)
{
  uint8_t round = 0;

  // Add the First round key to the state before starting the rounds.
  aes_add_round_key(0, state, round_key);

  // There will be Nr rounds.
  // The first Nr-1 rounds are identical.
  // These Nr rounds are executed in the loop below.
  // Last one without MixColumns()
  for (round = 1; ; ++round)
  {
    aes_sub_bytes(state);
    aes_shift_rows(state);
    if (round == Nr) {
      break;
    }
    aes_mix_columns(state);
    aes_add_round_key(round, state, round_key);
  }
  // Add round key to last round
  aes_add_round_key(Nr, state, round_key);
}

// Decryption function
static void aes_inv_cipher(state_t* state, const uint8_t* round_key)
{
  uint8_t round = 0;

  // Add the First round key to the state before starting the rounds.
  aes_add_round_key(Nr, state, round_key);

  // There will be Nr rounds.
  // The first Nr-1 rounds are identical.
  // These Nr rounds are executed in the loop below.
  // Last one without InvMixColumn()
  for (round = (Nr - 1); ; --round)
  {
    aes_inv_shift_rows(state);
    aes_inv_sub_bytes(state);
    aes_add_round_key(round, state, round_key);
    if (round == 0) {
      break;
    }
    aes_inv_mix_columns(state);
  }
}

// ECB mode encryption of a single block
static void aes_ecb_encrypt(const es_aes_ctx_t* ctx, uint8_t* buf)
{
  // The next function call encrypts the PlainText with the Key using AES algorithm.
  aes_cipher((state_t*)buf, ctx->round_key);
}

// ECB mode decryption of a single block
static void aes_ecb_decrypt(const es_aes_ctx_t* ctx, uint8_t* buf)
{
  // The next function call decrypts the PlainText with the Key using AES algorithm.
  aes_inv_cipher((state_t*)buf, ctx->round_key);
}

// ECB mode encryption of string (with padding)
static int aes_ecb_encrypt_buf(uint8_t *in, int len, const uint8_t *key)
{
    es_aes_ctx_t ctx;
    es_aes_init(&ctx, key);
    
    int pad_len = 0;
    if (len % 16 != 0)
    {
        pad_len = 16 - len % 16;
    }
    else
    {
        pad_len = 16;
    }

    for (int i = 0; i < pad_len; i++)
    {
        in[len + i] = pad_len;
    }

    for (int i = 0; i < len + pad_len; i += 16)
    {
        aes_ecb_encrypt(&ctx, in + i);
    }

    return len + pad_len;
}

// ECB mode decryption of string (removing padding)
static int aes_ecb_decrypt_buf(uint8_t *in, int len, const uint8_t *key)
{
    es_aes_ctx_t ctx;
    es_aes_init(&ctx, key);

    if (len % 16 != 0)
    {
        return -1;
    }

    for (int i = 0; i < len; i += 16)
    {
        aes_ecb_decrypt(&ctx, in + i);
    }

    const int pad_len = in[len - 1];
    if (pad_len > 16)
    {
        return -1;
    }

    return len - pad_len;
}

/**
 * @brief Initialize AES-128 encryption context
 * 
 * @param ctx AES context pointer
 * @param key 16-byte key data
 */
void es_aes_init(es_aes_ctx_t *ctx, const uint8_t key[AES_KEY_SIZE])
{
    aes_key_expansion(ctx->round_key, key);
}

/**
 * @brief AES-128 ECB mode encryption of a single block
 * 
 * @param ctx AES context pointer
 * @param plaintext 16-byte plaintext input
 * @param ciphertext 16-byte ciphertext output
 */
void es_aes_encrypt_block(const es_aes_ctx_t *ctx, const uint8_t plaintext[AES_BLOCK_SIZE], uint8_t ciphertext[AES_BLOCK_SIZE])
{
    // Copy plaintext to ciphertext, because AES_ECB_encrypt will directly modify the buffer
    memcpy(ciphertext, plaintext, AES_BLOCK_SIZE);
    
    // Call ECB encryption function
    aes_ecb_encrypt(ctx, ciphertext);
}

/**
 * @brief AES-128 ECB mode decryption of a single block
 * 
 * @param ctx AES context pointer
 * @param ciphertext 16-byte ciphertext input
 * @param plaintext 16-byte plaintext output
 */
void es_aes_decrypt_block(const es_aes_ctx_t *ctx, const uint8_t ciphertext[AES_BLOCK_SIZE], uint8_t plaintext[AES_BLOCK_SIZE])
{
    // Copy ciphertext to plaintext, because AES_ECB_decrypt will directly modify the buffer
    memcpy(plaintext, ciphertext, AES_BLOCK_SIZE);
    
    // Call ECB decryption function
    aes_ecb_decrypt(ctx, plaintext);
}

/**
 * @brief Use AES-128 encryption for string
 * 
 * @param ctx AES context pointer
 * @param plaintext Plaintext string
 * @param ciphertext Ciphertext output buffer
 * @param length Plaintext string length
 * @return int Encrypted byte count
 */
int es_aes_encrypt_string(const uint8_t *key, const char *plaintext, uint8_t *ciphertext, int length)
{
    // Copy data to ciphertext buffer (because ecb_encrypt will directly modify the buffer)
    memcpy(ciphertext, plaintext, length);
    
    // Use simpler ecb_encrypt function
    return aes_ecb_encrypt_buf(ciphertext, length, key);
}

/**
 * @brief Use AES-128 decryption for string
 * 
 * @param ctx AES context pointer
 * @param ciphertext Ciphertext data
 * @param plaintext Plaintext output buffer
 * @param length Ciphertext data length
 * @return int Decrypted string length, -1 on failure
 */
int es_aes_decrypt_string(const uint8_t *key, const uint8_t *ciphertext, char *plaintext, int length)
{
    // Copy data to plaintext buffer (because ecb_decrypt will directly modify the buffer)
    memcpy(plaintext, ciphertext, length);
    
    // Use simpler ecb_decrypt function
    int result = aes_ecb_decrypt_buf((uint8_t*)plaintext, length, key);
    
    // Add string terminator
    if (result > 0) {
        plaintext[result] = '\0';
    }
    
    return result;
}

int es_aes_encrypt_bytes(uint8_t *input, uint8_t *output, int length, const uint8_t *key)
{
    return aes_ecb_encrypt_buf(input, length, key);
}

int es_aes_decrypt_bytes(uint8_t *input, uint8_t *output, int length, const uint8_t *key)
{
    if (length % AES_BLOCK_SIZE != 0) {
        return -1;
    }

    return aes_ecb_decrypt_buf(input, length, key);
}
