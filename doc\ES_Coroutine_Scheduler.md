# ES 协程调度系统文档

## 1. 概述

ES 协程调度系统是 ES MCU Framework 的核心组件之一，提供了轻量级的无栈协程实现和任务调度功能。该系统基于 Duff's Device 技术实现，为嵌入式系统提供了高效的异步编程支持。

### 1.1 主要特性

- **无栈协程**: 基于状态机的协程实现，内存占用极小
- **协作式调度**: 协程主动让出CPU控制权，避免抢占式调度的开销
- **定时器支持**: 集成软件定时器，支持周期性和单次触发
- **事件驱动**: 支持事件订阅和发布机制
- **资源管理**: 自动管理协程生命周期和资源释放

### 1.2 核心组件

- **es_coro.h/c**: 无栈协程实现
- **es_scheduler.h/c**: 协程调度器实现

## 2. 协程系统 (es_coro)

### 2.1 协程状态

```c
typedef enum {
    ES_ASYNC_DONE = 0,   // 协程已完成
    ES_ASYNC_YIELD,      // 协程已让出CPU
    ES_ASYNC_WAIT,       // 协程等待条件
    ES_ASYNC_ERROR,      // 协程错误
} es_async_t;
```

### 2.2 协程上下文

```c
typedef struct {
    uint16_t line;          // 当前执行行号
    uint16_t enabled:1;     // 是否启用
    uint16_t child_id:10;   // 子协程ID
    uint16_t cnt:5;         // 计数器
} es_coro_t;
```

### 2.3 协程宏定义

#### 2.3.1 基本宏

```c
// 开始协程定义
#define es_co_begin switch((__coro)->line) { case 0:

// 结束协程定义
#define es_co_end (__coro)->line = 0; return ES_ASYNC_DONE; }

// 让出CPU控制权
#define es_co_yield do{(__coro)->line = __LINE__; return ES_ASYNC_YIELD; case __LINE__: ;} while(0)

// 等待条件满足
#define es_co_wait(condition) do{(__coro)->line = __LINE__; case __LINE__: if(!(condition)) return ES_ASYNC_WAIT;} while(0)

// 退出协程
#define es_co_exit do{(__coro)->line = 0; return ES_ASYNC_DONE;} while(0)

// 错误退出
#define es_co_err do{(__coro)->line = 0; return ES_ASYNC_ERROR;} while(0)
```

#### 2.3.2 兼容性宏

```c
// 兼容旧版本的宏定义
#define ES_CORO_BEGIN(coro) es_co_begin
#define ES_CORO_END(coro) es_co_end
#define ES_CORO_YIELD(coro) es_co_yield
#define ES_CORO_WAIT(coro, condition) es_co_wait(condition)
#define ES_CORO_EXIT(coro) es_co_exit
```

### 2.4 协程使用示例

#### 2.4.1 基本协程

```c
es_async_t simple_task(es_coro_t *coro, void *ctx) {
    static int counter = 0;
    
    es_co_begin;
    
    // 初始化
    counter = 0;
    ES_PRINTF_I("TASK", "Task started");
    
    while (counter < 10) {
        // 执行工作
        counter++;
        ES_PRINTF_I("TASK", "Counter: %d", counter);
        
        // 让出CPU
        es_co_yield;
    }
    
    ES_PRINTF_I("TASK", "Task completed");
    es_co_end;
}
```

#### 2.4.2 等待条件的协程

```c
es_async_t wait_condition_task(es_coro_t *coro, void *ctx) {
    static uint32_t start_time;
    
    es_co_begin;
    
    start_time = es_get_timestamp();
    ES_PRINTF_I("TASK", "Waiting for condition...");
    
    // 等待条件满足或超时
    es_co_wait(some_condition_met() || (es_get_timestamp() - start_time > 5000));
    
    if (some_condition_met()) {
        ES_PRINTF_I("TASK", "Condition met!");
    } else {
        ES_PRINTF_W("TASK", "Timeout occurred");
    }
    
    es_co_end;
}
```

## 3. 调度器系统 (es_scheduler)

### 3.1 调度器结构

```c
typedef struct es_coro_scheduler {
    struct es_list_head tasks;                    // 任务链表
    struct es_list_head timers;                   // 定时器链表
    struct es_list_head delayed_delete_timers;    // 延迟删除定时器链表
    struct es_list_head delayed_add_timers;       // 延迟添加定时器链表
    struct es_list_head event_subscribers;        // 事件订阅者链表
    uint8_t inited : 1;                          // 初始化标志
    uint8_t in_process_timers : 1;               // 正在处理定时器标志
} es_scheduler_t;
```

### 3.2 任务结构

```c
typedef struct es_coro_task {
    const char *name;           // 任务名称
    es_coro_func_t func;       // 协程函数
    es_coro_t coro;            // 协程上下文
    void *ctx;                 // 协程上下文数据
    struct es_list_head node;   // 链表节点
} es_coro_task_t;
```

### 3.3 定时器结构

```c
typedef struct es_timer {
    uint32_t period;                    // 定时周期(毫秒)
    uint32_t last_trigger;              // 上次触发时间
    es_timer_callback_t callback;       // 回调函数
    void *ctx;                         // 回调上下文
    bool one_shot;                     // 是否单次触发
    bool enabled;                      // 是否启用
    struct es_list_head node;          // 链表节点
} es_timer_t;
```

### 3.4 主要API

#### 3.4.1 调度器管理

```c
// 初始化调度器
void es_scheduler_init(es_scheduler_t *scheduler);

// 获取默认调度器
es_scheduler_t *es_scheduler_get_default(void);

// 运行调度器
void es_scheduler_run(es_scheduler_t *scheduler);
```

#### 3.4.2 任务管理

```c
// 初始化任务
void es_scheduler_task_init(es_coro_task_t *task, const char *name, 
                           es_coro_func_t func, void *ctx);

// 添加任务
int es_scheduler_task_add(es_scheduler_t *scheduler, es_coro_task_t *task);

// 移除任务
int es_scheduler_task_remove(const es_scheduler_t *scheduler, es_coro_task_t *task);
```

#### 3.4.3 定时器管理

```c
// 初始化定时器
void es_scheduler_timer_init(es_timer_t *timer, uint32_t period, 
                            es_timer_callback_t callback, void *ctx, bool one_shot);

// 添加定时器
int es_scheduler_timer_add(es_scheduler_t *scheduler, es_timer_t *timer);

// 移除定时器
int es_scheduler_timer_remove(es_scheduler_t *scheduler, es_timer_t *timer);

// 启用/禁用定时器
void es_scheduler_timer_enable(es_timer_t *timer, bool enable);
```

### 3.5 使用示例

#### 3.5.1 创建和运行任务

```c
// 定义协程任务
es_async_t led_blink_task(es_coro_t *coro, void *ctx) {
    static uint32_t last_time = 0;
    
    es_co_begin;
    
    while (1) {
        // 切换LED状态
        led_toggle();
        
        last_time = es_get_timestamp();
        
        // 等待500ms
        es_co_wait(es_get_timestamp() - last_time >= 500);
    }
    
    es_co_end;
}

// 初始化和添加任务
void app_init(void) {
    static es_coro_task_t led_task;
    
    // 初始化任务
    es_scheduler_task_init(&led_task, "led_blink", led_blink_task, NULL);
    
    // 添加到调度器
    es_scheduler_task_add(es_scheduler_get_default(), &led_task);
}

// 主循环
int main(void) {
    // 系统初始化
    es_init();
    app_init();
    
    // 运行调度器
    while (1) {
        es_scheduler_run(es_scheduler_get_default());
    }
}
```

#### 3.5.2 使用定时器

```c
// 定时器回调函数
es_async_t timer_callback(es_coro_t *coro, void *ctx) {
    es_co_begin;
    
    ES_PRINTF_I("TIMER", "Timer triggered");
    
    es_co_end;
}

// 初始化定时器
void timer_init(void) {
    static es_timer_t my_timer;
    
    // 初始化1秒周期定时器
    es_scheduler_timer_init(&my_timer, 1000, timer_callback, NULL, false);
    
    // 添加到调度器
    es_scheduler_timer_add(es_scheduler_get_default(), &my_timer);
}
```

## 4. 事件系统

### 4.1 事件订阅者结构

```c
typedef struct es_event_subscriber {
    uint32_t event_mask;                // 事件掩码
    es_event_callback_t callback;       // 回调函数
    void *ctx;                         // 回调上下文
    struct es_list_head node;          // 链表节点
} es_event_subscriber_t;
```

### 4.2 事件API

```c
// 订阅事件
int es_scheduler_event_subscribe(es_scheduler_t *scheduler, 
                                es_event_subscriber_t *subscriber);

// 取消订阅
int es_scheduler_event_unsubscribe(es_scheduler_t *scheduler, 
                                  es_event_subscriber_t *subscriber);

// 发布事件
void es_scheduler_event_publish(es_scheduler_t *scheduler, uint32_t event);
```

## 5. 最佳实践

### 5.1 协程设计原则

1. **避免阻塞操作**: 使用 `es_co_wait` 代替阻塞等待
2. **合理使用静态变量**: 协程函数中的局部变量在让出CPU后会丢失
3. **错误处理**: 使用 `es_co_err` 进行错误处理
4. **资源管理**: 确保在协程退出前释放资源

### 5.2 调度器使用建议

1. **任务优先级**: 重要任务应该更频繁地让出CPU
2. **定时器精度**: 定时器精度受系统滴答频率影响
3. **内存使用**: 合理控制同时运行的协程数量
4. **调试支持**: 使用任务名称便于调试和监控

### 5.3 性能优化

1. **减少上下文切换**: 合理安排协程让出时机
2. **内存对齐**: 协程结构体使用内存对齐优化
3. **编译优化**: 使用编译器优化选项提高性能

## 6. 故障排除

### 6.1 常见问题

1. **协程不执行**: 检查是否正确添加到调度器
2. **定时器不触发**: 检查定时器是否启用和时间设置
3. **内存泄漏**: 检查协程退出时是否释放资源
4. **栈溢出**: 检查协程函数中的局部变量使用

### 6.2 调试技巧

1. **日志输出**: 在关键位置添加日志输出
2. **状态监控**: 监控协程和定时器状态
3. **性能分析**: 统计协程执行时间和频率

这个协程调度系统为 ES MCU Framework 提供了高效的异步编程支持，是构建复杂嵌入式应用的重要基础。
