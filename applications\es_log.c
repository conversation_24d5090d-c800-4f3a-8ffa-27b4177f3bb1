﻿/**
 * @file es_log.c
 * @brief Simple logging module implementation
 * @date 2024/10/1
 */

#include "es_log.h"
#include "es_drv_os.h"
#include "es_config.h"
#include "es_time.h"

static bool is_initialized = false;

/* Global log level, default is CONFIG_LOG_LEVEL */
es_log_level_t g_es_log_level = ES_LOG_LEVEL_DEBUG;

/* Global log enable flag, default is true */
static bool g_es_log_enabled = true;

/* Log sink list head */
static ES_LIST_HEAD(g_sink_list);

/* Log level strings */
static const char *level_str[] = {
    "N",
    "E",
    "W",
    "I",
    "D",
    "V"
};

// /* Colors corresponding to log levels (ANSI escape sequences) */
// static const char *level_colors[] = {
//     "\033[0m",    /* NONE - Reset */
//     "\033[31m",   /* ERROR - Red */
//     "\033[33m",   /* WARN - Yellow */
//     "\033[32m",   /* INFO - Green */
//     "\033[36m",   /* DEBUG - Cyan */
//     "\033[35m"    /* VERBOSE - Purple */
// };

/**
 * @brief Initialize log module
 * @return 0 on success, non-zero on failure
 */
int es_log_init(void)
{
    if (is_initialized) {
        return 0;
    }

    /* Initialize sink list */
    es_init_list_head(&g_sink_list);
    is_initialized = true;
    return 0;
}

/**
 * @brief Deinitialize log module
 */
void es_log_deinit(void)
{
    /* Clear all sinks (list should be empty after use) */
    struct es_list_head *pos, *n;
    
    es_list_for_each_safe(pos, n, &g_sink_list) {
        es_log_sink_t *sink = es_list_entry(pos, es_log_sink_t, node);
        es_list_del(&sink->node);
    }
    is_initialized = false;
}

/**
 * @brief Register a log sink
 * @param sink Pointer to sink structure
 * @return 0 on success, non-zero on failure
 */
int es_log_add_sink(es_log_sink_t *sink)
{
    if (sink == NULL) {
        return -1;
    }
    
    /* Add to list */
    es_list_add_tail(&sink->node, &g_sink_list);
    
    return 0;
}

/**
 * @brief Unregister a log sink
 * @param sink Pointer to sink structure
 * @return 0 on success, non-zero on failure
 */
int es_log_remove_sink(es_log_sink_t *sink)
{
    if (sink == NULL) {
        return -1;
    }
    
    /* Remove from list */
    es_list_del(&sink->node);
    
    return 0;
}

/**
 * @brief Find a log sink by name
 * @param name Sink name
 * @return Pointer to sink structure or NULL if not found
 */
es_log_sink_t *es_log_find_sink(const char *name)
{
    struct es_list_head *pos;
    
    if (name == NULL) {
        return NULL;
    }
    
    es_list_for_each(pos, &g_sink_list) {
        es_log_sink_t *sink = es_list_entry(pos, es_log_sink_t, node);
        if (sink->name != NULL && strcmp(sink->name, name) == 0) {
            return sink;
        }
    }
    
    return NULL;
}

void es_log_set_level(es_log_level_t level)
{
    if (level <= ES_LOG_LEVEL_VERBOSE) {
        g_es_log_level = level;
    }
}

es_log_level_t es_log_get_level(void)
{
    return g_es_log_level;
}

/**
 * @brief Set log output state (enable/disable)
 * @param enabled true to enable log output, false to disable
 */
void es_log_set_enabled(bool enabled)
{
    g_es_log_enabled = enabled;
}

/**
 * @brief Get current log output state
 * @return true if log output is enabled, false otherwise
 */
bool es_log_get_enabled(void)
{
    return g_es_log_enabled;
}

void es_log_write(es_log_level_t level, const char *tag, bool to_flash, const char *fmt, ...)
{
    va_list args;
    char timestr[24] = {0};  /* Increase buffer to accommodate millisecond format */
    char log_buf[512] = {0};  /* Buffer to store formatted log message */
    int header_len = 0;
    int content_len = 0;
    struct es_list_head *pos;

    /* Check if logging is enabled */
    if (!g_es_log_enabled) {
        return;
    }

    /* Check log level */
    if (level == ES_LOG_LEVEL_NONE || level > g_es_log_level) {
        return;
    }

    // /* Get current time string */
    // es_os_get_time_str(timestr, sizeof(timestr));
    es_timestamp_to_str(es_get_timestamp(), timestr);

    // const char* data = timestr;

    /* Format log header - removed filename */
    header_len = snprintf(log_buf, sizeof(log_buf), "[%s][%s][%s] ", timestr + 5, level_str[level], tag);
    
    /* Format log content */
    va_start(args, fmt);
    content_len = vsnprintf(log_buf + header_len, sizeof(log_buf) - header_len - 3, fmt, args);
    log_buf[header_len + content_len] = '\r';
    log_buf[header_len + content_len + 1] = '\n';
    va_end(args);

    /* Call all registered sinks */
    es_list_for_each(pos, &g_sink_list) {
        es_log_sink_t *sink = es_list_entry(pos, es_log_sink_t, node);
        if (sink->sink_func) {
            sink->sink_func(level, to_flash, log_buf, header_len + content_len + 2);
        }
    }
}

void es_log_hex_dump(const char *tag, int width, const void *buf, int size)
{
    const uint8_t *data = (const uint8_t *)buf;
    char ascii[17] = {0};
    int i, j;
    char timestr[24];  /* Increase buffer to accommodate millisecond format */
    char log_buf[512] = {0};  /* Buffer to store formatted log message */
    struct es_list_head *pos;
    int len = 0;

    /* Check if logging is enabled */
    if (!g_es_log_enabled) {
        return;
    }

    //check level
    if (g_es_log_level < ES_LOG_LEVEL_DEBUG) {
        return;
    }

    /* Get current time string */
    // es_os_get_time_str(timestr, sizeof(timestr));
    es_timestamp_to_str(es_get_timestamp(), timestr);

    /* Limit number of bytes displayed per line */
    if (width <= 0 || width > 32) {
        width = 16;
    }

    /* Output header to sink */
    len = snprintf(log_buf, sizeof(log_buf), "[%s][%s][%s] Hex dump of %u bytes:\r\n", 
                   timestr + 5, level_str[ES_LOG_LEVEL_DEBUG], tag, size);
    
    /* Send header to all registered sinks */
    es_list_for_each(pos, &g_sink_list) {
        es_log_sink_t *sink = es_list_entry(pos, es_log_sink_t, node);
        if (sink->sink_func) {
            sink->sink_func(ES_LOG_LEVEL_DEBUG, false, log_buf, len);
        }
    }

    /* Print hexadecimal data line by line */
    for (i = 0; i < size; i++) {
        /* Create address at beginning of each line */
        if (i % width == 0) {
            len = snprintf(log_buf, sizeof(log_buf), "[%s][%s][%s] %04X: ", 
                          timestr + 5, level_str[ES_LOG_LEVEL_DEBUG], tag, i);
        }

        /* Add hexadecimal value to buffer */
        len += snprintf(log_buf + len, sizeof(log_buf) - len, "%02X ", data[i]);
        
        /* Prepare ASCII display */
        if (data[i] >= 32 && data[i] <= 126) {
            ascii[i % width] = data[i];
        } else {
            ascii[i % width] = '.';
        }
        
        /* Add ASCII part at line end */
        if ((i + 1) % width == 0 || i + 1 == size) {
            /* Align data that is less than a full line */
            for (j = (i % width) + 1; j < width; j++) {
                len += snprintf(log_buf + len, sizeof(log_buf) - len, "   ");
                ascii[j] = '\0';
            }
            
            ascii[width] = '\0';
            len += snprintf(log_buf + len, sizeof(log_buf) - len, " |%s|\r\n", ascii);
            
            /* Send to all registered sinks */
            es_list_for_each(pos, &g_sink_list) {
                es_log_sink_t *sink = es_list_entry(pos, es_log_sink_t, node);
                if (sink->sink_func) {
                    sink->sink_func(ES_LOG_LEVEL_DEBUG, false, log_buf, len);
                }
            }
            
            /* Reset buffer for next line */
            memset(log_buf, 0, sizeof(log_buf));
            len = 0;
        }
    }
}

