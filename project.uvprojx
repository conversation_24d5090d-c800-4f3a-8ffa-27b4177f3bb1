<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>rt-thread</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>HC32F460PETB</Device>
          <Vendor>HDSC</Vendor>
          <PackID>HDSC.HC32F460.1.0.7</PackID>
          <PackURL>https://raw.githubusercontent.com/hdscmcu/pack/master/</PackURL>
          <Cpu>IROM1(0x00000000,0x80000) IROM2(0x03000C00,0x3FC) IRAM1(0x1FFF8000,0x2F000)  IRAM2(0x200F0000,0x1000) CPUTYPE("Cortex-M4") FPU2 CLOCK(8000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>CMSIS_AGDI(-S0 -C0 -P0 -FD1FFF8000 -FC1000 -FN2 -FF0HC32F460_512K -FS00 -FL080000 -FP0($$Device:HC32F460PETB$FlashARM\HC32F460_512K.FLM) -FF1HC32F460_otp -FS103000C00 -FL13FC -FP1($$Device:HC32F460PETB$FlashARM\HC32F460_otp.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:HC32F460PETB$Device\Include\HC32F460PETB.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>../libraries/hc32f460_ddl/drivers/cmsis/Device/HDSC/hc32f4xx/Source/ARM/sfr/HDSC_HC32F460.SFR</SFDFile>
          <bCustSvd>1</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\build\keil\Obj\</OutputDirectory>
          <OutputName>rtthread</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\build\keil\List\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bin !L --output rtthread.bin</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>0</hadIROM>
            <hadIRAM>0</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x1fff8000</StartAddress>
                <Size>0x2f000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x3000c00</StartAddress>
                <Size>0x3fc</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x1fff8000</StartAddress>
                <Size>0x2f000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x200f0000</StartAddress>
                <Size>0x1000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>3</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>0</v6Lang>
            <v6LangP>0</v6LangP>
            <vShortEn>0</vShortEn>
            <vShortWch>0</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>HC32F460, USE_DDL_DRIVER</Define>
              <Undefine></Undefine>
              <IncludePath>platform\hc32\board;platform\hc32;.;platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\inc;platform\hc32\hc32f460_ddl\drivers\bsp\components\lsm6dsl;platform\hc32\hc32f460_ddl\drivers\cmsis\Include;platform\hc32\hc32f460_ddl\drivers\cmsis\Device\HDSC\hc32f4xx\Include;applications;platform\hc32\hc32f460_ddl\drivers\bsp\ev_hc32f460_lqfp100_v2;platform\hc32\hc32f460_ddl\drivers\bsp\components\w25qxx</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x1FFF8000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\platform\hc32\board\linker_scripts\link.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Applications</GroupName>
          <Files>
            <File>
              <FileName>es_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_crc.c</FilePath>
            </File>
            <File>
              <FileName>es_mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_mem.c</FilePath>
            </File>
            <File>
              <FileName>es_at_srv.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_at_srv.c</FilePath>
            </File>
            <File>
              <FileName>es_rule_engine.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_rule_engine.c</FilePath>
            </File>
            <File>
              <FileName>es_mdm.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_mdm.c</FilePath>
            </File>
            <File>
              <FileName>es_isotp.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_isotp.c</FilePath>
            </File>
            <File>
              <FileName>es_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_flash.c</FilePath>
            </File>
            <File>
              <FileName>es_pm.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_pm.c</FilePath>
            </File>
            <File>
              <FileName>es_time.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_time.c</FilePath>
            </File>
            <File>
              <FileName>es_des3.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_des3.c</FilePath>
            </File>
            <File>
              <FileName>es_obd.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_obd.c</FilePath>
            </File>
            <File>
              <FileName>es_aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_aes.c</FilePath>
            </File>
            <File>
              <FileName>es_coro.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_coro.c</FilePath>
            </File>
            <File>
              <FileName>es_button.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_button.c</FilePath>
            </File>
            <File>
              <FileName>es_frame_v2_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_frame_v2_fs.c</FilePath>
            </File>
            <File>
              <FileName>es_veh.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_veh.c</FilePath>
            </File>
            <File>
              <FileName>es_frame_q.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_frame_q.c</FilePath>
            </File>
            <File>
              <FileName>es_ringbuffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_ringbuffer.c</FilePath>
            </File>
            <File>
              <FileName>es_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_utils.c</FilePath>
            </File>
            <File>
              <FileName>es_frame.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_frame.c</FilePath>
            </File>
            <File>
              <FileName>es_log_v2_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_log_v2_fs.c</FilePath>
            </File>
            <File>
              <FileName>es_uds.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_uds.c</FilePath>
            </File>
            <File>
              <FileName>es_cfg_v2.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_cfg_v2.c</FilePath>
            </File>
            <File>
              <FileName>es_ota.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_ota.c</FilePath>
            </File>
            <File>
              <FileName>es_md5.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_md5.c</FilePath>
            </File>
            <File>
              <FileName>es_at_cli.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_at_cli.c</FilePath>
            </File>
            <File>
              <FileName>es_log.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_log.c</FilePath>
            </File>
            <File>
              <FileName>es_scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_scheduler.c</FilePath>
            </File>
            <File>
              <FileName>es_ringobj.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_ringobj.c</FilePath>
            </File>
            <File>
              <FileName>es_console.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es_console.c</FilePath>
            </File>
            <File>
              <FileName>es.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\es.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers</GroupName>
          <Files>
            <File>
              <FileName>startup_hc32f460.s</FileName>
              <FileType>2</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\cmsis\Device\HDSC\hc32f4xx\Source\ARM\startup_hc32f460.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>hc32</GroupName>
          <Files>
            <File>
              <FileName>hc32_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32_uart.c</FilePath>
            </File>
            <File>
              <FileName>hc32_w25qxx.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32_w25qxx.c</FilePath>
            </File>
            <File>
              <FileName>hc32_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32_adc.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\main.c</FilePath>
            </File>
            <File>
              <FileName>hc32_os.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32_os.c</FilePath>
            </File>
            <File>
              <FileName>hc32_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32_flash.c</FilePath>
            </File>
            <File>
              <FileName>hc32_pm.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32_pm.c</FilePath>
            </File>
            <File>
              <FileName>hc32_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32_can.c</FilePath>
            </File>
            <File>
              <FileName>hc32_lsm6dsl.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32_lsm6dsl.c</FilePath>
            </File>
            <File>
              <FileName>syscall.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\syscall.c</FilePath>
            </File>
            <File>
              <FileName>hc32_pin.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32_pin.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Libraries</GroupName>
          <Files>
            <File>
              <FileName>hc32_ll_clk.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_clk.c</FilePath>
            </File>
            <File>
              <FileName>system_hc32f460.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\cmsis\Device\HDSC\hc32f4xx\Source\system_hc32f460.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_utility.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_utility.c</FilePath>
            </File>
            <File>
              <FileName>lsm6dsl.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\bsp\components\lsm6dsl\lsm6dsl.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_fcg.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_fcg.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_interrupts.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_interrupts.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_sram.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_sram.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_pwc.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_pwc.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_aos.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_aos.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_fcm.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_fcm.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll.c</FilePath>
            </File>
            <File>
              <FileName>ev_hc32f460_lqfp100_v2_lsm6dsl.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\bsp\ev_hc32f460_lqfp100_v2\ev_hc32f460_lqfp100_v2_lsm6dsl.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_efm.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_efm.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_gpio.c</FilePath>
            </File>
            <File>
              <FileName>hc32f460_ll_interrupts_share.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32f460_ll_interrupts_share.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_rmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_rmu.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_spi.c</FilePath>
            </File>
            <File>
              <FileName>w25qxx.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\bsp\components\w25qxx\w25qxx.c</FilePath>
            </File>
            <File>
              <FileName>ev_hc32f460_lqfp100_v2_w25qxx.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\bsp\ev_hc32f460_lqfp100_v2\ev_hc32f460_lqfp100_v2_w25qxx.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_dma.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_adc.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_can.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_usart.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_aes.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_cmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_cmp.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_crc.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_dbgc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_dbgc.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_dcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_dcu.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_emb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_emb.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_event_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_event_port.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_hash.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_i2c.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_i2s.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_i2s.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_icg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_icg.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>0</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>hc32_ll_keyscan.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_keyscan.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_mpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_mpu.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_ots.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_ots.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_qspi.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_rtc.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_sdioc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_sdioc.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_swdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_swdt.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_tmr0.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_tmr0.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_tmr4.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_tmr4.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_tmr6.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_tmr6.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_tmra.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_tmra.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_trng.c</FilePath>
            </File>
            <File>
              <FileName>hc32_ll_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform\hc32\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_wdt.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

</Project>
