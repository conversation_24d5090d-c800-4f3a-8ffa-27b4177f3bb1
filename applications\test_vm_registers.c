#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

// 简化的VM定义，只包含我们需要测试的部分
#define VM_STACK_SIZE   16
#define VM_REG_COUNT    8

typedef enum {
    VM_STATE_READY = 0,
    VM_STATE_RUNNING,
    VM_STATE_DONE,
    VM_STATE_ERROR
} vm_state_t;

typedef struct {
    uint32_t stack[VM_STACK_SIZE];
    uint8_t sp;
    uint16_t pc;
    vm_state_t state;
    const uint8_t* bytecode;
    uint16_t bytecode_len;
    uint32_t registers[VM_REG_COUNT];
} vm_context_t;

// VM操作码
#define VM_OP_PUSH32    0x03
#define VM_OP_ADD       0x10
#define VM_OP_STORE_REG 0x42
#define VM_OP_LOAD_REG  0x43
#define VM_OP_HALT      0xFF

// 简化的日志宏
#define ES_PRINTF_E(tag, fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)
#define ES_PRINTF_I(tag, fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define TAG "VM_TEST"

// VM函数实现
static inline int vm_push(vm_context_t* vm, uint32_t value) {
    if (vm->sp >= VM_STACK_SIZE) {
        ES_PRINTF_E(TAG, "VM stack overflow");
        return -1;
    }
    vm->stack[vm->sp++] = value;
    return 0;
}

static inline uint32_t vm_pop(vm_context_t* vm) {
    if (vm->sp == 0) {
        ES_PRINTF_E(TAG, "VM stack underflow");
        return 0;
    }
    return vm->stack[--vm->sp];
}

static uint8_t vm_read_u8(vm_context_t* vm) {
    if (vm->pc >= vm->bytecode_len) {
        ES_PRINTF_E(TAG, "VM PC out of bounds");
        return 0;
    }
    return vm->bytecode[vm->pc++];
}

static uint16_t vm_read_u16(vm_context_t* vm) {
    uint16_t value = vm_read_u8(vm);
    value |= (uint16_t)vm_read_u8(vm) << 8;
    return value;
}

static uint32_t vm_read_u32(vm_context_t* vm) {
    uint32_t value = vm_read_u16(vm);
    value |= (uint32_t)vm_read_u16(vm) << 16;
    return value;
}

int vm_init(vm_context_t* vm, const uint8_t* bytecode, uint16_t len) {
    if (!vm || !bytecode || len == 0) {
        return -1;
    }

    memset(vm, 0, sizeof(vm_context_t));
    vm->bytecode = bytecode;
    vm->bytecode_len = len;
    vm->state = VM_STATE_READY;
    vm->sp = 0;
    vm->pc = 0;

    for (int i = 0; i < VM_REG_COUNT; i++) {
        vm->registers[i] = 0;
    }

    return 0;
}

uint32_t vm_get_result(vm_context_t* vm) {
    if (!vm || vm->sp == 0) {
        return 0;
    }
    return vm->stack[vm->sp - 1];
}

int vm_step(vm_context_t* vm) {
    if (!vm) {
        return -1;
    }

    if (vm->state != VM_STATE_RUNNING && vm->state != VM_STATE_READY) {
        return 0;
    }

    if (vm->pc >= vm->bytecode_len) {
        vm->state = VM_STATE_DONE;
        return 0;
    }

    vm->state = VM_STATE_RUNNING;
    uint8_t opcode = vm_read_u8(vm);

    switch (opcode) {
        case VM_OP_PUSH32: {
            uint32_t value = vm_read_u32(vm);
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_ADD: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM ADD: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a + b);
            break;
        }

        case VM_OP_STORE_REG: {
            uint8_t reg_id = vm_read_u8(vm);
            if (reg_id >= VM_REG_COUNT) {
                ES_PRINTF_E(TAG, "VM STORE_REG: invalid register ID %u", reg_id);
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            if (vm->sp < 1) {
                ES_PRINTF_E(TAG, "VM STORE_REG: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t value = vm_pop(vm);
            vm->registers[reg_id] = value;
            ES_PRINTF_I(TAG, "VM STORE_REG: R%u = 0x%08X", reg_id, value);
            break;
        }

        case VM_OP_LOAD_REG: {
            uint8_t reg_id = vm_read_u8(vm);
            if (reg_id >= VM_REG_COUNT) {
                ES_PRINTF_E(TAG, "VM LOAD_REG: invalid register ID %u", reg_id);
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t value = vm->registers[reg_id];
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            ES_PRINTF_I(TAG, "VM LOAD_REG: R%u -> 0x%08X", reg_id, value);
            break;
        }

        case VM_OP_HALT:
            vm->state = VM_STATE_DONE;
            break;

        default:
            ES_PRINTF_E(TAG, "VM: unknown opcode 0x%02X at PC=%u", opcode, vm->pc - 1);
            vm->state = VM_STATE_ERROR;
            return -1;
    }

    return 1;
}

int vm_execute(vm_context_t* vm) {
    if (!vm) {
        return -1;
    }

    if (vm->state != VM_STATE_READY) {
        return 0;
    }

    int max_steps = 10000;
    while (max_steps-- > 0 && vm->state == VM_STATE_RUNNING) {
        int result = vm_step(vm);
        if (result < 0) {
            return result;
        }

        if (vm->state != VM_STATE_RUNNING) {
            break;
        }
    }

    if (max_steps <= 0) {
        ES_PRINTF_E(TAG, "VM execution timeout");
        vm->state = VM_STATE_ERROR;
        return -1;
    }

    return 0;
}

// 测试寄存器存储和加载功能
void test_vm_register_operations(void) {
    printf("=== 测试VM寄存器操作 ===\n");
    
    // 测试字节码：
    // PUSH32 0x12345678    ; 压入测试值
    // STORE_REG 3          ; 存储到寄存器3
    // PUSH32 0xABCDEF00    ; 压入另一个测试值
    // STORE_REG 7          ; 存储到寄存器7
    // LOAD_REG 3           ; 加载寄存器3的值
    // LOAD_REG 7           ; 加载寄存器7的值
    // ADD                  ; 相加
    // HALT                 ; 停机
    
    uint8_t bytecode[] = {
        VM_OP_PUSH32, 0x78, 0x56, 0x34, 0x12,  // PUSH32 0x12345678
        VM_OP_STORE_REG, 3,                     // STORE_REG 3
        VM_OP_PUSH32, 0x00, 0xEF, 0xCD, 0xAB,  // PUSH32 0xABCDEF00
        VM_OP_STORE_REG, 7,                     // STORE_REG 7
        VM_OP_LOAD_REG, 3,                      // LOAD_REG 3
        VM_OP_LOAD_REG, 7,                      // LOAD_REG 7
        VM_OP_ADD,                              // ADD
        VM_OP_HALT                              // HALT
    };
    
    vm_context_t vm;
    
    // 初始化VM
    if (vm_init(&vm, bytecode, sizeof(bytecode)) < 0) {
        printf("❌ VM初始化失败\n");
        return;
    }
    
    printf("✅ VM初始化成功\n");
    
    // 检查寄存器初始值
    printf("📋 初始寄存器状态:\n");
    for (int i = 0; i < VM_REG_COUNT; i++) {
        printf("   R%d = 0x%08X\n", i, vm.registers[i]);
    }
    
    // 执行VM
    printf("🚀 开始执行VM...\n");
    int result = vm_execute(&vm);
    
    if (result < 0) {
        printf("❌ VM执行失败\n");
        return;
    }
    
    printf("✅ VM执行完成\n");
    
    // 检查最终寄存器状态
    printf("📋 最终寄存器状态:\n");
    for (int i = 0; i < VM_REG_COUNT; i++) {
        printf("   R%d = 0x%08X\n", i, vm.registers[i]);
    }
    
    // 检查执行结果
    uint32_t vm_result = vm_get_result(&vm);
    uint32_t expected = 0x12345678 + 0xABCDEF00;
    
    printf("🔍 执行结果: 0x%08X (期望: 0x%08X)\n", vm_result, expected);
    
    if (vm_result == expected) {
        printf("✅ 测试通过！寄存器操作正常工作\n");
    } else {
        printf("❌ 测试失败！结果不匹配\n");
    }
    
    // 验证特定寄存器的值
    if (vm.registers[3] == 0x12345678 && vm.registers[7] == 0xABCDEF00) {
        printf("✅ 寄存器存储验证通过\n");
    } else {
        printf("❌ 寄存器存储验证失败\n");
        printf("   R3 = 0x%08X (期望: 0x12345678)\n", vm.registers[3]);
        printf("   R7 = 0x%08X (期望: 0xABCDEF00)\n", vm.registers[7]);
    }
}

// 测试无效寄存器ID的错误处理
void test_vm_register_error_handling(void) {
    printf("\n=== 测试VM寄存器错误处理 ===\n");
    
    // 测试字节码：尝试访问无效的寄存器ID
    uint8_t bytecode[] = {
        VM_OP_PUSH32, 0x78, 0x56, 0x34, 0x12,  // PUSH32 0x12345678
        VM_OP_STORE_REG, 255,                   // STORE_REG 255 (无效ID)
        VM_OP_HALT                              // HALT
    };
    
    vm_context_t vm;
    
    if (vm_init(&vm, bytecode, sizeof(bytecode)) < 0) {
        printf("❌ VM初始化失败\n");
        return;
    }
    
    printf("🚀 测试无效寄存器ID...\n");
    int result = vm_execute(&vm);
    
    if (result < 0 && vm.state == VM_STATE_ERROR) {
        printf("✅ 错误处理测试通过！正确检测到无效寄存器ID\n");
    } else {
        printf("❌ 错误处理测试失败！应该检测到错误\n");
    }
}

int main(void) {
    printf("🧪 VM寄存器功能测试\n");
    printf("====================\n");
    
    test_vm_register_operations();
    test_vm_register_error_handling();
    
    printf("\n🏁 测试完成\n");
    return 0;
}
