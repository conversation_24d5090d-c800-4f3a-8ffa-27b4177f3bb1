/**
 * @file es_hal_adc.c
 * @brief 硬件抽象层ADC驱动实现
 */

#include "es_drv_adc.h"
#include <stdlib.h>
#include "es_log.h"
#include "hc32_ll_adc.h"
#include "hc32_ll_gpio.h"
#include "hc32_ll_fcg.h"


#define TAG "ADC"

// ADC通道定义 - 内部使用，不对外暴露
#define ADC1_CH4_PORT                     (GPIO_PORT_A) //ADC_CHN_MAINPWR
#define ADC1_CH4_PIN                      (GPIO_PIN_04)

#define ADC1_CH5_PORT                     (GPIO_PORT_A) //ADC_CHN_BAT
#define ADC1_CH5_PIN                      (GPIO_PIN_05)

// ADC参考电压，单位mV
#define ADC_VREF_MV          3300

#define ADC_MAIN_CH ADC1_PIN_PA4
#define ADC_BAT_CH ADC1_PIN_PA5

/**
 * @brief 启用ADC通道
 * 
 * @param channel ADC通道
 * @return int 0:成功，其他值:失败
 */
static int adc_enable(uint16_t channel)
{
    ADC_ChCmd(CM_ADC1, ADC_SEQ_A, channel, ENABLE);
    
    ADC_ConvDataAverageChCmd(CM_ADC1, channel, ENABLE);
    ADC_SetSampleTime(CM_ADC1, channel, 255U);
    
    return 0;
}

/**
 * @brief 初始化ADC
 * 
 * @return int 0:成功，其他值:失败
 */
int es_adc_init(void)
{
    stc_adc_init_t stcAdcInit;
    stc_gpio_init_t stcGpioInit;

    FCG_Fcg3PeriphClockCmd(FCG3_PERIPH_ADC1, ENABLE);

    ADC_DeInit(CM_ADC1);
    
    /* 初始化GPIO配置结构体 */
    (void)GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinAttr = PIN_ATTR_ANALOG;
    
    (void)GPIO_Init(ADC1_CH4_PORT, ADC1_CH4_PIN, &stcGpioInit);
    (void)GPIO_Init(ADC1_CH5_PORT, ADC1_CH5_PIN, &stcGpioInit);

    /* 初始化ADC配置结构体 */
    (void)ADC_StructInit(&stcAdcInit);
    
    /* 初始化ADC1 */
    if (ADC_Init(CM_ADC1, &stcAdcInit) != LL_OK) {
        ES_LOGI(TAG, "ADC init failed");
        return -1;
    }
    
    /* 配置ADC平均值 */
    ADC_ConvDataAverageConfig(CM_ADC1, ADC_AVG_CNT8);
    
    /* 启用ADC通道 */
    if (adc_enable(ADC_MAIN_CH) != 0 || adc_enable(ADC_BAT_CH) != 0) {
        return -1;
    }
    
    ES_LOGI(TAG, "ADC hardware initialized");
    
    return 0;
}

/**
 * @brief 读取主电源ADC原始值
 * 
 * @return uint16_t ADC原始值 (0-4095)
 */
uint16_t es_adc_read_main_raw(void)
{
    // 启动ADC转换
    ADC_Start(CM_ADC1);
    
    // 读取ADC值
    return ADC_GetValue(CM_ADC1, ADC_MAIN_CH);
}

/**
 * @brief 读取电池ADC原始值
 * 
 * @return uint16_t ADC原始值 (0-4095)
 */
uint16_t es_adc_read_battery_raw(void)
{
    // 启动ADC转换
    ADC_Start(CM_ADC1);
    
    // 读取ADC值
    return ADC_GetValue(CM_ADC1, ADC_BAT_CH);
}

/**
 * @brief 获取ADC分辨率位数
 * 
 * @return uint16_t ADC分辨率位数
 */
static uint16_t adc_get_resolution_bits(void)
{
    uint16_t resolution = ADC_GetResolution(CM_ADC1);
    
    switch (resolution)
    {
        case ADC_RESOLUTION_12BIT:
            return 12;
        case ADC_RESOLUTION_10BIT:
            return 10;
        case ADC_RESOLUTION_8BIT:
            return 8;
        default:
            return 12; // 默认返回12位
    }
}

/**
 * @brief 将ADC原始值转换为电压值
 * 
 * @param adc_value ADC原始值
 * @param factor 电压转换系数
 * @return uint16_t 电压值，单位mV
 */
static uint16_t adc_convert_to_voltage(uint16_t adc_value, float factor)
{
    uint16_t resolution = adc_get_resolution_bits();
    uint16_t voltage = adc_value * ADC_VREF_MV / (1 << resolution);
    return (uint16_t)(voltage * factor);
}

/**
 * @brief 读取主电源电压值
 * 
 * @param factor 电压转换系数
 * @return uint16_t 电压值，单位mV
 */
uint16_t es_adc_read_main_voltage(float factor)
{
    return adc_convert_to_voltage(es_adc_read_main_raw(), factor);
}

/**
 * @brief 读取电池电压值
 * 
 * @param factor 电压转换系数
 * @return uint16_t 电压值，单位mV
 */
uint16_t es_adc_read_battery_voltage(float factor)
{
    return adc_convert_to_voltage(es_adc_read_battery_raw(), factor);
} 
