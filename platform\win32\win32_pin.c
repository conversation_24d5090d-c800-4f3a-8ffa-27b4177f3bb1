#ifdef _WIN32

#include "es_drv_pin.h"
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <windows.h>

// 最大支持的引脚数量
#define MAX_PIN_COUNT 256

// 引脚状态结构体
typedef struct {
    uint8_t mode;           // 引脚模式
    uint8_t value;          // 引脚值
    uint8_t irq_enabled;    // 中断使能状态
    uint16_t irq_mode;      // 中断模式
    void (*irq_handler)(void *args);  // 中断处理函数
    void *irq_args;         // 中断处理参数
    uint8_t last_value;     // 上一次的值，用于边沿检测
} pin_state_t;

// 全局引脚状态数组
static pin_state_t g_pin_states[MAX_PIN_COUNT];
static uint8_t g_pin_initialized = 0;

// 调试输出开关
#define PIN_DEBUG_ENABLE 1

#if PIN_DEBUG_ENABLE
#define PIN_DEBUG(fmt, ...) printf("[PIN_DEBUG] " fmt "\n", ##__VA_ARGS__)
#else
#define PIN_DEBUG(fmt, ...)
#endif

/**
 * @brief 初始化引脚系统
 * @return int 0表示成功，非0表示失败
 */
int es_pin_init(void)
{
    if (g_pin_initialized) {
        return 0;
    }
    
    // 初始化所有引脚状态
    memset(g_pin_states, 0, sizeof(g_pin_states));
    
    // 设置默认状态
    for (int i = 0; i < MAX_PIN_COUNT; i++) {
        g_pin_states[i].mode = PIN_MODE_INPUT;
        g_pin_states[i].value = PIN_LOW;
        g_pin_states[i].irq_enabled = PIN_IRQ_DISABLE;
        g_pin_states[i].irq_mode = PIN_IRQ_MODE_RISING;
        g_pin_states[i].irq_handler = NULL;
        g_pin_states[i].irq_args = NULL;
        g_pin_states[i].last_value = PIN_LOW;
    }
    
    g_pin_initialized = 1;
    PIN_DEBUG("Pin system initialized successfully");
    return 0;
}

/**
 * @brief 设置引脚模式
 * @param pin 引脚编号
 * @param mode 引脚模式
 */
void es_pin_mode(uint16_t pin, uint8_t mode)
{
    if (!g_pin_initialized) {
        es_pin_init();
    }
    
    if (pin >= MAX_PIN_COUNT) {
        PIN_DEBUG("Invalid pin number: %d", pin);
        return;
    }
    
    g_pin_states[pin].mode = mode;
    
    // 根据模式设置默认值
    switch (mode) {
        case PIN_MODE_INPUT_PULLUP:
            g_pin_states[pin].value = PIN_HIGH;
            g_pin_states[pin].last_value = PIN_HIGH;
            break;
        case PIN_MODE_INPUT_PULLDOWN:
        case PIN_MODE_INPUT:
            g_pin_states[pin].value = PIN_LOW;
            g_pin_states[pin].last_value = PIN_LOW;
            break;
        case PIN_MODE_OUTPUT:
        case PIN_MODE_OUTPUT_OD:
            // 输出模式保持当前值
            break;
        default:
            PIN_DEBUG("Unknown pin mode: %d", mode);
            break;
    }
    
    PIN_DEBUG("Pin %d mode set to %d", pin, mode);
}

/**
 * @brief 写入引脚值
 * @param pin 引脚编号
 * @param value 引脚值 (PIN_LOW/PIN_HIGH)
 */
void es_pin_write(uint16_t pin, uint8_t value)
{
    if (!g_pin_initialized) {
        es_pin_init();
    }
    
    if (pin >= MAX_PIN_COUNT) {
        PIN_DEBUG("Invalid pin number: %d", pin);
        return;
    }
    
    // 检查是否为输出模式
    if (g_pin_states[pin].mode != PIN_MODE_OUTPUT && 
        g_pin_states[pin].mode != PIN_MODE_OUTPUT_OD) {
        PIN_DEBUG("Warning: Writing to non-output pin %d", pin);
    }
    
    uint8_t old_value = g_pin_states[pin].value;
    g_pin_states[pin].value = value ? PIN_HIGH : PIN_LOW;
    
    PIN_DEBUG("Pin %d write: %d -> %d", pin, old_value, g_pin_states[pin].value);
    
    // 模拟引脚值变化，触发中断检测
    if (old_value != g_pin_states[pin].value) {
        // 这里可以添加中断触发逻辑，但在实际应用中通常由外部信号触发
    }
}

/**
 * @brief 读取引脚值
 * @param pin 引脚编号
 * @return int 引脚值 (PIN_LOW/PIN_HIGH)，错误时返回-1
 */
int es_pin_read(uint16_t pin)
{
    if (!g_pin_initialized) {
        es_pin_init();
    }
    
    if (pin >= MAX_PIN_COUNT) {
        PIN_DEBUG("Invalid pin number: %d", pin);
        return -1;
    }
    
    int value = g_pin_states[pin].value;
    PIN_DEBUG("Pin %d read: %d", pin, value);
    return value;
}

/**
 * @brief 检查中断触发条件
 * @param pin 引脚编号
 * @param old_value 旧值
 * @param new_value 新值
 * @return int 1表示应该触发中断，0表示不触发
 */
static int should_trigger_irq(uint16_t pin, uint8_t old_value, uint8_t new_value)
{
    if (!g_pin_states[pin].irq_enabled || !g_pin_states[pin].irq_handler) {
        return 0;
    }
    
    switch (g_pin_states[pin].irq_mode) {
        case PIN_IRQ_MODE_RISING:
            return (old_value == PIN_LOW && new_value == PIN_HIGH);
        case PIN_IRQ_MODE_FALLING:
            return (old_value == PIN_HIGH && new_value == PIN_LOW);
        case PIN_IRQ_MODE_RISING_FALLING:
            return (old_value != new_value);
        case PIN_IRQ_MODE_HIGH_LEVEL:
            return (new_value == PIN_HIGH);
        case PIN_IRQ_MODE_LOW_LEVEL:
            return (new_value == PIN_LOW);
        default:
            return 0;
    }
}

/**
 * @brief 模拟外部信号变化（用于测试中断功能）
 * @param pin 引脚编号
 * @param value 新的引脚值
 */
void es_pin_simulate_input(uint16_t pin, uint8_t value)
{
    if (!g_pin_initialized) {
        es_pin_init();
    }
    
    if (pin >= MAX_PIN_COUNT) {
        PIN_DEBUG("Invalid pin number: %d", pin);
        return;
    }
    
    uint8_t old_value = g_pin_states[pin].value;
    uint8_t new_value = value ? PIN_HIGH : PIN_LOW;
    
    g_pin_states[pin].value = new_value;
    
    PIN_DEBUG("Pin %d simulated input: %d -> %d", pin, old_value, new_value);
    
    // 检查是否需要触发中断
    if (should_trigger_irq(pin, old_value, new_value)) {
        PIN_DEBUG("Triggering IRQ for pin %d", pin);
        if (g_pin_states[pin].irq_handler) {
            g_pin_states[pin].irq_handler(g_pin_states[pin].irq_args);
        }
    }
    
    g_pin_states[pin].last_value = new_value;
}

/**
 * @brief 绑定引脚中断
 * @param pin 引脚编号
 * @param mode 中断模式
 * @param hdr 中断处理函数
 * @param args 中断处理参数
 * @return int 0表示成功，非0表示失败
 */
int es_pin_attach_irq(uint16_t pin, uint16_t mode, void (*hdr)(void *args), void *args)
{
    if (!g_pin_initialized) {
        es_pin_init();
    }
    
    if (pin >= MAX_PIN_COUNT) {
        PIN_DEBUG("Invalid pin number: %d", pin);
        return -1;
    }
    
    if (!hdr) {
        PIN_DEBUG("Invalid interrupt handler");
        return -1;
    }
    
    // 检查中断模式是否有效
    if (mode > PIN_IRQ_MODE_LOW_LEVEL) {
        PIN_DEBUG("Invalid interrupt mode: %d", mode);
        return -1;
    }
    
    g_pin_states[pin].irq_mode = mode;
    g_pin_states[pin].irq_handler = hdr;
    g_pin_states[pin].irq_args = args;
    g_pin_states[pin].last_value = g_pin_states[pin].value;
    
    PIN_DEBUG("Pin %d IRQ attached, mode: %d", pin, mode);
    return 0;
}

/**
 * @brief 解绑引脚中断
 * @param pin 引脚编号
 * @return int 0表示成功，非0表示失败
 */
int es_pin_detach_irq(uint16_t pin)
{
    if (!g_pin_initialized) {
        es_pin_init();
    }
    
    if (pin >= MAX_PIN_COUNT) {
        PIN_DEBUG("Invalid pin number: %d", pin);
        return -1;
    }
    
    g_pin_states[pin].irq_handler = NULL;
    g_pin_states[pin].irq_args = NULL;
    g_pin_states[pin].irq_enabled = PIN_IRQ_DISABLE;
    
    PIN_DEBUG("Pin %d IRQ detached", pin);
    return 0;
}

/**
 * @brief 使能/禁用引脚中断
 * @param pin 引脚编号
 * @param enabled 中断使能状态 (PIN_IRQ_ENABLE/PIN_IRQ_DISABLE)
 * @return int 0表示成功，非0表示失败
 */
int es_pin_irq_enable(uint16_t pin, uint8_t enabled)
{
    if (!g_pin_initialized) {
        es_pin_init();
    }
    
    if (pin >= MAX_PIN_COUNT) {
        PIN_DEBUG("Invalid pin number: %d", pin);
        return -1;
    }
    
    if (!g_pin_states[pin].irq_handler && enabled) {
        PIN_DEBUG("Warning: Enabling IRQ for pin %d without handler", pin);
    }
    
    g_pin_states[pin].irq_enabled = enabled ? PIN_IRQ_ENABLE : PIN_IRQ_DISABLE;
    
    PIN_DEBUG("Pin %d IRQ %s", pin, enabled ? "enabled" : "disabled");
    return 0;
}

// 兼容性函数
int win32_pin_init(void) {
    return es_pin_init();
}

int es_pm_enter_lpm_stop(uint32_t wke_mask) {
    // do nothing
    return 0;
}

#endif /* _WIN32 */