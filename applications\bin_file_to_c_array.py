#!/usr/bin/env python3
"""
Convert binary file to C array format
"""

import sys
import os

def bin_to_c_array(bin_file_path, array_name=None, output_file=None):
    """
    Convert binary file to C array format
    
    Args:
        bin_file_path: Path to the binary file
        array_name: Name of the C array (default: based on filename)
        output_file: Output file path (default: stdout)
    """
    if not os.path.exists(bin_file_path):
        print(f"Error: File '{bin_file_path}' not found", file=sys.stderr)
        return False
    
    # Generate array name from filename if not provided
    if array_name is None:
        array_name = "rule_data"
    
    try:
        with open(bin_file_path, 'rb') as f:
            data = f.read()
        
        # Generate C array
        output_lines = []
        output_lines.append(f"// Generated from {bin_file_path} (len={len(data)})")
        output_lines.append(f"static const uint8_t {array_name}[] = {{")
        
        # Convert bytes to hex format, 16 bytes per line
        for i in range(0, len(data), 16):
            chunk = data[i:i+16]
            hex_values = [f"0x{byte:02x}" for byte in chunk]
            line = "    " + ", ".join(hex_values)
            if i + 16 < len(data):
                line += ","
            output_lines.append(line)
        
        output_lines.append("};")
        output_lines.append(f"static const unsigned int {array_name}_len = {len(data)};")
        
        # Output to file or stdout
        output_content = "\n".join(output_lines) + "\n"
        if output_file:
            with open(output_file, 'w') as f:
                f.write(output_content)
            print(f"C array written to {output_file}")
        else:
            print(output_content)
        
        return True
        
    except Exception as e:
        print(f"Error processing file: {e}", file=sys.stderr)
        return False

def main():
    # Default input file is example_raw.bin
    default_bin_file = "example_raw.bin"
    
    if len(sys.argv) < 2:
        bin_file = default_bin_file
        array_name = None
        output_file = None
    else:
        bin_file = sys.argv[1]
        array_name = sys.argv[2] if len(sys.argv) > 2 else None
        output_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    print(f"Usage: python bin_file_to_c_array.py [bin_file] [array_name] [output_file]")
    print(f"  bin_file: Path to binary file (default: {default_bin_file})")
    print(f"  array_name: Name of C array (optional)")
    print(f"  output_file: Output file path (optional, default: stdout)")
    print(f"Processing file: {bin_file}")
    print()
    
    success = bin_to_c_array(bin_file, array_name, output_file)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

