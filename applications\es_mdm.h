/**
 * @file es_mdm.h
 * @brief Module communication interface definitions
 */

#ifndef ES_MDM_H
#define ES_MDM_H

#include <stdint.h>
#include <stdbool.h>
#include "es_coro.h"
#include "es_at_cli.h"
#include "es_drv_uart.h"
#include "es_frame.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Module instance structure
 */
typedef struct {
    es_at_cli_t at_cli;            /**< AT command line interface */
    uint32_t event;                /**< 事件 */
    uint32_t mqtt_event;           /**< MQTT事件 */
    char version[32];
    char imei[16];                /**< IMEI number */
    char iccid[21];               /**< ICCID number */
    uint8_t rx_buf[2048+2];         /**< Receive buffer */
    uint16_t rx_buf_len;          /**< Receive buffer length */
    uint8_t tx_buf[1024];         /**< Transmit buffer */
    uint16_t tx_buf_len;          /**< Transmit buffer length */
    uint16_t seq;
    uint8_t cnt;
    uint8_t init_ok;              /**< Initialization status */
    struct {
        uint8_t mqtt_login : 1;       /**< MQTT login status */
        uint8_t mqtt_connect : 1;    /**< MQTT connection status */
    } flags;
    es_coro_task_t mdm_task;
} es_mdm_t;

/**
 * @brief Get module singleton instance
 * @return Module instance pointer
 */
es_mdm_t *es_mdm_get_instance(void);

/**
 * @brief Initialize module (singleton)
 * @return Returns 0 on success, negative on failure
 */
int es_mdm_init(void);


es_async_t es_mdm_at_mqtt_publish(es_coro_t* coro, const uint8_t* data, uint16_t len);

/**
 * @brief HTTP file download function
 * @param coro 协程上下文
 * @param url HTTP URL
 * @return Coroutine return value
 */
es_async_t es_mdm_at_http_download(es_coro_t* coro, const char* url);


/**
 * @brief Add frame to queue
 * @param frame 帧
 */
void es_mdm_add_to_queue(const frame_tx_t *frame);


#ifdef __cplusplus
}
#endif

#endif /* ES_MDM_H */ 
