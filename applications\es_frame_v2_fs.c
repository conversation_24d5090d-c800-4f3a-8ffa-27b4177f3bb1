//
// Created by lxy on 2025/1/14.
// ES Frame Storage - Ring buffer frame storage with flash persistence implementation
//

#include "es_frame_v2_fs.h"
#include <string.h>
#include <stdio.h>

#ifndef ES_FRAME_V2_TESTING
#include "es_flash.h"
#include "es_drv_os.h"
#endif

// Forward declarations
static int scan_write_position(uint32_t sector_id);
static int scan_read_position(uint32_t sector_id);

// Global context
static es_frame_v2_context_t s_frame_ctx = {0};

#ifndef ES_FRAME_V2_TESTING
// Static variables for flash interface implementation
static const es_partition_t *frame_partition = NULL;

// Implementation of external flash interface functions using es_flash
int frame_flash_read(uint32_t addr, void *data, uint32_t len) {
    if (!frame_partition) {
        return -1;
    }
    
    // Convert absolute address to partition offset
    // uint32_t offset = addr - s_frame_ctx.flash_base;
    return es_flash_read(frame_partition, addr, (uint8_t*)data, len) > 0 ? 0 : -1;
}

int frame_flash_write(uint32_t addr, const void *data, uint32_t len) {
    if (!frame_partition) {
        return -1;
    }
    
    // Convert absolute address to partition offset
    // uint32_t offset = addr - s_frame_ctx.flash_base;
    return es_flash_write(frame_partition, addr, (const uint8_t*)data, len) > 0 ? 0 : -1;
}

int frame_flash_erase_sector(uint32_t addr) {
    if (!frame_partition) {
        return -1;
    }
    
    // Convert absolute address to partition offset
    // uint32_t offset = addr - s_frame_ctx.flash_base;
    uint32_t sector_size = frame_partition->flash_dev->sector_size;
    
    return es_flash_erase(frame_partition, addr, sector_size);
}

uint32_t frame_flash_get_sector_size(void) {
    if (!frame_partition) {
        return 4096; // Default sector size
    }
    return frame_partition->flash_dev->sector_size;
}

uint32_t frame_flash_get_frame_base_addr(void) {
    if (!frame_partition) {
        return 0;
    }
    return 0;
}

uint32_t frame_flash_get_frame_size(void) {
    if (!frame_partition) {
        return 0;
    }
    return frame_partition->size;
}

// Initialize frame partition
static int init_frame_partition(void) {
    if (frame_partition) {
        return 0; // Already initialized
    }
    
    // Initialize flash system
    int ret = es_flash_init();
    if (ret != 0) {
        return ret;
    }
    
    // Find frame partition (assuming it uses "store" partition)
    frame_partition = es_partition_find("blind");
    if (!frame_partition) {
        return -1;
    }
    
    return 0;
}
#endif

// CRC32 calculation (same as es_log_storage)
static inline uint32_t frame_crc32_calc(const void *data, uint16_t len) {
    uint32_t crc = DEF_FRAME_CRC_INIT_VALUE;
    const uint8_t *ptr = (const uint8_t *)data;
    while (len--) {
        crc ^= *ptr++;
        for (int k = 0; k < 8; k++) {
            crc = crc & 1 ? (crc >> 1) ^ 0xEDB88320 : crc >> 1;
        }
    }
    return crc;
}

// Helper functions
static inline uint32_t align_up(uint32_t value, uint32_t alignment) {
    return (value + alignment - 1) & ~(alignment - 1);
}


static inline uint32_t get_sector_addr(uint32_t sector_id) {
    return s_frame_ctx.flash_base + sector_id * s_frame_ctx.sector_size;
}

static inline uint32_t get_next_sector(uint32_t sector_id) {
    return (sector_id + 1) % s_frame_ctx.sector_count;
}

static inline uint32_t get_sector_header_size(void) {
    return align_up(sizeof(es_frame_v2_sector_header_t), ES_FRAME_V2_ALIGN_SIZE);
}

static inline uint32_t get_entry_header_size(void) {
    return align_up(sizeof(es_frame_v2_entry_t), ES_FRAME_V2_ALIGN_SIZE);
}

static inline uint32_t get_entry_total_size(uint16_t data_len) {
    return get_entry_header_size() + align_up(data_len, ES_FRAME_V2_ALIGN_SIZE);
}

static inline uint32_t get_usable_sector_size(void) {
    return s_frame_ctx.sector_size - get_sector_header_size();
}

// Verify sector header CRC
static bool verify_sector_header_crc(const es_frame_v2_sector_header_t *header) {
    if (!header) {
        return false;
    }
    
    uint32_t calc_crc = frame_crc32_calc(header, sizeof(es_frame_v2_sector_header_t) - sizeof(header->crc32));
    return calc_crc == header->crc32;
}

// Write sector header
static int write_sector_header(uint32_t sector_id) {
    es_frame_v2_sector_header_t header = {0};

    // Check if sector is empty, if not, erase it
    uint32_t status = 0;
    if (frame_flash_read(get_sector_addr(sector_id), &status, sizeof(status)) == 0) {
        if (status != ES_FRAME_V2_STATUS_EMPTY) {
            if (frame_flash_erase_sector(get_sector_addr(sector_id)) != ES_FRAME_V2_OK) {
                return ES_FRAME_V2_ERR_FLASH_ERROR;
            }
        }
    }

    if (s_frame_ctx.max_write_count == 0xFFFFFFFF) {
        s_frame_ctx.max_write_count = 1;
    } else {
        s_frame_ctx.max_write_count++;
    }

    header.magic = ES_FRAME_V2_MAGIC;
    header.write_count = s_frame_ctx.max_write_count;
    header.crc32 = frame_crc32_calc(&header, sizeof(header) - sizeof(header.crc32));
    
    uint32_t sector_addr = get_sector_addr(sector_id);
    int ret = frame_flash_write(sector_addr, &header, sizeof(header));
    if (ret != 0) {
        return ret;
    }

    return ES_FRAME_V2_OK;
}

// Read sector header with retry
static int read_sector_header(uint32_t sector_id, es_frame_v2_sector_header_t *header) {
    if (!header) {
        return ES_FRAME_V2_ERR_INVALID_PARAM;
    }
    
    uint32_t sector_addr = get_sector_addr(sector_id);
    int ret = -1;
    
    for (int retry = 0; retry < ES_FRAME_V2_RETRY_COUNT; retry++) {
        ret = frame_flash_read(sector_addr, header, sizeof(*header));
        if (ret == 0) {
            break;
        }
    }
    
    return ret;
}

// Find current read/write sectors by scanning all sectors
static int scan_sectors(void) {
    uint32_t max_write_count = 0;
    uint32_t min_write_count = 0xFFFFFFFF;
    uint32_t write_sector = 0;
    uint32_t read_sector = 0;
    bool found_valid = false;
    
    // Find sectors with highest and lowest write counts
    for (uint32_t i = 0; i < s_frame_ctx.sector_count; i++) {
        es_frame_v2_sector_header_t header = {0};
        if (read_sector_header(i, &header) != 0) {
            continue;
        }
        
        // Check if sector is valid
        if (header.magic != ES_FRAME_V2_MAGIC) {
            continue;
        }
        
        if (!verify_sector_header_crc(&header)) {
            continue;
        }
        
        found_valid = true;
        
        // Find sector with maximum write count (current write sector)
        if (header.write_count > max_write_count) {
            max_write_count = header.write_count;
            write_sector = i;
        }

        // Find sector with minimum write count (oldest sector for read)
        if (header.write_count < min_write_count) {
            min_write_count = header.write_count;
            read_sector = i;
        }
    }
    
    if (!found_valid) {
        // No valid sectors found, initialize first sector
        s_frame_ctx.write_sector = 0;
        s_frame_ctx.read_sector = 0;
        s_frame_ctx.write_offset = get_sector_header_size();
        s_frame_ctx.read_offset = get_sector_header_size();
        s_frame_ctx.max_write_count = 0;
        return ES_FRAME_V2_OK;
    }
    
    // Set current read and write sectors
    s_frame_ctx.write_sector = write_sector;
    s_frame_ctx.read_sector = read_sector;
    s_frame_ctx.max_write_count = max_write_count;
    
    return ES_FRAME_V2_OK;
}

// Scan write sector to find current write position
static int scan_write_position(uint32_t sector_id) {
    uint32_t offset = get_sector_header_size();
    uint32_t sector_addr = get_sector_addr(sector_id);
    
    while (offset < s_frame_ctx.sector_size) {
        // Read entry header
        es_frame_v2_entry_t entry_header = {0};
        if (frame_flash_read(sector_addr + offset, &entry_header, sizeof(es_frame_v2_entry_t)) != 0) {
            return ES_FRAME_V2_ERR_FLASH_ERROR;
        }
        
        // If we find an empty slot, this is our write position
        if (entry_header.status == ES_FRAME_V2_STATUS_EMPTY) {
            break;
        }
        
        // Validate data_len to prevent corruption issues
        if (entry_header.data_len > ES_FRAME_V2_MAX_DATA_SIZE) {
            // Data length is corrupted, skip by header size and continue scanning
            offset += get_entry_header_size();
            continue;
        }
        
        // Skip to next entry
        uint32_t entry_size = get_entry_total_size(entry_header.data_len);
        offset += entry_size;
        
        // Ensure we don't go beyond sector boundary
        if (offset >= s_frame_ctx.sector_size) {
            offset = s_frame_ctx.sector_size; // Mark sector as full
            break;
        }
    }
    
    s_frame_ctx.write_offset = offset;
    return ES_FRAME_V2_OK;
}

// Scan read sector to find first valid entry
static int scan_read_position(uint32_t sector_id) {
    uint32_t offset = get_sector_header_size();
    uint32_t sector_addr = get_sector_addr(sector_id);
    
    while (offset < s_frame_ctx.sector_size) {
        // Read entry header
        es_frame_v2_entry_t entry_header = {0};
        if (frame_flash_read(sector_addr + offset, &entry_header, sizeof(es_frame_v2_entry_t)) != 0) {
            return ES_FRAME_V2_ERR_FLASH_ERROR;
        }
        
        // If we find an empty slot, no more data
        if (entry_header.status == ES_FRAME_V2_STATUS_EMPTY) {
            break;
        }
        
        // If we find a valid entry, this is our read position
        if (entry_header.status == ES_FRAME_V2_STATUS_VALID) {
            s_frame_ctx.read_offset = offset;
            return ES_FRAME_V2_OK;
        }
        
        // Validate data_len to prevent corruption issues
        if (entry_header.data_len > ES_FRAME_V2_MAX_DATA_SIZE) {
            // Data length is corrupted, skip by header size and continue scanning
            offset += get_entry_header_size();
            continue;
        }
        
        // Skip invalid entries
        uint32_t entry_size = get_entry_total_size(entry_header.data_len);
        offset += entry_size;
        
        // Ensure we don't go beyond sector boundary
        if (offset >= s_frame_ctx.sector_size) {
            break;
        }
    }
    
    // No valid entry found in this sector
    s_frame_ctx.read_offset = offset;
    return ES_FRAME_V2_OK;
}

// Allocate new sector for writing
static int allocate_new_write_sector(void) {
    uint32_t new_sector = get_next_sector(s_frame_ctx.write_sector);
    
    // If new write sector conflicts with current read sector, move read sector
    if (new_sector == s_frame_ctx.read_sector) {
        // Find next sector with valid data for reading
        uint32_t next_read_sector = get_next_sector(s_frame_ctx.read_sector);
        while (next_read_sector != new_sector) {
            if (scan_read_position(next_read_sector) == ES_FRAME_V2_OK) {
                // Check if this sector has valid data
                if (s_frame_ctx.read_offset < s_frame_ctx.sector_size) {
                    es_frame_v2_entry_t entry_header = {0};
                    uint32_t sector_addr = get_sector_addr(next_read_sector);
                    if (frame_flash_read(sector_addr + s_frame_ctx.read_offset, &entry_header, sizeof(es_frame_v2_entry_t)) == 0) {
                        if (entry_header.status == ES_FRAME_V2_STATUS_VALID) {
                            s_frame_ctx.read_sector = next_read_sector;
                            break;
                        }
                    }
                }
            }
            next_read_sector = get_next_sector(next_read_sector);
        }
        
        // If no valid read sector found, set read to same as write
        if (next_read_sector == new_sector) {
            s_frame_ctx.read_sector = new_sector;
            s_frame_ctx.read_offset = get_sector_header_size();
        }
    }
    
    // Erase new sector
    uint32_t new_sector_addr = get_sector_addr(new_sector);
    if (frame_flash_erase_sector(new_sector_addr) != 0) {
        return ES_FRAME_V2_ERR_FLASH_ERROR;
    }

    // Write header to new sector
    if (write_sector_header(new_sector) != ES_FRAME_V2_OK) {
        return ES_FRAME_V2_ERR_FLASH_ERROR;
    }
    
    s_frame_ctx.write_sector = new_sector;
    s_frame_ctx.write_offset = get_sector_header_size();
    
    return ES_FRAME_V2_OK;
}

// Public API implementation

int es_frame_v2_init(void) {
    if (s_frame_ctx.initialized) {
        return ES_FRAME_V2_OK;
    }
    
    memset(&s_frame_ctx, 0, sizeof(s_frame_ctx));
    
#ifndef ES_FRAME_V2_TESTING
    // Initialize frame partition
    if (init_frame_partition() != 0) {
        return ES_FRAME_V2_ERR_FLASH_ERROR;
    }
#endif
    
    s_frame_ctx.flash_base = frame_flash_get_frame_base_addr();
    s_frame_ctx.flash_size = frame_flash_get_frame_size();
    s_frame_ctx.sector_size = frame_flash_get_sector_size();
    s_frame_ctx.sector_count = s_frame_ctx.flash_size / s_frame_ctx.sector_size;
    
    if (s_frame_ctx.sector_count < 2) {
        return ES_FRAME_V2_ERR_NO_SPACE;
    }
    
    if (s_frame_ctx.sector_size < get_sector_header_size() + get_entry_total_size(1)) {
        return ES_FRAME_V2_ERR_NO_SPACE;
    }
    
    s_frame_ctx.initialized = true;
    return ES_FRAME_V2_OK;
}

int es_frame_v2_load(void) {
    if (!s_frame_ctx.initialized) {
        return ES_FRAME_V2_ERR_INVALID_PARAM;
    }
    
    if (scan_sectors() != ES_FRAME_V2_OK) {
        return ES_FRAME_V2_ERR_FLASH_ERROR;
    }
    
    if (scan_write_position(s_frame_ctx.write_sector) != ES_FRAME_V2_OK) {
        return ES_FRAME_V2_ERR_FLASH_ERROR;
    }
    
    if (scan_read_position(s_frame_ctx.read_sector) != ES_FRAME_V2_OK) {
        return ES_FRAME_V2_ERR_FLASH_ERROR;
    }
    
    // If write offset is at header size, check sector header validity
    if (s_frame_ctx.write_offset == get_sector_header_size()) {
        es_frame_v2_sector_header_t header = {0};
        if (read_sector_header(s_frame_ctx.write_sector, &header) != ES_FRAME_V2_OK) {
            return ES_FRAME_V2_ERR_FLASH_ERROR;
        }
        if (header.magic != ES_FRAME_V2_MAGIC || !verify_sector_header_crc(&header)) {
            if (frame_flash_erase_sector(get_sector_addr(s_frame_ctx.write_sector)) != 0) {
                return ES_FRAME_V2_ERR_FLASH_ERROR;
            }
            if (write_sector_header(s_frame_ctx.write_sector) != ES_FRAME_V2_OK) {
                return ES_FRAME_V2_ERR_FLASH_ERROR;
            }
        }
    }
    
    return ES_FRAME_V2_OK;
}

int es_frame_v2_push(const void *data, uint16_t data_len) {
    if (!s_frame_ctx.initialized || !data || data_len == 0) {
        return ES_FRAME_V2_ERR_INVALID_PARAM;
    }
    
    if (data_len > ES_FRAME_V2_MAX_DATA_SIZE) {
        return ES_FRAME_V2_ERR_FRAME_TOO_LARGE;
    }
    
    uint32_t entry_size = get_entry_total_size(data_len);
    
    // Check if current sector has enough space
    if (s_frame_ctx.write_offset + entry_size > s_frame_ctx.sector_size) {
        int ret = allocate_new_write_sector();
        if (ret != ES_FRAME_V2_OK) {
            return ret;
        }
    }
    
    // Create entry using static buffer
    uint32_t total_entry_size = get_entry_header_size() + align_up(data_len, ES_FRAME_V2_ALIGN_SIZE);
    static uint8_t entry_buffer[sizeof(es_frame_v2_entry_t) + ES_FRAME_V2_MAX_DATA_SIZE + ES_FRAME_V2_ALIGN_SIZE] = {0};
    
    es_frame_v2_entry_t *entry = (es_frame_v2_entry_t *)entry_buffer;
    entry->status = ES_FRAME_V2_STATUS_VALID;
    entry->data_len = data_len;
    entry->data_crc = frame_crc32_calc(data, data_len);
    
    // Copy data after the header
    memcpy(entry_buffer + get_entry_header_size(), data, data_len);
    
    // Write entry to flash
    uint32_t write_addr = get_sector_addr(s_frame_ctx.write_sector) + s_frame_ctx.write_offset;
    int ret = frame_flash_write(write_addr, entry_buffer, total_entry_size);
    
    if (ret != 0) {
        return ES_FRAME_V2_ERR_FLASH_ERROR;
    }
    
    // Update write offset
    s_frame_ctx.write_offset += entry_size;
    
    return ES_FRAME_V2_OK;
}

int es_frame_v2_pop(void *data, uint16_t *data_len) {
    if (!s_frame_ctx.initialized || !data || !data_len) {
        return ES_FRAME_V2_ERR_INVALID_PARAM;
    }
    
    uint16_t buffer_size = *data_len;
    *data_len = 0;
    
    // Search for valid entry starting from current read position
    while (true) {
        // Check if we've reached the end of current sector
        if (s_frame_ctx.read_offset >= s_frame_ctx.sector_size) {
            // Move to next sector
            uint32_t next_sector = get_next_sector(s_frame_ctx.read_sector);
            
            // If we've caught up with write sector, no more data
            if (next_sector == s_frame_ctx.write_sector && 
                s_frame_ctx.read_offset >= s_frame_ctx.write_offset) {
                return ES_FRAME_V2_ERR_NO_DATA;
            }
            
            s_frame_ctx.read_sector = next_sector;
            if (scan_read_position(s_frame_ctx.read_sector) != ES_FRAME_V2_OK) {
                return ES_FRAME_V2_ERR_FLASH_ERROR;
            }
            
            // If no valid data in this sector, continue to next
            if (s_frame_ctx.read_offset >= s_frame_ctx.sector_size) {
                continue;
            }
        }
        
        // Check if we've caught up with write position in same sector
        if (s_frame_ctx.read_sector == s_frame_ctx.write_sector && 
            s_frame_ctx.read_offset >= s_frame_ctx.write_offset) {
            return ES_FRAME_V2_ERR_NO_DATA;
        }
        
        // Read entry header
        uint32_t read_addr = get_sector_addr(s_frame_ctx.read_sector) + s_frame_ctx.read_offset;
        es_frame_v2_entry_t entry_header = {0};
        
        int ret = -1;
        for (int retry = 0; retry < ES_FRAME_V2_RETRY_COUNT; retry++) {
            ret = frame_flash_read(read_addr, &entry_header, sizeof(es_frame_v2_entry_t));
            if (ret == 0) {
                break;
            }
        }
        
        if (ret != 0) {
            return ES_FRAME_V2_ERR_FLASH_ERROR;
        }
        
        // If empty, no more data
        if (entry_header.status == ES_FRAME_V2_STATUS_EMPTY) {
            return ES_FRAME_V2_ERR_NO_DATA;
        }
        
        // Validate data_len field to prevent corruption issues
        if (entry_header.data_len > ES_FRAME_V2_MAX_DATA_SIZE) {
            // Data length is corrupted, try to find next valid frame
            s_frame_ctx.read_offset += get_entry_header_size();
            continue;
        }
        
        uint32_t entry_size = get_entry_total_size(entry_header.data_len);
        
        // Ensure entry doesn't exceed sector boundary
        if (s_frame_ctx.read_offset + entry_size > s_frame_ctx.sector_size) {
            // Entry extends beyond sector, move to next sector
            s_frame_ctx.read_offset = s_frame_ctx.sector_size;
            continue;
        }
        
        // If invalid, skip to next entry
        if (entry_header.status == ES_FRAME_V2_STATUS_INVALID) {
            s_frame_ctx.read_offset += entry_size;
            continue;
        }
        
        // Found valid entry
        if (entry_header.status == ES_FRAME_V2_STATUS_VALID) {
            // Check buffer size
            if (buffer_size < entry_header.data_len) {
                return ES_FRAME_V2_ERR_NO_SPACE;
            }
            
            // Read data
            uint32_t data_addr = read_addr + get_entry_header_size();
            ret = -1;
            for (int retry = 0; retry < ES_FRAME_V2_RETRY_COUNT; retry++) {
                ret = frame_flash_read(data_addr, data, entry_header.data_len);
                if (ret == 0) {
                    break;
                }
            }
            
            if (ret != 0) {
                return ES_FRAME_V2_ERR_FLASH_ERROR;
            }
            
            // Verify CRC
            uint32_t calc_crc = frame_crc32_calc(data, entry_header.data_len);
            if (calc_crc != entry_header.data_crc) {
                // CRC error, mark as invalid and continue
                uint32_t invalid_status = ES_FRAME_V2_STATUS_INVALID;
                frame_flash_write(read_addr, &invalid_status, sizeof(invalid_status));
                s_frame_ctx.read_offset += entry_size;
                continue;
            }
            
            // Mark entry as invalid (read)
            uint32_t invalid_status = ES_FRAME_V2_STATUS_INVALID;
            frame_flash_write(read_addr, &invalid_status, sizeof(invalid_status));
            
            // Update read position
            s_frame_ctx.read_offset += entry_size;
            *data_len = entry_header.data_len;
            
            return ES_FRAME_V2_OK;
        }
        
        // Unknown status, skip
        s_frame_ctx.read_offset += entry_size;
    }
    
    return ES_FRAME_V2_ERR_NO_DATA;
}

int es_frame_v2_clear(void) {
    if (!s_frame_ctx.initialized) {
        return ES_FRAME_V2_ERR_INVALID_PARAM;
    }
    
    // Erase all sectors
    for (uint32_t i = 0; i < s_frame_ctx.sector_count; i++) {
        if (frame_flash_erase_sector(get_sector_addr(i)) != 0) {
            return ES_FRAME_V2_ERR_FLASH_ERROR;
        }
    }
    
    // Reset context
    s_frame_ctx.write_sector = 0;
    s_frame_ctx.read_sector = 0;
    s_frame_ctx.write_offset = get_sector_header_size();
    s_frame_ctx.read_offset = get_sector_header_size();
    s_frame_ctx.max_write_count = 0;
    
    // Initialize first sector
    if (write_sector_header(0) != ES_FRAME_V2_OK) {
        return ES_FRAME_V2_ERR_FLASH_ERROR;
    }
    
    return ES_FRAME_V2_OK;
}

uint32_t es_frame_v2_count(void) {
    if (!s_frame_ctx.initialized) {
        return 0;
    }
    
    uint32_t count = 0;
    
    // Count valid frames in all sectors
    for (uint32_t sector = 0; sector < s_frame_ctx.sector_count; sector++) {
        uint32_t offset = get_sector_header_size();
        uint32_t sector_addr = get_sector_addr(sector);
        
        while (offset < s_frame_ctx.sector_size) {
            es_frame_v2_entry_t entry_header = {0};
            if (frame_flash_read(sector_addr + offset, &entry_header, sizeof(es_frame_v2_entry_t)) != 0) {
                break;
            }
            
            if (entry_header.status == ES_FRAME_V2_STATUS_EMPTY) {
                break;
            }
            
            // Validate data_len to prevent corruption issues
            if (entry_header.data_len > ES_FRAME_V2_MAX_DATA_SIZE) {
                // Data length is corrupted, skip by header size and continue scanning
                offset += get_entry_header_size();
                continue;
            }
            
            if (entry_header.status == ES_FRAME_V2_STATUS_VALID) {
                count++;
            }
            
            uint32_t entry_size = get_entry_total_size(entry_header.data_len);
            offset += entry_size;
            
            if (offset >= s_frame_ctx.sector_size) {
                break;
            }
        }
    }
    
    return count;
}

bool es_frame_v2_is_empty(void) {
    if (!s_frame_ctx.initialized) {
        return true;
    }
    
    // Quick check: if read and write positions are the same and at header size
    if (s_frame_ctx.read_sector == s_frame_ctx.write_sector && 
        s_frame_ctx.read_offset == s_frame_ctx.write_offset && 
        s_frame_ctx.read_offset == get_sector_header_size()) {
        return true;
    }
    
    // Quick check: if read position is ahead of write position in the same sector
    if (s_frame_ctx.read_sector == s_frame_ctx.write_sector && 
        s_frame_ctx.read_offset >= s_frame_ctx.write_offset) {
        return true;
    }
    
    return false;
}

#ifdef ES_FRAME_V2_TESTING
void es_frame_v2_reset_state(void) {
    memset(&s_frame_ctx, 0, sizeof(s_frame_ctx));
}

es_frame_v2_context_t* es_frame_v2_get_context(void) {
    return &s_frame_ctx;
}
#endif 
