/**
 *******************************************************************************
 * @file  lsm6dsl.h
 * @brief This file provides firmware functions to LSM6DSL 6-axis IMU sensor.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-11-01       CDT             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2022-2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */
#ifndef __LSM6DSL_H__
#define __LSM6DSL_H__

/* C binding of definitions if building with C++ compiler */
#ifdef __cplusplus
extern "C"
{
#endif

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "hc32_ll_def.h"

/**
 * @addtogroup BSP
 * @{
 */

/**
 * @addtogroup Components
 * @{
 */

/**
 * @addtogroup LSM6DSL
 * @{
 */

/*******************************************************************************
 * Global type definitions ('typedef')
 ******************************************************************************/
/**
 * @brief LSM6DSL sensor data type
 */
typedef struct {
    int16_t x;      /* X-axis data */
    int16_t y;      /* Y-axis data */
    int16_t z;      /* Z-axis data */
} stc_lsm6dsl_axis_t;

/**
 * @brief LSM6DSL 6-axis IMU data structure
 */
typedef struct {
    stc_lsm6dsl_axis_t accel;  /* Accelerometer data */
    stc_lsm6dsl_axis_t gyro;   /* Gyroscope data */
    float temperature;         /* Temperature data in Celsius */
} stc_lsm6dsl_data_t;

/**
 * @brief LSM6DSL accelerometer parameters
 */
typedef enum {
    LSM6DSL_ACCEL_SCALE_2G  = 0x00,    /* ±2g */
    LSM6DSL_ACCEL_SCALE_4G  = 0x08,    /* ±4g */
    LSM6DSL_ACCEL_SCALE_8G  = 0x0C,    /* ±8g */
    LSM6DSL_ACCEL_SCALE_16G = 0x04     /* ±16g */
} en_lsm6dsl_accel_scale_t;

/**
 * @brief LSM6DSL gyroscope parameters
 */
typedef enum {
    LSM6DSL_GYRO_SCALE_250DPS  = 0x00,  /* ±250dps */
    LSM6DSL_GYRO_SCALE_500DPS  = 0x04,  /* ±500dps */
    LSM6DSL_GYRO_SCALE_1000DPS = 0x08,  /* ±1000dps */
    LSM6DSL_GYRO_SCALE_2000DPS = 0x0C   /* ±2000dps */
} en_lsm6dsl_gyro_scale_t;

/**
 * @brief LSM6DSL output data rate
 */
typedef enum {
    LSM6DSL_ODR_POWER_DOWN = 0x00,     /* Power-down */
    LSM6DSL_ODR_12_5HZ     = 0x10,     /* 12.5Hz */
    LSM6DSL_ODR_26HZ       = 0x20,     /* 26Hz */
    LSM6DSL_ODR_52HZ       = 0x30,     /* 52Hz */
    LSM6DSL_ODR_104HZ      = 0x40,     /* 104Hz */
    LSM6DSL_ODR_208HZ      = 0x50,     /* 208Hz */
    LSM6DSL_ODR_416HZ      = 0x60,     /* 416Hz */
    LSM6DSL_ODR_833HZ      = 0x70,     /* 833Hz */
    LSM6DSL_ODR_1660HZ     = 0x80,     /* 1.66kHz */
    LSM6DSL_ODR_3330HZ     = 0x90,     /* 3.33kHz */
    LSM6DSL_ODR_6660HZ     = 0xA0      /* 6.66kHz */
} en_lsm6dsl_odr_t;

/**
 * @brief LSM6DSL low layer structure definition
 */
typedef struct {
    void (*Delay)(uint32_t);
    void (*Init)(void);
    void (*DeInit)(void);
    int32_t (*WriteReg)(uint8_t, const uint8_t *, uint16_t);
    int32_t (*ReadReg)(uint8_t, uint8_t *, uint16_t);
} stc_lsm6dsl_ll_t;

/*******************************************************************************
 * Global pre-processor symbols/macros ('#define')
 ******************************************************************************/
/**
 * @defgroup LSM6DSL_Global_Macros LSM6DSL Global Macros
 * @{
 */

/**
 * @defgroup LSM6DSL_I2C_Address LSM6DSL I2C Address
 * @{
 */
#define LSM6DSL_I2C_ADDR                (0x6AU)  /* SA0 pin = VDD */
#define LSM6DSL_I2C_ADDR_ALT            (0x6BU)  /* SA0 pin = GND */
/**
 * @}
 */

/**
 * @defgroup LSM6DSL_Register_Map LSM6DSL Register Map
 * @{
 */
#define LSM6DSL_FUNC_CFG_ACCESS         (0x01U)  /* Enable embedded functions */
#define LSM6DSL_SENSOR_SYNC_TIME_FRAME  (0x04U)  /* Sensor sync config */
#define LSM6DSL_SENSOR_SYNC_RES_RATIO   (0x05U)  /* Sensor sync config */
#define LSM6DSL_FIFO_CTRL1              (0x06U)  /* FIFO control register */
#define LSM6DSL_FIFO_CTRL2              (0x07U)  /* FIFO control register */
#define LSM6DSL_FIFO_CTRL3              (0x08U)  /* FIFO control register */
#define LSM6DSL_FIFO_CTRL4              (0x09U)  /* FIFO control register */
#define LSM6DSL_FIFO_CTRL5              (0x0AU)  /* FIFO control register */
#define LSM6DSL_DRDY_PULSE_CFG          (0x0BU)  /* DataReady config */
#define LSM6DSL_INT1_CTRL               (0x0DU)  /* INT1 pin control */
#define LSM6DSL_INT2_CTRL               (0x0EU)  /* INT2 pin control */
#define LSM6DSL_WHO_AM_I                (0x0FU)  /* Device identification */
#define LSM6DSL_CTRL1_XL                (0x10U)  /* Accel control register */
#define LSM6DSL_CTRL2_G                 (0x11U)  /* Gyro control register */
#define LSM6DSL_CTRL3_C                 (0x12U)  /* Control register */
#define LSM6DSL_CTRL4_C                 (0x13U)  /* Control register */
#define LSM6DSL_CTRL5_C                 (0x14U)  /* Control register */
#define LSM6DSL_CTRL6_C                 (0x15U)  /* Control register */
#define LSM6DSL_CTRL7_G                 (0x16U)  /* Control register */
#define LSM6DSL_CTRL8_XL                (0x17U)  /* Control register */
#define LSM6DSL_CTRL9_XL                (0x18U)  /* Control register */
#define LSM6DSL_CTRL10_C                (0x19U)  /* Control register */
#define LSM6DSL_MASTER_CONFIG           (0x1AU)  /* Master config register */
#define LSM6DSL_WAKE_UP_SRC             (0x1BU)  /* Wake up interrupt source */
#define LSM6DSL_TAP_SRC                 (0x1CU)  /* Tap source register */
#define LSM6DSL_D6D_SRC                 (0x1DU)  /* 6D source register */
#define LSM6DSL_STATUS_REG              (0x1EU)  /* Status data register */
#define LSM6DSL_OUT_TEMP_L              (0x20U)  /* Temperature output data */
#define LSM6DSL_OUT_TEMP_H              (0x21U)  /* Temperature output data */
#define LSM6DSL_OUTX_L_G                (0x22U)  /* Gyroscope X output */
#define LSM6DSL_OUTX_H_G                (0x23U)  /* Gyroscope X output */
#define LSM6DSL_OUTY_L_G                (0x24U)  /* Gyroscope Y output */
#define LSM6DSL_OUTY_H_G                (0x25U)  /* Gyroscope Y output */
#define LSM6DSL_OUTZ_L_G                (0x26U)  /* Gyroscope Z output */
#define LSM6DSL_OUTZ_H_G                (0x27U)  /* Gyroscope Z output */
#define LSM6DSL_OUTX_L_XL               (0x28U)  /* Accelerometer X output */
#define LSM6DSL_OUTX_H_XL               (0x29U)  /* Accelerometer X output */
#define LSM6DSL_OUTY_L_XL               (0x2AU)  /* Accelerometer Y output */
#define LSM6DSL_OUTY_H_XL               (0x2BU)  /* Accelerometer Y output */
#define LSM6DSL_OUTZ_L_XL               (0x2CU)  /* Accelerometer Z output */
#define LSM6DSL_OUTZ_H_XL               (0x2DU)  /* Accelerometer Z output */
/**
 * @}
 */

/**
 * @defgroup LSM6DSL_Chip_ID LSM6DSL Chip ID
 * @{
 */
#define LSM6DSL_WHO_AM_I_VALUE          (0x6AU)  /* Device identification value */
/**
 * @}
 */

/**
 * @defgroup LSM6DSL_Timeout_Value LSM6DSL Timeout Value
 * @{
 */
#define LSM6DSL_TIMEOUT                 (100000UL)
/**
 * @}
 */

/**
 * @}
 */

/**
 * @defgroup LSM6DSL_Local_Macros LSM6DSL Local Macros
 * @{
 */
#define LSM6DSL_FLAG_DRDY_XL         (1UL << 0U)  /* Accelerometer data ready */
#define LSM6DSL_FLAG_DRDY_G          (1UL << 1U)  /* Gyroscope data ready */
#define LSM6DSL_FLAG_DRDY_TEMP       (1UL << 2U)  /* Temperature data ready */

/* Accel scale factor for different range */
#define LSM6DSL_ACCEL_SCALE_FACTOR_2G   (0.061f)  /* mg/LSB */
#define LSM6DSL_ACCEL_SCALE_FACTOR_4G   (0.122f)  /* mg/LSB */
#define LSM6DSL_ACCEL_SCALE_FACTOR_8G   (0.244f)  /* mg/LSB */
#define LSM6DSL_ACCEL_SCALE_FACTOR_16G  (0.488f)  /* mg/LSB */

/* Gyro scale factor for different range */
#define LSM6DSL_GYRO_SCALE_FACTOR_250   (8.75f)   /* mdps/LSB */
#define LSM6DSL_GYRO_SCALE_FACTOR_500   (17.5f)   /* mdps/LSB */
#define LSM6DSL_GYRO_SCALE_FACTOR_1000  (35.0f)   /* mdps/LSB */
#define LSM6DSL_GYRO_SCALE_FACTOR_2000  (70.0f)   /* mdps/LSB */

/*******************************************************************************
 * Global variable definitions ('extern')
 ******************************************************************************/

/*******************************************************************************
  Global function prototypes (definition in C source)
 ******************************************************************************/
/**
 * @addtogroup LSM6DSL_Global_Functions LSM6DSL Global Functions
 * @{
 */

int32_t LSM6DSL_Init(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, en_lsm6dsl_accel_scale_t enAccelScale, en_lsm6dsl_gyro_scale_t enGyroScale, en_lsm6dsl_odr_t enODR);
int32_t LSM6DSL_DeInit(const stc_lsm6dsl_ll_t *pstcLsm6dslLL);
int32_t LSM6DSL_GetDeviceID(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, uint8_t *pu8ID);
int32_t LSM6DSL_ReadAccel(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, stc_lsm6dsl_axis_t *pstcAccel);
int32_t LSM6DSL_ReadGyro(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, stc_lsm6dsl_axis_t *pstcGyro);
int32_t LSM6DSL_ReadTemp(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, float *pfTemp);
int32_t LSM6DSL_ReadSensor(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, stc_lsm6dsl_data_t *pstcData);
int32_t LSM6DSL_SetAccelScale(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, en_lsm6dsl_accel_scale_t enAccelScale);
int32_t LSM6DSL_SetGyroScale(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, en_lsm6dsl_gyro_scale_t enGyroScale);
int32_t LSM6DSL_SetODR(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, en_lsm6dsl_odr_t enODR);
int32_t LSM6DSL_Reset(const stc_lsm6dsl_ll_t *pstcLsm6dslLL);

/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* __LSM6DSL_H__ */

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/ 
 