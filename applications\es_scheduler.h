﻿/**
 * @file scheduler.h
 * @brief Coroutine Scheduler
 * 
 * Provides coroutine creation, scheduling, and management functions.
 * 
 * <AUTHOR> @date 
 * @version 1.0
 */

#ifndef __es_scheduler_H__
#define __es_scheduler_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "es_coro.h"
#include "es_list.h"

/**
 * @brief Coroutine function type definition
 */
typedef es_async_t (*es_coro_func_t)(es_coro_t *coro, void *ctx);

/**
 * @brief Coroutine task structure
 */
typedef struct es_coro_task {
    const char *name;
    es_coro_func_t func;     /**< Coroutine function */
    es_coro_t coro;         /**< Coroutine context */
    void *ctx;            /**< Coroutine context */
    struct es_list_head node; /**< List node - changed to doubly linked list node */
} es_coro_task_t;

/* Forward declaration of timer structure */
struct es_timer;

/**
 * @brief Timer callback function type - coroutine style
 * @param coro Coroutine context (with timeout)
 * @param timer Timer pointer
 * @param ctx User context
 * @return Coroutine return value
 */
typedef es_async_t (*es_timer_callback_t)(es_coro_t *coro, struct es_timer *timer, void *ctx);

/**
 * @brief Timer state enumeration
 */
typedef enum {
    ES_TIMER_INACTIVE = 0,    /**< Timer inactive */
    ES_TIMER_ACTIVE,          /**< Timer active */
    ES_TIMER_DELETED          /**< Timer deleted */
} es_timer_state_t;

/**
 * @brief Timer structure
 */
typedef struct es_timer {
    es_coro_timeout_t coro;      /**< Timer's coroutine context (with timeout) */
    uint32_t next_trigger_time;  /**< Next trigger time (milliseconds) */
    es_timer_callback_t callback; /**< Callback function (coroutine style) */
    void *ctx;                   /**< Callback function context */
    struct es_list_head node;    /**< List node - changed to doubly linked list node */
    struct es_coro_scheduler *scheduler; /**< Owning scheduler */
    /* Use bit fields to reduce memory usage */
    uint32_t one_shot : 1;        /**< One-shot trigger flag */
    uint32_t state : 2;           /**< Timer state */
    uint32_t need_free : 1;       /**< Whether memory needs to be freed flag */
    uint32_t period : 28;         /**< Timer period (milliseconds) */
} es_timer_t;

/**
 * @brief Event type definition (using 16 bits to save memory)
 */
typedef uint16_t es_event_type_t;

/**
 * @brief Event data structure
 */
typedef struct {
    es_event_type_t type;        /**< Event type */
    uint16_t use_dynamic_alloc : 1; /**< Whether to use dynamic memory allocation */
    uint16_t data_len : 15;           /**< Data length (valid only when using pointer data) */
    union {
        uint32_t value;          /**< Directly stored 32-bit data */
        void *data;              /**< Event data pointer */
    } u;
} es_event_t;

/**
 * @brief Event handler callback function type (normal function style)
 * @param event Event pointer
 * @param ctx Handler context
 */
typedef void (*es_event_handler_t)(const es_event_t *event, void *ctx);

/**
 * @brief Event subscriber structure
 */
typedef struct es_event_subscriber {
    es_event_type_t event_type;        /**< Subscribed event type, 0 means subscribe to all events */
    es_event_handler_t handler;        /**< Event handler */
    void *handler_ctx;                 /**< Handler context */
    struct es_list_head node;          /**< List node */
} es_event_subscriber_t;

/**
 * @brief Coroutine scheduler
 */
typedef struct es_coro_scheduler {
    struct es_list_head tasks;  /**< Task list - changed to doubly linked list */
    struct es_list_head timers; /**< Timer list - changed to doubly linked list */
    struct es_list_head delayed_delete_timers; /**< Delayed delete timer list - changed to doubly linked list */
    struct es_list_head delayed_add_timers;    /**< Delayed add timer list - changed to doubly linked list */
    struct es_list_head event_subscribers;     /**< Event subscriber list */
    uint8_t inited : 1;
    uint8_t in_process_timers : 1;  /**< Whether processing timers */
} es_scheduler_t;

/**
 * @brief Initialize coroutine scheduler
 * @param scheduler Scheduler instance
 */
void es_scheduler_init(es_scheduler_t *scheduler);

/**
 * @brief Get default coroutine scheduler
 * @return Pointer to default scheduler instance
 */
es_scheduler_t *es_scheduler_get_default(void);

/**
 * @brief Add coroutine task
 * @param scheduler Scheduler instance
 * @param task Task instance
 * @return Returns 0 if successful, -1 if failed
 */
int es_scheduler_task_add(es_scheduler_t *scheduler, es_coro_task_t *task);

/**
 * @brief Remove coroutine task
 * @param scheduler Scheduler instance
 * @param task Task instance
 * @return Returns 0 if successful, -1 if failed
 */
int es_scheduler_task_remove(const es_scheduler_t *scheduler, es_coro_task_t *task);

/**
 * @brief Initialize timer
 * @param timer Timer instance
 * @param period Timer period (milliseconds)
 * @param callback Callback function (coroutine style)
 * @param ctx Callback context
 * @param one_shot Whether it's a one-shot trigger
 */
void es_scheduler_timer_init(es_timer_t *timer, uint32_t period, es_timer_callback_t callback, void *ctx, bool one_shot);

/**
 * @brief Add timer to scheduler
 * @param scheduler Scheduler instance
 * @param timer Timer instance
 * @return Returns 0 on success, -1 on failure
 * @note Timer instance must be statically allocated
 * @note If adding new timer during timer processing, the new timer will be delayed until current processing cycle ends before being added to timer list
 */
int es_scheduler_timer_add(es_scheduler_t *scheduler, es_timer_t *timer);

/**
 * @brief Remove timer from scheduler
 * @param scheduler Scheduler instance
 * @param timer Timer instance
 * @return Returns 0 on success, -1 on failure
 */
int es_scheduler_timer_remove(es_scheduler_t *scheduler, es_timer_t *timer);

/**
 * @brief Start timer
 * @param scheduler Scheduler instance
 * @param timer Timer instance
 * @return Returns 0 on success, -1 on failure
 * @note If timer is not assigned to any scheduler, it will be automatically added to the specified scheduler
 * @note If starting new timer during timer processing, the new timer will be delayed until current processing cycle ends before being added to timer list
 */
int es_scheduler_timer_start(es_scheduler_t *scheduler, es_timer_t *timer);

/**
 * @brief Start timer and choose whether to execute immediately once
 * @param scheduler Scheduler instance
 * @param timer Timer instance
 * @param immediate_run Whether to execute immediately once
 * @return Returns 0 on success, -1 on failure
 * @note If timer is not assigned to any scheduler, it will be automatically added to the specified scheduler
 * @note If starting new timer during timer processing, the new timer will be delayed until current processing cycle ends before being added to timer list
 */
int es_scheduler_timer_start_immediate(es_scheduler_t *scheduler, es_timer_t *timer);

/**
 * @brief Stop timer
 * @param scheduler Scheduler instance
 * @param timer Timer instance
 * @return Returns 0 on success, -1 on failure
 */
int es_scheduler_timer_stop(const es_scheduler_t *scheduler, es_timer_t *timer);

/**
 * @brief Reset timer
 * @param scheduler Scheduler instance
 * @param timer Timer instance
 * @return Returns 0 on success, -1 on failure
 */
int es_scheduler_timer_reset(const es_scheduler_t *scheduler, es_timer_t *timer);

/**
 * @brief Modify timer period
 * @param scheduler Scheduler instance
 * @param timer Timer instance
 * @param period New timer period (milliseconds)
 * @return Returns 0 on success, -1 on failure
 */
int es_scheduler_timer_change_period(const es_scheduler_t *scheduler, es_timer_t *timer, uint32_t period);

/**
 * @brief Run scheduler once
 * @param scheduler Scheduler instance
 * @return Number of tasks executed
 */
int es_scheduler_run_once(es_scheduler_t *scheduler);

/**
 * @brief Run scheduler main loop
 * @param scheduler Scheduler instance
 * @note This function will not return
 */
void es_scheduler_run(es_scheduler_t *scheduler);

/**
 * @brief Allocate and initialize a timer
 * @param period Timer period (milliseconds)
 * @param callback Callback function (coroutine style)
 * @param ctx Callback context
 * @param one_shot Whether it's a one-shot trigger
 * @return Returns timer pointer on success, NULL on failure
 * @note Timers allocated with this function will automatically free memory when removed
 *
 * Usage example:
 * @code
 * // Define timer callback function
 * es_async_t timer_callback(es_coro_t *coro, es_timer_t *timer, void *ctx)
 * {
 *     // Timer processing logic
 *     return ES_ASYNC_DONE;
 * }
 *
 * // Allocate and initialize timer
 * es_timer_t *timer = es_scheduler_timer_alloc(1000, timer_callback, NULL, false);
 * if (timer != NULL) {
 *     // Start timer directly, no need to call es_scheduler_timer_add first
 *     es_scheduler_timer_start(es_scheduler_get_default(), timer);
 * }
 *
 * // When timer is no longer needed
 * es_scheduler_timer_remove(es_scheduler_get_default(), timer);
 * // No need to manually free memory, it will be freed automatically
 * @endcode
 */
es_timer_t *es_scheduler_timer_alloc(uint32_t period, es_timer_callback_t callback, void *ctx, bool one_shot);

/**
 * @brief Initialize event subscriber
 * @param subscriber Subscriber instance
 * @param event_type Subscribed event type, 0 means subscribe to all events
 * @param handler Event handler
 * @param handler_ctx Handler context
 */
void es_scheduler_event_subscriber_init(es_event_subscriber_t *subscriber, 
                                        es_event_type_t event_type,
                                        es_event_handler_t handler, 
                                        void *handler_ctx);

/**
 * @brief Subscribe to event
 * @param scheduler Scheduler instance
 * @param subscriber Subscriber instance
 * @return Returns 0 on success, -1 on failure
 */
int es_scheduler_event_subscribe(es_scheduler_t *scheduler, es_event_subscriber_t *subscriber);

/**
 * @brief Unsubscribe from event
 * @param scheduler Scheduler instance
 * @param subscriber Subscriber instance
 * @return Returns 0 on success, -1 on failure
 */
int es_scheduler_event_unsubscribe(const es_scheduler_t *scheduler, es_event_subscriber_t *subscriber);

/**
 * @brief Synchronously publish event (trigger processing immediately)
 * @param scheduler Scheduler instance
 * @param event_type Event type
 * @param data Event data pointer
 * @param data_len Event data length
 * @return Returns 0 on success, -1 on failure
 * @note This function will process the event immediately
 */
int es_scheduler_event_publish(es_scheduler_t *scheduler, es_event_type_t event_type, void *data, uint16_t data_len);

/**
 * @brief Synchronously publish event (using 32-bit value data, trigger processing immediately)
 * @param scheduler Scheduler instance
 * @param event_type Event type
 * @param value 32-bit event data value
 * @return Returns 0 on success, -1 on failure
 * @note This function will process the event immediately
 */
int es_scheduler_event_publish_value(es_scheduler_t *scheduler, es_event_type_t event_type, uint32_t value);

/**
 * @brief Macro for publishing event using default scheduler (using pointer data)
 * @param event_type Event type
 * @param data Event data pointer
 * @param data_len Event data length
 */
#define ES_EVENT_PUBLISH(event_type, data, data_len) \
    es_scheduler_event_publish(es_scheduler_get_default(), (event_type), (data), (data_len))

/**
 * @brief Macro for publishing event using default scheduler (using 32-bit value data)
 * @param event_type Event type
 * @param value 32-bit event data value
 */
#define ES_EVENT_PUBLISH_VALUE(event_type, value) \
    es_scheduler_event_publish_value(es_scheduler_get_default(), (event_type), (value))

#ifdef __cplusplus
}
#endif

#endif /* __es_scheduler_H__ */ 
