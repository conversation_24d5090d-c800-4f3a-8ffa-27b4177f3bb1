/**
 * @file es_veh.h
 * @brief Vehicle CAN data processing module interface
 * @date 2024/10/1
 */

#ifndef __ES_VEH_H__
#define __ES_VEH_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "es_drv_can.h"

/**
 * @brief Initialize vehicle CAN module
 * 
 * Initializes the vehicle CAN processing module, sets up CAN interface,
 * and starts the CAN receive task for processing incoming messages.
 * 
 * @return 0 on success, non-zero on failure
 */
int es_veh_init(void);

/**
 * @brief Deinitialize vehicle CAN module
 * 
 * Cleans up resources and stops the CAN processing tasks.
 */
void es_veh_deinit(void);

/**
 * @brief Send CAN message
 * 
 * Sends a CAN message with specified parameters.
 * 
 * @param id CAN ID (0-0x7FF for standard, 0-0x1FFFFFFF for extended)
 * @param ide IDE flag (0=standard frame, 1=extended frame)
 * @param rtr RTR flag (0=data frame, 1=remote frame)
 * @param dlc Data length code (0-8)
 * @param data Pointer to data bytes (can be NULL for remote frames)
 * @return 0 on success, -1 on failure
 */
int es_veh_can_send(uint32_t id, uint8_t ide, uint8_t rtr, uint8_t dlc, const uint8_t *data);

/**
 * @brief Get CAN status information
 * 
 * Returns the current CAN status information.
 * 
 * @return CAN status flags, 0 if not initialized
 */
uint32_t es_veh_get_can_status(void);

/**
 * @brief Get CAN error information
 * 
 * Retrieves detailed error information including error counters and error types.
 * 
 * @param error_info Pointer to store error information structure
 * @return 0 on success, -1 on failure
 */
int es_veh_get_can_error_info(es_can_error_info_t *error_info);

#ifdef __cplusplus
}
#endif

#endif /* __ES_VEH_H__ */ 
