/**
 * @file test_ringbuffer_interrupt_safety.c
 * @brief 环形缓冲区中断安全性测试
 * @date 2024/10/1
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include <stdint.h>
#include <stdbool.h>

#include "../applications/es_ringobj.h"
#include "../applications/es_ringbuffer.h"

/* 测试数据结构 */
typedef struct {
    uint32_t id;
    uint32_t timestamp;
    uint8_t data[8];
} test_message_t;

/* 全局测试变量 */
static es_ringobj_t g_ring_obj;
static test_message_t g_obj_buffer[16];  /* 必须是2的幂 */

static es_ringbuffer_t g_ring_buffer;
static uint8_t g_byte_buffer[256];  /* 必须是2的幂 */

/* 模拟中断写入计数器 */
static volatile uint32_t g_interrupt_write_count = 0;
static volatile uint32_t g_app_read_count = 0;

/**
 * @brief 模拟中断中的写入操作
 */
void simulate_interrupt_write_obj(void)
{
    test_message_t msg;
    msg.id = g_interrupt_write_count;
    msg.timestamp = g_interrupt_write_count * 1000;
    
    for (int i = 0; i < 8; i++) {
        msg.data[i] = (uint8_t)(g_interrupt_write_count + i);
    }
    
    if (es_ringobj_put(&g_ring_obj, &msg)) {
        g_interrupt_write_count++;
    }
}

/**
 * @brief 模拟中断中的字节写入操作
 */
void simulate_interrupt_write_bytes(void)
{
    uint8_t data[4];
    for (int i = 0; i < 4; i++) {
        data[i] = (uint8_t)(g_interrupt_write_count + i);
    }
    
    uint32_t written = es_ringbuffer_write(&g_ring_buffer, data, 4);
    if (written == 4) {
        g_interrupt_write_count++;
    }
}

/**
 * @brief 模拟应用层读取操作
 */
bool simulate_app_read_obj(void)
{
    test_message_t msg;
    if (es_ringobj_get(&g_ring_obj, &msg)) {
        /* 验证数据完整性 */
        if (msg.id == g_app_read_count && 
            msg.timestamp == g_app_read_count * 1000) {
            
            bool data_valid = true;
            for (int i = 0; i < 8; i++) {
                if (msg.data[i] != (uint8_t)(g_app_read_count + i)) {
                    data_valid = false;
                    break;
                }
            }
            
            if (data_valid) {
                g_app_read_count++;
                return true;
            } else {
                printf("数据完整性检查失败！读取ID: %u\n", msg.id);
                return false;
            }
        } else {
            printf("消息顺序错误！期望ID: %u, 实际ID: %u\n", 
                   g_app_read_count, msg.id);
            return false;
        }
    }
    return false;
}

/**
 * @brief 模拟应用层字节读取操作
 */
bool simulate_app_read_bytes(void)
{
    uint8_t data[4];
    uint32_t read_count = es_ringbuffer_read(&g_ring_buffer, data, 4);
    
    if (read_count == 4) {
        /* 验证数据完整性 */
        bool data_valid = true;
        for (int i = 0; i < 4; i++) {
            if (data[i] != (uint8_t)(g_app_read_count + i)) {
                data_valid = false;
                break;
            }
        }
        
        if (data_valid) {
            g_app_read_count++;
            return true;
        } else {
            printf("字节数据完整性检查失败！\n");
            return false;
        }
    }
    return false;
}

/**
 * @brief 测试环形对象缓冲区的中断安全性
 */
void test_ringobj_interrupt_safety(void)
{
    printf("测试环形对象缓冲区中断安全性...\n");
    
    /* 初始化环形缓冲区 */
    int ret = es_ringobj_init(&g_ring_obj, g_obj_buffer, 
                              sizeof(test_message_t), 16);
    assert(ret == 0);
    
    /* 重置计数器 */
    g_interrupt_write_count = 0;
    g_app_read_count = 0;
    
    /* 模拟混合读写操作 */
    for (int cycle = 0; cycle < 100; cycle++) {
        /* 模拟中断写入 */
        for (int i = 0; i < 3; i++) {
            simulate_interrupt_write_obj();
        }
        
        /* 模拟应用层读取 */
        for (int i = 0; i < 2; i++) {
            simulate_app_read_obj();
        }
    }
    
    /* 读取剩余数据 */
    while (simulate_app_read_obj()) {
        /* 继续读取 */
    }
    
    printf("环形对象缓冲区测试完成：写入 %u 个消息，读取 %u 个消息\n", 
           g_interrupt_write_count, g_app_read_count);
    
    /* 验证数据一致性 */
    assert(g_interrupt_write_count == g_app_read_count);
    printf("✓ 环形对象缓冲区中断安全性测试通过\n\n");
}

/**
 * @brief 测试环形字节缓冲区的中断安全性
 */
void test_ringbuffer_interrupt_safety(void)
{
    printf("测试环形字节缓冲区中断安全性...\n");
    
    /* 初始化环形缓冲区 */
    int ret = es_ringbuffer_init(&g_ring_buffer, g_byte_buffer, 256);
    assert(ret == 0);
    
    /* 重置计数器 */
    g_interrupt_write_count = 0;
    g_app_read_count = 0;
    
    /* 模拟混合读写操作 */
    for (int cycle = 0; cycle < 100; cycle++) {
        /* 模拟中断写入 */
        for (int i = 0; i < 3; i++) {
            simulate_interrupt_write_bytes();
        }
        
        /* 模拟应用层读取 */
        for (int i = 0; i < 2; i++) {
            simulate_app_read_bytes();
        }
    }
    
    /* 读取剩余数据 */
    while (simulate_app_read_bytes()) {
        /* 继续读取 */
    }
    
    printf("环形字节缓冲区测试完成：写入 %u 个数据块，读取 %u 个数据块\n", 
           g_interrupt_write_count, g_app_read_count);
    
    /* 验证数据一致性 */
    assert(g_interrupt_write_count == g_app_read_count);
    printf("✓ 环形字节缓冲区中断安全性测试通过\n\n");
}

/**
 * @brief 主测试函数
 */
int main(void)
{
    printf("=== 环形缓冲区中断安全性测试 ===\n\n");
    
    test_ringobj_interrupt_safety();
    test_ringbuffer_interrupt_safety();
    
    printf("=== 所有测试通过！===\n");
    return 0;
}
