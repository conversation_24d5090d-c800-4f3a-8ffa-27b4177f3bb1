/**
 * @file test_system_status.c
 * @brief 系统状态监控模块测试
 * <AUTHOR>
 * @date 2025/1/15
 * @copyright Copyright (c) 2025
 */

#include "es_system_status.h"
#include "es_mem.h"
#include "es_log.h"
#include <stdio.h>
#include <assert.h>

#define TAG "TEST_SYS_STATUS"

// 测试统计
static int test_passed = 0;
static int test_failed = 0;

// 测试宏
#define TEST_ASSERT(condition, message) \
    do { \
        if (condition) { \
            test_passed++; \
            printf("✓ PASS: %s\n", message); \
        } else { \
            test_failed++; \
            printf("✗ FAIL: %s\n", message); \
        } \
    } while(0)

#define TEST_ASSERT_NOT_NULL(ptr, message) TEST_ASSERT((ptr) != NULL, message)
#define TEST_ASSERT_EQUAL(expected, actual, message) \
    TEST_ASSERT((expected) == (actual), message)

/**
 * @brief 测试模块初始化和反初始化
 */
static void test_init_deinit(void)
{
    printf("\n--- Testing Init/Deinit ---\n");
    
    // 测试初始化
    int result = es_sys_status_init();
    TEST_ASSERT_EQUAL(0, result, "System status init should succeed");
    
    // 测试重复初始化
    result = es_sys_status_init();
    TEST_ASSERT_EQUAL(0, result, "Repeated init should succeed");
    
    // 测试反初始化
    es_sys_status_deinit();
    
    // 重新初始化以便后续测试
    es_sys_status_init();
}

/**
 * @brief 测试内存信息获取
 */
static void test_memory_info(void)
{
    printf("\n--- Testing Memory Info ---\n");
    
    es_sys_memory_info_t info;
    int result = es_sys_get_memory_info(&info);
    TEST_ASSERT_EQUAL(0, result, "Get memory info should succeed");
    
    // 验证基本的内存信息
    TEST_ASSERT(info.total_size > 0, "Total memory size should be > 0");
    TEST_ASSERT(info.free_size <= info.total_size, "Free size should <= total size");
    TEST_ASSERT(info.used_size <= info.total_size, "Used size should <= total size");
    TEST_ASSERT((info.used_size + info.free_size) == info.total_size, "Used + Free should equal Total");
    TEST_ASSERT(info.usage_percent >= 0.0f && info.usage_percent <= 100.0f, "Usage percent should be 0-100");
    
    // 测试NULL指针
    result = es_sys_get_memory_info(NULL);
    TEST_ASSERT(result != 0, "Get memory info with NULL should fail");
}

/**
 * @brief 测试时间信息获取
 */
static void test_time_info(void)
{
    printf("\n--- Testing Time Info ---\n");
    
    es_sys_time_info_t info;
    int result = es_sys_get_time_info(&info);
    TEST_ASSERT_EQUAL(0, result, "Get time info should succeed");
    
    // 验证时间信息的合理性
    TEST_ASSERT(info.uptime_ms > 0, "Uptime in ms should be > 0");
    TEST_ASSERT(info.uptime_us >= info.uptime_ms * 1000ULL, "Uptime in us should be >= ms * 1000");
    TEST_ASSERT(info.uptime_seconds == info.uptime_ms / 1000, "Seconds should match ms/1000");
    TEST_ASSERT(info.uptime_minutes == info.uptime_seconds / 60, "Minutes should match seconds/60");
    TEST_ASSERT(info.uptime_hours == info.uptime_minutes / 60, "Hours should match minutes/60");
    TEST_ASSERT(info.uptime_days == info.uptime_hours / 24, "Days should match hours/24");
    
    // 测试NULL指针
    result = es_sys_get_time_info(NULL);
    TEST_ASSERT(result != 0, "Get time info with NULL should fail");
}

/**
 * @brief 测试健康状态获取
 */
static void test_health_status(void)
{
    printf("\n--- Testing Health Status ---\n");
    
    es_sys_health_status_t status = es_sys_get_health_status();
    TEST_ASSERT(status >= ES_SYS_HEALTH_EXCELLENT && status <= ES_SYS_HEALTH_UNKNOWN, 
                "Health status should be in valid range");
    
    // 测试状态字符串
    const char *status_str = es_sys_get_health_status_string(status);
    TEST_ASSERT_NOT_NULL(status_str, "Health status string should not be NULL");
    
    // 测试所有状态的字符串
    for (int i = ES_SYS_HEALTH_EXCELLENT; i <= ES_SYS_HEALTH_UNKNOWN; i++) {
        const char *str = es_sys_get_health_status_string((es_sys_health_status_t)i);
        TEST_ASSERT_NOT_NULL(str, "All health status strings should be valid");
    }
}

/**
 * @brief 测试完整系统状态获取
 */
static void test_complete_status(void)
{
    printf("\n--- Testing Complete Status ---\n");
    
    es_sys_status_info_t info;
    int result = es_sys_get_status_info(&info);
    TEST_ASSERT_EQUAL(0, result, "Get complete status should succeed");
    
    // 验证各个组件的信息
    TEST_ASSERT(info.memory.total_size > 0, "Memory total size should be > 0");
    TEST_ASSERT(info.time.uptime_ms > 0, "Uptime should be > 0");
    TEST_ASSERT(info.health >= ES_SYS_HEALTH_EXCELLENT && info.health <= ES_SYS_HEALTH_UNKNOWN,
                "Health status should be valid");
    TEST_ASSERT(info.memory_v1_available == true, "Memory V1 should be available");
    
    // 测试NULL指针
    result = es_sys_get_status_info(NULL);
    TEST_ASSERT(result != 0, "Get complete status with NULL should fail");
}

/**
 * @brief 测试内存分配对统计的影响
 */
static void test_memory_allocation_impact(void)
{
    printf("\n--- Testing Memory Allocation Impact ---\n");
    
    // 获取初始内存状态
    es_sys_memory_info_t initial_info;
    es_sys_get_memory_info(&initial_info);
    
    // 分配一些内存
    void *ptr1 = es_mem_alloc(32);
    void *ptr2 = es_mem_alloc(64);
    void *ptr3 = es_mem_alloc(128);
    
    TEST_ASSERT_NOT_NULL(ptr1, "32-byte allocation should succeed");
    TEST_ASSERT_NOT_NULL(ptr2, "64-byte allocation should succeed");
    TEST_ASSERT_NOT_NULL(ptr3, "128-byte allocation should succeed");
    
    // 获取分配后的内存状态
    es_sys_memory_info_t after_alloc_info;
    es_sys_get_memory_info(&after_alloc_info);
    
    // 验证内存使用量增加
    TEST_ASSERT(after_alloc_info.used_size > initial_info.used_size, 
                "Used memory should increase after allocation");
    TEST_ASSERT(after_alloc_info.free_size < initial_info.free_size,
                "Free memory should decrease after allocation");
    
    // 释放内存
    es_mem_free(ptr1);
    es_mem_free(ptr2);
    es_mem_free(ptr3);
    
    // 获取释放后的内存状态
    es_sys_memory_info_t after_free_info;
    es_sys_get_memory_info(&after_free_info);
    
    // 验证内存使用量恢复
    TEST_ASSERT_EQUAL(initial_info.used_size, after_free_info.used_size,
                      "Used memory should return to initial value after free");
    TEST_ASSERT_EQUAL(initial_info.free_size, after_free_info.free_size,
                      "Free memory should return to initial value after free");
}

/**
 * @brief 测试打印函数（不验证输出，只确保不崩溃）
 */
static void test_dump_functions(void)
{
    printf("\n--- Testing Dump Functions ---\n");
    
    // 这些函数主要是打印输出，我们只测试它们不会崩溃
    es_sys_dump_memory_status();
    es_sys_dump_time_status();
    es_sys_dump_health_status();
    es_sys_dump_status();
    
    TEST_ASSERT(true, "All dump functions executed without crash");
}

/**
 * @brief 主测试函数
 */
int main(void)
{
    printf("=== System Status Monitor Test Suite ===\n");
    
    // 初始化内存管理器
    if (es_mem_init() != 0) {
        printf("Failed to initialize memory manager\n");
        return -1;
    }
    
    // 运行测试
    test_init_deinit();
    test_memory_info();
    test_time_info();
    test_health_status();
    test_complete_status();
    test_memory_allocation_impact();
    test_dump_functions();
    
    // 清理
    es_sys_status_deinit();
    
    // 输出测试结果
    printf("\n=== Test Results ===\n");
    printf("Passed: %d\n", test_passed);
    printf("Failed: %d\n", test_failed);
    printf("Total:  %d\n", test_passed + test_failed);
    
    if (test_failed == 0) {
        printf("All tests PASSED! ✓\n");
        return 0;
    } else {
        printf("Some tests FAILED! ✗\n");
        return 1;
    }
}
