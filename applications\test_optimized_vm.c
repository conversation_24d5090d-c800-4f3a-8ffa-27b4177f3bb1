#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

// 简化的VM定义
#define VM_STACK_SIZE   16
#define VM_REG_COUNT    8

typedef enum {
    VM_STATE_READY = 0,
    VM_STATE_RUNNING,
    VM_STATE_DONE,
    VM_STATE_ERROR
} vm_state_t;

typedef struct {
    uint32_t stack[VM_STACK_SIZE];
    uint8_t sp;
    uint16_t pc;
    vm_state_t state;
    const uint8_t* bytecode;
    uint16_t bytecode_len;
    uint32_t registers[VM_REG_COUNT];
} vm_context_t;

// VM操作码
#define VM_OP_PUSH8     0x01
#define VM_OP_ADD       0x10
#define VM_OP_SUB       0x11
#define VM_OP_MUL       0x12
#define VM_OP_CALL      0x40
#define VM_OP_RET       0x41
#define VM_OP_STORE_REG 0x42
#define VM_OP_LOAD_REG  0x43
#define VM_OP_HALT      0xFF

// VM系统调用ID
#define VM_SYSCALL_GET_EVENT    0x01
#define VM_SYSCALL_GET_IO       0x02
#define VM_SYSCALL_GET_SIGNAL   0x03
#define VM_SYSCALL_GET_TIME     0x04
#define VM_SYSCALL_GET_MSG_TIME 0x05

// 简化的日志宏
#define ES_PRINTF_E(tag, fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)
#define ES_PRINTF_I(tag, fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define TAG "VM_TEST"

// 模拟系统调用
static uint32_t mock_vm_syscall(uint8_t call_id, uint32_t arg1, uint32_t arg2, uint32_t arg3) {
    switch (call_id) {
        case VM_SYSCALL_GET_TIME:
            return 12345;  // 模拟时间戳
        case VM_SYSCALL_GET_EVENT:
            return arg1 * 2;  // 模拟事件值
        case VM_SYSCALL_GET_IO:
            return arg1 % 2;  // 模拟IO电平
        case VM_SYSCALL_GET_SIGNAL:
            return arg1 + arg2;  // 模拟信号值
        case VM_SYSCALL_GET_MSG_TIME:
            return 54321;  // 模拟消息时间
        default:
            return 0;
    }
}

// VM函数实现（简化版）
static inline int vm_push(vm_context_t* vm, uint32_t value) {
    if (vm->sp >= VM_STACK_SIZE) {
        ES_PRINTF_E(TAG, "VM stack overflow");
        return -1;
    }
    vm->stack[vm->sp++] = value;
    return 0;
}

static inline uint32_t vm_pop(vm_context_t* vm) {
    if (vm->sp == 0) {
        ES_PRINTF_E(TAG, "VM stack underflow");
        return 0;
    }
    return vm->stack[--vm->sp];
}

static uint8_t vm_read_u8(vm_context_t* vm) {
    if (vm->pc >= vm->bytecode_len) {
        ES_PRINTF_E(TAG, "VM PC out of bounds");
        return 0;
    }
    return vm->bytecode[vm->pc++];
}

int vm_init(vm_context_t* vm, const uint8_t* bytecode, uint16_t len) {
    if (!vm || !bytecode || len == 0) {
        return -1;
    }

    memset(vm, 0, sizeof(vm_context_t));
    vm->bytecode = bytecode;
    vm->bytecode_len = len;
    vm->state = VM_STATE_READY;
    vm->sp = 0;
    vm->pc = 0;
    
    for (int i = 0; i < VM_REG_COUNT; i++) {
        vm->registers[i] = 0;
    }

    return 0;
}

uint32_t vm_get_result(vm_context_t* vm) {
    if (!vm || vm->sp == 0) {
        return 0;
    }
    return vm->stack[vm->sp - 1];
}

int vm_step(vm_context_t* vm) {
    if (!vm) {
        return -1;
    }

    if (vm->state != VM_STATE_RUNNING && vm->state != VM_STATE_READY) {
        return 0;
    }

    if (vm->pc >= vm->bytecode_len) {
        vm->state = VM_STATE_DONE;
        return 0;
    }

    vm->state = VM_STATE_RUNNING;
    uint8_t opcode = vm_read_u8(vm);

    switch (opcode) {
        case VM_OP_PUSH8: {
            uint8_t value = vm_read_u8(vm);
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_ADD: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM ADD: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a + b);
            break;
        }

        case VM_OP_SUB: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM SUB: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a - b);
            break;
        }

        case VM_OP_MUL: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM MUL: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a * b);
            break;
        }

        case VM_OP_CALL: {
            uint8_t call_id = vm_read_u8(vm);

            // 根据系统调用ID确定参数个数
            uint32_t arg1 = 0, arg2 = 0, arg3 = 0;
            switch (call_id) {
                case VM_SYSCALL_GET_TIME:
                    // 无参数
                    break;
                case VM_SYSCALL_GET_EVENT:
                case VM_SYSCALL_GET_IO:
                case VM_SYSCALL_GET_MSG_TIME:
                    // 1个参数
                    arg1 = (vm->sp > 0) ? vm_pop(vm) : 0;
                    break;
                case VM_SYSCALL_GET_SIGNAL:
                    // 2个参数
                    arg2 = (vm->sp > 0) ? vm_pop(vm) : 0;
                    arg1 = (vm->sp > 0) ? vm_pop(vm) : 0;
                    break;
                default:
                    // 通用处理：最多3个参数
                    arg3 = (vm->sp > 0) ? vm_pop(vm) : 0;
                    arg2 = (vm->sp > 0) ? vm_pop(vm) : 0;
                    arg1 = (vm->sp > 0) ? vm_pop(vm) : 0;
                    break;
            }

            uint32_t result = mock_vm_syscall(call_id, arg1, arg2, arg3);
            if (vm_push(vm, result) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            ES_PRINTF_I(TAG, "VM CALL: syscall %u(%u, %u, %u) = %u", call_id, arg1, arg2, arg3, result);
            break;
        }

        case VM_OP_STORE_REG: {
            uint8_t reg_id = vm_read_u8(vm);
            if (reg_id >= VM_REG_COUNT) {
                ES_PRINTF_E(TAG, "VM STORE_REG: invalid register ID %u", reg_id);
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            if (vm->sp < 1) {
                ES_PRINTF_E(TAG, "VM STORE_REG: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t value = vm_pop(vm);
            vm->registers[reg_id] = value;
            ES_PRINTF_I(TAG, "VM STORE_REG: R%u = %u", reg_id, value);
            break;
        }

        case VM_OP_LOAD_REG: {
            uint8_t reg_id = vm_read_u8(vm);
            if (reg_id >= VM_REG_COUNT) {
                ES_PRINTF_E(TAG, "VM LOAD_REG: invalid register ID %u", reg_id);
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t value = vm->registers[reg_id];
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            ES_PRINTF_I(TAG, "VM LOAD_REG: R%u -> %u", reg_id, value);
            break;
        }

        case VM_OP_RET:
            vm->state = VM_STATE_DONE;
            break;

        case VM_OP_HALT:
            vm->state = VM_STATE_DONE;
            break;

        default:
            ES_PRINTF_E(TAG, "VM: unknown opcode 0x%02X at PC=%u", opcode, vm->pc - 1);
            vm->state = VM_STATE_ERROR;
            return -1;
    }

    return 1;
}

int vm_execute(vm_context_t* vm) {
    if (!vm) {
        return -1;
    }

    if (vm->state != VM_STATE_READY) {
        return 0;
    }

    vm->state = VM_STATE_RUNNING;
    
    int max_steps = 10000;
    while (max_steps-- > 0 && (vm->state == VM_STATE_RUNNING || vm->state == VM_STATE_READY)) {
        int result = vm_step(vm);
        if (result < 0) {
            return result;
        }

        if (vm->state == VM_STATE_DONE || vm->state == VM_STATE_ERROR) {
            break;
        }
    }

    if (max_steps <= 0) {
        ES_PRINTF_E(TAG, "VM execution timeout");
        vm->state = VM_STATE_ERROR;
        return -1;
    }

    return 0;
}

// 测试优化后的字节码
int main() {
    printf("🧪 测试优化编译器生成的字节码\n");
    printf("================================\n");
    
    // 常量折叠优化后的字节码 (a=8, b=15, c=17, d=0, return a+b+c+d=40)
    uint8_t optimized_bytecode[] = {
        0x01, 0x08, 0x42, 0x00,  // PUSH8 8, STORE_REG 0 (a=8)
        0x01, 0x0F, 0x42, 0x01,  // PUSH8 15, STORE_REG 1 (b=15)
        0x01, 0x11, 0x42, 0x02,  // PUSH8 17, STORE_REG 2 (c=17)
        0x01, 0x00, 0x42, 0x03,  // PUSH8 0, STORE_REG 3 (d=0)
        0x43, 0x00, 0x43, 0x01, 0x10,  // LOAD_REG 0, LOAD_REG 1, ADD
        0x43, 0x02, 0x10,        // LOAD_REG 2, ADD
        0x43, 0x03, 0x10,        // LOAD_REG 3, ADD
        0x41, 0xFF               // RET, HALT
    };
    
    // VM系统调用测试字节码
    uint8_t syscall_bytecode[] = {
        0x40, 0x04, 0x42, 0x00,  // CALL get_time(), STORE_REG 0
        0x01, 0x2A, 0x40, 0x01, 0x42, 0x01,  // PUSH8 42, CALL get_event(42), STORE_REG 1
        0x43, 0x00, 0x43, 0x01, 0x10,  // LOAD_REG 0, LOAD_REG 1, ADD
        0x41, 0xFF               // RET, HALT
    };
    
    vm_context_t vm;
    
    // 测试1: 常量折叠优化
    printf("📋 测试1: 常量折叠优化\n");
    if (vm_init(&vm, optimized_bytecode, sizeof(optimized_bytecode)) < 0) {
        printf("❌ VM初始化失败\n");
        return 1;
    }
    
    if (vm_execute(&vm) < 0) {
        printf("❌ VM执行失败\n");
        return 1;
    }
    
    uint32_t result1 = vm_get_result(&vm);
    printf("🔍 结果: %u (期望: 40)\n", result1);
    
    if (result1 == 40) {
        printf("✅ 常量折叠优化测试通过!\n");
    } else {
        printf("❌ 常量折叠优化测试失败!\n");
    }
    
    // 测试2: VM系统调用
    printf("\n📋 测试2: VM系统调用\n");
    if (vm_init(&vm, syscall_bytecode, sizeof(syscall_bytecode)) < 0) {
        printf("❌ VM初始化失败\n");
        return 1;
    }
    
    if (vm_execute(&vm) < 0) {
        printf("❌ VM执行失败\n");
        return 1;
    }
    
    uint32_t result2 = vm_get_result(&vm);
    uint32_t expected2 = 12345 + (42 * 2);  // get_time() + get_event(42)
    printf("🔍 结果: %u (期望: %u)\n", result2, expected2);
    
    if (result2 == expected2) {
        printf("✅ VM系统调用测试通过!\n");
    } else {
        printf("❌ VM系统调用测试失败!\n");
    }
    
    printf("\n🏁 测试完成\n");
    return 0;
}
