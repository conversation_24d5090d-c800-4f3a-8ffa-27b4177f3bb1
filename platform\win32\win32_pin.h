/**
 * @file win32_pin.h
 * @brief Windows平台引脚操作接口头文件
 * <AUTHOR>
 * @date 2025/1/14
 * @copyright Copyright (c) 2025
 */

#ifndef WIN32_PIN_H
#define WIN32_PIN_H

#include <stdint.h>
#include "es_drv_pin.h"

#ifdef __cplusplus
extern "C" {
#endif

// 兼容性函数
int win32_pin_init(void);

// 模拟测试函数 - 用于在Windows平台测试引脚中断功能
void es_pin_simulate_input(uint16_t pin, uint8_t value);

#ifdef __cplusplus
}
#endif

#endif /* WIN32_PIN_H */
