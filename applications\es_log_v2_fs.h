//
// Created by lxy on 2025/1/14.
// ES Log Storage - Ring buffer log storage with flash persistence
//

#ifndef ES_LOG_V2_STORAGE_H
#define ES_LOG_V2_STORAGE_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// Configuration constants
#define ES_LOG_V2_SLOT_SIZE            128
#define ES_LOG_V2_MAGIC                0x4C4F4753  // "LOGS"
#define ES_LOG_V2_VERSION              0x0001
#define ES_LOG_V2_ALIGN_SIZE           4
#define ES_LOG_V2_RETRY_COUNT          10
#define DEF_LOG_CRC_INIT_VALUE      0xFFFFFFFF

enum {
    ES_LOG_V2_OK = 0,
    ES_LOG_V2_ERR_INVALID_PARAM = -1,
    ES_LOG_V2_ERR_NO_SPACE = -2,
    ES_LOG_V2_ERR_FLASH_ERROR = -3,
    ES_LOG_V2_ERR_CRC_ERROR = -4,
    ES_LOG_V2_ERR_NO_DATA = -5,
};

#pragma pack(push, 1)

// Flash sector header structure
typedef struct  {
    uint32_t magic;         // Magic number for validation
    uint32_t write_count;   // Write counter for this sector
    uint32_t crc32;  // CRC32 of header (excluding this field)
} es_log_v2_sector_header_t;

// Log entry structure in flash
typedef struct  {
    uint32_t status;                      // 0=empty, 1=written
    uint8_t data[ES_LOG_V2_SLOT_SIZE];  // Log data, null-terminated
} es_log_v2_entry_t;

#pragma pack(pop)

// Cache entry structure
typedef struct {
    uint32_t offset;
    uint8_t data[ES_LOG_V2_SLOT_SIZE];  // Log data
} es_log_v2_cache_entry_t;

// Log storage context
typedef struct {
    // Flash configuration
    uint32_t flash_base;
    uint32_t flash_size;
    uint32_t sector_size;
    uint32_t sector_count;
    uint32_t slots_per_sector;
    // Current sectors
    uint32_t write_sector;      // Current write sector
    uint32_t read_sector;       // Current read sector
    uint32_t write_slot;        // Current write slot
    uint32_t read_slot;         // Current read slot
    
    uint32_t max_write_count;   // Max write count
    
    // Cache
    es_log_v2_cache_entry_t cache;
    
    // State flags
    bool initialized;
} es_log_v2_context_t;

// API Functions

/**
 * Initialize the log storage system
 * @return ES_LOG_V2_OK on success, error code on failure
 */
int es_log_v2_init(void);

/**
 * Load existing log data from flash
 * @return ES_LOG_V2_OK on success, error code on failure
 */
int es_log_v2_load(void);

/**
 * Write a log entry
 * @param data Log data
 * @param data_len Data length
 * @return ES_LOG_V2_OK on success, error code on failure
 */
int es_log_v2_write(const void *data, uint16_t data_len);

/**
 * Read log entry at read slot
 * @param data Output buffer for data, data len must be at least ES_LOG_V2_SLOT_SIZE
 * @return ES_LOG_V2_OK on success, error code on failure
 */
int es_log_v2_read(char *data);

/**
 * Move read slot to specific slot
 * @param slot Slot to move to
 * @return ES_LOG_V2_OK on success, error code on failure
 */
int es_log_v2_rewind(void);

/**
 * Flush cache to flash
 * @return ES_LOG_V2_OK on success, error code on failure
 */
int es_log_v2_flush(void);

/**
 * Clear all logs
 * @return ES_LOG_V2_OK on success, error code on failure
 */
int es_log_v2_clear(void);

// #ifndef ES_LOG_V2_TESTING
// // AT command handler functions
// #include "es_coro.h"
// #include "es_at_srv.h"

// /**
//  * AT+LOGV2 command handler - Query/search logs
//  * @param coro Coroutine context
//  * @param ctx AT command context
//  * @param cmd_type Command type
//  * @return Coroutine return value
//  */
// es_async_t at_srv_cmd_logv2_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type);

// /**
//  * AT+LOGV2SEEK command handler - Seek log position
//  * @param coro Coroutine context
//  * @param ctx AT command context
//  * @param cmd_type Command type
//  * @return Coroutine return value
//  */
// es_async_t at_srv_cmd_logv2seek_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type);

// /**
//  * AT+LOGV2CLR command handler - Clear all logs
//  * @param coro Coroutine context
//  * @param ctx AT command context
//  * @param cmd_type Command type
//  * @return Coroutine return value
//  */
// es_async_t at_srv_cmd_logv2clr_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type);

// #endif

// External flash interface functions (to be implemented by user)
extern int log_flash_read(uint32_t addr, void *data, uint32_t len);
extern int log_flash_write(uint32_t addr, const void *data, uint32_t len);
extern int log_flash_erase_sector(uint32_t addr);
extern uint32_t log_flash_get_sector_size(void);
extern uint32_t log_flash_get_log_base_addr(void);
extern uint32_t log_flash_get_log_size(void);
extern uint32_t log_get_timestamp(void);

#ifdef ES_LOG_V2_TESTING
// Test helper functions
void es_log_v2_reset_state(void);
es_log_v2_context_t* es_log_v2_get_context(void);
#endif

#ifdef __cplusplus
}
#endif

#endif // ES_LOG_V2_STORAGE_H 
