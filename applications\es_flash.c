﻿/**
 * @file es_partition.c
 * @brief ES Flash partition management module implementation
 */
#include <string.h>
#include "es_flash.h"
#include "es_drv_os.h"
#include "es_fal_cfg.h"

static bool is_initialized = false;

static const es_flash_dev_t* es_flash_table[] = ES_FAL_FLASH_DEV_TABLE;
static const es_partition_t es_part_table[] = ES_FAL_PARTITION_TABLE;

int es_flash_init(void) 
{
    if (is_initialized) {
        return 0;
    }

    for (uint32_t i = 0; i < sizeof(es_flash_table) / sizeof(es_flash_table[0]); i++) {
        es_flash_table[i]->init();
    }
    is_initialized = true;
    return 0;
}


int es_flash_read(const es_partition_t *part, uint32_t offset, uint8_t *data, uint32_t len)
{
    if (part == NULL || data == NULL) {
        return -1;
    }

    if (offset + len > part->size) {
        return -1;
    }

    if (part->flash_dev == NULL) {
        return -1;
    }

    int status;
    status = part->flash_dev->read(part->start_addr + offset, data, len);
    
    return status;
}

int es_flash_write(const es_partition_t *part, uint32_t offset, const uint8_t *data, uint32_t len)
{
    if (part == NULL || data == NULL) {
        return -1;
    }

    if (offset + len > part->size) {
        return -1;
    }

    if (part->flash_dev == NULL) {
        return -1;
    }

    int status;
    status = part->flash_dev->write(part->start_addr + offset, data, len);
    
    return status;
}

int es_flash_erase(const es_partition_t *part, uint32_t offset, uint32_t len)
{
    if (part == NULL) {
        return -1;
    }

    if (part->flash_dev == NULL) {
        return -1;
    }

    //erase len bytes
    //calculate the number of sectors to erase
    // Calculate the number of sectors to erase
    uint32_t sector_size = part->flash_dev->sector_size;
    uint32_t sector_count = (len + sector_size - 1) / sector_size;

    //check if the offset is mod sector_size
    // Check if offset is a multiple of sector size
    if (offset % sector_size != 0) {
        return -1;
    }

    for (uint32_t i = 0; i < sector_count; i++) {
        if(part->flash_dev->erase_sector(part->start_addr + offset + i * sector_size) != 0) {
            return -1;
        }
        es_os_feed_wdt(); // Feed watchdog to prevent system reset
    }
    return 0;
}

/* Internal function implementation */
const es_partition_t* es_partition_find(const char *part_name)
{
        for (uint32_t i = 0; i < sizeof(es_part_table) / sizeof(es_part_table[0]); i++) {
        if (strcmp(es_part_table[i].name, part_name) == 0) {
            return &es_part_table[i];
        }
    }
    return NULL;
}



