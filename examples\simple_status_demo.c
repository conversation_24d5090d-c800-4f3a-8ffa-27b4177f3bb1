/**
 * @file simple_status_demo.c
 * @brief 简单的系统状态演示程序
 * <AUTHOR>
 * @date 2025/1/15
 * @copyright Copyright (c) 2025
 */

#include "es_system_status.h"
#include "es_mem.h"
#include "es_log.h"
#include "es_drv_os.h"

#define TAG "STATUS_DEMO"

/**
 * @brief 简单的系统状态演示
 */
void simple_status_demo(void)
{
    ES_PRINTF_I(TAG, "=== 系统状态监控演示 ===");
    
    // 初始化内存管理器
    es_mem_init();
    
    // 初始化系统状态监控
    es_sys_status_init();
    
    // 显示初始状态
    ES_PRINTF_I(TAG, "\n--- 初始系统状态 ---");
    es_sys_dump_status();
    
    // 分配一些内存来演示内存使用变化
    ES_PRINTF_I(TAG, "\n--- 分配内存后的状态 ---");
    void *ptr1 = es_mem_alloc(64);
    void *ptr2 = es_mem_alloc(128);
    void *ptr3 = es_mem_alloc(32);
    
    if (ptr1 && ptr2 && ptr3) {
        ES_PRINTF_I(TAG, "成功分配了 64 + 128 + 32 = 224 字节内存");
        es_sys_dump_memory_status();
        
        // 显示健康状态
        es_sys_health_status_t health = es_sys_get_health_status();
        ES_PRINTF_I(TAG, "当前系统健康状态: %s", es_sys_get_health_status_string(health));
    }
    
    // 等待一段时间
    ES_PRINTF_I(TAG, "\n--- 等待2秒后的状态 ---");
    es_os_msleep(2000);
    es_sys_dump_time_status();
    
    // 释放内存
    ES_PRINTF_I(TAG, "\n--- 释放内存后的状态 ---");
    if (ptr1) es_mem_free(ptr1);
    if (ptr2) es_mem_free(ptr2);
    if (ptr3) es_mem_free(ptr3);
    
    es_sys_dump_memory_status();
    
    // 最终状态
    ES_PRINTF_I(TAG, "\n--- 最终系统状态 ---");
    es_sys_dump_status();
    
    // 清理
    es_sys_status_deinit();
    
    ES_PRINTF_I(TAG, "\n=== 演示完成 ===");
}

/**
 * @brief 主函数（如果作为独立程序运行）
 */
#ifdef STANDALONE_DEMO
int main(void)
{
    simple_status_demo();
    return 0;
}
#endif
