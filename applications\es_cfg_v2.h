//
// Created by lxy on 2025/1/14.
// ES Config V2 - Enhanced embedded parameter storage solution
//

#ifndef MCU_CORE_ES_CFG_V2_H
#define MCU_CORE_ES_CFG_V2_H

#include <stdint.h>
#include <stdbool.h>


#define ES_CFG_V2_MAGIC   0x45534356  // "ESCV"

// Version definitions for different parameter categories
#define ES_CFG_V2_FACTORY_VERSION 0x0100
#define ES_CFG_V2_USER_VERSION    0x0201

// CRC32 initial value definition
#define DEF_CRC_INIT_VALUE 0xFFFFFFFF

// Key type - fixed 2 bytes
typedef uint16_t es_cfg_v2_key_t;

#define ES_CFG_SERVER_ADDR "conn.crmlot.honda-sundiro.com:8284"

// Value types enumeration
typedef enum {
    ES_CFG_V2_TYPE_UINT8    = 1,
    ES_CFG_V2_TYPE_UINT16,
    ES_CFG_V2_TYPE_UINT32,
    ES_CFG_V2_TYPE_INT8,
    ES_CFG_V2_TYPE_INT16,
    ES_CFG_V2_TYPE_INT32,
    ES_CFG_V2_TYPE_STRING,
    ES_CFG_V2_TYPE_BINARY,
} es_cfg_v2_value_type_t;

// Parameter category enumeration (also used for reset operations)
typedef enum {
    ES_CFG_V2_CATEGORY_FACTORY = 0x01,  // Factory parameters (read-only in normal operation)
    ES_CFG_V2_CATEGORY_USER    = 0x02,  // User configurable parameters
    ES_CFG_V2_CATEGORY_ALL     = 0x03,  // All parameters (factory + user) - used for reset operations
} es_cfg_v2_category_t;

// Metadata for numeric types
typedef struct {
    union {
        struct { uint32_t def, min, max; } u32;
        struct { int32_t def, min, max; } i32;
        struct { uint16_t def, min, max; } u16;
        struct { int16_t def, min, max; } i16;
        struct { uint8_t def, min, max; } u8;
        struct { int8_t def, min, max; } i8;
    } u;
} es_cfg_v2_num_meta_t;

// Metadata for string/binary types
typedef struct {
    const void *def_data;          // Default data pointer (can be NULL)
    uint16_t def_len;              // Default data length (can be 0)
    uint16_t max_len;              // Maximum data length (can be 0)
} es_cfg_v2_data_meta_t;

// Parameter metadata entry
typedef struct {
    uint16_t key;                  // Parameter key (16 bits)
    uint8_t type : 4;              // Parameter type (4 bits, max 15)
    uint8_t category : 2;          // Parameter category (2 bits, max 3)
    uint8_t reserved : 2;          // Reserved for future use (2 bits)
    uint8_t value_len;             // Value length (8 bits, max 255, but we use max 128)
    const void *meta;              // Metadata pointer (can be NULL)
    void *value_ptr;               // Value storage pointer (can be NULL)
} es_cfg_v2_param_t;

// Storage entry header in flash
#pragma pack(1)
typedef struct {
    uint16_t key;
    uint16_t value_len;
    uint32_t crc32;  // CRC32 of value data
    // value data follows immediately
} es_cfg_v2_entry_t;

// Flash storage header
typedef struct {
    uint32_t magic;
    uint32_t factory_version;
    uint32_t user_version;
    uint32_t write_count;
    uint32_t header_crc32;
} es_cfg_v2_header_t;
#pragma pack()

// Configuration parameter definitions using X-macro
// Format: X(name, key, type, len, category, meta)
#define ES_CFG_V2_PARAMS \
    X(device_id,         0x0001, ES_CFG_V2_TYPE_UINT32,  4,  ES_CFG_V2_CATEGORY_FACTORY, (&(es_cfg_v2_num_meta_t){.u={.u32={0x12345678, 0, 0xFFFFFFFF}}})) \
    X(device_name,       0x0010, ES_CFG_V2_TYPE_STRING,  32, ES_CFG_V2_CATEGORY_USER,    (&(es_cfg_v2_data_meta_t){"ES_Device", 9, 32})) \
    X(server_addr,       0x0011, ES_CFG_V2_TYPE_STRING,  64, ES_CFG_V2_CATEGORY_USER,    (&(es_cfg_v2_data_meta_t){ES_CFG_SERVER_ADDR, sizeof(ES_CFG_SERVER_ADDR)-1, 64})) \
    X(acc_threshold,     0x0020, ES_CFG_V2_TYPE_UINT8,   1,  ES_CFG_V2_CATEGORY_USER,    (&(es_cfg_v2_num_meta_t){.u={.u8={30, 0, 100}}})) \
    X(dec_threshold,     0x0021, ES_CFG_V2_TYPE_UINT8,   1,  ES_CFG_V2_CATEGORY_USER,    (&(es_cfg_v2_num_meta_t){.u={.u8={35, 0, 255}}})) \
    X(turn_threshold,    0x0022, ES_CFG_V2_TYPE_UINT8,   1,  ES_CFG_V2_CATEGORY_USER,    (&(es_cfg_v2_num_meta_t){.u={.u8={40, 0, 255}}})) \
    X(speed_threshold,   0x0023, ES_CFG_V2_TYPE_UINT8,   1,  ES_CFG_V2_CATEGORY_USER,    (&(es_cfg_v2_num_meta_t){.u={.u8={80, 0, 255}}})) \
    X(report_interval,   0x0030, ES_CFG_V2_TYPE_UINT16,  2,  ES_CFG_V2_CATEGORY_USER,    (&(es_cfg_v2_num_meta_t){.u={.u16={10, 1, 3600}}})) \
    X(wakeup_timer,      0x0031, ES_CFG_V2_TYPE_UINT16,  2,  ES_CFG_V2_CATEGORY_USER,    (&(es_cfg_v2_num_meta_t){.u={.u16={24, 0, 24}}})) \

// Generate parameter enumeration
typedef enum {
#define X(name, key, type, len, category, meta) es_cfg_v2_name_##name,
    ES_CFG_V2_PARAMS
#undef X
    ES_CFG_V2_PARAM_MAX
} es_cfg_v2_param_name_t;

// Parameter storage variables are now static in the implementation file
// No extern declarations needed

// Generate parameter metadata table
extern es_cfg_v2_param_t es_cfg_v2_param_table[ES_CFG_V2_PARAM_MAX];

// API Functions

/**
 * @brief Initialize the configuration system
 * @return 0 on success, negative on error
 */
int es_cfg_v2_init(void);

/**
 * @brief Load all parameters from flash storage
 * @return 0 on success, negative on error
 */
int es_cfg_v2_load(void);

/**
 * @brief Get parameter value by key
 * @param key Parameter key
 * @param value Buffer to store the value
 * @param value_len Buffer size
 * @return Actual value length on success, negative on error
 */
int es_cfg_v2_get(es_cfg_v2_key_t key, void *value, uint16_t value_len);

/**
 * @brief Set parameter value by key
 * @param key Parameter key
 * @param value Pointer to new value
 * @param value_len Value length
 * @return 0 on success, negative on error
 */
int es_cfg_v2_set(es_cfg_v2_key_t key, const void *value, uint16_t value_len);

/**
 * @brief Reset parameter to default value
 * @param key Parameter key
 * @return 0 on success, negative on error
 */
int es_cfg_v2_reset_to_default(es_cfg_v2_key_t key);

/**
 * @brief Reset all parameters to default values
 * @return 0 on success, negative on error
 */
int es_cfg_v2_reset_all(void);

/**
 * @brief Reset parameters by category to default values
 * @param category Category of parameters to reset (factory, user, or all)
 * @return 0 on success, negative on error
 */
int es_cfg_v2_reset_by_category(es_cfg_v2_category_t category);

/**
 * @brief Validate parameter value against metadata constraints
 * @param param Parameter metadata pointer
 * @param value Pointer to value to validate
 * @param value_len Value length
 * @return true if valid, false otherwise
 */
bool es_cfg_v2_validate(const es_cfg_v2_param_t *param, const void *value, uint16_t value_len);

/**
 * @brief Get parameter info by key
 * @param key Parameter key
 * @return Pointer to parameter metadata, NULL if not found
 */
const es_cfg_v2_param_t* es_cfg_v2_get_param(es_cfg_v2_key_t key);

/**
 * @brief Get current write counter value
 * @return Current write counter value
 */
uint32_t es_cfg_v2_get_write_count(void);

/**
 * @brief Compact flash storage (remove deleted entries)
 * @return 0 on success, negative on error
 */
int es_cfg_v2_compact(void);

/**
 * @brief Get parameter value by parameter name
 * @param param_name Parameter name (es_cfg_v2_name_##name)
 * @param value Buffer to store the value
 * @param value_len Buffer size
 * @return Actual value length on success, negative on error
 */
int es_cfg_v2_get_by_name(es_cfg_v2_param_name_t name, void *value, uint16_t value_len);

/**
 * @brief Set parameter value by parameter name
 * @param param_name Parameter name (es_cfg_v2_name_##name)
 * @param value Pointer to new value
 * @param value_len Value length
 * @return 0 on success, negative on error
 */
int es_cfg_v2_set_by_name(es_cfg_v2_param_name_t name, const void *value, uint16_t value_len);

/**
 * @brief Reset parameter to default value by parameter name
 * @param param_name Parameter name (es_cfg_v2_name_##name)
 * @return 0 on success, negative on error
 */
int es_cfg_v2_reset_to_default_by_name(es_cfg_v2_param_name_t name);

/**
 * @brief Get parameter info by parameter name
 * @param param_name Parameter name (es_cfg_v2_name_##name)
 * @return Pointer to parameter metadata, NULL if invalid
 */
const es_cfg_v2_param_t* es_cfg_v2_get_param_by_name(es_cfg_v2_param_name_t name);

// Test helper functions (only available when ES_CFG_V2_TESTING is defined)
#ifdef ES_CFG_V2_TESTING
/**
 * @brief Reset initialization state (for unit testing only)
 */
void es_cfg_v2_reset_init_state(void);
#endif

// Generate inline setter functions for each parameter
#define X(name, key, type, len, category, meta) \
    static inline int es_cfg_v2_set_##name(const void *value) { \
        return es_cfg_v2_set(key, value, len); \
    } \
    static inline int es_cfg_v2_get_##name(void *value) { \
        return es_cfg_v2_get(key, value, len); \
    }
ES_CFG_V2_PARAMS
#undef X

// Convenience macros for parameter name based operations
#define ES_CFG_V2_GET_PARAM(name, value_ptr, value_len) \
    es_cfg_v2_get_by_name(es_cfg_v2_name_##name, (value_ptr), (value_len))

#define ES_CFG_V2_SET_PARAM(name, value_ptr, value_len) \
    es_cfg_v2_set_by_name(es_cfg_v2_name_##name, (value_ptr), (value_len))

#define ES_CFG_V2_RESET_PARAM(name) \
    es_cfg_v2_reset_to_default_by_name(es_cfg_v2_name_##name)

#define ES_CFG_V2_GET_PARAM_INFO(name) \
    es_cfg_v2_get_param_by_name(es_cfg_v2_name_##name)


#define es_cfg(n) es_cfg_v2_param_table[es_cfg_v2_name_##n].value_ptr

#define es_cfg_uint8(n) ((uint8_t*)es_cfg(n))
#define es_cfg_uint16(n) ((uint16_t*)es_cfg(n))
#define es_cfg_uint32(n) ((uint32_t*)es_cfg(n))
#define es_cfg_int8(n) ((int8_t*)es_cfg(n))
#define es_cfg_int16(n) ((int16_t*)es_cfg(n))
#define es_cfg_int32(n) ((int32_t*)es_cfg(n))
#define es_cfg_string(n) ((char*)es_cfg(n))


// Error codes
#define ES_CFG_V2_OK                0
#define ES_CFG_V2_ERR_INVALID_KEY   -1
#define ES_CFG_V2_ERR_INVALID_LEN   -2
#define ES_CFG_V2_ERR_INVALID_VALUE -3
#define ES_CFG_V2_ERR_FLASH_ERROR   -4
#define ES_CFG_V2_ERR_CRC_ERROR     -5
#define ES_CFG_V2_ERR_NO_SPACE      -6
#define ES_CFG_V2_ERR_NOT_FOUND     -7

#endif //MCU_CORE_ES_CFG_V2_H 
