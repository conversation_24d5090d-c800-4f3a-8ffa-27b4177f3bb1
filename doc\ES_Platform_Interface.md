# ES 平台和接口模块文档

## 1. 概述

ES 平台和接口模块为 ES MCU Framework 提供了硬件抽象层和用户交互接口。这些模块包括平台相关代码、AT命令服务、控制台接口、硬件驱动接口等，为框架提供了良好的可移植性和易用性。

### 1.1 模块分类

- **平台支持**: HC32F460 和 Win32 平台适配
- **AT命令服务**: es_at_srv, es_at_cli - AT命令解析和处理
- **控制台接口**: es_console - 调试控制台
- **硬件驱动接口**: es_drv_* - 硬件抽象层
- **车辆模块**: es_veh - 车辆相关功能
- **OTA升级**: es_ota - 固件在线升级

### 1.2 设计原则

- **硬件抽象**: 提供统一的硬件接口，屏蔽平台差异
- **模块化设计**: 各模块独立，便于移植和扩展
- **标准接口**: 遵循行业标准，提高兼容性
- **易于调试**: 提供丰富的调试和诊断接口

## 2. 平台支持

### 2.1 HC32F460 平台

#### 2.1.1 平台特性

- **ARM Cortex-M4 内核**: 200MHz 主频
- **Flash存储**: 512KB 程序存储
- **SRAM**: 192KB 数据存储
- **外设丰富**: UART、CAN、ADC、Timer等
- **低功耗设计**: 多种低功耗模式

#### 2.1.2 目录结构

```
platform/hc32/
├── board/                  # 板级支持包
│   ├── board.h            # 板级配置
│   ├── board.c            # 板级初始化
│   └── pin_config.h       # 引脚配置
├── hc32f460_ddl/          # 硬件驱动库
│   ├── driver/            # 外设驱动
│   ├── mcu/               # MCU相关
│   └── cmsis/             # CMSIS接口
├── es_drv_*.c             # 驱动适配层
└── startup/               # 启动代码
```

#### 2.1.3 板级配置

```c
// board.h - 板级配置
#define BOARD_NAME "EV_F460_LQ100_V2"

// 时钟配置
#define SYSTEM_CLOCK_FREQ   200000000UL
#define HCLK_FREQ          200000000UL
#define PCLK1_FREQ         100000000UL
#define PCLK4_FREQ         100000000UL

// LED配置
#define LED_GREEN_PORT     PortE
#define LED_GREEN_PIN      Pin06
#define LED_RED_PORT       PortA
#define LED_RED_PIN        Pin07

// 按键配置
#define KEY_PORT           PortA
#define KEY_PIN            Pin00

// 串口配置
#define DEBUG_UART_UNIT    M4_USART1
#define DEBUG_UART_BAUDRATE 115200
```

### 2.2 Win32 平台

#### 2.2.1 平台特性

- **仿真环境**: 在PC上仿真嵌入式环境
- **调试便利**: 便于开发和调试
- **快速验证**: 快速验证算法和逻辑
- **跨平台**: 支持Windows开发环境

#### 2.2.2 目录结构

```
platform/win32/
├── es_drv_*.c             # Win32驱动适配
├── win32_port.c           # Win32移植层
├── win32_port.h           # Win32接口定义
└── build.bat              # 构建脚本
```

## 3. 硬件驱动接口

### 3.1 操作系统接口 (es_drv_os)

#### 3.1.1 功能特性

- **时间服务**: 系统时钟和时间戳
- **延时函数**: 毫秒和微秒级延时
- **看门狗**: 系统看门狗管理
- **中断管理**: 中断使能和禁用

#### 3.1.2 主要API

```c
// 系统初始化
void es_os_init(void);

// 时间服务
uint32_t es_os_get_tick(void);
uint32_t es_os_get_timestamp(void);
void es_os_get_time_str(char *str, int len);

// 延时函数
void es_os_delay_ms(uint32_t ms);
void es_os_delay_us(uint32_t us);

// 看门狗
void es_os_wdt_init(uint32_t timeout_ms);
void es_os_wdt_feed(void);
void es_os_wdt_enable(bool enable);

// 中断管理
void es_os_enter_critical(void);
void es_os_exit_critical(void);
```

### 3.2 UART驱动接口 (es_drv_uart)

#### 3.2.1 主要API

```c
// UART初始化
int es_uart_init(es_uart_port_t port, uint32_t baudrate);

// 数据发送
int es_uart_write(es_uart_port_t port, const uint8_t *data, uint16_t len);
int es_uart_write_byte(es_uart_port_t port, uint8_t byte);

// 数据接收
int es_uart_read(es_uart_port_t port, uint8_t *data, uint16_t len);
int es_uart_read_byte(es_uart_port_t port, uint8_t *byte);

// 状态查询
bool es_uart_is_tx_ready(es_uart_port_t port);
bool es_uart_is_rx_ready(es_uart_port_t port);
uint16_t es_uart_get_rx_count(es_uart_port_t port);
```

### 3.3 CAN驱动接口 (es_drv_can)

#### 3.3.1 主要API

```c
// CAN初始化
int es_can_init(es_can_port_t port, uint32_t baudrate);

// 消息发送
int es_can_send(es_can_port_t port, const es_can_msg_t *msg);
int es_can_send_async(es_can_port_t port, const es_can_msg_t *msg);

// 消息接收
int es_can_receive(es_can_port_t port, es_can_msg_t *msg);
int es_can_receive_async(es_can_port_t port, es_can_msg_t *msg);

// 过滤器配置
int es_can_set_filter(es_can_port_t port, const es_can_filter_t *filter);

// 状态查询
es_can_state_t es_can_get_state(es_can_port_t port);
uint32_t es_can_get_error_count(es_can_port_t port);
```

### 3.4 ADC驱动接口 (es_drv_adc)

#### 3.4.1 主要API

```c
// ADC初始化
int es_adc_init(es_adc_channel_t channel);

// 单次转换
uint16_t es_adc_read(es_adc_channel_t channel);
int es_adc_read_voltage(es_adc_channel_t channel, uint16_t *voltage);

// 连续转换
int es_adc_start_continuous(es_adc_channel_t channel, 
                           es_adc_callback_t callback, void *arg);
int es_adc_stop_continuous(es_adc_channel_t channel);

// 校准
int es_adc_calibrate(es_adc_channel_t channel);
```

### 3.5 GPIO驱动接口 (es_drv_pin)

#### 3.5.1 主要API

```c
// GPIO配置
int es_pin_mode(es_pin_t pin, es_pin_mode_t mode);
int es_pin_pull(es_pin_t pin, es_pin_pull_t pull);

// GPIO操作
void es_pin_write(es_pin_t pin, es_pin_level_t level);
es_pin_level_t es_pin_read(es_pin_t pin);
void es_pin_toggle(es_pin_t pin);

// 中断配置
int es_pin_attach_interrupt(es_pin_t pin, es_pin_interrupt_t trigger,
                           es_pin_callback_t callback, void *arg);
int es_pin_detach_interrupt(es_pin_t pin);
```

## 4. AT命令服务

### 4.1 AT命令服务器 (es_at_srv)

#### 4.1.1 功能特性

- **命令解析**: 自动解析AT命令格式
- **命令注册**: 支持动态注册AT命令
- **参数处理**: 自动处理命令参数
- **响应格式**: 标准AT响应格式

#### 4.1.2 命令定义

```c
// AT命令定义宏
#define DEMO_AT_SRV_ITEMS \
    X("VERSION", "AT+VERSION?", version) \
    X("IMEI", "AT+IMEI?", imei) \
    X("SN", "AT+SN?|AT+SN=<value>", sn) \
    X("RST", "AT+RST", rst) \
    X("PM", "AT+PM=<event>|AT+PM?", pm) \
    X("VOLT", "AT+VOLT?", volt) \
    X("LOGV2", "AT+LOGV2=<max>,[<keyword>,...]|AT+LOGV2?", logv2) \
    X("CFG2RST", "AT+CFG2RST", cfg2rst)
```

#### 4.1.3 主要API

```c
// AT服务初始化
int es_at_srv_init(void);

// 命令注册
int es_at_srv_register_cmd(const char *name, const char *help, 
                          es_at_cmd_handler_t handler);

// 命令处理
int es_at_srv_process(const char *cmd_line);

// 响应发送
int es_at_srv_response_ok(void);
int es_at_srv_response_error(int error_code);
int es_at_srv_response_data(const char *data);
```

#### 4.1.4 命令实现示例

```c
// 版本查询命令实现
static int at_cmd_version(const char *param) {
    if (param == NULL || strlen(param) == 0) {
        // 查询版本
        es_at_srv_response_data("ES Framework v1.0.0");
        return es_at_srv_response_ok();
    } else {
        return es_at_srv_response_error(AT_ERROR_INVALID_PARAM);
    }
}

// 参数设置命令实现
static int at_cmd_sn(const char *param) {
    if (param == NULL || strlen(param) == 0) {
        // 查询序列号
        char sn[32];
        es_cfg_v2_get(0x0002, sn, sizeof(sn));
        es_at_srv_response_data(sn);
        return es_at_srv_response_ok();
    } else {
        // 设置序列号
        int ret = es_cfg_v2_set(0x0002, param, strlen(param));
        if (ret == ES_CFG_V2_OK) {
            return es_at_srv_response_ok();
        } else {
            return es_at_srv_response_error(AT_ERROR_SET_FAILED);
        }
    }
}
```

### 4.2 AT命令客户端 (es_at_cli)

#### 4.2.1 功能特性

- **命令发送**: 发送AT命令到模组
- **响应解析**: 解析模组响应
- **超时处理**: 命令超时处理
- **协程支持**: 与协程系统集成

#### 4.2.2 主要API

```c
// AT客户端初始化
int es_at_cli_init(es_at_cli_t *cli, es_uart_port_t port);

// 发送命令
es_async_t es_at_cli_send_cmd(es_coro_t *coro, es_at_cli_t *cli, 
                             const char *cmd, char *response, 
                             uint16_t response_len, uint32_t timeout);

// 等待响应
es_async_t es_at_cli_wait_response(es_coro_t *coro, es_at_cli_t *cli,
                                  const char *expected, uint32_t timeout);

// 数据处理
int es_at_cli_process_data(es_at_cli_t *cli, const uint8_t *data, uint16_t len);
```

## 5. 控制台接口 (es_console)

### 5.1 功能特性

- **命令行接口**: 提供交互式命令行
- **命令历史**: 支持命令历史记录
- **自动补全**: 支持命令自动补全
- **多用户**: 支持多用户访问

### 5.2 主要API

```c
// 控制台初始化
int es_console_init(void);

// 命令注册
int es_console_register_cmd(const char *name, const char *help,
                           es_console_cmd_handler_t handler);

// 输入处理
int es_console_process_input(const char *input);

// 输出函数
int es_console_printf(const char *fmt, ...);
int es_console_print(const char *str);
```

### 5.3 内置命令

```c
// 系统信息命令
static int cmd_info(int argc, char **argv) {
    es_console_printf("ES Framework v1.0.0\n");
    es_console_printf("Build: %s %s\n", __DATE__, __TIME__);
    es_console_printf("Free memory: %d bytes\n", es_mem_get_free_size());
    return 0;
}

// 内存统计命令
static int cmd_mem(int argc, char **argv) {
    es_mem_stats_t stats;
    es_mem_get_stats(&stats);
    
    es_console_printf("Memory Statistics:\n");
    es_console_printf("  Total: %d bytes\n", stats.total_size);
    es_console_printf("  Used:  %d bytes\n", stats.used_size);
    es_console_printf("  Free:  %d bytes\n", stats.free_size);
    es_console_printf("  Peak:  %d bytes\n", stats.peak_used);
    
    return 0;
}
```

## 6. 车辆模块 (es_veh)

### 6.1 功能特性

- **车辆状态**: 监控车辆运行状态
- **诊断功能**: 车辆故障诊断
- **数据采集**: 车辆数据采集和处理
- **协议支持**: 支持标准车载协议

### 6.2 主要API

```c
// 车辆模块初始化
int es_veh_init(void);

// 状态监控
es_veh_state_t es_veh_get_state(void);
uint16_t es_veh_get_speed(void);
uint16_t es_veh_get_rpm(void);
uint8_t es_veh_get_fuel_level(void);

// 故障诊断
int es_veh_get_dtc_count(void);
int es_veh_read_dtc(uint16_t index, es_veh_dtc_t *dtc);
int es_veh_clear_dtc(void);

// 数据流
int es_veh_start_data_stream(es_veh_data_callback_t callback, void *arg);
int es_veh_stop_data_stream(void);
```

## 7. OTA升级模块 (es_ota)

### 7.1 功能特性

- **固件升级**: 支持固件在线升级
- **增量升级**: 支持增量升级减少传输量
- **安全验证**: 固件签名验证
- **断点续传**: 支持升级中断后续传

### 7.2 主要API

```c
// OTA初始化
int es_ota_init(void);

// 升级控制
int es_ota_start(const es_ota_info_t *info);
int es_ota_write_data(const uint8_t *data, uint16_t len);
int es_ota_finish(void);
int es_ota_abort(void);

// 状态查询
es_ota_state_t es_ota_get_state(void);
uint32_t es_ota_get_progress(void);

// 固件验证
int es_ota_verify_firmware(const uint8_t *signature, uint16_t sig_len);
```

### 7.3 升级流程

```c
// OTA升级示例
es_async_t ota_upgrade_task(es_coro_t *coro, void *ctx) {
    static es_ota_info_t ota_info;
    static uint8_t data_buffer[1024];
    static uint16_t data_len;
    
    es_co_begin;
    
    // 准备升级信息
    ota_info.total_size = firmware_size;
    ota_info.crc32 = firmware_crc32;
    ota_info.version = new_version;
    
    // 开始升级
    if (es_ota_start(&ota_info) != 0) {
        ES_LOGE("OTA", "Failed to start OTA");
        es_co_err;
    }
    
    // 传输固件数据
    while (has_more_data()) {
        // 接收数据
        es_co_await(receive_firmware_data(coro, data_buffer, &data_len));
        
        // 写入数据
        if (es_ota_write_data(data_buffer, data_len) != 0) {
            ES_LOGE("OTA", "Failed to write OTA data");
            es_ota_abort();
            es_co_err;
        }
        
        // 报告进度
        uint32_t progress = es_ota_get_progress();
        ES_PRINTF_I("OTA", "Progress: %d%%", progress);
    }
    
    // 完成升级
    if (es_ota_finish() != 0) {
        ES_LOGE("OTA", "Failed to finish OTA");
        es_co_err;
    }
    
    ES_PRINTF_I("OTA", "OTA upgrade completed successfully");
    
    // 重启系统
    es_os_delay_ms(1000);
    NVIC_SystemReset();
    
    es_co_end;
}
```

## 8. 平台移植指南

### 8.1 新平台移植步骤

1. **创建平台目录**: 在 `platform/` 下创建新平台目录
2. **实现驱动接口**: 实现 `es_drv_*` 接口
3. **配置编译系统**: 添加编译配置和脚本
4. **测试验证**: 运行测试用例验证移植

### 8.2 驱动接口实现

```c
// 示例：新平台UART驱动实现
int es_uart_init(es_uart_port_t port, uint32_t baudrate) {
    // 平台相关的UART初始化代码
    switch (port) {
        case ES_UART_PORT_0:
            // 初始化UART0
            break;
        case ES_UART_PORT_1:
            // 初始化UART1
            break;
        default:
            return -1;
    }
    
    return 0;
}

int es_uart_write(es_uart_port_t port, const uint8_t *data, uint16_t len) {
    // 平台相关的UART发送代码
    for (uint16_t i = 0; i < len; i++) {
        // 发送单个字节
        platform_uart_send_byte(port, data[i]);
    }
    
    return len;
}
```

## 9. 最佳实践

### 9.1 平台抽象

1. **统一接口**: 保持接口的一致性和简洁性
2. **错误处理**: 统一的错误码和处理机制
3. **资源管理**: 合理管理硬件资源
4. **性能优化**: 针对平台特性进行优化

### 9.2 AT命令设计

1. **标准格式**: 遵循AT命令标准格式
2. **参数验证**: 严格验证命令参数
3. **错误响应**: 提供详细的错误信息
4. **帮助信息**: 提供完整的命令帮助

### 9.3 调试支持

1. **日志集成**: 与日志系统深度集成
2. **状态监控**: 提供丰富的状态监控接口
3. **调试命令**: 提供专用的调试命令
4. **错误追踪**: 完善的错误追踪机制

这些平台和接口模块为 ES MCU Framework 提供了良好的可移植性和易用性，是构建跨平台嵌入式系统的重要基础。
