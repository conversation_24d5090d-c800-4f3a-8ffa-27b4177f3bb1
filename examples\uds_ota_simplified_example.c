/**
 * @file uds_ota_simplified_example.c
 * @brief Example demonstrating the simplified UDS OTA interface
 * @version 1.0.0
 * @date 2025-01-17
 *
 * This example shows how to use the simplified UDS OTA interface for
 * firmware updates with a clean, step-based approach.
 */

#include "es_uds_client.h"
#include "es_scheduler.h"
#include "es_log.h"
#include "es.h"

#define TAG "OTA_EXAMPLE"

/* Example firmware data (dummy data for demonstration) */
static const uint8_t example_firmware[] = {
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
    /* ... more firmware data would go here ... */
};

static const uint32_t example_firmware_size = sizeof(example_firmware);

/* Example security key generation callback */
static uint16_t example_security_key_generator(uint8_t level, const uint8_t *seed, uint16_t seed_length,
                                              uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
    // Simple XOR-based key generation (for demonstration only)
    // In real implementation, use proper cryptographic algorithms
    if (seed_length == 0 || max_key_length < 4) {
        return 0;  // Failed
    }

    // Generate a simple 4-byte key by XORing seed with a fixed pattern
    const uint8_t pattern[] = {0x12, 0x34, 0x56, 0x78};
    for (int i = 0; i < 4 && i < max_key_length; i++) {
        key_buffer[i] = (i < seed_length ? seed[i] : 0x00) ^ pattern[i];
    }

    ES_PRINTF_I(TAG, "Generated security key for level %d (seed_len=%d)", level, seed_length);
    return 4;  // Return key length
}

/* Task for running the OTA example */
static es_coro_task_t s_ota_example_task;

/**
 * @brief Simple diagnostic session example
 */
static es_async_t simple_diagnostic_session(es_coro_t *coro)
{
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Simple Diagnostic Session Example ===");
    
    // Initialize OTA system
    int ret = es_uds_ota_init(0x7E0, 0x7E8, 0x7DF);
    if (ret != 0) {
        ES_PRINTF_E(TAG, "Failed to initialize OTA: %d", ret);
        es_co_err;
    }
    
    // Step 1: Enter programming session
    es_uds_ota_step_t step = {
        .type = ES_UDS_OTA_STEP_SESSION_CONTROL,
        .data.session_control.session_type = ES_UDS_SESSION_PROGRAMMING,
        .timeout_ms = 5000,
        .description = "Enter programming session"
    };
    es_uds_ota_step_add(step);
    
    // Step 2: Send Tester Present
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_TESTER_PRESENT,
        .timeout_ms = 1000,
        .description = "Send Tester Present"
    };
    es_uds_ota_step_add(step);
    
    // Step 3: Read VIN (Data Identifier 0xF190)
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_READ_DATA_BY_ID,
        .data.data_by_id.data_id = 0xF190,
        .timeout_ms = 3000,
        .description = "Read VIN"
    };
    es_uds_ota_step_add(step);
    
    // Execute all steps
    ES_PRINTF_I(TAG, "Executing %d diagnostic steps", es_uds_ota_step_count());
    es_co_await_ex(err, es_uds_ota_exec);
    
    ES_PRINTF_I(TAG, "Simple diagnostic session completed successfully!");
    
    es_co_eee(
        ES_PRINTF_E(TAG, "Simple diagnostic session failed");
        es_uds_ota_step_clear();
    );
}

/**
 * @brief Complete firmware update example
 */
static es_async_t complete_firmware_update(es_coro_t *coro)
{
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Complete Firmware Update Example ===");
    
    // Initialize OTA system
    int ret = es_uds_ota_init(0x7E0, 0x7E8, 0x7DF);
    if (ret != 0) {
        ES_PRINTF_E(TAG, "Failed to initialize OTA: %d", ret);
        es_co_err;
    }
    
    // Clear any existing steps
    es_uds_ota_step_clear();
    
    // Step 1: Enter programming session
    es_uds_ota_step_t step = {
        .type = ES_UDS_OTA_STEP_SESSION_CONTROL,
        .data.session_control.session_type = ES_UDS_SESSION_PROGRAMMING,
        .timeout_ms = 5000,
        .description = "Enter programming session"
    };
    es_uds_ota_step_add(step);
    
    // Step 2: Security access - request seed
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_SECURITY_ACCESS_SEED,
        .data.security_access.security_level = 1,
        .timeout_ms = 5000,
        .description = "Request security seed"
    };
    es_uds_ota_step_add(step);
    
    // Step 3: Security access - send key
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_SECURITY_ACCESS_KEY,
        .data.security_access.security_level = 1,
        .data.security_access.key_data = example_security_key,
        .data.security_access.key_length = sizeof(example_security_key),
        .timeout_ms = 5000,
        .description = "Send security key"
    };
    es_uds_ota_step_add(step);
    
    // Step 4: Erase memory routine
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_ROUTINE_CONTROL,
        .data.routine_control.sub_function = ES_UDS_ROUTINE_CONTROL_START,
        .data.routine_control.routine_id = ES_UDS_ROUTINE_ERASE_MEMORY,
        .data.routine_control.routine_data = NULL,
        .data.routine_control.routine_data_length = 0,
        .timeout_ms = 30000, // Long timeout for erase
        .description = "Erase memory"
    };
    es_uds_ota_step_add(step);
    
    // Step 5: Request download
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_REQUEST_DOWNLOAD,
        .data.request_download.data_format_id = 0x00,
        .data.request_download.memory_address = 0x08000000,
        .data.request_download.memory_size = example_firmware_size,
        .timeout_ms = 5000,
        .description = "Request download"
    };
    es_uds_ota_step_add(step);
    
    // Step 6: Transfer firmware data
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_TRANSFER_DATA,
        .data.transfer_data.data = example_firmware,
        .data.transfer_data.data_length = example_firmware_size,
        .data.transfer_data.chunk_size = 240, // Custom chunk size
        .timeout_ms = 1000,
        .description = "Transfer firmware data"
    };
    es_uds_ota_step_add(step);
    
    // Step 7: Request transfer exit
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_REQUEST_TRANSFER_EXIT,
        .timeout_ms = 5000,
        .description = "Exit transfer"
    };
    es_uds_ota_step_add(step);
    
    // Step 8: Checksum verification routine
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_ROUTINE_CONTROL,
        .data.routine_control.sub_function = ES_UDS_ROUTINE_CONTROL_START,
        .data.routine_control.routine_id = ES_UDS_ROUTINE_CHECKSUM_VERIFICATION,
        .data.routine_control.routine_data = NULL,
        .data.routine_control.routine_data_length = 0,
        .timeout_ms = 10000,
        .description = "Verify checksum"
    };
    es_uds_ota_step_add(step);
    
    // Step 9: ECU reset
    step = (es_uds_ota_step_t){
        .type = ES_UDS_OTA_STEP_ECU_RESET,
        .data.ecu_reset.reset_type = ES_UDS_RESET_HARD,
        .timeout_ms = 5000,
        .description = "Reset ECU"
    };
    es_uds_ota_step_add(step);
    
    // Execute all steps
    ES_PRINTF_I(TAG, "Starting firmware update with %d steps", es_uds_ota_step_count());
    es_co_await_ex(err, es_uds_ota_exec);
    
    ES_PRINTF_I(TAG, "Firmware update completed successfully!");
    
    es_co_eee(
        ES_PRINTF_E(TAG, "Firmware update failed");
        es_uds_ota_step_clear();
    );
}

/**
 * @brief Custom service example
 */
static es_async_t custom_service_example(es_coro_t *coro)
{
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Custom Service Example ===");
    
    // Initialize OTA system
    es_uds_ota_init(0x7E0, 0x7E8, 0x7DF);
    es_uds_ota_step_clear();
    
    // Custom service example (hypothetical service 0x85)
    static const uint8_t custom_data[] = {0x01, 0x02, 0x03};
    es_uds_ota_step_t step = {
        .type = ES_UDS_OTA_STEP_CUSTOM,
        .data.custom.service_id = 0x85,
        .data.custom.sub_function = 0x01,
        .data.custom.request_data = custom_data,
        .data.custom.request_length = sizeof(custom_data),
        .timeout_ms = 3000,
        .description = "Custom service call"
    };
    es_uds_ota_step_add(step);
    
    ES_PRINTF_I(TAG, "Executing custom service");
    es_co_await_ex(err, es_uds_ota_exec);
    
    ES_PRINTF_I(TAG, "Custom service completed successfully!");
    
    es_co_eee(
        ES_PRINTF_E(TAG, "Custom service failed");
        es_uds_ota_step_clear();
    );
}

/**
 * @brief Main OTA example task
 */
static es_async_t ota_example_task(es_coro_t *coro, void *ctx)
{
    static uint32_t example_counter = 0;
    
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "UDS OTA Simplified Interface Example Started");
    
    while (1) {
        // Wait a bit before starting examples
        es_co_wait_timeout(false, 2000);
        
        example_counter++;
        
        if (example_counter == 1) {
            // Run simple diagnostic session
            es_co_await_ex(err, simple_diagnostic_session);
        } else if (example_counter == 2) {
            // Run custom service example
            es_co_await_ex(err, custom_service_example);
        } else if (example_counter == 3) {
            // Run complete firmware update
            es_co_await_ex(err, complete_firmware_update);
            
            // Reset counter to repeat examples
            example_counter = 0;
        }
        
        // Wait before next example
        es_co_wait_timeout(false, 5000);
    }
    
    es_co_eee(
        ES_PRINTF_E(TAG, "Error in OTA example task");
        // Wait and continue
        es_co_wait_timeout(false, 1000);
        example_counter = 0;
    );
}

/**
 * @brief Initialize and start the OTA example
 */
int uds_ota_simplified_example_init(void)
{
    // Initialize the example task
    es_scheduler_task_init(&s_ota_example_task, "ota_example", ota_example_task, NULL);
    es_scheduler_task_add(es_scheduler_get_default(), &s_ota_example_task);
    
    ES_PRINTF_I(TAG, "UDS OTA Simplified Example initialized");
    
    return 0;
}

/**
 * @brief Deinitialize the OTA example
 */
void uds_ota_simplified_example_deinit(void)
{
    es_scheduler_task_remove(es_scheduler_get_default(), &s_ota_example_task);
    es_uds_ota_step_clear();
    
    ES_PRINTF_I(TAG, "UDS OTA Simplified Example deinitialized");
}
