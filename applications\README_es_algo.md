# ES Algorithm Module - 两轮车事件检测算法

## 概述

ES Algorithm模块是一个专为两轮车设计的事件检测算法，通过融合LSM6DSL传感器数据和GPS数据，能够检测急加速、急减速、急转弯、倾倒等事件。该模块使用Q15.16定点运算，仅依赖32位算术运算，适合嵌入式系统。

## 主要特性

### 1. 数据融合
- **传感器数据**: LSM6DSL加速度计和陀螺仪数据（50Hz采样）
- **GPS数据**: 位置、速度、航向角数据（1Hz采样）
- **航向角融合**: 结合GPS航向角确定车辆正前方向
- **自适应方向**: 处理设备安装方向不固定的问题

### 2. 事件检测
- **急加速**: 检测阈值 > 300mg
- **急减速**: 检测阈值 > 400mg  
- **急转弯**: 检测阈值 > 30000mdps (30°/s)
- **倾倒事件**: 检测阈值 > 45°倾斜角
- **恢复事件**: 从倾倒状态恢复

### 3. 数据过滤
- **移动平均滤波**: 10点窗口滤波
- **异常值检测**: 加速度和陀螺仪数据范围验证
- **GPS数据验证**: 卫星数量、HDOP、速度范围检查
- **重力估计**: 低通滤波估计重力向量

### 4. 技术特点
- **Q15.16定点运算**: 32位定点算术，无需浮点运算单元
- **协程任务**: 非阻塞数据采集和处理
- **事件发布**: 集成到ES框架事件系统
- **实时统计**: 数据采集和处理统计信息

## 文件结构

```
applications/
├── es_algo.h              # 算法模块头文件
├── es_algo.c              # 算法模块实现
├── es_algo_example.c      # 使用示例和测试
└── README_es_algo.md      # 本文档
```

## API接口

### 初始化和清理
```c
int es_algo_init(void);                    // 初始化算法模块
void es_algo_deinit(void);                 // 清理算法模块
int es_algo_task_init(void);               // 初始化协程任务
void es_algo_task_deinit(void);            // 清理协程任务
```

### 数据处理
```c
int es_algo_process_sensor_data(const stc_lsm6dsl_axis_t *accel, 
                               const stc_lsm6dsl_axis_t *gyro, 
                               float temperature);

int es_algo_process_gps_data(const gps_t *gps);
```

### 状态查询
```c
const es_algo_context_t *es_algo_get_context(void);
void es_algo_get_statistics(uint32_t *sensor_samples, uint32_t *gps_samples,
                           uint32_t *invalid_sensor, uint32_t *invalid_gps);
void es_algo_reset(void);
```

## 数据结构

### 事件类型
```c
typedef enum {
    ES_ALGO_EVENT_NONE = 0,
    ES_ALGO_EVENT_RAPID_ACCEL,      // 急加速
    ES_ALGO_EVENT_RAPID_DECEL,      // 急减速
    ES_ALGO_EVENT_SHARP_TURN_LEFT,  // 急左转
    ES_ALGO_EVENT_SHARP_TURN_RIGHT, // 急右转
    ES_ALGO_EVENT_TIPPING,          // 倾倒
    ES_ALGO_EVENT_RECOVERED,        // 恢复
} es_algo_event_type_t;
```

### 事件数据
```c
typedef struct {
    es_algo_event_type_t type;      // 事件类型
    int32_t magnitude_q15;          // 事件强度 (Q15.16)
    uint32_t timestamp;             // 时间戳 (ms)
    es_algo_sensor_data_t sensor;   // 传感器数据
    es_algo_gps_data_t gps;         // GPS数据
} es_algo_event_t;
```

## 使用方法

### 1. 基本使用
```c
#include "es_algo.h"

// 初始化
es_algo_example_init();

// 算法会自动运行，检测到事件时会发布到事件系统
// 事件处理函数会自动调用

// 清理
es_algo_example_deinit();
```

### 2. 手动数据处理
```c
// 初始化算法模块
es_algo_init();

// 处理传感器数据
stc_lsm6dsl_axis_t accel = {100, 50, 1000}; // mg
stc_lsm6dsl_axis_t gyro = {0, 0, 5000};     // mdps
es_algo_process_sensor_data(&accel, &gyro, 25.0f);

// 处理GPS数据
gps_t gps = {/* GPS数据 */};
es_algo_process_gps_data(&gps);
```

### 3. 事件订阅
```c
static void vehicle_event_handler(const es_event_t *event, void *ctx) {
    if (event->type == EVENT_TYPE_VEHICLE_EVENT) {
        const es_algo_event_t *vehicle_event = (const es_algo_event_t *)event->u.data;
        // 处理检测到的事件
        ES_PRINTF_I("Event", "Detected: %d, magnitude: %.2f", 
                   vehicle_event->type, 
                   ES_ALGO_Q15_TO_FLOAT(vehicle_event->magnitude_q15));
    }
}

// 订阅事件
es_event_subscriber_t subscriber;
es_scheduler_event_subscriber_init(&subscriber, EVENT_TYPE_VEHICLE_EVENT, 
                                   vehicle_event_handler, NULL);
es_scheduler_event_subscribe(es_scheduler_get_default(), &subscriber);
```

## 配置参数

### 采样率
```c
#define ES_ALGO_SENSOR_SAMPLE_RATE_HZ   50  // 传感器采样率
#define ES_ALGO_GPS_SAMPLE_RATE_HZ      1   // GPS采样率
```

### 检测阈值
```c
#define ES_ALGO_ACCEL_THRESHOLD_MG      300     // 急加速阈值 (mg)
#define ES_ALGO_DECEL_THRESHOLD_MG      400     // 急减速阈值 (mg)
#define ES_ALGO_TURN_THRESHOLD_MDPS     30000   // 急转弯阈值 (mdps)
#define ES_ALGO_TILT_THRESHOLD_DEG      45.0f   // 倾倒阈值 (度)
#define ES_ALGO_SPEED_THRESHOLD_KMH     5       // 最小检测速度 (km/h)
```

### 数据验证阈值
```c
#define ES_ALGO_MAX_ACCEL_MG            8000    // 最大有效加速度 (mg)
#define ES_ALGO_MAX_GYRO_MDPS           200000  // 最大有效陀螺仪 (mdps)
#define ES_ALGO_GPS_MIN_SATELLITES      4       // 最小卫星数
#define ES_ALGO_GPS_MAX_HDOP            500     // 最大HDOP (x100)
```

## 算法原理

### 1. 方向检测
- 使用GPS航向角确定车辆前进方向
- 结合重力向量估计车辆姿态
- 建立车辆坐标系（前进、右侧、向上）

### 2. 事件检测逻辑
- **急加速/减速**: 将加速度投影到车辆前进方向，检测超过阈值的加速度
- **急转弯**: 检测Z轴陀螺仪数据，判断左转或右转
- **倾倒**: 计算roll和pitch角度的合成角度，检测是否超过倾倒阈值

### 3. 数据融合
- GPS速度用于验证车辆是否在运动
- GPS航向角用于校准车辆方向
- 传感器数据用于实时事件检测
- 低通滤波平滑重力估计

## 测试

### 仿真测试
```c
// 运行仿真测试
es_algo_test_simulation();
```

### 打印使用说明
```c
// 打印详细使用说明
es_algo_print_usage();
```

## 注意事项

1. **GPS依赖**: 方向检测需要GPS有效且车辆在运动（速度 > 5km/h）
2. **定点运算**: 所有计算使用Q15.16定点格式，避免浮点运算
3. **内存使用**: 滤波器使用固定大小的环形缓冲区
4. **实时性**: 传感器数据50Hz处理，GPS数据1Hz处理
5. **事件防抖**: 相同事件间隔1秒防止重复触发

## 性能特点

- **CPU使用**: 纯定点运算，CPU占用低
- **内存占用**: 约2KB RAM用于算法上下文和滤波缓冲区
- **实时性**: 50Hz传感器数据处理，延迟 < 20ms
- **精度**: Q15.16格式提供足够精度用于事件检测
- **可靠性**: 多层数据验证和过滤，减少误检测
