# ES MCU Framework - 企业级嵌入式系统开发框架

[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-HC32F460-green.svg)](platform/hc32/)
[![Build](https://img.shields.io/badge/Build-SCons-orange.svg)](SConstruct)
[![RT-Thread](https://img.shields.io/badge/RTOS-RT--Thread-red.svg)](rt-thread/)

## 🚀 项目简介

ES MCU Framework 是一个专为 HC32F460 微控制器设计的企业级嵌入式系统开发框架。该框架基于 RT-Thread 实时操作系统，提供了完整的模块化架构、协程调度、通信协议栈、存储管理等核心功能，适用于车联网、物联网等复杂嵌入式应用场景。

### ✨ 核心特性

- **🔧 模块化架构**: 采用清晰的分层设计，各功能模块相互独立
- **⚡ 协程调度系统**: 基于协程的轻量级任务调度，支持事件驱动
- **🔐 安全加密模块**: 集成 AES、DES3、MD5 等加密算法
- **📡 通信协议栈**: 支持 TCP/IP、蓝牙、串口等多种通信方式
- **💾 配置存储系统**: 双扇区安全存储，支持工厂参数和用户参数分类管理
- **📝 日志文件系统**: 完整的日志记录和查询功能，支持远程访问
- **🔋 电源管理**: 低功耗设计，支持多种唤醒方式
- **🛠️ 多平台支持**: 支持 HC32F460 硬件平台和 Win32 仿真平台

## 📁 项目结构

```
es_mcu/
├── applications/           # 应用层模块
│   ├── es.h               # 系统核心头文件
│   ├── es_scheduler.*     # 协程调度器
│   ├── es_frame.*         # 通信协议框架
│   ├── es_cfg_v2.*        # 配置管理系统
│   ├── es_log.*           # 日志系统
│   ├── es_mem.*           # 内存管理
│   ├── es_flash.*         # Flash存储
│   └── ...               # 其他功能模块
├── platform/              # 平台相关代码
│   ├── hc32/              # HC32F460平台
│   │   ├── board/         # 板级支持包
│   │   ├── hc32f460_ddl/  # 硬件驱动库
│   │   └── *.c/*.h        # 平台特定实现
│   └── win32/             # Win32仿真平台
├── rt-thread/             # RT-Thread操作系统
├── doc/                   # 项目文档
├── tests/                 # 测试代码
├── demo/                  # 示例代码
└── build files           # 构建脚本
```

## 🛠️ 快速开始

### 环境准备

#### 硬件要求
- **开发板**: EV_F460_LQ100_V2 (HC32F460PETB)
- **调试器**: J-Link 或 ST-Link
- **连接线**: USB Type-A to Micro USB

#### 软件要求
- **IDE**: Keil MDK5 / IAR Embedded Workbench / VS Code
- **编译器**: ARM GCC / ARM Compiler
- **构建工具**: SCons / CMake
- **版本控制**: Git
- **调试工具**: 串口调试助手

### 编译构建

#### 1. HC32F460 平台

```bash
# 使用 SCons 构建（推荐）
scons

# 或使用便捷脚本
build_hc32.bat

# 生成 Keil/IAR 工程
scons --target=mdk5    # 生成 Keil 工程
scons --target=iar     # 生成 IAR 工程
```

#### 2. Win32 仿真平台

```bash
# Windows 平台仿真
build_win32.bat

# 或手动构建
cd platform/win32
build.bat
```

### 硬件连接

1. 使用 USB 线连接开发板到 PC 供电
2. 连接调试器到开发板的 SWD 接口
3. 打开串口调试工具，连接到虚拟串口（波特率 115200）

### 运行程序

1. 编译并下载程序到开发板
2. 复位设备，观察串口输出：

```
 \ | /
- RT -     Thread Operating System
 / | \     ES Framework v1.0.0
 2006 - 2024 Copyright by RT-Thread team
[I] ES Framework initialized successfully
[I] Scheduler started
msh >
```

3. 观察开发板 LED 运行状态，绿色 LED 应周期性闪烁

## 📋 功能模块

### 🔄 协程调度系统 (`es_scheduler`)

支持轻量级协程任务和事件驱动编程：

```c
// 创建协程任务
es_async_t my_task(es_coro_t *coro, void *ctx) {
    ES_CORO_BEGIN(coro);
    
    while (1) {
        // 非阻塞任务逻辑
        do_work();
        ES_CORO_YIELD(coro);
    }
    
    ES_CORO_END(coro);
}

// 添加任务到调度器
es_coro_task_t task;
es_scheduler_task_init(&task, "my_task", my_task, NULL);
es_scheduler_task_add(es_scheduler_get_default(), &task);
```

### 📡 通信协议框架 (`es_frame`)

支持多种通信协议和自动帧解析：

- **TSP 协议**: TCP/IP 车联网通信协议
- **BLE 协议**: 蓝牙低功耗通信
- **串口协议**: RS232/RS485 通信
- **自定义协议**: 可扩展协议栈

### 💾 配置管理系统 (`es_cfg_v2`)

提供安全的参数存储和管理：

```c
// 设置用户参数
uint8_t threshold = 50;
es_cfg_v2_set(0x0020, &threshold, sizeof(threshold), true);

// 获取参数
uint8_t read_threshold;
es_cfg_v2_get(0x0020, &read_threshold, sizeof(read_threshold));

// 按类别重置参数
es_cfg_v2_reset_by_category(ES_CFG_V2_CATEGORY_USER, true);
```

### 📝 日志系统 (`es_log`)

完整的日志记录和查询功能：

```c
// 记录日志
ES_LOG_INFO("System", "Device initialized successfully");
ES_LOG_ERROR("Network", "Connection failed: %d", error_code);

// 支持远程日志查询和串口导出
```

### 🔐 安全加密模块

- **AES 加密**: AES-128/192/256，支持 ECB/CBC/CTR 模式
- **DES3 加密**: 三重 DES 加密算法
- **MD5 哈希**: 数据完整性校验和密码存储
- **CRC 校验**: 多种 CRC 算法支持

## 🔧 开发指南

### 添加新的协程任务

1. 定义协程函数：

```c
es_async_t new_task(es_coro_t *coro, void *ctx) {
    ES_CORO_BEGIN(coro);
    
    // 初始化代码
    init_task();
    
    while (1) {
        // 主要逻辑
        process_data();
        
        // 让出控制权
        ES_CORO_YIELD(coro);
    }
    
    ES_CORO_END(coro);
}
```

2. 注册任务到调度器：

```c
static es_coro_task_t my_task;

void app_init(void) {
    es_scheduler_task_init(&my_task, "new_task", new_task, NULL);
    es_scheduler_task_add(es_scheduler_get_default(), &my_task);
}
```

### 添加新的通信协议

1. 在 `es_frame.h` 中定义协议帧格式
2. 实现编码/解码函数
3. 注册协议处理器

详细开发指南请参考 [ES开发指南](doc/ES_Development_Guide.md)

## 📖 文档

### 📚 核心文档

- **[系统架构文档](doc/ES_Framework_Architecture.md)**: 系统整体架构和设计原理
- **[API参考手册](doc/ES_API_Reference.md)**: 完整的API接口文档

### 🔧 核心模块文档

- **[协程调度系统](doc/ES_Coroutine_Scheduler.md)**: 协程和任务调度详细说明
- **[配置管理系统](doc/ES_Configuration_Management.md)**: 参数配置和存储系统
- **[通信协议框架](doc/ES_Communication_Framework.md)**: 通信协议栈和ISO-TP/UDS实现
- **[日志系统](doc/ES_Logging_System.md)**: 日志记录、存储和查询系统

### 🛠️ 工具和平台文档

- **[工具和驱动模块](doc/ES_Utility_Modules.md)**: 内存管理、存储、加密等工具模块
- **[平台和接口模块](doc/ES_Platform_Interface.md)**: 平台适配和用户接口模块

### 📖 开发指南和示例

- **[开发指南](doc/ES_Development_Guide.md)**: 详细的开发指南和最佳实践
- **[使用示例](doc/ES_Usage_Examples.md)**: 典型应用场景和代码示例

## 🧪 测试

### 运行单元测试

```bash
# 编译测试程序
cd tests/unit
make

# 运行测试
./test_runner
```

### 运行集成测试

```bash
# 在目标硬件上运行
cd tests/integration
scons
# 下载并运行测试固件
```

## 🔧 配置

### 系统配置

主要配置文件：`applications/es_config.h`

```c
// 调度器配置
#define CONFIG_MAX_TASKS            16      // 最大任务数
#define CONFIG_MAX_TIMERS          32      // 最大定时器数
#define CONFIG_EVENT_QUEUE_SIZE    32      // 事件队列大小

// 内存配置  
#define CONFIG_HEAP_SIZE           (32*1024)  // 堆大小
#define CONFIG_STACK_SIZE          (4*1024)   // 默认堆栈大小

// 日志配置
#define CONFIG_LOG_LEVEL           ES_LOG_LEVEL_INFO
#define CONFIG_LOG_BUFFER_SIZE     512
```

### 平台配置

使用 `menuconfig` 进行图形化配置：

```bash
menuconfig
```

## 🤝 贡献

我们欢迎各种形式的贡献！

### 如何贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范

- 遵循 C 语言编码规范
- 添加适当的注释和文档
- 编写单元测试
- 确保代码通过静态分析检查

## 📊 性能指标

| 指标 | 数值 |
|------|------|
| ROM 占用 | < 256KB |
| RAM 占用 | < 64KB |
| 支持协程数量 | 64个 |
| 事件队列深度 | 32个 |
| 定时器精度 | 毫秒级 |
| 启动时间 | < 500ms |

## 🐛 故障排除

### 常见问题

1. **编译错误**: 检查工具链路径和版本
2. **下载失败**: 确认调试器连接和驱动安装
3. **运行异常**: 检查堆栈大小和内存配置
4. **通信异常**: 验证协议配置和硬件连接

详细故障排除指南请参考 [故障排除文档](doc/ES_Development_Guide.md#故障排除)

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 👥 团队

- **维护团队**: 小华半导体 MCU 生态团队
- **联系邮箱**: [<EMAIL>](mailto:<EMAIL>)
- **官方网站**: [http://www.xhsc.com.cn](http://www.xhsc.com.cn)

## 🔗 相关链接

- [HC32F460 官方文档](platform/hc32/hc32f460_ddl/)
- [RT-Thread 官方网站](https://www.rt-thread.org/)
- [项目 Wiki](../../wiki)
- [问题反馈](../../issues)
- [版本发布](../../releases)

## 📈 版本历史

- **v1.0.0** (2024-01-01): 初始版本发布
  - 完整的协程调度系统
  - 通信协议框架
  - 配置管理系统
  - 日志文件系统

详细版本历史请查看 [CHANGELOG.md](CHANGELOG.md)

---

<div align="center">

**[⬆ 回到顶部](#es-mcu-framework---企业级嵌入式系统开发框架)**

Made with ❤️ by 小华半导体团队

</div>