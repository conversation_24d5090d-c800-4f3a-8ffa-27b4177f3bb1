/**
 * @file test_mem_v2_basic.c
 * @brief 内存管理器V2基本功能测试
 * <AUTHOR> Assistant
 * @date 2025/1/21
 */

#include <stdio.h>
#include <string.h>
#include <assert.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdbool.h>
#include "../applications/es_mem_v2.h"

// 简单的日志实现用于测试
void es_log_write(int level, const char *tag, bool to_flash, const char *fmt, ...) {
    va_list args;
    va_start(args, fmt);
    printf("[%s] ", tag);
    vprintf(fmt, args);
    printf("\n");
    va_end(args);
}

static void test_basic_allocation(void) {
    printf("\n=== Testing Basic Allocation ===\n");
    
    // 测试基本分配
    void *ptr1 = es_mem_v2_alloc(64);
    assert(ptr1 != NULL);
    printf("✓ Allocated 64 bytes at %p\n", ptr1);
    
    void *ptr2 = es_mem_v2_alloc(128);
    assert(ptr2 != NULL);
    printf("✓ Allocated 128 bytes at %p\n", ptr2);
    
    void *ptr3 = es_mem_v2_alloc(256);
    assert(ptr3 != NULL);
    printf("✓ Allocated 256 bytes at %p\n", ptr3);
    
    // 测试写入数据
    strcpy((char*)ptr1, "Hello");
    strcpy((char*)ptr2, "World");
    strcpy((char*)ptr3, "Memory V2 Test");
    
    printf("✓ Data written: '%s', '%s', '%s'\n", 
           (char*)ptr1, (char*)ptr2, (char*)ptr3);
    
    // 释放内存
    es_mem_v2_free(ptr1);
    es_mem_v2_free(ptr2);
    es_mem_v2_free(ptr3);
    printf("✓ All memory freed\n");
}

static void test_alignment(void) {
    printf("\n=== Testing 4-byte Alignment ===\n");
    
    // 测试不同大小的对齐
    void *ptr1 = es_mem_v2_alloc(1);   // 应该对齐到4字节
    void *ptr2 = es_mem_v2_alloc(3);   // 应该对齐到4字节
    void *ptr3 = es_mem_v2_alloc(5);   // 应该对齐到8字节
    void *ptr4 = es_mem_v2_alloc(7);   // 应该对齐到8字节
    
    assert(ptr1 != NULL && ptr2 != NULL && ptr3 != NULL && ptr4 != NULL);
    
    // 检查地址对齐
    assert(((uintptr_t)ptr1 % 4) == 0);
    assert(((uintptr_t)ptr2 % 4) == 0);
    assert(((uintptr_t)ptr3 % 4) == 0);
    assert(((uintptr_t)ptr4 % 4) == 0);
    
    printf("✓ All pointers are 4-byte aligned\n");
    printf("  ptr1=%p, ptr2=%p, ptr3=%p, ptr4=%p\n", ptr1, ptr2, ptr3, ptr4);
    
    es_mem_v2_free(ptr1);
    es_mem_v2_free(ptr2);
    es_mem_v2_free(ptr3);
    es_mem_v2_free(ptr4);
}

static void test_next_fit_strategy(void) {
    printf("\n=== Testing Next Fit Strategy ===\n");
    
    // 分配多个块
    void *ptrs[10];
    for (int i = 0; i < 10; i++) {
        ptrs[i] = es_mem_v2_alloc(100);
        assert(ptrs[i] != NULL);
        printf("  Allocated block %d at %p\n", i, ptrs[i]);
    }
    
    // 释放一些中间的块
    es_mem_v2_free(ptrs[2]);
    es_mem_v2_free(ptrs[5]);
    es_mem_v2_free(ptrs[7]);
    printf("✓ Freed blocks 2, 5, 7\n");
    
    // 再次分配，应该使用Next Fit策略
    void *new_ptr1 = es_mem_v2_alloc(50);
    void *new_ptr2 = es_mem_v2_alloc(50);
    void *new_ptr3 = es_mem_v2_alloc(50);
    
    assert(new_ptr1 != NULL && new_ptr2 != NULL && new_ptr3 != NULL);
    printf("✓ Next fit allocation successful\n");
    printf("  new_ptr1=%p, new_ptr2=%p, new_ptr3=%p\n", new_ptr1, new_ptr2, new_ptr3);
    
    // 清理
    for (int i = 0; i < 10; i++) {
        if (i != 2 && i != 5 && i != 7) {
            es_mem_v2_free(ptrs[i]);
        }
    }
    es_mem_v2_free(new_ptr1);
    es_mem_v2_free(new_ptr2);
    es_mem_v2_free(new_ptr3);
}

static void test_calloc_and_realloc(void) {
    printf("\n=== Testing Calloc and Realloc ===\n");
    
    // 测试calloc
    void *ptr = es_mem_v2_calloc(10, sizeof(int));
    assert(ptr != NULL);
    
    // 检查是否清零
    int *arr = (int*)ptr;
    for (int i = 0; i < 10; i++) {
        assert(arr[i] == 0);
    }
    printf("✓ Calloc allocated and zeroed memory\n");
    
    // 写入数据
    for (int i = 0; i < 10; i++) {
        arr[i] = i + 1;
    }
    
    // 测试realloc扩大
    ptr = es_mem_v2_realloc(ptr, 20 * sizeof(int));
    assert(ptr != NULL);
    arr = (int*)ptr;
    
    // 检查原数据是否保留
    for (int i = 0; i < 10; i++) {
        assert(arr[i] == i + 1);
    }
    printf("✓ Realloc preserved original data\n");
    
    // 测试realloc缩小
    ptr = es_mem_v2_realloc(ptr, 5 * sizeof(int));
    assert(ptr != NULL);
    arr = (int*)ptr;
    
    // 检查数据
    for (int i = 0; i < 5; i++) {
        assert(arr[i] == i + 1);
    }
    printf("✓ Realloc shrink successful\n");
    
    es_mem_v2_free(ptr);
}

static void test_statistics(void) {
    printf("\n=== Testing Statistics ===\n");
    
    es_mem_v2_stats_t stats;
    int ret = es_mem_v2_get_stats(&stats);
    assert(ret == 0);
    
    printf("Initial stats:\n");
    printf("  Total: %u, Used: %u, Free: %u\n", 
           stats.total_size, stats.used_size, stats.free_size);
    printf("  Active blocks: %u\n", stats.active_blocks);
    
    // 分配一些内存
    void *ptr1 = es_mem_v2_alloc(100);
    void *ptr2 = es_mem_v2_alloc(200);
    
    ret = es_mem_v2_get_stats(&stats);
    assert(ret == 0);
    assert(stats.active_blocks == 2);
    
    printf("After allocation:\n");
    printf("  Active blocks: %u\n", stats.active_blocks);
    printf("  Alloc count: %u\n", stats.alloc_count);
    
    es_mem_v2_free(ptr1);
    es_mem_v2_free(ptr2);
    
    ret = es_mem_v2_get_stats(&stats);
    assert(ret == 0);
    assert(stats.active_blocks == 0);
    
    printf("After free:\n");
    printf("  Active blocks: %u\n", stats.active_blocks);
    printf("  Free count: %u\n", stats.free_count);
    
    printf("✓ Statistics working correctly\n");
}

static void test_error_conditions(void) {
    printf("\n=== Testing Error Conditions ===\n");
    
    // 测试过大分配
    void *ptr = es_mem_v2_alloc(ES_MEM_V2_MAX_ALLOC + 1);
    assert(ptr == NULL);
    printf("✓ Large allocation rejected\n");
    
    // 测试零大小分配
    ptr = es_mem_v2_alloc(0);
    assert(ptr == NULL);
    printf("✓ Zero size allocation rejected\n");
    
    // 测试无效指针释放
    es_mem_v2_free((void*)0x12345678);
    printf("✓ Invalid pointer free handled\n");
    
    // 测试双重释放
    ptr = es_mem_v2_alloc(100);
    assert(ptr != NULL);
    es_mem_v2_free(ptr);
    es_mem_v2_free(ptr);  // 应该被检测到
    printf("✓ Double free detected\n");
}

int main(void) {
    printf("=== Memory Allocator V2 Test Suite ===\n");
    
    // 初始化
    int ret = es_mem_v2_init();
    assert(ret == 0);
    printf("✓ Memory allocator initialized\n");
    
    // 运行测试
    test_basic_allocation();
    test_alignment();
    test_next_fit_strategy();
    test_calloc_and_realloc();
    test_statistics();
    test_error_conditions();
    
    // 打印最终统计
    printf("\n=== Final Statistics ===\n");
    es_mem_v2_dump_stats();
    es_mem_v2_dump_pools();
    
    // 检查内存泄漏
    int leaks = es_mem_v2_check_leaks();
    if (leaks == 0) {
        printf("✓ No memory leaks detected\n");
    } else {
        printf("⚠ %d memory leaks detected\n", leaks);
    }
    
    // 验证完整性
    ret = es_mem_v2_verify_integrity();
    if (ret == 0) {
        printf("✓ Memory integrity verified\n");
    } else {
        printf("⚠ Memory integrity check failed\n");
    }
    
    // 反初始化
    ret = es_mem_v2_deinit();
    assert(ret == 0);
    printf("✓ Memory allocator deinitialized\n");
    
    printf("\n=== All Tests Passed! ===\n");
    return 0;
}
