# ES MCU Framework 文档中心

欢迎来到 ES MCU Framework 文档中心！这里包含了框架的完整文档，帮助您快速了解和使用框架的各项功能。

## 📚 文档导航

### 🏗️ 核心架构文档

#### [系统架构文档](ES_Framework_Architecture.md)
- 系统整体架构和设计原理
- 模块组织和依赖关系
- 分层架构设计
- 初始化流程和内存布局
- 设计原则和性能指标

#### [API参考手册](ES_API_Reference.md)
- 完整的API接口文档
- 函数参数和返回值说明
- 错误码定义
- 使用注意事项

### 🔧 核心模块文档

#### [协程调度系统](ES_Coroutine_Scheduler.md)
- 无栈协程实现原理
- 协程宏定义和使用方法
- 调度器管理和任务调度
- 定时器和事件系统
- 协程编程最佳实践

#### [配置管理系统](ES_Configuration_Management.md)
- 双扇区存储机制
- 参数分类和版本控制
- 配置API和批量操作
- 参数定义和元数据
- 配置管理最佳实践

#### [通信协议框架](ES_Communication_Framework.md)
- 统一协议处理架构
- ISO-TP协议实现
- UDS诊断服务
- 协议注册和扩展
- 通信协议开发指南

#### [日志系统](ES_Logging_System.md)
- 多级日志和输出方式
- 环形文件系统存储
- 日志查询和AT命令接口
- 远程日志访问
- 日志系统配置和优化

### 🛠️ 工具和平台文档

#### [工具和驱动模块](ES_Utility_Modules.md)
- 内存管理和内存池
- Flash存储和环形文件系统
- 加密算法（AES、DES3、MD5、CRC）
- 电源管理和时间服务
- 工具函数和环形缓冲

#### [平台和接口模块](ES_Platform_Interface.md)
- HC32F460和Win32平台支持
- 硬件驱动接口抽象
- AT命令服务和控制台接口
- 车辆模块和OTA升级
- 平台移植指南

### 📖 开发指南和示例

#### [开发指南](ES_Development_Guide.md)
- 快速开始和环境搭建
- 协程编程指南
- 配置管理最佳实践
- 通信协议开发
- 调试和故障排除
- 性能优化建议

#### [使用示例](ES_Usage_Examples.md)
- 基础应用示例（LED、按键、串口）
- 协程应用示例（多任务、定时器）
- 通信应用示例（CAN、UDS诊断）
- 存储应用示例（配置、日志）
- 综合应用示例（车载终端）

## 🚀 快速开始

如果您是第一次使用 ES MCU Framework，建议按以下顺序阅读文档：

1. **[系统架构文档](ES_Framework_Architecture.md)** - 了解框架整体架构
2. **[开发指南](ES_Development_Guide.md)** - 学习如何搭建开发环境和编写第一个应用
3. **[协程调度系统](ES_Coroutine_Scheduler.md)** - 掌握协程编程的核心概念
4. **[使用示例](ES_Usage_Examples.md)** - 通过实际示例学习框架使用
5. **[API参考手册](ES_API_Reference.md)** - 查阅具体的API接口

## 📋 文档特色

### 🎯 面向实用
- 每个模块都提供完整的使用示例
- 包含最佳实践和常见问题解决方案
- 提供性能优化和调试技巧

### 📖 结构清晰
- 按功能模块组织文档结构
- 从概念到实践的渐进式介绍
- 丰富的代码示例和图表说明

### 🔄 持续更新
- 随框架版本同步更新
- 根据用户反馈持续改进
- 新增功能及时补充文档

## 🤝 文档贡献

我们欢迎社区贡献文档内容！如果您发现文档中的错误或希望补充内容，请：

1. 在项目仓库中提交 Issue 报告问题
2. 提交 Pull Request 贡献改进
3. 通过邮件联系维护团队

## 📞 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获得帮助：

- **文档查阅**: 首先查阅相关模块的文档
- **示例参考**: 查看使用示例中的相关代码
- **问题反馈**: 在项目仓库中提交 Issue
- **技术交流**: 联系维护团队进行技术交流

## 📈 版本说明

当前文档版本对应 ES MCU Framework v1.0.0，包含以下主要内容：

### ✅ 已完成文档
- [x] 系统架构文档
- [x] API参考手册
- [x] 协程调度系统文档
- [x] 配置管理系统文档
- [x] 通信协议框架文档
- [x] 日志系统文档
- [x] 工具和驱动模块文档
- [x] 平台和接口模块文档
- [x] 开发指南
- [x] 使用示例

### 🔄 持续改进
- 根据用户反馈优化文档结构
- 补充更多实际应用场景示例
- 增加故障排除和调试指南
- 完善性能优化建议

## 🏷️ 文档标签

为了方便查找，我们为文档添加了以下标签：

- **🏗️ 架构**: 系统架构和设计相关
- **🔧 核心**: 核心功能模块
- **🛠️ 工具**: 工具和驱动模块
- **📖 指南**: 开发指南和教程
- **💡 示例**: 代码示例和应用案例
- **🔍 参考**: API参考和技术规范

## 📝 文档约定

### 代码示例
- 所有代码示例都经过测试验证
- 使用统一的代码风格和注释规范
- 提供完整的上下文和说明

### 术语定义
- 统一使用框架定义的术语
- 首次出现的术语提供详细解释
- 保持中英文术语的一致性

### 版本兼容
- 明确标注API的版本要求
- 说明不同版本间的差异
- 提供版本升级指导

---

**ES MCU Framework 文档团队**  
*让嵌入式开发更简单、更高效*

📧 联系我们: [<EMAIL>](mailto:<EMAIL>)  
🌐 项目主页: [ES MCU Framework](../README.md)  
📅 最后更新: 2024年1月
