/**
 * @file es_uds.h
 * @brief UDS Client Implementation for OTA Services
 *
 * This module provides a UDS client implementation focused on:
 * - UDS OTA services according to ISO 14229-1
 * - Proper 0x78 NRC handling
 * - Coroutine-based async operations
 * - Uses separate ISO-TP transport layer
 *
 * <AUTHOR> MCU Framework
 * @date 2025-01-21
 * @version 1.0.0
 */

#ifndef ES_UDS_H
#define ES_UDS_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "es.h"
#include "es_coro.h"
#include "es_drv_can.h"
#include "es_ringbuffer.h"
#include "es_log.h"
#include "es_mem.h"
#include "es_drv_os.h"
#include "es_isotp.h"

/* ========================================================================== */
/*                              CONFIGURATION                                */
/* ========================================================================== */

/**
 * @brief UDS debug log level configuration
 *
 * Controls the verbosity of UDS module logging:
 * - 0: No logging (production builds)
 * - 1: Error and critical messages only
 * - 2: Info level (important operations like session changes, transfers)
 * - 3: Debug level (detailed protocol information, responses)
 * - 4: Verbose level (all frame-level details)
 *
 * @note Set this before including es_uds.h or define in build system
 */
#ifndef ES_UDS_LOG_LEVEL
#define ES_UDS_LOG_LEVEL 2  // Default: Info level
#endif

/** Maximum buffer sizes */
#ifndef ES_UDS_MAX_TX_BUFFER_SIZE
#define ES_UDS_MAX_TX_BUFFER_SIZE       512
#endif

#ifndef ES_UDS_MAX_RX_BUFFER_SIZE
#define ES_UDS_MAX_RX_BUFFER_SIZE       512
#endif

/** Legacy compatibility - use the larger of TX/RX for backward compatibility */
#ifndef ES_UDS_MAX_BUFFER_SIZE
#define ES_UDS_MAX_BUFFER_SIZE          ((ES_UDS_MAX_TX_BUFFER_SIZE > ES_UDS_MAX_RX_BUFFER_SIZE) ? ES_UDS_MAX_TX_BUFFER_SIZE : ES_UDS_MAX_RX_BUFFER_SIZE)
#endif

#ifndef ES_ISOTP_RING_BUFFER_SIZE
#define ES_ISOTP_RING_BUFFER_SIZE       8
#endif

/** Timing defaults (milliseconds) */
#define ES_UDS_DEFAULT_P2_TIMEOUT       50
#define ES_UDS_DEFAULT_P2_STAR_TIMEOUT  5000
#define ES_ISOTP_DEFAULT_TIMEOUT        1000

/** Maximum response pending count for 0x78 */
#define ES_UDS_MAX_RESPONSE_PENDING     10

/* ========================================================================== */
/*                               ERROR CODES                                 */
/* ========================================================================== */

/**
 * @brief UDS error codes
 */
typedef enum {
    ES_UDS_OK = 0,                      /**< Success */
    ES_UDS_ERR_INVALID_PARAM = -1,      /**< Invalid parameter */
    ES_UDS_ERR_TIMEOUT = -2,            /**< Timeout occurred */
    ES_UDS_ERR_BUSY = -3,               /**< Resource busy */
    ES_UDS_ERR_NO_MEMORY = -4,          /**< No memory available */
    ES_UDS_ERR_CAN_ERROR = -5,          /**< CAN communication error */
    ES_UDS_ERR_NEGATIVE_RESPONSE = -6,  /**< UDS negative response */
    ES_UDS_ERR_RESPONSE_PENDING = -7    /**< Response pending (0x78) */
} es_uds_error_t;

/* ========================================================================== */
/*                            UDS SERVICE IDS                                */
/* ========================================================================== */

/**
 * @brief UDS Service Identifiers for OTA (ISO 15765-2)
 */
typedef enum {
    ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL = 0x10,      /**< Diagnostic Session Control */
    ES_UDS_SID_ECU_RESET = 0x11,                       /**< ECU Reset */
    ES_UDS_SID_READ_DTC_INFORMATION = 0x19,            /**< Read DTC Information */
    ES_UDS_SID_READ_DATA_BY_ID = 0x22,                 /**< Read Data By Identifier */
    ES_UDS_SID_SECURITY_ACCESS = 0x27,                 /**< Security Access */
    ES_UDS_SID_COMMUNICATION_CONTROL = 0x28,           /**< Communication Control */
    ES_UDS_SID_WRITE_DATA_BY_ID = 0x2E,                /**< Write Data By Identifier */
    ES_UDS_SID_ROUTINE_CONTROL = 0x31,                 /**< Routine Control */
    ES_UDS_SID_REQUEST_DOWNLOAD = 0x34,                /**< Request Download */
    ES_UDS_SID_TRANSFER_DATA = 0x36,                   /**< Transfer Data */
    ES_UDS_SID_REQUEST_TRANSFER_EXIT = 0x37,           /**< Request Transfer Exit */
    ES_UDS_SID_TESTER_PRESENT = 0x3E,                  /**< Tester Present */
    ES_UDS_SID_CONTROL_DTC_SETTING = 0x85              /**< Control DTC Setting */
} es_uds_sid_t;

/**
 * @brief UDS Negative Response Codes
 */
typedef enum {
    ES_UDS_NRC_POSITIVE_RESPONSE = 0x00,               /**< Positive response */
    ES_UDS_NRC_GENERAL_REJECT = 0x10,                  /**< General reject */
    ES_UDS_NRC_SERVICE_NOT_SUPPORTED = 0x11,           /**< Service not supported */
    ES_UDS_NRC_INCORRECT_MESSAGE_LENGTH = 0x13,        /**< Incorrect message length */
    ES_UDS_NRC_BUSY_REPEAT_REQUEST = 0x21,             /**< Busy repeat request */
    ES_UDS_NRC_CONDITIONS_NOT_CORRECT = 0x22,          /**< Conditions not correct */
    ES_UDS_NRC_REQUEST_SEQUENCE_ERROR = 0x24,          /**< Request sequence error */
    ES_UDS_NRC_REQUEST_OUT_OF_RANGE = 0x31,            /**< Request out of range */
    ES_UDS_NRC_SECURITY_ACCESS_DENIED = 0x33,          /**< Security access denied */
    ES_UDS_NRC_RESPONSE_PENDING = 0x78                 /**< Response pending */
} es_uds_nrc_t;

/**
 * @brief UDS Session Types
 */
typedef enum {
    ES_UDS_SESSION_DEFAULT = 0x01,                     /**< Default session */
    ES_UDS_SESSION_PROGRAMMING = 0x02,                 /**< Programming session */
    ES_UDS_SESSION_EXTENDED_DIAGNOSTIC = 0x03          /**< Extended diagnostic session */
} es_uds_session_t;

/**
 * @brief UDS ECU Reset Types
 */
typedef enum {
    ES_UDS_RESET_HARD = 0x01,                              /**< Hard reset */
    ES_UDS_RESET_KEY_OFF_ON = 0x02,                        /**< Key off on reset */
    ES_UDS_RESET_SOFT = 0x03                               /**< Soft reset */
} es_uds_reset_type_t;

/**
 * @brief UDS Communication Control Types
 */
typedef enum {
    ES_UDS_COMM_CTRL_ENABLE_RX_TX = 0x00,                  /**< Enable Rx and Tx */
    ES_UDS_COMM_CTRL_ENABLE_RX_DISABLE_TX = 0x01,          /**< Enable Rx and disable Tx */
    ES_UDS_COMM_CTRL_DISABLE_RX_ENABLE_TX = 0x02,          /**< Disable Rx and enable Tx */
    ES_UDS_COMM_CTRL_DISABLE_RX_TX = 0x03                  /**< Disable Rx and Tx */
} es_uds_communication_control_type_t;

/**
 * @brief UDS Control DTC Setting Types
 */
typedef enum {
    ES_UDS_DTC_SETTING_ON = 0x01,                          /**< DTC setting on */
    ES_UDS_DTC_SETTING_OFF = 0x02                          /**< DTC setting off */
} es_uds_control_dtc_setting_type_t;

/**
 * @brief UDS Read DTC Information Sub-functions
 */
typedef enum {
    ES_UDS_DTC_REPORT_NUMBER_OF_DTC_BY_STATUS_MASK = 0x01,         /**< Report number of DTC by status mask */
    ES_UDS_DTC_REPORT_DTC_BY_STATUS_MASK = 0x02,                   /**< Report DTC by status mask */
    ES_UDS_DTC_REPORT_DTC_SNAPSHOT_IDENTIFICATION = 0x03,          /**< Report DTC snapshot identification */
    ES_UDS_DTC_REPORT_DTC_SNAPSHOT_RECORD_BY_DTC_NUMBER = 0x04,    /**< Report DTC snapshot record by DTC number */
    ES_UDS_DTC_REPORT_DTC_SNAPSHOT_RECORD_BY_RECORD_NUMBER = 0x05, /**< Report DTC snapshot record by record number */
    ES_UDS_DTC_REPORT_DTC_EXTENDED_DATA_RECORD_BY_DTC_NUMBER = 0x06, /**< Report DTC extended data record by DTC number */
    ES_UDS_DTC_REPORT_NUMBER_OF_DTC_BY_SEVERITY_MASK_RECORD = 0x07, /**< Report number of DTC by severity mask record */
    ES_UDS_DTC_REPORT_DTC_BY_SEVERITY_MASK_RECORD = 0x08,          /**< Report DTC by severity mask record */
    ES_UDS_DTC_REPORT_SEVERITY_INFORMATION_OF_DTC = 0x09,          /**< Report severity information of DTC */
    ES_UDS_DTC_REPORT_SUPPORTED_DTC = 0x0A,                        /**< Report supported DTC */
    ES_UDS_DTC_REPORT_FIRST_TEST_FAILED_DTC = 0x0B,                /**< Report first test failed DTC */
    ES_UDS_DTC_REPORT_FIRST_CONFIRMED_DTC = 0x0C,                  /**< Report first confirmed DTC */
    ES_UDS_DTC_REPORT_MOST_RECENT_TEST_FAILED_DTC = 0x0D,          /**< Report most recent test failed DTC */
    ES_UDS_DTC_REPORT_MOST_RECENT_CONFIRMED_DTC = 0x0E,            /**< Report most recent confirmed DTC */
    ES_UDS_DTC_REPORT_MIRROR_MEMORY_DTC_BY_STATUS_MASK = 0x0F,     /**< Report mirror memory DTC by status mask */
    ES_UDS_DTC_REPORT_MIRROR_MEMORY_DTC_EXTENDED_DATA_RECORD_BY_DTC_NUMBER = 0x10, /**< Report mirror memory DTC extended data record by DTC number */
    ES_UDS_DTC_REPORT_NUMBER_OF_MIRROR_MEMORY_DTC_BY_STATUS_MASK = 0x11, /**< Report number of mirror memory DTC by status mask */
    ES_UDS_DTC_REPORT_NUMBER_OF_EMISSIONS_OBD_DTC_BY_STATUS_MASK = 0x12, /**< Report number of emissions OBD DTC by status mask */
    ES_UDS_DTC_REPORT_EMISSIONS_OBD_DTC_BY_STATUS_MASK = 0x13      /**< Report emissions OBD DTC by status mask */
} es_uds_read_dtc_sub_function_t;

/**
 * @brief UDS DTC Status Mask Bits
 */
typedef enum {
    ES_UDS_DTC_STATUS_TEST_FAILED = 0x01,                          /**< Test failed */
    ES_UDS_DTC_STATUS_TEST_FAILED_THIS_OPERATION_CYCLE = 0x02,     /**< Test failed this operation cycle */
    ES_UDS_DTC_STATUS_PENDING_DTC = 0x04,                          /**< Pending DTC */
    ES_UDS_DTC_STATUS_CONFIRMED_DTC = 0x08,                        /**< Confirmed DTC */
    ES_UDS_DTC_STATUS_TEST_NOT_COMPLETED_SINCE_LAST_CLEAR = 0x10,  /**< Test not completed since last clear */
    ES_UDS_DTC_STATUS_TEST_FAILED_SINCE_LAST_CLEAR = 0x20,         /**< Test failed since last clear */
    ES_UDS_DTC_STATUS_TEST_NOT_COMPLETED_THIS_OPERATION_CYCLE = 0x40, /**< Test not completed this operation cycle */
    ES_UDS_DTC_STATUS_WARNING_INDICATOR_REQUESTED = 0x80           /**< Warning indicator requested */
} es_uds_dtc_status_mask_t;



/* ========================================================================== */
/*                            CONNECTION STRUCTURES                          */
/* ========================================================================== */



/**
 * @brief UDS client connection structure
 */
typedef struct {
    /* ISO-TP connection */
    es_isotp_connection_t isotp;                       /**< ISO-TP connection */

    uint32_t functional_tx_id;                          /**< Functional transmit ID */

    /* UDS state management */
    es_uds_session_t current_session;                  /**< Current UDS session */
    uint8_t current_service;                           /**< Current service ID */
    uint8_t response_pending_count;                    /**< 0x78 response count */
    uint32_t p2_timeout;                               /**< P2 timeout */
    uint32_t p2_star_timeout;                          /**< P2* timeout */
    uint32_t request_start_time;                       /**< Request start time */
    bool waiting_for_response;                         /**< Waiting for response flag */

    /* Last response data */
    uint8_t last_nrc;                                  /**< Last negative response code */
    /* Note: response_length is now shared with isotp.rx_length */

    /* Request buffer (shared with ISO-TP tx_buffer) */
    /* Note: request_length is now shared with isotp.tx_length */

    /* Transfer parameters */
    uint16_t max_transfer_block_size;                  /**< Maximum transfer block size from server */
    uint8_t block_sequence_counter;                    /**< Block sequence counter */
    uint32_t transfer_offset;                          /**< Current transfer offset (total bytes transferred) */

    /* UDS wait response state variables */
    /* Note: wait_response_length is now shared with isotp.rx_length */
    uint32_t wait_current_timeout;                     /**< Current timeout during wait */
    bool wait_using_p2_star;                           /**< Using P2* timeout flag */

    /* UDS buffers (shared with ISO-TP) */
    uint8_t tx_buffer[ES_UDS_MAX_TX_BUFFER_SIZE];      /**< Transmit buffer (shared with ISO-TP) */
    uint8_t rx_buffer[ES_UDS_MAX_RX_BUFFER_SIZE];      /**< Receive buffer (shared with ISO-TP) */
} es_uds_connection_t;

/* ========================================================================== */
/*                             UDS CALLBACKS                                 */
/* ========================================================================== */

/**
 * @brief Transfer data read callback function type
 * @param offset Current offset (total bytes already read)
 * @param buffer Buffer to store the read data
 * @param max_length Maximum length that can be read
 * @param user_data User-defined data pointer
 * @return Number of bytes read, 0 indicates end of transfer
 *
 * @example
 * uint16_t my_data_reader(uint32_t offset, uint8_t *buffer,
 *                         uint16_t max_len, void *user_data) {
 *     // Read data from file, flash, or other source starting at offset
 *     // Return 0 when no more data available
 *     return bytes_read;
 * }
 */
typedef uint16_t (*es_uds_transfer_data_read_callback_t)(uint32_t offset,
                                                         uint8_t *buffer,
                                                         uint16_t max_length,
                                                         void *user_data);

/**
 * @brief Security access key generation callback function type
 * @param level Security level (same as used in request)
 * @param seed Pointer to seed data received from ECU
 * @param seed_length Length of seed data
 * @param key_buffer Buffer to store the generated key
 * @param max_key_length Maximum length of key buffer
 * @param user_data User-defined data pointer
 * @return Length of generated key, 0 indicates failure
 *
 * @example
 * uint16_t my_key_generator(uint8_t level, const uint8_t *seed, uint16_t seed_length,
 *                          uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
 *     // Generate key based on seed using your algorithm
 *     // Return actual key length, or 0 on failure
 *     return key_length;
 * }
 */
typedef uint16_t (*es_uds_security_access_key_callback_t)(uint8_t level,
                                                          const uint8_t *seed,
                                                          uint16_t seed_length,
                                                          uint8_t *key_buffer,
                                                          uint16_t max_key_length,
                                                          void *user_data);

/* ========================================================================== */
/*                             UDS CALLBACKS                                 */
/* ========================================================================== */

/* ========================================================================== */
/*                             UDS MACROS                                    */
/* ========================================================================== */

/**
 * @brief Access the shared request/transmit buffer
 * @note This buffer is shared between UDS request preparation and ISO-TP transmission
 */
#define UDS_REQUEST_BUFFER(conn) ((conn)->isotp.tx_buffer)

/**
 * @brief Access the shared request/transmit length
 * @note This length is shared between UDS request preparation and ISO-TP transmission
 */
#define UDS_REQUEST_LENGTH(conn) ((conn)->isotp.tx_length)

/**
 * @brief Access the shared response/receive buffer
 * @note This buffer is shared between ISO-TP reception and UDS response processing
 */
#define UDS_RESPONSE_BUFFER(conn) ((conn)->isotp.rx_buffer)

/**
 * @brief Access the shared response/receive length
 * @note This length is shared between ISO-TP reception and UDS response processing
 */
#define UDS_RESPONSE_LENGTH(conn) ((conn)->isotp.rx_length)

/**
 * @brief Access the data portion of Read Data By Identifier response
 * @param conn Pointer to UDS connection
 * @note This macro returns a pointer to the actual data after the UDS response header
 *       Response format: [0x62][DID_high][DID_low][data...]
 *       This macro points to the [data...] portion
 * @warning Only valid after successful es_uds_read_data_by_identifier call
 */
#define UDS_READ_DATA_BY_ID_DATA(conn) (&UDS_RESPONSE_BUFFER(conn)[3])

/**
 * @brief Get the length of data portion in Read Data By Identifier response
 * @param conn Pointer to UDS connection
 * @note This macro returns the length of actual data (excluding the 3-byte header)
 *       Response format: [0x62][DID_high][DID_low][data...]
 *       This macro returns the length of [data...] portion
 * @warning Only valid after successful es_uds_read_data_by_identifier call
 * @return Data length in bytes, or 0 if response is too short
 */
#define UDS_READ_DATA_BY_ID_LENGTH(conn) \
    ((UDS_RESPONSE_LENGTH(conn) > 3) ? (UDS_RESPONSE_LENGTH(conn) - 3) : 0)

/**
 * @brief Get the Data Identifier from Read Data By Identifier response
 * @param conn Pointer to UDS connection
 * @note This macro extracts the 16-bit DID from the response
 *       Response format: [0x62][DID_high][DID_low][data...]
 * @warning Only valid after successful es_uds_read_data_by_identifier call
 * @return 16-bit Data Identifier, or 0 if response is too short
 */
#define UDS_READ_DATA_BY_ID_DID(conn) \
    ((UDS_RESPONSE_LENGTH(conn) >= 3) ? \
     ((uint16_t)(UDS_RESPONSE_BUFFER(conn)[1] << 8) | UDS_RESPONSE_BUFFER(conn)[2]) : 0)

/**
 * @brief Get the Data Identifier from Write Data By Identifier response
 * @param conn Pointer to UDS connection
 * @note This macro extracts the 16-bit DID from the response
 *       Response format: [0x6E][DID_high][DID_low]
 * @warning Only valid after successful es_uds_write_data_by_identifier call
 * @return 16-bit Data Identifier, or 0 if response is too short
 */
#define UDS_WRITE_DATA_BY_ID_DID(conn) \
    ((UDS_RESPONSE_LENGTH(conn) >= 3) ? \
     ((uint16_t)(UDS_RESPONSE_BUFFER(conn)[1] << 8) | UDS_RESPONSE_BUFFER(conn)[2]) : 0)

/**
 * @brief Get the sub-function from Read DTC Information response
 * @param conn Pointer to UDS connection
 * @note Response format: [0x59][sub_function][status_availability_mask][data...]
 * @warning Only valid after successful es_uds_read_dtc_information call
 * @return Sub-function value, or 0 if response is too short
 */
#define UDS_READ_DTC_SUB_FUNCTION(conn) \
    ((UDS_RESPONSE_LENGTH(conn) >= 2) ? UDS_RESPONSE_BUFFER(conn)[1] : 0)

/**
 * @brief Get the status availability mask from Read DTC Information response
 * @param conn Pointer to UDS connection
 * @note Response format: [0x59][sub_function][status_availability_mask][data...]
 * @warning Only valid after successful es_uds_read_dtc_information call
 * @return Status availability mask, or 0 if response is too short
 */
#define UDS_READ_DTC_STATUS_AVAILABILITY_MASK(conn) \
    ((UDS_RESPONSE_LENGTH(conn) >= 3) ? UDS_RESPONSE_BUFFER(conn)[2] : 0)

/**
 * @brief Get the number of DTCs from Read DTC Information response (sub-function 0x01)
 * @param conn Pointer to UDS connection
 * @note Response format: [0x59][0x01][status_availability_mask][DTC_format_id][DTC_count_high][DTC_count_low]
 * @warning Only valid after successful es_uds_read_dtc_information call with sub-function 0x01
 * @return Number of DTCs, or 0 if response is too short
 */
#define UDS_READ_DTC_COUNT(conn) \
    ((UDS_RESPONSE_LENGTH(conn) >= 6) ? \
     ((uint16_t)(UDS_RESPONSE_BUFFER(conn)[4] << 8) | UDS_RESPONSE_BUFFER(conn)[5]) : 0)

/**
 * @brief Get the DTC format identifier from Read DTC Information response
 * @param conn Pointer to UDS connection
 * @note Response format: [0x59][sub_function][status_availability_mask][DTC_format_id][...]
 * @warning Only valid after successful es_uds_read_dtc_information call
 * @return DTC format identifier, or 0 if response is too short
 */
#define UDS_READ_DTC_FORMAT_ID(conn) \
    ((UDS_RESPONSE_LENGTH(conn) >= 4) ? UDS_RESPONSE_BUFFER(conn)[3] : 0)

/**
 * @brief Get pointer to DTC data from Read DTC Information response (sub-function 0x02)
 * @param conn Pointer to UDS connection
 * @note Response format: [0x59][0x02][status_availability_mask][DTC_and_status_record...]
 *       Each DTC record: [DTC_high][DTC_mid][DTC_low][status_mask]
 * @warning Only valid after successful es_uds_read_dtc_information call with sub-function 0x02
 * @return Pointer to DTC data, or NULL if response is too short
 */
#define UDS_READ_DTC_DATA(conn) \
    ((UDS_RESPONSE_LENGTH(conn) > 3) ? &UDS_RESPONSE_BUFFER(conn)[3] : NULL)

/**
 * @brief Get the length of DTC data from Read DTC Information response
 * @param conn Pointer to UDS connection
 * @note This returns the length of DTC data portion (excluding header)
 * @warning Only valid after successful es_uds_read_dtc_information call
 * @return Length of DTC data in bytes, or 0 if response is too short
 */
#define UDS_READ_DTC_DATA_LENGTH(conn) \
    ((UDS_RESPONSE_LENGTH(conn) > 3) ? (UDS_RESPONSE_LENGTH(conn) - 3) : 0)

/**
 * @brief Extract DTC number from DTC record
 * @param dtc_record Pointer to 4-byte DTC record [DTC_high][DTC_mid][DTC_low][status_mask]
 * @return 24-bit DTC number
 */
#define UDS_DTC_EXTRACT_NUMBER(dtc_record) \
    (((uint32_t)(dtc_record)[0] << 16) | ((uint32_t)(dtc_record)[1] << 8) | (dtc_record)[2])

/**
 * @brief Extract status mask from DTC record
 * @param dtc_record Pointer to 4-byte DTC record [DTC_high][DTC_mid][DTC_low][status_mask]
 * @return 8-bit status mask
 */
#define UDS_DTC_EXTRACT_STATUS(dtc_record) ((dtc_record)[3])

/**
 * @brief Get the number of DTC records in response (sub-function 0x02)
 * @param conn Pointer to UDS connection
 * @note Each DTC record is 4 bytes: [DTC_high][DTC_mid][DTC_low][status_mask]
 * @warning Only valid after successful es_uds_read_dtc_information call with sub-function 0x02
 * @return Number of DTC records, or 0 if response is too short
 */
#define UDS_READ_DTC_RECORD_COUNT(conn) \
    ((UDS_RESPONSE_LENGTH(conn) > 3) ? ((UDS_RESPONSE_LENGTH(conn) - 3) / 4) : 0)

/**
 * @brief Check if a DTC status bit is set
 * @param status DTC status mask
 * @param bit Status bit to check (see es_uds_dtc_status_mask_t)
 * @return true if bit is set, false otherwise
 */
#define UDS_DTC_STATUS_IS_SET(status, bit) (((status) & (bit)) != 0)

/**
 * @brief Check if DTC is currently failed
 * @param status DTC status mask
 * @return true if DTC test failed, false otherwise
 */
#define UDS_DTC_IS_FAILED(status) UDS_DTC_STATUS_IS_SET(status, ES_UDS_DTC_STATUS_TEST_FAILED)

/**
 * @brief Check if DTC is confirmed
 * @param status DTC status mask
 * @return true if DTC is confirmed, false otherwise
 */
#define UDS_DTC_IS_CONFIRMED(status) UDS_DTC_STATUS_IS_SET(status, ES_UDS_DTC_STATUS_CONFIRMED_DTC)

/**
 * @brief Check if DTC is pending
 * @param status DTC status mask
 * @return true if DTC is pending, false otherwise
 */
#define UDS_DTC_IS_PENDING(status) UDS_DTC_STATUS_IS_SET(status, ES_UDS_DTC_STATUS_PENDING_DTC)

/**
 * @brief Check if DTC warning indicator is requested
 * @param status DTC status mask
 * @return true if warning indicator is requested, false otherwise
 */
#define UDS_DTC_IS_WARNING_INDICATOR_REQUESTED(status) \
    UDS_DTC_STATUS_IS_SET(status, ES_UDS_DTC_STATUS_WARNING_INDICATOR_REQUESTED)

/**
 * @brief Convert DTC number to ASCII string format
 * @param dtc_number 24-bit DTC number
 * @param buffer Pointer to buffer for ASCII string (minimum 6 bytes)
 * @note Converts DTC number to standard format: P0XXX, B0XXX, C0XXX, U0XXX
 *       First character indicates system: P=Powertrain, B=Body, C=Chassis, U=Network
 *       Remaining 4 characters are hexadecimal digits
 * @example
 *   uint32_t dtc = 0x010203;
 *   char dtc_str[6];
 *   UDS_DTC_TO_ASCII(dtc, dtc_str);
 *   // Result: dtc_str = "P0203"
 */
#define UDS_DTC_TO_ASCII(dtc_number, buffer) do { \
    uint32_t dtc = (dtc_number) & 0xFFFFFF; \
    uint8_t system = (dtc >> 14) & 0x03; \
    uint16_t code = dtc & 0x3FFF; \
    (buffer)[0] = (system == 0) ? 'P' : (system == 1) ? 'C' : (system == 2) ? 'B' : 'U'; \
    (buffer)[1] = '0' + ((code >> 12) & 0x0F); \
    (buffer)[2] = ((code >> 8) & 0x0F) < 10 ? '0' + ((code >> 8) & 0x0F) : 'A' + ((code >> 8) & 0x0F) - 10; \
    (buffer)[3] = ((code >> 4) & 0x0F) < 10 ? '0' + ((code >> 4) & 0x0F) : 'A' + ((code >> 4) & 0x0F) - 10; \
    (buffer)[4] = (code & 0x0F) < 10 ? '0' + (code & 0x0F) : 'A' + (code & 0x0F) - 10; \
    (buffer)[5] = '\0'; \
} while(0)

/**
 * @brief Convert DTC number to ASCII string format (alternative implementation)
 * @param dtc_number 24-bit DTC number
 * @param buffer Pointer to buffer for ASCII string (minimum 8 bytes)
 * @note Converts DTC number to full hexadecimal format: XXXXXX
 *       All 6 hexadecimal digits of the DTC number
 * @example
 *   uint32_t dtc = 0x123456;
 *   char dtc_str[8];
 *   UDS_DTC_TO_HEX_ASCII(dtc, dtc_str);
 *   // Result: dtc_str = "123456"
 */
#define UDS_DTC_TO_HEX_ASCII(dtc_number, buffer) do { \
    uint32_t dtc = (dtc_number) & 0xFFFFFF; \
    for (int i = 5; i >= 0; i--) { \
        uint8_t nibble = (dtc >> (i * 4)) & 0x0F; \
        (buffer)[5 - i] = nibble < 10 ? '0' + nibble : 'A' + nibble - 10; \
    } \
    (buffer)[6] = '\0'; \
} while(0)

/**
 * @brief Convert DTC record directly to ASCII string format
 * @param dtc_record Pointer to 4-byte DTC record [DTC_high][DTC_mid][DTC_low][status_mask]
 * @param buffer Pointer to buffer for ASCII string (minimum 6 bytes)
 * @note Combines UDS_DTC_EXTRACT_NUMBER and UDS_DTC_TO_ASCII for convenience
 * @example
 *   uint8_t dtc_record[4] = {0x01, 0x02, 0x03, 0x08};
 *   char dtc_str[6];
 *   UDS_DTC_RECORD_TO_ASCII(dtc_record, dtc_str);
 *   // Result: dtc_str = "P0203"
 */
#define UDS_DTC_RECORD_TO_ASCII(dtc_record, buffer) \
    UDS_DTC_TO_ASCII(UDS_DTC_EXTRACT_NUMBER(dtc_record), buffer)

/**
 * @brief Convert DTC record directly to hexadecimal ASCII string format
 * @param dtc_record Pointer to 4-byte DTC record [DTC_high][DTC_mid][DTC_low][status_mask]
 * @param buffer Pointer to buffer for ASCII string (minimum 8 bytes)
 * @note Combines UDS_DTC_EXTRACT_NUMBER and UDS_DTC_TO_HEX_ASCII for convenience
 * @example
 *   uint8_t dtc_record[4] = {0x12, 0x34, 0x56, 0x08};
 *   char dtc_str[8];
 *   UDS_DTC_RECORD_TO_HEX_ASCII(dtc_record, dtc_str);
 *   // Result: dtc_str = "123456"
 */
#define UDS_DTC_RECORD_TO_HEX_ASCII(dtc_record, buffer) \
    UDS_DTC_TO_HEX_ASCII(UDS_DTC_EXTRACT_NUMBER(dtc_record), buffer)

/* ========================================================================== */
/*                            FUNCTION DECLARATIONS                          */
/* ========================================================================== */

/**
 * @brief Initialize UDS module
 * @return ES_UDS_OK on success, error code on failure
 */
es_uds_error_t es_uds_init(void);

/**
 * @brief Initialize UDS connection
 * @param conn Pointer to UDS connection structure
 * @param tx_id Transmit CAN ID
 * @param rx_id Receive CAN ID
 * @param functional_tx_id Functional address for broadcast requests (0 to disable)
 * @return ES_UDS_OK on success, error code on failure
 */
es_uds_error_t es_uds_connection_init(es_uds_connection_t *conn, uint32_t tx_id, uint32_t rx_id, uint32_t functional_tx_id);

/**
 * @brief Process incoming CAN message
 * @param conn Pointer to UDS connection
 * @param msg Pointer to CAN message
 * @return ES_UDS_OK on success, error code on failure
 */
es_uds_error_t es_uds_process_can_message(es_uds_connection_t *conn, const es_can_msg_t *msg);



/* ========================================================================== */
/*                             UDS FUNCTIONS                                 */
/* ========================================================================== */



/* ========================================================================== */
/*                           UDS OTA SERVICES                                */
/* ========================================================================== */

/**
 * @brief Diagnostic Session Control (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param session_type Session type
 * @return Coroutine return value
 * @note Automatically parses P2 and P2* timing parameters from ECU response
 *       and updates conn->p2_timeout and conn->p2_star_timeout accordingly
 */
es_async_t es_uds_diagnostic_session_control(es_coro_t *coro, es_uds_connection_t *conn, es_uds_session_t session_type);

/**
 * @brief ECU Reset (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param reset_type Reset type (see es_uds_reset_type_t)
 * @return Coroutine return value
 */
es_async_t es_uds_ecu_reset(es_coro_t *coro, es_uds_connection_t *conn, es_uds_reset_type_t reset_type);

/**
 * @brief Security Access (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param level Security level (must be odd, e.g., 1, 3, 5, etc.)
 * @param key_callback Callback function to generate key from seed
 * @param user_data User data passed to callback
 * @return Coroutine return value
 *
 * @note This function performs a complete security access sequence:
 *       1. Requests seed using the provided odd level
 *       2. Calls the callback to generate key from the received seed
 *       3. Sends the generated key using level + 1 (even level)
 */
es_async_t es_uds_security_access(es_coro_t *coro, es_uds_connection_t *conn,
                                     uint8_t level, es_uds_security_access_key_callback_t key_callback,
                                     void *user_data);

/**
 * @brief Tester Present (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param suppress_response Suppress positive response flag
 * @return Coroutine return value
 */
es_async_t es_uds_tester_present(es_coro_t *coro, es_uds_connection_t *conn, bool suppress_response);

/**
 * @brief Request Download (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param data_format_id Data format identifier
 * @param address_length_format Address and length format
 * @param memory_address Memory address
 * @param memory_size Memory size
 * @return Coroutine return value
 * @note This function parses the server response to extract the maximum
 *       transfer block size and stores it in conn->max_transfer_block_size
 *       for use by subsequent es_uds_transfer_data calls
 */
es_async_t es_uds_request_download(es_coro_t *coro, es_uds_connection_t *conn,
                                      uint8_t data_format_id, uint8_t address_length_format,
                                      uint32_t memory_address, uint32_t memory_size);

/**
 * @brief Transfer Data (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param read_callback Callback function to read transfer data
 * @param user_data User-defined data pointer passed to callback
 * @return Coroutine return value
 * @note The function will loop until the callback returns 0 bytes
 *       The max_length parameter passed to the callback is limited by the
 *       maximum transfer block size obtained from es_uds_request_download
 *
 * @example Usage:
 * typedef struct {
 *     const uint8_t *data;
 *     uint32_t total_size;
 * } transfer_context_t;
 *
 * uint16_t my_transfer_reader(uint32_t offset, uint8_t *buffer,
 *                             uint16_t max_len, void *user_data) {
 *     transfer_context_t *ctx = (transfer_context_t*)user_data;
 *     uint32_t remaining = ctx->total_size - offset;
 *     if (remaining == 0) return 0;  // End of transfer
 *
 *     uint16_t to_copy = (remaining > max_len) ? max_len : remaining;
 *     memcpy(buffer, &ctx->data[offset], to_copy);
 *     return to_copy;
 * }
 *
 * // Usage:
 * transfer_context_t ctx = {firmware_data, firmware_size};
 * es_co_await_ex(err, es_uds_transfer_data, conn, my_transfer_reader, &ctx);
 */
es_async_t es_uds_transfer_data(es_coro_t *coro, es_uds_connection_t *conn,
                                   es_uds_transfer_data_read_callback_t read_callback, void *user_data);

/**
 * @brief Request Transfer Exit (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @return Coroutine return value
 */
es_async_t es_uds_request_transfer_exit(es_coro_t *coro, es_uds_connection_t *conn);

/**
 * @brief Routine Control (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param routine_control_type Routine control type (0x01=start, 0x02=stop, 0x03=request results)
 * @param routine_id Routine identifier
 * @param data Pointer to routine data (can be NULL)
 * @param length Data length
 * @return Coroutine return value
 */
es_async_t es_uds_routine_control(es_coro_t *coro, es_uds_connection_t *conn,
                                     uint8_t routine_control_type, uint16_t routine_id,
                                     const uint8_t *data, uint16_t length);

/**
 * @brief Read Data By Identifier (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param data_identifier Data identifier (16-bit)
 * @return Coroutine return value
 * @note Response data is available in UDS_RESPONSE_BUFFER(conn) after successful completion
 *       Response format: [0x62][DID_high][DID_low][data...]
 *       Use UDS_READ_DATA_BY_ID_DATA(conn) and UDS_READ_DATA_BY_ID_LENGTH(conn)
 *       macros to access the data portion without additional buffers
 *
 * @example Usage:
 * es_co_await_ex(err, es_uds_read_data_by_identifier, &conn, 0xF190);
 * uint8_t *vin_data = UDS_READ_DATA_BY_ID_DATA(&conn);
 * uint16_t vin_length = UDS_READ_DATA_BY_ID_LENGTH(&conn);
 * uint16_t did = UDS_READ_DATA_BY_ID_DID(&conn);
 * ES_PRINTF_I("VIN", "DID=0x%04X, Length=%d, Data=%.*s", did, vin_length, vin_length, vin_data);
 */
es_async_t es_uds_read_data_by_identifier(es_coro_t *coro, es_uds_connection_t *conn,
                                             uint16_t data_identifier);

/**
 * @brief Write Data By Identifier (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param data_identifier Data identifier (16-bit)
 * @param data Pointer to data to write
 * @param data_length Length of data to write
 * @return Coroutine return value
 * @note Response data is available in UDS_RESPONSE_BUFFER(conn) after successful completion
 *       Response format: [0x6E][DID_high][DID_low]
 *       Use UDS_WRITE_DATA_BY_ID_DID(conn) macro to verify the DID in response
 *
 * @example Usage:
 * uint8_t config_data[] = {0x01, 0x02, 0x03, 0x04};
 * es_co_await_ex(err, es_uds_write_data_by_identifier, &conn, 0xF190, config_data, sizeof(config_data));
 * uint16_t response_did = UDS_WRITE_DATA_BY_ID_DID(&conn);
 * ES_PRINTF_I("WRITE", "Write DID=0x%04X success, response DID=0x%04X", 0xF190, response_did);
 */
es_async_t es_uds_write_data_by_identifier(es_coro_t *coro, es_uds_connection_t *conn,
                                              uint16_t data_identifier, const uint8_t *data, uint16_t data_length);

/**
 * @brief Communication Control (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param control_type Communication control type (see es_uds_communication_control_type_t)
 * @param communication_type Communication type parameter (optional, can be 0)
 * @return Coroutine return value
 */
es_async_t es_uds_communication_control(es_coro_t *coro, es_uds_connection_t *conn,
                                           es_uds_communication_control_type_t control_type,
                                           uint8_t communication_type);

/**
 * @brief Control DTC Setting (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param dtc_setting_type DTC setting type (see es_uds_control_dtc_setting_type_t)
 * @param dtc_setting_control_option_record DTC setting control option record (optional, can be NULL)
 * @param record_length Length of the control option record
 * @return Coroutine return value
 */
es_async_t es_uds_control_dtc_setting(es_coro_t *coro, es_uds_connection_t *conn,
                                         es_uds_control_dtc_setting_type_t dtc_setting_type,
                                         const uint8_t *dtc_setting_control_option_record,
                                         uint16_t record_length);

/**
 * @brief Read DTC Information (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to UDS connection
 * @param sub_function DTC sub-function (see es_uds_read_dtc_sub_function_t)
 * @param dtc_status_mask DTC status mask (for sub-functions that require it)
 * @param dtc_mask_record Additional DTC mask record (optional, can be NULL)
 * @param record_length Length of the DTC mask record
 * @return Coroutine return value
 * @note Response data is available through UDS_READ_DTC_* macros after successful completion
 *       Common sub-functions:
 *       - 0x01: Report number of DTC by status mask
 *       - 0x02: Report DTC by status mask
 *       - 0x0A: Report supported DTC
 *
 * @example Usage for reading DTC count:
 * es_co_await_ex(err, es_uds_read_dtc_information, &conn,
 *                ES_UDS_DTC_REPORT_NUMBER_OF_DTC_BY_STATUS_MASK, 0xFF, NULL, 0);
 * uint16_t dtc_count = UDS_READ_DTC_COUNT(&conn);
 *
 * @example Usage for reading DTCs:
 * es_co_await_ex(err, es_uds_read_dtc_information, &conn,
 *                ES_UDS_DTC_REPORT_DTC_BY_STATUS_MASK, 0xFF, NULL, 0);
 * uint8_t *dtc_data = UDS_READ_DTC_DATA(&conn);
 * uint16_t record_count = UDS_READ_DTC_RECORD_COUNT(&conn);
 * for (int i = 0; i < record_count; i++) {
 *     uint8_t *record = &dtc_data[i * 4];
 *     uint32_t dtc_num = UDS_DTC_EXTRACT_NUMBER(record);
 *     uint8_t status = UDS_DTC_EXTRACT_STATUS(record);
 *     char dtc_ascii[6];
 *     UDS_DTC_RECORD_TO_ASCII(record, dtc_ascii);
 *     ES_PRINTF_I("DTC", "DTC: %s (0x%06X), Status: 0x%02X", dtc_ascii, dtc_num, status);
 * }
 */
es_async_t es_uds_read_dtc_information(es_coro_t *coro, es_uds_connection_t *conn,
                                          es_uds_read_dtc_sub_function_t sub_function,
                                          uint8_t dtc_status_mask,
                                          const uint8_t *dtc_mask_record,
                                          uint16_t record_length);

// /* ========================================================================== */
// /*                            UTILITY FUNCTIONS                              */
// /* ========================================================================== */

// /**
//  * @brief Check if error code indicates success
//  * @param error Error code
//  * @return true if success, false otherwise
//  */
// static inline bool es_uds_is_success(es_uds_error_t error) {
//     return error == ES_UDS_OK;
// }

// /**
//  * @brief Get string representation of error code
//  * @param error Error code
//  * @return Error string
//  */
// const char* es_uds_error_to_string(es_uds_error_t error);

// /**
//  * @brief Get string representation of NRC
//  * @param nrc Negative response code
//  * @return NRC string
//  */
// const char* es_uds_nrc_to_string(uint8_t nrc);

#ifdef __cplusplus
}
#endif

#endif /* ES_UDS_H */