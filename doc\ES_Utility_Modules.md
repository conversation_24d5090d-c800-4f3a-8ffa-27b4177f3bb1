# ES 工具和驱动模块文档

## 1. 概述

ES 工具和驱动模块为 ES MCU Framework 提供了基础的系统服务和硬件抽象功能。这些模块包括内存管理、存储系统、加密模块、电源管理、时间服务等核心组件，为上层应用提供了稳定可靠的基础支撑。

### 1.1 模块分类

- **内存管理**: es_mem - 内存分配和池管理
- **存储系统**: es_flash, ringfs - Flash存储和文件系统
- **加密模块**: es_aes, es_des3, es_md5, es_crc - 各种加密算法
- **电源管理**: es_pm - 低功耗和电源状态管理
- **时间服务**: es_time - 时间戳和时间管理
- **工具函数**: es_utils - 通用工具函数
- **按键处理**: es_button - 按键检测和处理
- **环形缓冲**: es_ringbuffer, es_ringobj - 环形缓冲区实现

## 2. 内存管理模块 (es_mem)

### 2.1 功能特性

- **内存池管理**: 支持固定大小的内存池分配
- **动态分配**: 提供类似malloc/free的动态内存分配
- **内存统计**: 实时监控内存使用情况
- **内存对齐**: 支持内存对齐分配
- **碎片管理**: 减少内存碎片化

### 2.2 主要API

```c
// 内存管理初始化
int es_mem_init(void);

// 动态内存分配
void *es_malloc(size_t size);
void *es_calloc(size_t num, size_t size);
void *es_realloc(void *ptr, size_t size);
void es_free(void *ptr);

// 内存池操作
es_mem_pool_t *es_mem_pool_create(size_t block_size, size_t block_count);
void *es_mem_pool_alloc(es_mem_pool_t *pool);
void es_mem_pool_free(es_mem_pool_t *pool, void *ptr);
void es_mem_pool_destroy(es_mem_pool_t *pool);

// 内存统计
void es_mem_get_stats(es_mem_stats_t *stats);
size_t es_mem_get_free_size(void);
size_t es_mem_get_used_size(void);
```

### 2.3 使用示例

```c
// 动态内存分配示例
void memory_example(void) {
    // 分配内存
    char *buffer = es_malloc(256);
    if (buffer != NULL) {
        strcpy(buffer, "Hello, World!");
        ES_PRINTF_I("MEM", "Allocated buffer: %s", buffer);
        
        // 释放内存
        es_free(buffer);
    }
    
    // 内存池使用示例
    es_mem_pool_t *pool = es_mem_pool_create(64, 10);
    if (pool != NULL) {
        void *block = es_mem_pool_alloc(pool);
        if (block != NULL) {
            // 使用内存块
            memset(block, 0, 64);
            
            // 释放内存块
            es_mem_pool_free(pool, block);
        }
        
        // 销毁内存池
        es_mem_pool_destroy(pool);
    }
}
```

## 3. 存储系统模块

### 3.1 Flash存储模块 (es_flash)

#### 3.1.1 功能特性

- **扇区管理**: Flash扇区的擦除和写入管理
- **磨损均衡**: 实现简单的磨损均衡算法
- **坏块管理**: 检测和管理坏块
- **数据校验**: 使用CRC校验确保数据完整性

#### 3.1.2 主要API

```c
// Flash初始化
int es_flash_init(void);

// 扇区操作
int es_flash_erase_sector(uint32_t sector_addr);
int es_flash_write(uint32_t addr, const void *data, uint32_t len);
int es_flash_read(uint32_t addr, void *data, uint32_t len);

// 状态查询
bool es_flash_is_ready(void);
uint32_t es_flash_get_sector_size(void);
uint32_t es_flash_get_total_size(void);
```

### 3.2 环形文件系统 (ringfs)

#### 3.2.1 功能特性

- **环形存储**: 自动覆盖最旧的数据
- **文件管理**: 支持多个文件的管理
- **原子操作**: 确保写入操作的原子性
- **故障恢复**: 支持断电恢复

#### 3.2.2 主要API

```c
// 文件系统操作
int ringfs_init(struct ringfs *fs, const struct ringfs_flash_partition *partition);
int ringfs_format(struct ringfs *fs);
int ringfs_scan(struct ringfs *fs);

// 文件操作
int ringfs_append(struct ringfs *fs, uint16_t file_id, const void *data, uint16_t len);
int ringfs_read(struct ringfs *fs, uint16_t file_id, void *data, uint16_t len);
int ringfs_find_next(struct ringfs *fs, uint16_t file_id);

// 统计信息
int ringfs_get_stats(struct ringfs *fs, struct ringfs_stats *stats);
```

## 4. 加密模块

### 4.1 AES加密模块 (es_aes)

#### 4.1.1 功能特性

- **多种密钥长度**: 支持AES-128/192/256
- **多种模式**: 支持ECB、CBC、CTR模式
- **硬件加速**: 支持硬件AES加速器
- **PKCS7填充**: 支持PKCS7填充模式

#### 4.1.2 主要API

```c
// AES上下文初始化
int es_aes_init(es_aes_context_t *ctx, const uint8_t *key, uint32_t key_bits);

// 加密操作
int es_aes_encrypt_ecb(es_aes_context_t *ctx, const uint8_t *input, uint8_t *output);
int es_aes_encrypt_cbc(es_aes_context_t *ctx, const uint8_t *iv, 
                       const uint8_t *input, uint8_t *output, uint32_t length);

// 解密操作
int es_aes_decrypt_ecb(es_aes_context_t *ctx, const uint8_t *input, uint8_t *output);
int es_aes_decrypt_cbc(es_aes_context_t *ctx, const uint8_t *iv,
                       const uint8_t *input, uint8_t *output, uint32_t length);

// 资源清理
void es_aes_free(es_aes_context_t *ctx);
```

### 4.2 3DES加密模块 (es_des3)

#### 4.2.1 主要API

```c
// 3DES加密
int es_des3_encrypt(const uint8_t *key, const uint8_t *input, uint8_t *output);
int es_des3_decrypt(const uint8_t *key, const uint8_t *input, uint8_t *output);

// CBC模式
int es_des3_encrypt_cbc(const uint8_t *key, const uint8_t *iv,
                        const uint8_t *input, uint8_t *output, uint32_t length);
int es_des3_decrypt_cbc(const uint8_t *key, const uint8_t *iv,
                        const uint8_t *input, uint8_t *output, uint32_t length);
```

### 4.3 MD5哈希模块 (es_md5)

#### 4.3.1 主要API

```c
// MD5上下文操作
void es_md5_init(es_md5_context_t *ctx);
void es_md5_update(es_md5_context_t *ctx, const uint8_t *input, uint32_t length);
void es_md5_final(es_md5_context_t *ctx, uint8_t digest[16]);

// 一次性计算
void es_md5(const uint8_t *input, uint32_t length, uint8_t digest[16]);

// 字符串形式
void es_md5_string(const uint8_t *input, uint32_t length, char *output);
```

### 4.4 CRC校验模块 (es_crc)

#### 4.4.1 主要API

```c
// CRC16计算
uint16_t es_crc16(const uint8_t *data, uint32_t length);
uint16_t es_crc16_update(uint16_t crc, const uint8_t *data, uint32_t length);

// CRC32计算
uint32_t es_crc32(const uint8_t *data, uint32_t length);
uint32_t es_crc32_update(uint32_t crc, const uint8_t *data, uint32_t length);

// 自定义CRC
uint32_t es_crc_calculate(const uint8_t *data, uint32_t length, 
                         uint32_t polynomial, uint32_t initial, bool reflect);
```

## 5. 电源管理模块 (es_pm)

### 5.1 功能特性

- **低功耗模式**: 支持多种低功耗模式
- **唤醒源管理**: 管理各种唤醒源
- **电源状态监控**: 监控电源电压和状态
- **电池管理**: 电池电量监测和管理

### 5.2 主要API

```c
// 电源管理初始化
int es_pm_init(void);

// 低功耗模式
int es_pm_enter_sleep(es_pm_sleep_mode_t mode);
int es_pm_enter_standby(void);
int es_pm_enter_stop(void);

// 唤醒源管理
int es_pm_enable_wakeup_source(es_pm_wakeup_source_t source);
int es_pm_disable_wakeup_source(es_pm_wakeup_source_t source);

// 电源监控
uint16_t es_pm_get_battery_voltage(void);
es_pm_power_state_t es_pm_get_power_state(void);
bool es_pm_is_charging(void);

// 电源事件
int es_pm_register_event_callback(es_pm_event_callback_t callback);
```

### 5.3 使用示例

```c
// 电源管理示例
void power_management_example(void) {
    // 获取电池电压
    uint16_t voltage = es_pm_get_battery_voltage();
    ES_PRINTF_I("PM", "Battery voltage: %d mV", voltage);
    
    // 检查电源状态
    if (voltage < 3300) {
        ES_LOGW("PM", "Low battery, entering power save mode");
        
        // 启用RTC唤醒
        es_pm_enable_wakeup_source(ES_PM_WAKEUP_RTC);
        
        // 进入低功耗模式
        es_pm_enter_sleep(ES_PM_SLEEP_MODE_DEEP);
    }
}
```

## 6. 时间服务模块 (es_time)

### 6.1 功能特性

- **时间戳服务**: 提供毫秒级时间戳
- **时间格式化**: 支持多种时间格式输出
- **定时器服务**: 软件定时器实现
- **时间同步**: 支持网络时间同步

### 6.2 主要API

```c
// 时间戳操作
uint32_t es_get_timestamp(void);
uint64_t es_get_timestamp_us(void);
void es_delay_ms(uint32_t ms);
void es_delay_us(uint32_t us);

// 时间格式化
void es_timestamp_to_str(uint32_t timestamp, char *str);
void es_time_to_str(const es_time_t *time, char *str);

// 时间设置
int es_set_time(const es_time_t *time);
int es_get_time(es_time_t *time);

// 定时器
es_timer_handle_t es_timer_create(uint32_t period, bool repeat, 
                                 es_timer_callback_t callback, void *arg);
int es_timer_start(es_timer_handle_t timer);
int es_timer_stop(es_timer_handle_t timer);
int es_timer_delete(es_timer_handle_t timer);
```

## 7. 工具函数模块 (es_utils)

### 7.1 字符串工具

```c
// 字符串操作
int es_str_to_hex(const char *str, uint8_t *hex, int max_len);
int es_hex_to_str(const uint8_t *hex, int len, char *str);
char *es_str_trim(char *str);
int es_str_split(char *str, char delimiter, char **tokens, int max_tokens);

// 数值转换
int es_str_to_int(const char *str, int *value);
int es_str_to_float(const char *str, float *value);
int es_int_to_str(int value, char *str, int base);
```

### 7.2 数据处理

```c
// 字节序转换
uint16_t es_be16_to_cpu(uint16_t value);
uint32_t es_be32_to_cpu(uint32_t value);
uint16_t es_cpu_to_be16(uint16_t value);
uint32_t es_cpu_to_be32(uint32_t value);

// 位操作
void es_set_bit(uint32_t *value, int bit);
void es_clear_bit(uint32_t *value, int bit);
bool es_test_bit(uint32_t value, int bit);
int es_count_bits(uint32_t value);
```

## 8. 按键处理模块 (es_button)

### 8.1 功能特性

- **多按键支持**: 支持多个按键同时检测
- **按键事件**: 支持按下、释放、长按、双击等事件
- **防抖处理**: 硬件和软件防抖
- **协程集成**: 与协程系统集成

### 8.2 主要API

```c
// 按键初始化
int es_button_init(void);

// 按键注册
int es_button_register(es_button_t *button, uint8_t pin, 
                      es_button_callback_t callback, void *arg);

// 按键扫描
void es_button_scan(void);

// 按键状态
bool es_button_is_pressed(uint8_t pin);
uint32_t es_button_get_press_time(uint8_t pin);
```

## 9. 环形缓冲模块

### 9.1 环形缓冲区 (es_ringbuffer)

```c
// 环形缓冲区操作
int es_ringbuffer_init(es_ringbuffer_t *rb, uint8_t *buffer, uint32_t size);
uint32_t es_ringbuffer_write(es_ringbuffer_t *rb, const uint8_t *data, uint32_t len);
uint32_t es_ringbuffer_read(es_ringbuffer_t *rb, uint8_t *data, uint32_t len);
uint32_t es_ringbuffer_get_used(es_ringbuffer_t *rb);
uint32_t es_ringbuffer_get_free(es_ringbuffer_t *rb);
void es_ringbuffer_reset(es_ringbuffer_t *rb);
```

### 9.2 环形对象缓冲 (es_ringobj)

```c
// 环形对象缓冲区操作
int es_ringobj_init(es_ringobj_t *ro, void *buffer, uint32_t obj_size, uint32_t obj_count);
int es_ringobj_put(es_ringobj_t *ro, const void *obj);
int es_ringobj_get(es_ringobj_t *ro, void *obj);
uint32_t es_ringobj_get_count(es_ringobj_t *ro);
bool es_ringobj_is_full(es_ringobj_t *ro);
bool es_ringobj_is_empty(es_ringobj_t *ro);
```

## 10. 使用示例

### 10.1 综合示例

```c
// 系统初始化示例
void system_init_example(void) {
    // 初始化内存管理
    es_mem_init();
    
    // 初始化Flash存储
    es_flash_init();
    
    // 初始化电源管理
    es_pm_init();
    
    // 初始化时间服务
    es_time_init();
    
    // 初始化按键
    es_button_init();
    
    ES_PRINTF_I("SYSTEM", "All utility modules initialized");
}

// 数据处理示例
void data_processing_example(void) {
    // 字符串转十六进制
    const char *hex_str = "48656C6C6F";
    uint8_t hex_data[16];
    int len = es_str_to_hex(hex_str, hex_data, sizeof(hex_data));
    
    // 计算MD5
    uint8_t md5_digest[16];
    es_md5(hex_data, len, md5_digest);
    
    // 转换为字符串
    char md5_str[33];
    es_md5_string(hex_data, len, md5_str);
    ES_PRINTF_I("CRYPTO", "MD5: %s", md5_str);
    
    // CRC32校验
    uint32_t crc = es_crc32(hex_data, len);
    ES_PRINTF_I("CRC", "CRC32: 0x%08X", crc);
}
```

## 11. 最佳实践

### 11.1 内存管理

1. **及时释放**: 及时释放不再使用的内存
2. **内存池**: 对固定大小的对象使用内存池
3. **内存监控**: 定期检查内存使用情况
4. **避免碎片**: 合理安排内存分配策略

### 11.2 存储管理

1. **磨损均衡**: 合理使用Flash避免过度磨损
2. **数据校验**: 重要数据使用校验机制
3. **备份策略**: 关键数据进行备份
4. **故障恢复**: 实现数据恢复机制

### 11.3 加密安全

1. **密钥管理**: 安全存储和管理密钥
2. **算法选择**: 根据需求选择合适的加密算法
3. **性能优化**: 使用硬件加速提高性能
4. **安全编程**: 避免密钥泄露和时序攻击

这些工具和驱动模块为 ES MCU Framework 提供了完整的基础服务，是构建稳定可靠嵌入式系统的重要基石。
