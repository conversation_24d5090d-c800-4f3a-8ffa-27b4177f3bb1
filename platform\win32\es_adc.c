/**
 * @file es_hal_adc.c
 * @brief 硬件抽象层ADC驱动实现 - Win32模拟版本
 */

#include "es_drv_adc.h"
#include "es_log.h"
#include <stdlib.h>
#include <time.h>

#define TAG "es_hal_adc"

// ADC参考电压，单位mV
#define ADC_VREF_MV          3300

// Win32平台下的模拟ADC值
static uint16_t s_main_voltage_raw = 0;     // 主电源ADC原始值
static uint16_t s_battery_voltage_raw = 0;  // 电池ADC原始值

// 模拟ADC值范围
#define ADC_RAW_MAX          4095           // 12位ADC最大值
#define MAIN_VOLTAGE_MIN     11000          // 主电源最小电压，单位mV
#define MAIN_VOLTAGE_MAX     14500          // 主电源最大电压，单位mV
#define BATTERY_VOLTAGE_MIN  3500           // 电池最小电压，单位mV
#define BATTERY_VOLTAGE_MAX  4200           // 电池最大电压，单位mV

/**
 * @brief 初始化ADC
 * 
 * @return int 0:成功，其他值:失败
 */
int es_adc_init(void)
{
    // 初始化随机数种子
    srand((unsigned int)time(NULL));
    
    // 初始化模拟ADC值 - 设置为中间值
    s_main_voltage_raw = ADC_RAW_MAX / 2;
    s_battery_voltage_raw = ADC_RAW_MAX / 2;
    
    ES_LOGI(TAG, "Win32 ADC initialized with simulated values");
    
    return 0;
}

/**
 * @brief 读取主电源ADC原始值
 * 
 * @return uint16_t ADC原始值 (0-4095)
 */
uint16_t es_adc_read_main_raw(void)
{
    // 模拟ADC值波动 - 在当前值基础上随机波动±50
    int variation = (rand() % 101) - 50;
    
    s_main_voltage_raw += variation;
    
    // 确保值在有效范围内
    if (s_main_voltage_raw > ADC_RAW_MAX) {
        s_main_voltage_raw = ADC_RAW_MAX;
    } else if (s_main_voltage_raw < 0) {
        s_main_voltage_raw = 0;
    }
    
    return s_main_voltage_raw;
}

/**
 * @brief 读取电池ADC原始值
 * 
 * @return uint16_t ADC原始值 (0-4095)
 */
uint16_t es_adc_read_battery_raw(void)
{
    // 模拟ADC值波动 - 在当前值基础上随机波动±30
    int variation = (rand() % 61) - 30;
    
    s_battery_voltage_raw += variation;
    
    // 确保值在有效范围内
    if (s_battery_voltage_raw > ADC_RAW_MAX) {
        s_battery_voltage_raw = ADC_RAW_MAX;
    } else if (s_battery_voltage_raw < 0) {
        s_battery_voltage_raw = 0;
    }
    
    return s_battery_voltage_raw;
}

/**
 * @brief 读取主电源电压值
 * 
 * @param factor 电压转换系数
 * @return uint16_t 电压值，单位mV
 */
uint16_t es_adc_read_main_voltage(float factor)
{
    uint16_t raw = es_adc_read_main_raw();
    
    // 在Win32模拟环境下，直接生成一个合理的电压值
    // 模拟电压在11V-14.5V之间
    uint16_t voltage = MAIN_VOLTAGE_MIN + (raw * (MAIN_VOLTAGE_MAX - MAIN_VOLTAGE_MIN) / ADC_RAW_MAX);
    
    // 应用转换系数
    if (factor > 0) {
        voltage = (uint16_t)((float)voltage / factor);
    }
    
    return voltage;
}

/**
 * @brief 读取电池电压值
 * 
 * @param factor 电压转换系数
 * @return uint16_t 电压值，单位mV
 */
uint16_t es_adc_read_battery_voltage(float factor)
{
    uint16_t raw = es_adc_read_battery_raw();
    
    // 在Win32模拟环境下，直接生成一个合理的电压值
    // 模拟电池电压在3.5V-4.2V之间
    uint16_t voltage = BATTERY_VOLTAGE_MIN + (raw * (BATTERY_VOLTAGE_MAX - BATTERY_VOLTAGE_MIN) / ADC_RAW_MAX);
    
    // 应用转换系数
    if (factor > 0) {
        voltage = (uint16_t)((float)voltage / factor);
    }
    
    return voltage;
} 