/**
 * @file es_crc.h
 * @brief CRC calculation utility functions
 */
#ifndef __ES_CRC_H__
#define __ES_CRC_H__

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* CRC32 initial value definition */
#define DEF_CRC_INIT_VALUE 0xFFFFFFFF

/**
 * @brief Calculate CRC32 checksum
 * 
 * @param crc Initial CRC value, typically DEF_CRC_INIT_VALUE
 * @param buf Data buffer pointer
 * @param size Data length
 * @return uint32_t Calculated CRC32 value
 */
uint32_t es_crc32(uint32_t crc, const uint8_t *buf, size_t size);

#ifdef __cplusplus
}
#endif

#endif /* __ES_CRC_H__ */ 

