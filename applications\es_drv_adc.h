/**
 * @file es_drv_adc.h
 * @brief Hardware abstraction layer ADC driver header file
 */

#ifndef __ES_DRV_ADC_H__
#define __ES_DRV_ADC_H__

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize ADC
 *
 * @return int 0: success, other values: failure
 */
int es_adc_init(void);

/**
 * @brief Read main power supply voltage value
 *
 * @param factor Voltage conversion factor
 * @return uint16_t Voltage value in mV
 */
uint16_t es_adc_read_main_voltage(float factor);

/**
 * @brief Read battery voltage value
 *
 * @param factor Voltage conversion factor
 * @return uint16_t Voltage value in mV
 */
uint16_t es_adc_read_battery_voltage(float factor);

/**
 * @brief Read main power supply ADC raw value
 *
 * @return uint16_t ADC raw value (0-4095)
 */
uint16_t es_adc_read_main_raw(void);

/**
 * @brief Read battery ADC raw value
 *
 * @return uint16_t ADC raw value (0-4095)
 */
uint16_t es_adc_read_battery_raw(void);

#ifdef __cplusplus
}
#endif

#endif /* __ES_DRV_ADC_H__ */ 

