# ES规则引擎VM扩展文档

## 概述

ES规则引擎现在支持脚本节点，通过内置的简易虚拟机(VM)执行字节码，提供更灵活的规则表达能力。VM采用同步执行模式，每次表达式求值时都会完整执行脚本到结束。

## 特性

- **字节码执行**: 支持自定义字节码指令集
- **同步执行**: 每次调用都完整执行脚本，不保持状态
- **系统调用**: 可以调用规则引擎的系统接口
- **多种数据类型**: 支持8位、16位、32位操作数
- **丰富的指令集**: 包括算术、逻辑、比较、跳转等操作

## 指令集

### 栈操作指令
- `VM_OP_NOP` (0x00): 空操作
- `VM_OP_PUSH8` (0x01): 压入8位立即数
- `VM_OP_PUSH16` (0x02): 压入16位立即数
- `VM_OP_PUSH32` (0x03): 压入32位立即数
- `VM_OP_POP` (0x04): 弹出栈顶元素
- `VM_OP_DUP` (0x05): 复制栈顶元素
- `VM_OP_SWAP` (0x06): 交换栈顶两个元素

### 算术运算指令
- `VM_OP_ADD` (0x10): 加法
- `VM_OP_SUB` (0x11): 减法
- `VM_OP_MUL` (0x12): 乘法
- `VM_OP_DIV` (0x13): 除法
- `VM_OP_MOD` (0x14): 取模

### 位运算指令
- `VM_OP_AND` (0x15): 按位与
- `VM_OP_OR` (0x16): 按位或
- `VM_OP_XOR` (0x17): 按位异或
- `VM_OP_NOT` (0x18): 按位取反
- `VM_OP_SHL` (0x19): 左移
- `VM_OP_SHR` (0x1A): 右移

### 比较指令
- `VM_OP_EQ` (0x20): 等于
- `VM_OP_NE` (0x21): 不等于
- `VM_OP_LT` (0x22): 小于
- `VM_OP_LE` (0x23): 小于等于
- `VM_OP_GT` (0x24): 大于
- `VM_OP_GE` (0x25): 大于等于

### 控制流指令
- `VM_OP_JMP` (0x30): 无条件跳转
- `VM_OP_JZ` (0x31): 零跳转
- `VM_OP_JNZ` (0x32): 非零跳转

### 系统调用和控制指令
- `VM_OP_CALL` (0x40): 调用系统接口
- `VM_OP_RET` (0x41): 返回
- `VM_OP_HALT` (0xFF): 停机

## 系统调用

VM可以通过`VM_OP_CALL`指令调用以下系统接口，**每个调用使用实际需要的参数个数**：

### 标准系统调用

| 调用ID | 名称 | 参数个数 | 参数说明 | 功能 |
|--------|------|----------|----------|------|
| `VM_SYSCALL_GET_EVENT` (0x01) | 获取事件值 | 1 | event_id | 获取指定事件的当前值 |
| `VM_SYSCALL_GET_IO` (0x02) | 获取IO电平 | 1 | pin | 获取指定IO引脚的电平 |
| `VM_SYSCALL_GET_SIGNAL` (0x03) | 获取信号值 | 2 | msg_idx, sig_idx | 获取指定消息中信号的值 |
| `VM_SYSCALL_GET_TIME` (0x04) | 获取时间戳 | 0 | 无 | 获取当前系统时间戳 |
| `VM_SYSCALL_GET_MSG_TIME` (0x05) | 获取消息时间 | 1 | msg_idx | 获取指定消息的时间戳 |

### 参数传递规则

- 参数通过VM栈传递，按照**从左到右**的顺序压栈
- VM会根据调用ID自动确定参数个数
- 多余的栈元素不会被消耗
- 参数不足会导致VM错误

## 使用示例

### 基本算术运算

```c
// 计算 (10 + 20) * 2
static const uint8_t bytecode_arithmetic[] = {
    VM_OP_PUSH8, 10,        // 压入10
    VM_OP_PUSH8, 20,        // 压入20
    VM_OP_ADD,              // 加法：10 + 20 = 30
    VM_OP_PUSH8, 2,         // 压入2
    VM_OP_MUL,              // 乘法：30 * 2 = 60
    VM_OP_RET               // 返回
};
```

### 条件判断

```c
// 判断事件值是否大于阈值
static const uint8_t bytecode_condition[] = {
    VM_OP_PUSH8, 1,         // 事件ID（1个参数）
    VM_OP_CALL, VM_SYSCALL_GET_EVENT,  // 获取事件值
    VM_OP_PUSH8, 100,       // 阈值
    VM_OP_GT,               // 比较
    VM_OP_RET               // 返回
};
```

### 多参数系统调用

```c
// 获取信号值和消息时间
static const uint8_t bytecode_signal[] = {
    VM_OP_PUSH8, 0,         // 消息索引
    VM_OP_PUSH8, 1,         // 信号索引（2个参数）
    VM_OP_CALL, VM_SYSCALL_GET_SIGNAL,  // 获取信号值

    VM_OP_PUSH8, 0,         // 消息索引（1个参数）
    VM_OP_CALL, VM_SYSCALL_GET_MSG_TIME, // 获取消息时间

    VM_OP_ADD,              // 将信号值和时间相加（示例操作）
    VM_OP_RET               // 返回结果
};
```

### 同步执行示例

```c
// 同步执行示例：简单的计算脚本
static const uint8_t bytecode_sync[] = {
    VM_OP_PUSH8, 10,        // 压入10
    VM_OP_PUSH8, 20,        // 压入20
    VM_OP_ADD,              // 加法：10 + 20 = 30
    VM_OP_DUP,              // 复制结果
    VM_OP_CALL, VM_SYSCALL_LOG,  // 输出日志
    VM_OP_POP,              // 清理日志结果
    VM_OP_RET               // 返回30
};
```

### 无参数调用

```c
// 获取当前时间戳
static const uint8_t bytecode_timestamp[] = {
    VM_OP_CALL, VM_SYSCALL_GET_TIME,  // 无参数调用
    VM_OP_RET               // 返回时间戳
};
```

## 在规则表达式中使用

脚本节点可以作为规则表达式的一部分使用。**重要**：VM采用同步执行模式，每次表达式求值都会重新执行脚本：

```c
// 脚本表达式节点结构
struct {
    uint16_t bytecode_len;      // 字节码长度
    const uint8_t* bytecode;    // 字节码数据指针
} script;

// 在表达式求值中使用（每次都重新执行）
uint32_t result = evaluate_expr_value(&script_expr, frame);
bool condition = evaluate_expr(&script_expr, frame);

// 多次调用会得到相同结果（除非脚本依赖外部状态）
uint32_t result2 = evaluate_expr_value(&script_expr, frame);
```

## VM架构

### 数据栈
- 大小：16个32位元素
- 用于存储操作数和中间结果

### 调用栈
- 大小：4层调用深度
- 用于支持函数调用和返回

### 程序计数器(PC)
- 16位地址空间
- 指向当前执行的指令

### 状态管理
- `VM_STATE_READY`: 准备执行
- `VM_STATE_RUNNING`: 正在执行
- `VM_STATE_YIELD`: 已让出控制权
- `VM_STATE_WAIT`: 等待条件满足
- `VM_STATE_DELAY`: 延时等待
- `VM_STATE_DONE`: 执行完成
- `VM_STATE_ERROR`: 执行错误

## 同步执行模式

VM采用同步执行模式，具有以下特性：

1. **完整执行**: 每次表达式求值都会完整执行脚本到结束
2. **无状态保持**: 不在调用之间保持VM状态
3. **确定性结果**: 相同输入总是产生相同输出（除非依赖外部状态）
4. **简单可靠**: 无需考虑状态管理和协程调度
5. **高性能**: 直接执行，无协程切换开销

## 扩展系统接口

可以通过扩展`es_rule_sys_if`结构来添加新的VM功能：

```c
typedef struct {
    uint32_t (*get_event_value)(uint16_t event_id);
    uint32_t (*get_io_level)(uint16_t pin);
    void (*send_frame)(const es_rule_frame* frame);
    // VM扩展接口
    uint32_t (*vm_syscall)(uint8_t call_id, uint32_t arg1, uint32_t arg2, uint32_t arg3);
} es_rule_sys_if;
```

## 测试

提供了完整的测试套件：

### 基本功能测试
```c
void es_rule_vm_test(void);
```
包含以下测试用例：
- 基本算术运算
- 比较操作
- 系统调用
- 协程操作
- 表达式集成
- **协程状态持久性测试**

### 协程演示
```c
void es_rule_vm_coroutine_demo(void);
```
演示状态机脚本的协程状态持久性：
- 多状态状态机实现
- YIELD操作的状态保持
- 多次调用间的状态连续性
- VM重置和重新开始

## 注意事项

1. **内存管理**: VM使用规则引擎的内存池，字节码数据会被复制到内存池中
2. **执行限制**: 单次执行最多100条指令，防止无限循环
3. **栈溢出**: 注意栈深度限制，避免栈溢出
4. **错误处理**: VM执行错误会设置错误状态并停止执行
5. **协程安全**: VM状态在协程切换时需要正确管理

## 性能考虑

- VM执行开销相对较小，适合嵌入式环境
- 字节码紧凑，内存占用少
- 支持增量执行，不会阻塞系统
- 协程机制确保实时性要求
