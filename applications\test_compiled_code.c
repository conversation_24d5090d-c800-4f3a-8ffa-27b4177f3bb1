#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

// 简化的VM定义，只包含我们需要测试的部分
#define VM_STACK_SIZE   16
#define VM_REG_COUNT    8

typedef enum {
    VM_STATE_READY = 0,
    VM_STATE_RUNNING,
    VM_STATE_DONE,
    VM_STATE_ERROR
} vm_state_t;

typedef struct {
    uint32_t stack[VM_STACK_SIZE];
    uint8_t sp;
    uint16_t pc;
    vm_state_t state;
    const uint8_t* bytecode;
    uint16_t bytecode_len;
    uint32_t registers[VM_REG_COUNT];
} vm_context_t;

// VM操作码
#define VM_OP_PUSH8     0x01
#define VM_OP_PUSH16    0x02
#define VM_OP_PUSH32    0x03
#define VM_OP_POP       0x04
#define VM_OP_DUP       0x05
#define VM_OP_SWAP      0x06
#define VM_OP_ADD       0x10
#define VM_OP_SUB       0x11
#define VM_OP_MUL       0x12
#define VM_OP_DIV       0x13
#define VM_OP_MOD       0x14
#define VM_OP_AND       0x15
#define VM_OP_OR        0x16
#define VM_OP_XOR       0x17
#define VM_OP_NOT       0x18
#define VM_OP_SHL       0x19
#define VM_OP_SHR       0x1A
#define VM_OP_EQ        0x20
#define VM_OP_NE        0x21
#define VM_OP_LT        0x22
#define VM_OP_LE        0x23
#define VM_OP_GT        0x24
#define VM_OP_GE        0x25
#define VM_OP_JMP       0x30
#define VM_OP_JZ        0x31
#define VM_OP_JNZ       0x32
#define VM_OP_CALL      0x40
#define VM_OP_RET       0x41
#define VM_OP_STORE_REG 0x42
#define VM_OP_LOAD_REG  0x43
#define VM_OP_HALT      0xFF

// 简化的日志宏
#define ES_PRINTF_E(tag, fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)
#define ES_PRINTF_I(tag, fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define TAG "VM_TEST"

// VM函数实现
static inline int vm_push(vm_context_t* vm, uint32_t value) {
    if (vm->sp >= VM_STACK_SIZE) {
        ES_PRINTF_E(TAG, "VM stack overflow");
        return -1;
    }
    vm->stack[vm->sp++] = value;
    return 0;
}

static inline uint32_t vm_pop(vm_context_t* vm) {
    if (vm->sp == 0) {
        ES_PRINTF_E(TAG, "VM stack underflow");
        return 0;
    }
    return vm->stack[--vm->sp];
}

static uint8_t vm_read_u8(vm_context_t* vm) {
    if (vm->pc >= vm->bytecode_len) {
        ES_PRINTF_E(TAG, "VM PC out of bounds");
        return 0;
    }
    return vm->bytecode[vm->pc++];
}

static uint16_t vm_read_u16(vm_context_t* vm) {
    uint16_t value = vm_read_u8(vm);
    value |= (uint16_t)vm_read_u8(vm) << 8;
    return value;
}

static uint32_t vm_read_u32(vm_context_t* vm) {
    uint32_t value = vm_read_u16(vm);
    value |= (uint32_t)vm_read_u16(vm) << 16;
    return value;
}

int vm_init(vm_context_t* vm, const uint8_t* bytecode, uint16_t len) {
    if (!vm || !bytecode || len == 0) {
        return -1;
    }

    memset(vm, 0, sizeof(vm_context_t));
    vm->bytecode = bytecode;
    vm->bytecode_len = len;
    vm->state = VM_STATE_READY;
    vm->sp = 0;
    vm->pc = 0;
    
    for (int i = 0; i < VM_REG_COUNT; i++) {
        vm->registers[i] = 0;
    }

    return 0;
}

uint32_t vm_get_result(vm_context_t* vm) {
    if (!vm || vm->sp == 0) {
        return 0;
    }
    return vm->stack[vm->sp - 1];
}

int vm_step(vm_context_t* vm) {
    if (!vm) {
        return -1;
    }

    if (vm->state != VM_STATE_RUNNING && vm->state != VM_STATE_READY) {
        return 0;
    }

    if (vm->pc >= vm->bytecode_len) {
        vm->state = VM_STATE_DONE;
        return 0;
    }

    vm->state = VM_STATE_RUNNING;
    uint8_t opcode = vm_read_u8(vm);

    switch (opcode) {
        case VM_OP_PUSH8: {
            uint8_t value = vm_read_u8(vm);
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_PUSH16: {
            uint16_t value = vm_read_u16(vm);
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_PUSH32: {
            uint32_t value = vm_read_u32(vm);
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_POP:
            vm_pop(vm);
            break;

        case VM_OP_SWAP: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM SWAP: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t a = vm_pop(vm);
            uint32_t b = vm_pop(vm);
            vm_push(vm, a);
            vm_push(vm, b);
            break;
        }

        case VM_OP_ADD: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM ADD: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a + b);
            break;
        }

        case VM_OP_SUB: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM SUB: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a - b);
            break;
        }

        case VM_OP_MUL: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM MUL: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a * b);
            break;
        }

        case VM_OP_GT: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM GT: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, (a > b) ? 1 : 0);
            break;
        }

        case VM_OP_JZ: {
            uint16_t addr = vm_read_u16(vm);
            if (vm->sp < 1) {
                ES_PRINTF_E(TAG, "VM JZ: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t value = vm_pop(vm);
            if (value == 0) {
                if (addr >= vm->bytecode_len) {
                    ES_PRINTF_E(TAG, "VM JZ: invalid address %u", addr);
                    vm->state = VM_STATE_ERROR;
                    return -1;
                }
                vm->pc = addr;
            }
            break;
        }

        case VM_OP_STORE_REG: {
            uint8_t reg_id = vm_read_u8(vm);
            if (reg_id >= VM_REG_COUNT) {
                ES_PRINTF_E(TAG, "VM STORE_REG: invalid register ID %u", reg_id);
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            if (vm->sp < 1) {
                ES_PRINTF_E(TAG, "VM STORE_REG: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t value = vm_pop(vm);
            vm->registers[reg_id] = value;
            ES_PRINTF_I(TAG, "VM STORE_REG: R%u = %u", reg_id, value);
            break;
        }

        case VM_OP_LOAD_REG: {
            uint8_t reg_id = vm_read_u8(vm);
            if (reg_id >= VM_REG_COUNT) {
                ES_PRINTF_E(TAG, "VM LOAD_REG: invalid register ID %u", reg_id);
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t value = vm->registers[reg_id];
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            ES_PRINTF_I(TAG, "VM LOAD_REG: R%u -> %u", reg_id, value);
            break;
        }

        case VM_OP_RET:
            vm->state = VM_STATE_DONE;
            break;

        case VM_OP_HALT:
            vm->state = VM_STATE_DONE;
            break;

        default:
            ES_PRINTF_E(TAG, "VM: unknown opcode 0x%02X at PC=%u", opcode, vm->pc - 1);
            vm->state = VM_STATE_ERROR;
            return -1;
    }

    return 1;
}

int vm_execute(vm_context_t* vm) {
    if (!vm) {
        return -1;
    }

    if (vm->state != VM_STATE_READY) {
        return 0;
    }

    vm->state = VM_STATE_RUNNING;
    
    int max_steps = 10000;
    while (max_steps-- > 0 && (vm->state == VM_STATE_RUNNING || vm->state == VM_STATE_READY)) {
        int result = vm_step(vm);
        if (result < 0) {
            return result;
        }

        if (vm->state == VM_STATE_DONE || vm->state == VM_STATE_ERROR) {
            break;
        }
    }

    if (max_steps <= 0) {
        ES_PRINTF_E(TAG, "VM execution timeout");
        vm->state = VM_STATE_ERROR;
        return -1;
    }

    return 0;
}

// 测试编译生成的字节码
int main() {
    printf("🧪 测试编译器生成的字节码\n");
    printf("==========================\n");
    
    // 编译器生成的字节码
    uint8_t bytecode[] = {
        0x01, 0x0A, 0x42, 0x00, 0x01, 0x14, 0x42, 0x01, 0x43, 0x00, 0x43, 0x01, 0x01, 0x02, 0x12, 0x10,
        0x42, 0x02, 0x43, 0x02, 0x01, 0x28, 0x24, 0x31, 0x21, 0x00, 0x43, 0x02, 0x01, 0x0A, 0x11, 0x42,
        0x02, 0x43, 0x02, 0x41, 0xFF
    };
    
    vm_context_t vm;
    
    // 初始化VM
    if (vm_init(&vm, bytecode, sizeof(bytecode)) < 0) {
        printf("❌ VM初始化失败\n");
        return 1;
    }
    
    printf("✅ VM初始化成功\n");
    printf("📋 字节码长度: %zu bytes\n", sizeof(bytecode));
    
    // 执行VM
    printf("🚀 开始执行编译后的代码...\n");
    int result = vm_execute(&vm);
    
    if (result < 0) {
        printf("❌ VM执行失败\n");
        return 1;
    }
    
    printf("✅ VM执行完成\n");
    
    // 检查寄存器状态
    printf("📋 最终寄存器状态:\n");
    for (int i = 0; i < VM_REG_COUNT; i++) {
        if (vm.registers[i] != 0) {
            printf("   R%d = %u\n", i, vm.registers[i]);
        }
    }
    
    // 检查执行结果
    uint32_t vm_result = vm_get_result(&vm);
    printf("🔍 程序返回值: %u\n", vm_result);
    
    // 验证结果
    // 原始C代码逻辑：
    // a = 10, b = 20
    // result = a + b * 2 = 10 + 40 = 50
    // if (result > 40) result = result - 10 = 40
    // return result = 40
    
    if (vm_result == 40) {
        printf("✅ 测试通过！编译器生成的代码执行正确\n");
        printf("   预期结果: 40, 实际结果: %u\n", vm_result);
    } else {
        printf("❌ 测试失败！结果不匹配\n");
        printf("   预期结果: 40, 实际结果: %u\n", vm_result);
    }
    
    printf("\n🏁 测试完成\n");
    return 0;
}
