/**
 *******************************************************************************
 * @file  ev_hc32f460_lqfp100_v2_lsm6dsl.h
 * @brief This file contains all the functions prototypes of the
          ev_hc32f460_lqfp100_v2_lsm6dsl driver library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-11-01       CDT             First version
   2023-11-02       CDT             Add SPI interface support
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2022-2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */
#ifndef __EV_HC32F460_LQFP100_V2_LSM6DSL_H__
#define __EV_HC32F460_LQFP100_V2_LSM6DSL_H__

/* C binding of definitions if building with C++ compiler */
#ifdef __cplusplus
extern "C"
{
#endif

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "lsm6dsl.h"
#include "ev_hc32f460_lqfp100_v2.h"

/**
 * @addtogroup BSP
 * @{
 */

/**
 * @addtogroup EV_HC32F460_LQFP100_V2
 * @{
 */

/**
 * @addtogroup EV_HC32F460_LQFP100_V2_LSM6DSL
 * @{
 */

#if ((BSP_LSM6DSL_ENABLE == DDL_ON) && (BSP_EV_HC32F460_LQFP100_V2 == BSP_EV_HC32F4XX))

/*******************************************************************************
 * Global type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Global pre-processor symbols/macros ('#define')
 ******************************************************************************/
/**
 * @defgroup EV_HC32F460_LQFP100_V2_LSM6DSL_Global_Macros EV_HC32F460_LQFP100_V2 LSM6DSL Global Macros
 * @{
 */

/**
 * @defgroup BSP_LSM6DSL_Interface BSP LSM6DSL Interface
 * @{
 */
/* Select interface type: I2C or SPI */
#define BSP_LSM6DSL_INTERFACE_I2C      (1U)
#define BSP_LSM6DSL_INTERFACE_SPI      (2U)
#define BSP_LSM6DSL_INTERFACE_TYPE     (BSP_LSM6DSL_INTERFACE_SPI)
/**
 * @}
 */

#if (BSP_LSM6DSL_INTERFACE_TYPE == BSP_LSM6DSL_INTERFACE_I2C)
/**
 * @defgroup BSP_I2C_Port BSP I2C Port
 * @{
 */
#define BSP_LSM6DSL_I2C_UNIT           CM_I2C1
#define BSP_LSM6DSL_I2C_PERIPH_CLK     FCG1_PERIPH_I2C1

#define BSP_LSM6DSL_I2C_SCL_PORT       (GPIO_PORT_D)
#define BSP_LSM6DSL_I2C_SCL_PIN        (GPIO_PIN_03)
#define BSP_LSM6DSL_I2C_SCL_PIN_FUNC   (GPIO_FUNC_49)  /* I2C1_SCL */

#define BSP_LSM6DSL_I2C_SDA_PORT       (GPIO_PORT_F)
#define BSP_LSM6DSL_I2C_SDA_PIN        (GPIO_PIN_10)
#define BSP_LSM6DSL_I2C_SDA_PIN_FUNC   (GPIO_FUNC_48)  /* I2C1_SDA */

#define BSP_LSM6DSL_I2C_ADDRESS        (LSM6DSL_I2C_ADDR)
/**
 * @}
 */

/**
 * @defgroup LSM6DSL_I2C_Timeout LSM6DSL I2C Timeout
 * @{
 */
#define BSP_LSM6DSL_I2C_TIMEOUT        (HCLK_VALUE)
/**
 * @}
 */
#else /* BSP_LSM6DSL_INTERFACE_SPI */
/**
 * @defgroup BSP_SPI_Port BSP SPI Port
 * @{
 */
#define BSP_LSM6DSL_SPI_UNIT           CM_SPI2
#define BSP_LSM6DSL_SPI_PERIPH_CLK     FCG1_PERIPH_SPI2

#define BSP_LSM6DSL_CS_PORT            (GPIO_PORT_D)
#define BSP_LSM6DSL_CS_PIN             (GPIO_PIN_02)
#define BSP_LSM6DSL_CS_ACTIVE()        GPIO_ResetPins(BSP_LSM6DSL_CS_PORT, BSP_LSM6DSL_CS_PIN)
#define BSP_LSM6DSL_CS_INACTIVE()      GPIO_SetPins(BSP_LSM6DSL_CS_PORT, BSP_LSM6DSL_CS_PIN)

#define BSP_LSM6DSL_SCK_PORT           (GPIO_PORT_C)
#define BSP_LSM6DSL_SCK_PIN            (GPIO_PIN_10)
#define BSP_LSM6DSL_SCK_PIN_FUNC       (GPIO_FUNC_47)  /* SPI2_SCK */

#define BSP_LSM6DSL_MISO_PORT          (GPIO_PORT_C)
#define BSP_LSM6DSL_MISO_PIN           (GPIO_PIN_11)
#define BSP_LSM6DSL_MISO_PIN_FUNC      (GPIO_FUNC_45)  /* SPI2_MISO */

#define BSP_LSM6DSL_MOSI_PORT          (GPIO_PORT_C)
#define BSP_LSM6DSL_MOSI_PIN           (GPIO_PIN_12)
#define BSP_LSM6DSL_MOSI_PIN_FUNC      (GPIO_FUNC_44)  /* SPI2_MOSI */

/* LSM6DSL SPI max frequency: 1MHz */
#define BSP_LSM6DSL_SPI_MAX_HZ         (1000000UL)
/**
 * @}
 */

/**
 * @defgroup LSM6DSL_SPI_Timeout LSM6DSL SPI Timeout
 * @{
 */
#define BSP_LSM6DSL_SPI_TIMEOUT        (HCLK_VALUE)
/**
 * @}
 */
#endif /* BSP_LSM6DSL_INTERFACE_TYPE */

/**
 * @defgroup LSM6DSL_INT_Port LSM6DSL Interrupt Port
 * @{
 */
#define BSP_LSM6DSL_INT1_PORT          (GPIO_PORT_D)
#define BSP_LSM6DSL_INT1_PIN           (GPIO_PIN_08)
#define BSP_LSM6DSL_INT1_EXINT_CH      (EXINT_CH08)
#define BSP_LSM6DSL_INT1_INT_SRC       (INT_SRC_PORT_D)

#define BSP_LSM6DSL_INT2_PORT          (GPIO_PORT_D)
#define BSP_LSM6DSL_INT2_PIN           (GPIO_PIN_09)
#define BSP_LSM6DSL_INT2_EXINT_CH      (EXINT_CH09)
#define BSP_LSM6DSL_INT2_INT_SRC       (INT_SRC_PORT_D)
/**
 * @}
 */

/**
 * @}
 */

/*******************************************************************************
 * Global variable definitions ('extern')
 ******************************************************************************/

/*******************************************************************************
  Global function prototypes (definition in C source)
 ******************************************************************************/
/**
 * @addtogroup EV_HC32F460_LQFP100_V2_LSM6DSL_Global_Functions
 * @{
 */

void BSP_LSM6DSL_Init(void);
void BSP_LSM6DSL_DeInit(void);
int32_t BSP_LSM6DSL_ReadAccel(stc_lsm6dsl_axis_t *pstcAccel);
int32_t BSP_LSM6DSL_ReadGyro(stc_lsm6dsl_axis_t *pstcGyro);
int32_t BSP_LSM6DSL_ReadTemp(float *pfTemp);
int32_t BSP_LSM6DSL_ReadSensor(stc_lsm6dsl_data_t *pstcData);
int32_t BSP_LSM6DSL_ConfigInt(uint8_t u8IntPin, uint8_t u8IntType, en_functional_state_t enNewState);



#include "es_coro.h"

es_async_t BSP_LSM6DSL_Co_ReadAccel(es_coro_t *coro, stc_lsm6dsl_axis_t *pstcAccel);
es_async_t BSP_LSM6DSL_Co_ReadGyro(es_coro_t *coro, stc_lsm6dsl_axis_t *pstcGyro);
es_async_t BSP_LSM6DSL_Co_ReadTemp(es_coro_t *coro, float *pfTemp);

/**
 * @}
 */

#endif /* (BSP_LSM6DSL_ENABLE && BSP_EV_HC32F460_LQFP100_V2) */

/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* __EV_HC32F460_LQFP100_V2_LSM6DSL_H__ */

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/ 