/**
 * @file mem_integrity_test.c
 * @brief 内存完整性检查测试示例
 * <AUTHOR> Assistant
 * @date 2025/1/24
 */

#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdbool.h>
#include "../applications/es_mem.h"

#define TAG "MEM_INTEGRITY_TEST"

// 简单的日志实现
void es_log_write(int level, const char *tag, bool to_flash, const char *fmt, ...) {
    va_list args;
    va_start(args, fmt);
    printf("[%s] ", tag);
    vprintf(fmt, args);
    printf("\n");
    va_end(args);
}

/**
 * @brief 测试初始状态的完整性检查
 */
void test_initial_integrity(void)
{
    printf("=== Testing Initial Memory Integrity ===\n");
    
    // 初始化内存管理器
    int ret = es_mem_v2_init();
    if (ret != 0) {
        printf("Failed to initialize memory manager\n");
        return;
    }
    
    // 检查初始状态的完整性
    ret = es_mem_v2_verify_integrity();
    if (ret == 0) {
        printf("✓ Initial memory integrity check PASSED\n");
    } else {
        printf("✗ Initial memory integrity check FAILED\n");
    }
    
    // 显示初始内存状态
    es_mem_v2_dump_pools();
    
    // 清理
    es_mem_v2_deinit();
}

/**
 * @brief 测试分配后的完整性检查
 */
void test_allocation_integrity(void)
{
    printf("\n=== Testing Memory Integrity After Allocations ===\n");
    
    // 初始化内存管理器
    int ret = es_mem_v2_init();
    if (ret != 0) {
        printf("Failed to initialize memory manager\n");
        return;
    }
    
    // 分配一些内存块
    void *ptr1 = es_mem_v2_alloc(64);
    void *ptr2 = es_mem_v2_alloc(128);
    void *ptr3 = es_mem_v2_alloc(256);
    
    printf("Allocated: ptr1=%p (64 bytes), ptr2=%p (128 bytes), ptr3=%p (256 bytes)\n", 
           ptr1, ptr2, ptr3);
    
    // 检查分配后的完整性
    ret = es_mem_v2_verify_integrity();
    if (ret == 0) {
        printf("✓ Memory integrity check after allocations PASSED\n");
    } else {
        printf("✗ Memory integrity check after allocations FAILED\n");
    }
    
    // 释放中间的块以创建碎片
    if (ptr2) {
        es_mem_v2_free(ptr2);
        printf("Freed ptr2 to create fragmentation\n");
    }
    
    // 检查碎片化后的完整性
    ret = es_mem_v2_verify_integrity();
    if (ret == 0) {
        printf("✓ Memory integrity check after fragmentation PASSED\n");
    } else {
        printf("✗ Memory integrity check after fragmentation FAILED\n");
    }
    
    // 显示内存状态
    es_mem_v2_dump_pools();
    
    // 清理剩余内存
    if (ptr1) es_mem_v2_free(ptr1);
    if (ptr3) es_mem_v2_free(ptr3);
    
    // 最终完整性检查
    ret = es_mem_v2_verify_integrity();
    if (ret == 0) {
        printf("✓ Final memory integrity check PASSED\n");
    } else {
        printf("✗ Final memory integrity check FAILED\n");
    }
    
    // 清理
    es_mem_v2_deinit();
}

/**
 * @brief 测试压力测试中的完整性检查
 */
void test_stress_integrity(void)
{
    printf("\n=== Testing Memory Integrity During Stress Test ===\n");
    
    // 初始化内存管理器
    int ret = es_mem_v2_init();
    if (ret != 0) {
        printf("Failed to initialize memory manager\n");
        return;
    }
    
    // 执行小规模压力测试
    uint32_t count = 100;
    uint32_t size = 128;
    uint32_t success_count = 0;
    uint32_t fail_count = 0;
    
    printf("Starting stress test: %u allocations of %u bytes each\n", count, size);
    
    for (uint32_t i = 0; i < count; i++) {
        void *ptr = es_mem_v2_alloc(size);
        if (ptr) {
            success_count++;
            // 写入测试数据
            memset(ptr, (uint8_t)(i & 0xFF), size);
            // 立即释放
            es_mem_v2_free(ptr);
        } else {
            fail_count++;
        }
        
        // 每10次操作检查一次完整性
        if ((i + 1) % 10 == 0) {
            ret = es_mem_v2_verify_integrity();
            if (ret != 0) {
                printf("✗ Memory integrity check failed at iteration %u\n", i + 1);
                break;
            }
        }
    }
    
    if (ret == 0) {
        printf("✓ Stress test completed successfully\n");
        printf("  Success: %u, Fail: %u\n", success_count, fail_count);
    }
    
    // 最终完整性检查
    ret = es_mem_v2_verify_integrity();
    if (ret == 0) {
        printf("✓ Final integrity check after stress test PASSED\n");
    } else {
        printf("✗ Final integrity check after stress test FAILED\n");
    }
    
    // 清理
    es_mem_v2_deinit();
}

/**
 * @brief 主函数
 */
int main(void)
{
    printf("Memory Integrity Test Suite\n");
    printf("===========================\n");
    
    // 运行各种完整性测试
    test_initial_integrity();
    test_allocation_integrity();
    test_stress_integrity();
    
    printf("\n=== All Tests Completed ===\n");
    
    return 0;
}
