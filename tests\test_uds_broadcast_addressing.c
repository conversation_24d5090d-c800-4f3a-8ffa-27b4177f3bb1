/**
 * @file test_uds_broadcast_addressing.c
 * @brief UDS广播地址切换功能测试
 * <AUTHOR>
 * @date 2025/1/17
 * @copyright Copyright (c) 2025
 */

#include "es_uds_client.h"
#include "es_coro.h"
#include "es_log.h"
#include <stdio.h>
#include <assert.h>
#include <string.h>

#define TAG "TEST_UDS_BROADCAST"

// 测试统计
static int test_passed = 0;
static int test_failed = 0;

// 测试宏
#define TEST_ASSERT(condition, message) \
    do { \
        if (condition) { \
            test_passed++; \
            printf("✓ PASS: %s\n", message); \
        } else { \
            test_failed++; \
            printf("✗ FAIL: %s\n", message); \
        } \
    } while(0)

#define TEST_ASSERT_NOT_NULL(ptr, message) TEST_ASSERT((ptr) != NULL, message)
#define TEST_ASSERT_EQUAL(expected, actual, message) \
    TEST_ASSERT((expected) == (actual), message)

// 模拟ISO-TP连接
static es_isotp_connection_t mock_isotp_conn = {0};
static uint32_t current_tx_id = 0;
static bool address_switch_called = false;
static bool address_restore_called = false;

// 模拟ISO-TP地址切换函数
es_isotp_error_t es_isotp_set_functional_address(es_isotp_connection_t *conn,
                                                uint32_t functional_tx_id)
{
    if (conn == NULL) {
        return ES_ISOTP_ERR_INVALID_PARAM;
    }
    
    current_tx_id = functional_tx_id;
    address_switch_called = true;
    ES_PRINTF_I(TAG, "Mock: Switched to functional address 0x%03X", functional_tx_id);
    return ES_ISOTP_OK;
}

// 模拟ISO-TP地址恢复函数
es_isotp_error_t es_isotp_restore_physical_address(es_isotp_connection_t *conn)
{
    if (conn == NULL) {
        return ES_ISOTP_ERR_INVALID_PARAM;
    }
    
    current_tx_id = 0x123; // 模拟物理地址
    address_restore_called = true;
    ES_PRINTF_I(TAG, "Mock: Restored physical address 0x%03X", current_tx_id);
    return ES_ISOTP_OK;
}

// 模拟ISO-TP发送函数
static es_async_t mock_isotp_send(es_coro_t *coro, es_isotp_connection_t *conn)
{
    es_co_begin(coro);

    ES_PRINTF_I(TAG, "Mock: Sending %d bytes via ISO-TP", conn->tx_length);

    es_co_end;
}

/**
 * @brief 测试广播请求识别
 */
static void test_broadcast_request_identification(void)
{
    printf("\n--- Testing Broadcast Request Identification ---\n");
    
    // 测试诊断会话控制广播
    uint8_t session_data[] = {ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL, ES_UDS_SESSION_DEFAULT};
    bool is_broadcast = es_uds_client_is_broadcast_request(ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL, 
                                                          session_data, sizeof(session_data));
    TEST_ASSERT_EQUAL(true, is_broadcast, "Default session should be broadcast");
    
    // 测试编程会话广播
    uint8_t prog_session_data[] = {ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL, ES_UDS_SESSION_PROGRAMMING};
    is_broadcast = es_uds_client_is_broadcast_request(ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL, 
                                                     prog_session_data, sizeof(prog_session_data));
    TEST_ASSERT_EQUAL(true, is_broadcast, "Programming session should be broadcast");
    
    // 测试扩展诊断会话（不应该广播）
    uint8_t ext_session_data[] = {ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL, ES_UDS_SESSION_EXTENDED_DIAGNOSTIC};
    is_broadcast = es_uds_client_is_broadcast_request(ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL, 
                                                     ext_session_data, sizeof(ext_session_data));
    TEST_ASSERT_EQUAL(false, is_broadcast, "Extended diagnostic session should not be broadcast");
    
    // 测试ECU重置广播
    uint8_t reset_data[] = {ES_UDS_SID_ECU_RESET, ES_UDS_RESET_HARD};
    is_broadcast = es_uds_client_is_broadcast_request(ES_UDS_SID_ECU_RESET, 
                                                     reset_data, sizeof(reset_data));
    TEST_ASSERT_EQUAL(true, is_broadcast, "Hard reset should be broadcast");
    
    // 测试DTC设置控制广播
    uint8_t dtc_data[] = {ES_UDS_SID_CONTROL_DTC_SETTING, 0x01};
    is_broadcast = es_uds_client_is_broadcast_request(ES_UDS_SID_CONTROL_DTC_SETTING, 
                                                     dtc_data, sizeof(dtc_data));
    TEST_ASSERT_EQUAL(true, is_broadcast, "DTC setting control should be broadcast");
    
    // 测试非广播请求
    uint8_t read_data[] = {ES_UDS_SID_READ_DATA_BY_IDENTIFIER, 0x12, 0x34};
    is_broadcast = es_uds_client_is_broadcast_request(ES_UDS_SID_READ_DATA_BY_IDENTIFIER, 
                                                     read_data, sizeof(read_data));
    TEST_ASSERT_EQUAL(false, is_broadcast, "Read data by identifier should not be broadcast");
}

// 测试地址切换的协程
static es_async_t test_address_switching_coro(es_coro_t *coro, int *result)
{
    es_co_begin(coro);
    
    // 重置测试状态
    address_switch_called = false;
    address_restore_called = false;
    current_tx_id = 0x123; // 物理地址
    
    // 创建UDS客户端连接
    es_uds_client_connection_t conn = {0};
    conn.isotp_conn = &mock_isotp_conn;
    conn.addr.physical_tx_id = 0x123;
    conn.addr.functional_tx_id = 0x7DF; // 标准功能地址
    
    // 设置缓冲区
    uint8_t tx_buffer[64];
    uint8_t rx_buffer[64];
    conn.tx_buffer = tx_buffer;
    conn.tx_buffer_size = sizeof(tx_buffer);
    conn.rx_buffer = rx_buffer;
    conn.rx_buffer_size = sizeof(rx_buffer);
    
    // 设置广播请求
    conn.current_request.service_id = ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL;
    conn.current_request.sub_function = ES_UDS_SESSION_DEFAULT;
    conn.current_request.request_data = NULL;
    conn.current_request.request_length = 0;
    
    // 调用发送请求函数（这应该触发地址切换）
    es_co_await_ex(err, es_uds_client_send_request, &conn);
    
    // 验证地址切换和恢复
    if (address_switch_called && address_restore_called) {
        *result = 1; // 成功
    } else {
        *result = 0; // 失败
    }
    
    es_co_end;
    
err:
    *result = 0; // 错误
    es_co_err;
}

/**
 * @brief 测试地址切换机制
 */
static void test_address_switching_mechanism(void)
{
    printf("\n--- Testing Address Switching Mechanism ---\n");
    
    es_coro_t coro = {0};
    int result = 0;
    es_async_t status;
    
    // 运行测试协程
    do {
        status = test_address_switching_coro(&coro, &result);
    } while (status == ES_ASYNC_YIELD || status == ES_ASYNC_WAIT);
    
    // 验证结果
    TEST_ASSERT_EQUAL(1, result, "Address switching should work correctly");
    TEST_ASSERT_EQUAL(true, address_switch_called, "Functional address should be set");
    TEST_ASSERT_EQUAL(true, address_restore_called, "Physical address should be restored");
}

/**
 * @brief 测试ISO-TP地址切换API
 */
static void test_isotp_address_api(void)
{
    printf("\n--- Testing ISO-TP Address API ---\n");
    
    es_isotp_connection_t conn = {0};
    conn.initialized = 1;
    conn.addr.tx_id = 0x123; // 物理地址
    
    // 测试功能地址设置
    es_isotp_error_t result = es_isotp_set_functional_address(&conn, 0x7DF);
    TEST_ASSERT_EQUAL(ES_ISOTP_OK, result, "Setting functional address should succeed");
    
    // 测试地址恢复
    result = es_isotp_restore_physical_address(&conn);
    TEST_ASSERT_EQUAL(ES_ISOTP_OK, result, "Restoring physical address should succeed");
    
    // 测试无效参数
    result = es_isotp_set_functional_address(NULL, 0x7DF);
    TEST_ASSERT_EQUAL(ES_ISOTP_ERR_INVALID_PARAM, result, "NULL connection should return error");
    
    result = es_isotp_restore_physical_address(NULL);
    TEST_ASSERT_EQUAL(ES_ISOTP_ERR_INVALID_PARAM, result, "NULL connection should return error");
}

/**
 * @brief 运行所有测试
 */
void run_uds_broadcast_addressing_tests(void)
{
    printf("=== UDS Broadcast Addressing Tests ===\n");
    
    test_broadcast_request_identification();
    test_address_switching_mechanism();
    test_isotp_address_api();
    
    printf("\n=== Test Results ===\n");
    printf("Passed: %d\n", test_passed);
    printf("Failed: %d\n", test_failed);
    printf("Total:  %d\n", test_passed + test_failed);
    
    if (test_failed == 0) {
        printf("✓ All tests passed!\n");
    } else {
        printf("✗ Some tests failed!\n");
    }
}

/**
 * @brief 主测试函数
 */
int main(void)
{
    // 初始化日志系统
    es_log_init();
    
    // 运行测试
    run_uds_broadcast_addressing_tests();
    
    return test_failed;
}
