/**
 * @file es.c
 * @brief ES system core implementation file
 * @version 1.0.0
 * @date 2023-12-01
 * 
 * @copyright Copyright (c) 2023
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include "es.h"
#include "es_drv_os.h"
#include "es_config.h"
#include "es_log.h"
#include "es_at_srv.h"
#include "es_drv_uart.h"
#include "es_coro.h"
#include "es_console.h"
#include "es_scheduler.h"
// #include "es_log_fs.h"
#include "es_flash.h"
// #include "es_cfg.h"
#include "es_pm.h"
#include "es_ota.h"
#include "es_at_cli.h"
#include "es_mem.h"
#include "es_mdm.h"
#include "es_lsm6dsl.h"
// #include "es_frame_fs.h"
#include "es_frame_v2_fs.h"
#include "es_cfg_v2.h"
#include "es_log_v2_fs.h"
#include "es_drv_can.h"
#include "es_veh.h"
#include "es_rule_engine.h"
#include "es.h"
/* Log tag */
#define TAG "ES"



/* Event data structure definitions */
typedef struct {
    uint8_t button_id;
    uint8_t state;  /* 0: released, 1: pressed */
} button_event_data_t;

typedef struct {
    uint8_t sensor_id;
    int16_t value;
    uint8_t status;
} sensor_event_data_t;

typedef struct {
    uint8_t timer_id;
    uint32_t count;
} timer_event_data_t;

typedef struct {
    uint8_t system_state;
    char message[32];
} system_event_data_t;

typedef struct {
    uint8_t emergency_type;
    uint8_t severity;
    char description[48];
} emergency_event_data_t;

typedef struct {
    uint8_t alarm_id;
    uint8_t alarm_type;
    uint16_t value;
    char location[16];
} alarm_event_data_t;

static es_coro_task_t mem_task = {
    .name = "memory_management",
    .func = NULL,
    .ctx = NULL
};

// Define static timers
static es_timer_t static_timer1 = {0};
static es_timer_t static_timer2 = {0};
static es_timer_t one_shot_timer = {0};
static es_timer_t immediate_timer = {0};

// Define event subscribers
static es_event_subscriber_t button_subscriber = {0};
static es_event_subscriber_t sensor_subscriber = {0};
static es_event_subscriber_t timer_subscriber = {0};
static es_event_subscriber_t all_events_subscriber = {0};
static es_event_subscriber_t emergency_subscriber = {0};
static es_event_subscriber_t alarm_subscriber = {0};

// Define event publisher timer
static es_timer_t event_publisher_timer = {0};

/**
 * @brief One-shot timer callback function
 */
static es_async_t one_shot_timer_callback(es_coro_t *coro, es_timer_t *timer, void *ctx)
{
    es_co_begin(coro);
    
    ES_PRINTF_D(TAG, "One-shot timer triggered, will not repeat");
    
    es_co_end;
}

/**
 * @brief Static timer 1 callback function
 */
static es_async_t static_timer1_callback(es_coro_t *coro, es_timer_t *timer, void *ctx)
{
    static int count = 0;
    es_co_begin(coro);
    
    count++;
    ES_PRINTF_D(TAG, "Static timer 1 triggered #%d, period: %u ms", count, timer->period);
    
    // Demonstrate how to modify timer period
    if (count == 5) {
        uint32_t new_period = timer->period * 2;
        ES_PRINTF_D(TAG, "Changing timer 1 period to %u ms", new_period);
        es_scheduler_timer_change_period(timer->scheduler, timer, new_period);
    }
    
    // Demonstrate how to stop timer
    if (count == 10) {
        ES_PRINTF_D(TAG, "Stopping timer 1");
        es_scheduler_timer_stop(timer->scheduler, timer);
    }
    
    es_co_end;
}

/**
 * @brief Static timer 2 callback function
 */
static es_async_t static_timer2_callback(es_coro_t *coro, es_timer_t *timer, void *ctx)
{
    static int count = 0;
    es_co_begin(coro);
    
    count++;
    ES_PRINTF_D(TAG, "Static timer 2 triggered #%d, period: %u ms, context: %s", 
                count, timer->period, (char*)ctx);
    
    // Demonstrate how to reset timer
    if (count % 3 == 0) {
        ES_PRINTF_D(TAG, "Resetting timer 2");
        es_scheduler_timer_reset(timer->scheduler, timer);
    }
    
    es_co_end;
}

/**
 * @brief Dynamic allocated timer callback function
 */
static es_async_t dynamic_timer_callback(es_coro_t *coro, es_timer_t *timer, void *ctx)
{
    static int count = 0;
    es_co_begin(coro);
    
    count++;
    ES_PRINTF_D(TAG, "Dynamic timer triggered #%d, period: %u ms", count, timer->period);
    
    // Demonstrate creating new timer in callback
    if (count == 3) {
        ES_PRINTF_D(TAG, "Creating a new one-shot timer in callback");
        es_timer_t *new_timer = es_scheduler_timer_alloc(5000, one_shot_timer_callback, NULL, true);
        if (new_timer) {
            es_scheduler_timer_start(timer->scheduler, new_timer);
            // This timer is one-shot and allocated through alloc, will be automatically released after execution, no need to manually call remove
        }
    }
    
    // Demonstrate how to remove timer
    if (count == 5) {
        ES_PRINTF_D(TAG, "Removing dynamic timer");
        // Since this timer is periodic (non-one-shot), even if allocated through alloc, it needs manual remove call
        es_scheduler_timer_remove(timer->scheduler, timer);
        // Timers allocated with es_scheduler_timer_alloc will automatically free memory after removal
    }
    
    es_co_end;
}

/**
 * @brief Immediate execution timer callback function
 */
static es_async_t immediate_timer_callback(es_coro_t *coro, es_timer_t *timer, void *ctx)
{
    static int count = 0;
    es_co_begin(coro);
    
    count++;
    ES_PRINTF_D(TAG, "Immediate timer triggered #%d, period: %u ms", count, timer->period);
    
    es_co_end;
}

/**
 * @brief Initialize and start timer examples
 */
static void timer_examples_init(void)
{
    es_scheduler_t *scheduler = es_scheduler_get_default();
    
    // Initialize static timer 1 - periodic, trigger once per second
    es_scheduler_timer_init(&static_timer1, 1000, static_timer1_callback, NULL, false);
    es_scheduler_timer_add(scheduler, &static_timer1);
    es_scheduler_timer_start(scheduler, &static_timer1);
    
    // Initialize static timer 2 - periodic, trigger once per 2 seconds, with context parameter
    static char *timer2_ctx = "Timer 2 context";
    es_scheduler_timer_init(&static_timer2, 2000, static_timer2_callback, timer2_ctx, false);
    // Can start directly, no need to add first
    es_scheduler_timer_start(scheduler, &static_timer2);
    
    // Initialize one-shot timer - trigger once after 10 seconds
    es_scheduler_timer_init(&one_shot_timer, 10000, one_shot_timer_callback, NULL, true);
    es_scheduler_timer_start(scheduler, &one_shot_timer);
    
    // Create timer using dynamic allocation - periodic, trigger once per 3 seconds
    es_timer_t *dynamic_timer = es_scheduler_timer_alloc(3000, dynamic_timer_callback, NULL, false);
    if (dynamic_timer) {
        es_scheduler_timer_start(scheduler, dynamic_timer);
    }
    
    // Use immediate execution to start timer - periodic, trigger once per 4 seconds, but execute immediately first time
    ES_PRINTF_D(TAG, "Initializing timer with immediate execution");
    es_scheduler_timer_init(&immediate_timer, 4000, immediate_timer_callback, NULL, false);
    // Use start_immediate instead of start, make timer execute immediately once, then execute periodically
    ES_PRINTF_D(TAG, "Starting timer with immediate execution");
    es_scheduler_timer_start_immediate(scheduler, &immediate_timer);
}

/**
 * @brief AT service VERSION command handler function
 */
es_async_t at_srv_cmd_version_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_QUERY) {
        // Query version
        es_at_srv_fmt_send("+VERSION: 1.2.3\r\n");
        es_at_srv_send_ok();
    } else {
        // Unsupported command type
        es_at_srv_send_error_code(2);
    }
    
    es_co_end;
}

/**
 * @brief AT service IMEI command handler function
 */
 es_async_t at_srv_cmd_imei_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_QUERY) {
        // Query IMEI
        es_at_srv_fmt_send("+IMEI: 123456789012345\r\n");
        es_at_srv_send_ok();
    } else {
        // Unsupported command type
        es_at_srv_send_error_code(2);
    }
    
    es_co_end;
}


/**
 * @brief AT service SN command handler function
 */
es_async_t at_srv_cmd_sn_handler(es_coro_t* coro, es_at_srv_cmd_ctx_t* ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);

    if (cmd_type == ES_AT_SRV_CMD_QUERY) {
        // Query SN
        es_at_srv_fmt_send("+SN: 123456789012345\r\n");
        es_at_srv_send_ok();
    } else if (cmd_type == ES_AT_SRV_CMD_SET) {
        // Set SN
        es_at_srv_fmt_send("+SN: %.*s\r\n", 
            ctx->params[0].len, ctx->params[0].param);
        es_at_srv_send_ok();
    } else {
        // Unsupported command type
        es_at_srv_send_error_code(2);
    }
    
    es_co_end;
}



/**
 * @brief AT service RST command handler function - used to exit demo
 */
es_async_t at_srv_cmd_rst_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_EXEC) {
        // Send restart response
        es_at_srv_fmt_send("+RST: Restarting...\r\n");
        es_os_reboot();
    } else {
        // Unsupported command type
        es_at_srv_send_error_code(2);
    }
    
    es_co_end;
}

/**
 * @brief Memory pool status monitoring coroutine
 */
static es_async_t mem_monitor_task(es_coro_t *coro, void *arg)
{
    static uint32_t last_mem_status_time_ms = 0;
    es_co_begin(coro);
    
    while(1) {
        es_co_yield;
        if(es_os_check_timeout(last_mem_status_time_ms, 3000)) {
            last_mem_status_time_ms = es_os_get_tick_ms();
            es_mem_dump_status();
        }
    }
    
    es_co_end;
}

/**
 * @brief Button event handler
 */
static void button_event_handler(const es_event_t *event, void *ctx)
{
    if (event->data_len == sizeof(button_event_data_t)) {
        button_event_data_t *data = (button_event_data_t *)event->u.data;
        ES_PRINTF_D(TAG, "[BUTTON] Button %d %s", 
                    data->button_id, 
                    data->state ? "pressed" : "released");
    } else if (event->data_len == 0) {
        /* Use 32-bit value data */
        ES_PRINTF_D(TAG, "[BUTTON] Button event value: 0x%08lX", event->u.value);
    }
}

/**
 * @brief Sensor event handler
 */
static void sensor_event_handler(const es_event_t *event, void *ctx)
{
    if (event->data_len == sizeof(sensor_event_data_t)) {
        sensor_event_data_t *data = (sensor_event_data_t *)event->u.data;
        ES_PRINTF_D(TAG, "[SENSOR] Sensor %d value: %d, status: %d", 
                    data->sensor_id, data->value, data->status);
    } else if (event->data_len == 0) {
        /* Use 32-bit value data */
        ES_PRINTF_D(TAG, "[SENSOR] Sensor value: %lu", event->u.value);
    }
}

/**
 * @brief Timer event handler
 */
static void timer_event_handler(const es_event_t *event, void *ctx)
{
    if (event->data_len == sizeof(timer_event_data_t)) {
        timer_event_data_t *data = (timer_event_data_t *)event->u.data;
        ES_PRINTF_D(TAG, "[TIMER] Timer %d triggered, count: %lu", 
                    data->timer_id, data->count);
    } else if (event->data_len == 0) {
        /* Use 32-bit value data */
        ES_PRINTF_D(TAG, "[TIMER] Timer value: %lu", event->u.value);
    }
}

/**
 * @brief All events handler (monitor all events)
 */
static void all_events_handler(const es_event_t *event, void *ctx)
{
    ES_PRINTF_D(TAG, "[MONITOR] Event type: %d, data_len: %d, dynamic_alloc: %d", 
                event->type, event->data_len, event->use_dynamic_alloc);
}

/**
 * @brief Emergency event handler
 */
static void emergency_event_handler(const es_event_t *event, void *ctx)
{
    if (event->data_len == sizeof(emergency_event_data_t)) {
        emergency_event_data_t *data = (emergency_event_data_t *)event->u.data;
        ES_PRINTF_D(TAG, "[EMERGENCY] Type: %d, Severity: %d, Description: %s", 
                    data->emergency_type, data->severity, data->description);
    } else if (event->data_len == 0) {
        /* Use 32-bit value data */
        ES_PRINTF_D(TAG, "[EMERGENCY] Emergency code: 0x%08lX", event->u.value);
    }
}

/**
 * @brief Alarm event handler
 */
static void alarm_event_handler(const es_event_t *event, void *ctx)
{
    if (event->data_len == sizeof(alarm_event_data_t)) {
        alarm_event_data_t *data = (alarm_event_data_t *)event->u.data;
        ES_PRINTF_D(TAG, "[ALARM] ID: %d, Type: %d, Value: %d, Location: %s", 
                    data->alarm_id, data->alarm_type, data->value, data->location);
    } else if (event->data_len == 0) {
        /* Use 32-bit value data */
        ES_PRINTF_D(TAG, "[ALARM] Alarm code: 0x%08lX", event->u.value);
    }
}

/**
 * @brief Event publisher timer callback function
 */
static es_async_t event_publisher_callback(es_coro_t *coro, es_timer_t *timer, void *ctx)
{
    static uint32_t publish_count = 0;
    static button_event_data_t btn_data = {0};
    static sensor_event_data_t sensor_data = {0};
    static timer_event_data_t timer_data = {0};
    static alarm_event_data_t alarm_data = {0};
    static system_event_data_t sys_data = {0};
    static emergency_event_data_t emergency_data = {0};
    
    es_scheduler_t *scheduler = timer->scheduler;
    
    es_co_begin(coro);
    
    publish_count++;
    
    /* Publish different types of events each time to demonstrate event system */
    switch (publish_count % 6) {
        case 1: {
            /* Publish button event */
            btn_data.button_id = 1;
            btn_data.state = publish_count % 2;
            ES_PRINTF_D(TAG, "Publishing button event...");
            es_scheduler_event_publish(scheduler, EVENT_TYPE_BUTTON, &btn_data, sizeof(btn_data));
            break;
        }
        case 2: {
            /* Publish sensor event */
            sensor_data.sensor_id = 1;
            sensor_data.value = (publish_count * 25) % 200;  /* Value varies between 0-200 */
            sensor_data.status = 1;
            ES_PRINTF_D(TAG, "Publishing sensor event...");
            es_scheduler_event_publish(scheduler, EVENT_TYPE_SENSOR, &sensor_data, sizeof(sensor_data));
            break;
        }
        case 3: {
            /* Publish timer event */
            timer_data.timer_id = 1;
            timer_data.count = publish_count;
            ES_PRINTF_D(TAG, "Publishing timer event...");
            es_scheduler_event_publish(scheduler, EVENT_TYPE_TIMER, &timer_data, sizeof(timer_data));
            break;
        }
        case 4: {
            /* Publish alarm event */
            alarm_data.alarm_id = 1;
            alarm_data.alarm_type = publish_count % 3;
            alarm_data.value = publish_count * 10;
            snprintf(alarm_data.location, sizeof(alarm_data.location), "Room%d", publish_count % 5);
            ES_PRINTF_D(TAG, "Publishing alarm event...");
            es_scheduler_event_publish(scheduler, EVENT_TYPE_ALARM, &alarm_data, sizeof(alarm_data));
            break;
        }
        case 5: {
            /* Publish system event */
            sys_data.system_state = publish_count % 3;
            snprintf(sys_data.message, sizeof(sys_data.message), "System event #%lu", publish_count);
            ES_PRINTF_D(TAG, "Publishing system event...");
            es_scheduler_event_publish(scheduler, EVENT_TYPE_SYSTEM, &sys_data, sizeof(sys_data));
            break;
        }
        case 0: {
            /* Publish emergency event */
            emergency_data.emergency_type = publish_count % 3;
            emergency_data.severity = (publish_count % 3) + 1;
            snprintf(emergency_data.description, sizeof(emergency_data.description), 
                     "Emergency situation #%lu detected", publish_count);
            ES_PRINTF_D(TAG, "Publishing emergency event...");
            es_scheduler_event_publish(scheduler, EVENT_TYPE_EMERGENCY, &emergency_data, sizeof(emergency_data));
            break;
        }
    }
    
    /* Use macro to publish value event */
    if (publish_count % 3 == 0) {
        ES_PRINTF_D(TAG, "Publishing value event using macro...");
        ES_EVENT_PUBLISH_VALUE(EVENT_TYPE_SYSTEM, publish_count);
    }
    
    /* Demo: stop publishing events after 15 times */
    if (publish_count >= 15) {
        ES_PRINTF_D(TAG, "Event publishing completed, stopping timer");
        es_scheduler_timer_stop(scheduler, timer);
    }
    
    es_co_end;
}

/**
 * @brief Initialize event system examples
 */
static void event_system_examples_init(void)
{
    es_scheduler_t *scheduler = es_scheduler_get_default();
    
    ES_PRINTF_D(TAG, "Initializing event system examples...");
    
    /* Initialize and subscribe to button events */
    es_scheduler_event_subscriber_init(&button_subscriber, EVENT_TYPE_BUTTON, 
                                       button_event_handler, NULL);
    es_scheduler_event_subscribe(scheduler, &button_subscriber);
    
    /* Initialize and subscribe to sensor events */
    es_scheduler_event_subscriber_init(&sensor_subscriber, EVENT_TYPE_SENSOR, 
                                       sensor_event_handler, NULL);
    es_scheduler_event_subscribe(scheduler, &sensor_subscriber);
    
    /* Initialize and subscribe to timer events */
    es_scheduler_event_subscriber_init(&timer_subscriber, EVENT_TYPE_TIMER, 
                                       timer_event_handler, NULL);
    es_scheduler_event_subscribe(scheduler, &timer_subscriber);
    
    /* Initialize and subscribe to emergency events */
    es_scheduler_event_subscriber_init(&emergency_subscriber, EVENT_TYPE_EMERGENCY, 
                                       emergency_event_handler, NULL);
    es_scheduler_event_subscribe(scheduler, &emergency_subscriber);
    
    /* Initialize and subscribe to alarm events */
    es_scheduler_event_subscriber_init(&alarm_subscriber, EVENT_TYPE_ALARM, 
                                       alarm_event_handler, NULL);
    es_scheduler_event_subscribe(scheduler, &alarm_subscriber);
    
    /* Initialize and subscribe to all events (for monitoring) */
    es_scheduler_event_subscriber_init(&all_events_subscriber, 0,  /* 0 means subscribe to all events */
                                       all_events_handler, NULL);
    es_scheduler_event_subscribe(scheduler, &all_events_subscriber);
    
    /* Initialize event publisher timer - publish one event every 2 seconds */
    es_scheduler_timer_init(&event_publisher_timer, 2000, event_publisher_callback, NULL, false);
    es_scheduler_timer_start(scheduler, &event_publisher_timer);
    
    ES_PRINTF_D(TAG, "Event system examples initialized successfully");
    ES_PRINTF_D(TAG, "Regular events will be published every 2s");
    ES_PRINTF_D(TAG, "All events are now processed synchronously");
}

static const uint8_t rule_data[] = {
    0x01, 0x0e, 0xd6, 0x08, 0x01, 0xc9, 0x01, 0x80, 0x80, 0x01, 0x01, 0xbd, 0x05, 0x80, 0x80, 0x7b,
    0x01, 0x91, 0x03, 0x07, 0xb7, 0x05, 0x01, 0xad, 0x02, 0x80, 0x40, 0x0a, 0x02, 0x65, 0x07, 0x66,
    0x88, 0x40, 0x9b, 0x02, 0x01, 0xa1, 0x06, 0x80, 0x40, 0xd7, 0x08, 0x01, 0x85, 0x07, 0x07, 0xd7,
    0x08, 0x01, 0xe9, 0x07, 0x80, 0x40, 0xd7, 0x08, 0x01, 0xcd, 0x08, 0x07, 0x7b, 0x01, 0xf5, 0x03,
    0x80, 0x40, 0xdc, 0x07, 0x01, 0xb1, 0x09, 0x80, 0x40, 0xd7, 0x08, 0x01, 0x95, 0x0a, 0x80, 0x80,
    0xd7, 0x08, 0x01, 0xf9, 0x0a, 0x07, 0xd7, 0x08, 0x01, 0xd9, 0x04, 0x07, 0x0e, 0xe9, 0x07, 0x01,
    0x04, 0x02, 0x65, 0x66, 0xe8, 0x07, 0x64, 0x00, 0x64, 0xea, 0x07, 0x01, 0x00, 0x01, 0xc9, 0x01,
    0xd0, 0x0f, 0x00, 0x10, 0x65, 0xeb, 0x07, 0x01, 0x03, 0x01, 0xad, 0x02, 0xf4, 0x03, 0x32, 0x20,
    0xc9, 0x01, 0xec, 0x07, 0x01, 0x02, 0x01, 0x91, 0x03, 0xdc, 0x0b, 0xc8, 0x01, 0x30, 0x04, 0xed,
    0x07, 0x01, 0x09, 0x01, 0xf5, 0x03, 0xb8, 0x17, 0x00, 0x40, 0xee, 0x07, 0x01, 0x0d, 0x01, 0xd9,
    0x04, 0xa0, 0x06, 0x64, 0x50, 0x05, 0xef, 0x07, 0x01, 0x01, 0x01, 0xbd, 0x05, 0xb0, 0x09, 0x96,
    0x01, 0x62, 0x10, 0x65, 0x00, 0x32, 0xf0, 0x07, 0x01, 0x05, 0x01, 0xa1, 0x06, 0xc4, 0x13, 0xac,
    0x02, 0x70, 0x02, 0x10, 0x65, 0x50, 0x03, 0xf1, 0x07, 0x01, 0x06, 0x01, 0x85, 0x07, 0x88, 0x0e,
    0xfa, 0x01, 0x80, 0x10, 0xc9, 0x01, 0x00, 0x0a, 0xf2, 0x07, 0x01, 0x07, 0x01, 0xe9, 0x07, 0x98,
    0x11, 0x90, 0x03, 0x92, 0x10, 0xad, 0x02, 0x00, 0xff, 0x01, 0xf3, 0x07, 0x01, 0x08, 0x01, 0xcd,
    0x08, 0xe8, 0x07, 0x00, 0x64, 0x82, 0x10, 0x65, 0x00, 0x02, 0x00, 0x64, 0xf4, 0x07, 0x01, 0x0a,
    0x01, 0xb1, 0x09, 0xdc, 0x0b, 0x64, 0x71, 0x02, 0x60, 0x10, 0xc9, 0x01, 0x00, 0x00, 0x50, 0x07,
    0xf5, 0x07, 0x01, 0x0b, 0x01, 0x95, 0x0a, 0xb8, 0x17, 0xf4, 0x03, 0x72, 0x01, 0x61, 0x91, 0x10,
    0xad, 0x02, 0x00, 0x04, 0x00, 0x0f, 0xf6, 0x07, 0x01, 0x0c, 0x01, 0xf9, 0x0a, 0xa0, 0x06, 0xc8,
    0x01, 0x94, 0x10, 0x91, 0x03
};

static uint32_t get_event_value(uint16_t event_id) {
    if(event_id == 1) {
        return es_read_acc_state();
    }
    return 0;
}

static uint32_t get_io_level(uint16_t pin) {
    return 0;
}

static void send_frame(const es_rule_frame* frame) {
    // Send CAN frame
    ES_PRINTF_D(TAG, "Sending frame cmd 0x%02X", frame->cmd);
}

static es_rule_sys_if sys_if = {
    .get_event_value = get_event_value,
    .get_io_level = get_io_level,
    .send_frame = send_frame,
};

/**
 * @brief Initialize ES system
 * @return int 0 indicates success, non-zero indicates failure
 */
int es_init(void)
{
    //int ret;
    es_os_init();
    es_mem_init();
    es_console_init();
    es_flash_init();
    es_log_init();
    // es_cfg_init();
    es_cfg_v2_init();
	// es_log_ringfs_init();
    es_log_v2_init();
    // es_frame_fs_init();
    es_frame_v2_init();
    es_can_init();
    es_veh_init();
    es_at_srv_init();
    es_pm_init();
    es_ota_init();
     es_mdm_init();
    // es_lsm6dsl_init();
    
    // Initialize timer examples
//     timer_examples_init();
    
    // Initialize event system examples
    // event_system_examples_init();

    es_rule_engine_init(&sys_if, rule_data, sizeof(rule_data));
    
    es_scheduler_t *scheduler = es_scheduler_get_default();
    
    mem_task.func = mem_monitor_task;
    mem_task.ctx = NULL;

    // es_scheduler_task_add(scheduler, &mem_task);  // 1000ms period

    // Add loop count statistics variables
    uint32_t loop_count = 0;
    uint32_t last_stats_time_ms = es_os_get_tick_ms();

    while(1) {
        es_scheduler_run_once(scheduler);
        
        // Increment loop count
        loop_count++;
        
        // // Output statistics once per second
        // uint32_t now_ms = es_os_get_tick_ms();
        // if (es_os_check_timeout(last_stats_time_ms, 1000)) {
        //     ES_PRINTF_D(TAG, "Loop count per second: %lu", loop_count);
        //     loop_count = 0;
        //     last_stats_time_ms = now_ms;
            
        //     // Check long-running coroutines (1 minute timeout)
        //     es_coro_monitor_timeout(60000);
        // }
    }
    
    return 0;
}
