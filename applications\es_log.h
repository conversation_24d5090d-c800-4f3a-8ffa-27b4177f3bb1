/**
 * @file es_log.h
 * @brief Simple logging module
 * @date 2024/10/1
 */

#ifndef __ES_LOG_H__
#define __ES_LOG_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <stdarg.h>
#include <string.h>
#include "es_list.h"  /* Add list header file */

/**
 * @brief Log level enumeration
 */
typedef enum {
    ES_LOG_LEVEL_NONE = 0,  /**< No log output */
    ES_LOG_LEVEL_ERROR,     /**< Error log */
    ES_LOG_LEVEL_WARN,      /**< Warning log */
    ES_LOG_LEVEL_INFO,      /**< Information log */
    ES_LOG_LEVEL_DEBUG,     /**< Debug log */
    ES_LOG_LEVEL_VERBOSE    /**< Verbose log */
} es_log_level_t;

/**
 * @brief Log sink callback function type definition
 * @param level Log level
 * @param log_msg Log message string
 * @param len Log message length
 */
typedef void (*es_log_sink_func_t)(es_log_level_t level, bool to_flash, const char *log_msg, uint32_t len);

/**
 * @brief Log sink structure definition
 */
typedef struct {
    const char *name;                /**< Sink name */
    struct es_list_head node;           /**< List node */
    es_log_sink_func_t sink_func;    /**< Sink callback function */
    void *user_data;                 /**< User data */
} es_log_sink_t;

/**
 * @brief Global log level setting
 * Logs with lower levels than this will not be printed
 */
extern es_log_level_t g_es_log_level;

/**
 * @brief Set log level
 * @param level Log level
 */
void es_log_set_level(es_log_level_t level);

/**
 * @brief Get current log level
 * @return Current log level
 */
es_log_level_t es_log_get_level(void);

/**
 * @brief Set log output state (enable/disable)
 * @param enabled true to enable log output, false to disable
 */
void es_log_set_enabled(bool enabled);

/**
 * @brief Get current log output state
 * @return true if log output is enabled, false otherwise
 */
bool es_log_get_enabled(void);

/**
 * @brief Initialize log module
 * @return 0 on success, non-zero on failure
 */
int es_log_init(void);

/**
 * @brief Deinitialize log module
 */
void es_log_deinit(void);

/**
 * @brief Register a log sink
 * @param sink Pointer to sink structure
 * @return 0 on success, non-zero on failure
 */
int es_log_add_sink(es_log_sink_t *sink);

/**
 * @brief Unregister a log sink
 * @param sink Pointer to sink structure
 * @return 0 on success, non-zero on failure
 */
int es_log_remove_sink(es_log_sink_t *sink);

/**
 * @brief Find a log sink by name
 * @param name Sink name
 * @return Pointer to sink structure or NULL if not found
 */
es_log_sink_t *es_log_find_sink(const char *name);

/**
 * @brief Original log output function
 * @param level Log level
 * @param tag Log tag
 * @param file Source file name (not used in output anymore)
 * @param line Line number
 * @param func Function name (not used)
 * @param to_flash Whether to write log to flash
 * @param fmt Format string
 * @param ... Variable arguments
 */
void es_log_write(es_log_level_t level, const char *tag, bool to_flash, const char *fmt, ...);



/**
 * @brief Print hexadecimal data
 * @param tag Log tag
 * @param width Number of bytes to display per line
 * @param buf Data buffer
 * @param size Data size
 * @note This function uses VERBOSE log level
 */
void es_log_hex_dump(const char *tag, int width, const void *buf, int size);

/* Log macro definitions */
/* Note: __FILE__ is still passed for compatibility but not printed in output */
#define ES_LOGE(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_ERROR, tag, true, fmt, ##__VA_ARGS__)
#define ES_LOGW(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_WARN, tag, true, fmt, ##__VA_ARGS__)
#define ES_LOGI(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_INFO, tag, true, fmt, ##__VA_ARGS__)
#define ES_LOGD(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_DEBUG, tag, true, fmt, ##__VA_ARGS__)
#define ES_LOGV(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_VERBOSE, tag, true, fmt, ##__VA_ARGS__)


/* Simplified log macro definitions */
#define ES_PRINTF_E(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_ERROR, tag, false, fmt, ##__VA_ARGS__)
#define ES_PRINTF_W(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_WARN, tag, false, fmt, ##__VA_ARGS__)
#define ES_PRINTF_I(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_INFO, tag, false, fmt, ##__VA_ARGS__)
#define ES_PRINTF_D(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_DEBUG, tag, false, fmt, ##__VA_ARGS__)
#define ES_PRINTF_V(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_VERBOSE, tag, false, fmt, ##__VA_ARGS__)

/* Hexadecimal data printing macro (uses VERBOSE level) */
#define ES_LOG_HEX(tag, width, buf, size) es_log_hex_dump(tag, width, buf, size)


#ifdef __cplusplus
}
#endif

#endif /* __ES_LOG_H__ */ 
