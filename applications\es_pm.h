/**
 * @file es_pm.h
 * @brief Power management module header file
 */

#ifndef __ES_PM_H__
#define __ES_PM_H__

#include <stdint.h>
#include "es_drv_os.h"
#include "es_coro.h"
#include "es_list.h"
#include "es_scheduler.h"
#include "es_drv_adc.h"

#ifdef __cplusplus
extern "C" {
#endif


#define ES_PM_LPM_DEV_PM    (1 << 0)
#define ES_PM_LPM_DEV_CAN   (1 << 1)
#define ES_PM_LPM_DEV_ALL   (ES_PM_LPM_DEV_PM | ES_PM_LPM_DEV_CAN)

// Voltage sampling configuration
#define PM_VOLTAGE_SAMPLE_COUNT     5               // 电压采样次数
#define PM_VOLTAGE_UPDATE_INTERVAL  1000            // 电压更新间隔，单位ms

// Voltage conversion factors
#define PM_MAINPWR_FACTOR           40.0f           // 主电源电压转换系数
#define PM_BATTERY_FACTOR           4.0f            // 电池电压转换系数

enum {
    ES_PM_STATE_OFF = 0,
    ES_PM_STATE_ON,
    ES_PM_STATE_SLEEP_READY,
    ES_PM_STATE_SLEEP,
    ES_PM_STATE_MAX,
    ES_PM_STATE_NONE,
};

/**
 * @brief Power management event definitions - bit-wise definitions to support event combinations
 */
#define ES_PM_EVENT_NONE    0               ///< No event
#define ES_PM_EVENT_ENTRY    (1 << 0)        ///< 进入状态事件
#define ES_PM_EVENT_EXIT     (1 << 1)        ///< 退出状态事件
#define ES_PM_EVENT_TICK     (1 << 2)        ///< 定时事件
#define ES_PM_EVENT_ON       (1 << 3)        ///< 检测到ON信号（
#define ES_PM_EVENT_OFF      (1 << 4)        ///< 检测到OFF信号
#define ES_PM_EVENT_SLEEP    (1 << 5)        ///< 休眠事件


typedef struct {
    uint8_t cur_state;
    uint8_t next_state;
    uint32_t lpm_dev_mask;
    uint32_t pending_event;
    es_coro_task_t pm_task;
    uint32_t enter_state_tick_ms;
} es_pm_ctx_t;

extern es_pm_ctx_t g_pm_ctx;


/**
 * @brief Power management module initialization
 *
 * @param scheduler Coroutine scheduler pointer, pass NULL to use default scheduler
 * @return int 0: success, other values: failure
 */
int es_pm_init(void);

/**
 * @brief Set whether power management task is enabled
 *
 * @param en Whether to enable
 */
void es_pm_set_task_enable(bool en);


/**
 * @brief Enter low power sleep state
 *
 * @return int 0: success, other values: failure
 */
int es_pm_enter_lpm_stop(uint32_t wke_mask);

/**
 * @brief Trigger power management event
 *
 * @param event Event type
 */
void es_pm_event_trigger(uint32_t event);

/**
 * @brief Get current voltage value
 *
 * @return uint16_t Current voltage value in mV
 */
uint16_t es_pm_get_voltage(void);

/**
 * @brief Get main power supply voltage
 *
 * @return uint16_t Main power supply voltage value in mV
 */
uint16_t es_pm_get_main_voltage(void);

/**
 * @brief Get battery voltage
 *
 * @return uint16_t Battery voltage value in mV
 */
uint16_t es_pm_get_battery_voltage(void);

/**
 * @brief Initialize ADC sampling
 *
 * @return int 0: success, other values: failure
 */
int es_pm_adc_init(void);


int es_read_acc_state(void);


#ifdef __cplusplus
}
#endif

#endif /* __ES_PM_H__ */ 
