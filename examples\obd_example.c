/**
 * @file obd_example.c
 * @brief OBD-II Client Usage Example
 *
 * This example demonstrates how to use the OBD-II client module
 * to communicate with vehicle ECUs for diagnostic purposes.
 *
 * <AUTHOR> MCU Framework
 * @date 2025-01-22
 * @version 1.0.0
 */

#include "es_obd.h"
#include "es_veh.h"
#include "es_scheduler.h"

#define TAG "OBD_EXAMPLE"

/* Global OBD connection */
static es_obd_connection_t g_obd_conn;
static bool g_obd_initialized = false;

/* ========================================================================== */
/*                            EXAMPLE FUNCTIONS                              */
/* ========================================================================== */

/**
 * @brief Initialize OBD-II connection
 */
static es_obd_error_t obd_example_init(void) {
    if (g_obd_initialized) {
        return ES_OBD_OK;
    }
    
    // Initialize OBD connection for ECU 0 using physical addressing
    es_obd_error_t ret = es_obd_init(&g_obd_conn, 0, false);
    if (ret != ES_OBD_OK) {
        ES_PRINTF_I(TAG, "Failed to initialize OBD connection: %d", ret);
        return ret;
    }
    
    g_obd_initialized = true;
    ES_PRINTF_I(TAG, "OBD-II connection initialized successfully");
    return ES_OBD_OK;
}

/**
 * @brief Read basic engine parameters (coroutine)
 */
static es_async_t obd_read_basic_engine_data(es_coro_t *coro) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Reading Basic Engine Data ===");
    
    // Read Engine RPM
    es_co_await_ex(err, es_obd_read_current_data, &g_obd_conn, ES_OBD_PID_ENGINE_RPM);
    es_obd_parse_engine_data(&g_obd_conn, ES_OBD_PID_ENGINE_RPM);
    
    // Read Vehicle Speed
    es_co_await_ex(err, es_obd_read_current_data, &g_obd_conn, ES_OBD_PID_VEHICLE_SPEED);
    es_obd_parse_engine_data(&g_obd_conn, ES_OBD_PID_VEHICLE_SPEED);
    
    // Read Coolant Temperature
    es_co_await_ex(err, es_obd_read_current_data, &g_obd_conn, ES_OBD_PID_COOLANT_TEMP);
    es_obd_parse_engine_data(&g_obd_conn, ES_OBD_PID_COOLANT_TEMP);
    
    // Read Engine Load
    es_co_await_ex(err, es_obd_read_current_data, &g_obd_conn, ES_OBD_PID_ENGINE_LOAD);
    es_obd_parse_engine_data(&g_obd_conn, ES_OBD_PID_ENGINE_LOAD);
    
    // Read Throttle Position
    es_co_await_ex(err, es_obd_read_current_data, &g_obd_conn, ES_OBD_PID_THROTTLE_POSITION);
    es_obd_parse_engine_data(&g_obd_conn, ES_OBD_PID_THROTTLE_POSITION);
    
    ES_PRINTF_I(TAG, "Basic engine data read completed");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Failed to read basic engine data");
    );
}

/**
 * @brief Check supported PIDs (coroutine)
 */
static es_async_t obd_check_supported_pids(es_coro_t *coro) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Checking Supported PIDs ===");
    
    // Check PIDs 01-20
    es_co_await_ex(err, es_obd_read_current_data, &g_obd_conn, ES_OBD_PID_SUPPORTED_PIDS_01_20);
    
    // Check if common PIDs are supported
    bool rpm_supported = es_obd_is_pid_supported(&g_obd_conn, 0x00, ES_OBD_PID_ENGINE_RPM);
    bool speed_supported = es_obd_is_pid_supported(&g_obd_conn, 0x00, ES_OBD_PID_VEHICLE_SPEED);
    bool coolant_supported = es_obd_is_pid_supported(&g_obd_conn, 0x00, ES_OBD_PID_COOLANT_TEMP);
    bool load_supported = es_obd_is_pid_supported(&g_obd_conn, 0x00, ES_OBD_PID_ENGINE_LOAD);
    
    ES_PRINTF_I(TAG, "PID Support Status:");
    ES_PRINTF_I(TAG, "  Engine RPM (0x0C): %s", rpm_supported ? "Supported" : "Not Supported");
    ES_PRINTF_I(TAG, "  Vehicle Speed (0x0D): %s", speed_supported ? "Supported" : "Not Supported");
    ES_PRINTF_I(TAG, "  Coolant Temp (0x05): %s", coolant_supported ? "Supported" : "Not Supported");
    ES_PRINTF_I(TAG, "  Engine Load (0x04): %s", load_supported ? "Supported" : "Not Supported");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Failed to check supported PIDs");
    );
}

/**
 * @brief Read and display DTCs (coroutine)
 */
static es_async_t obd_read_dtcs(es_coro_t *coro) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Reading Diagnostic Trouble Codes ===");
    
    // Read stored DTCs
    es_co_await_ex(err, es_obd_read_stored_dtcs, &g_obd_conn);
    ES_PRINTF_I(TAG, "Stored DTCs:");
    es_obd_parse_dtcs(&g_obd_conn);
    
    // Read pending DTCs
    es_co_await_ex(err, es_obd_read_pending_dtcs, &g_obd_conn);
    ES_PRINTF_I(TAG, "Pending DTCs:");
    es_obd_parse_dtcs(&g_obd_conn);
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Failed to read DTCs");
    );
}

/**
 * @brief Read vehicle information (coroutine)
 */
static es_async_t obd_read_vehicle_info(es_coro_t *coro) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Reading Vehicle Information ===");
    
    // Read VIN
    es_co_await_ex(err, es_obd_request_vehicle_info, &g_obd_conn, ES_OBD_VIN_VEHICLE_IDENTIFICATION);
    
    char vin_buffer[18];
    uint8_t vin_length = es_obd_extract_vin(&g_obd_conn, vin_buffer);
    if (vin_length > 0) {
        ES_PRINTF_I(TAG, "Vehicle VIN: %s", vin_buffer);
    } else {
        ES_PRINTF_I(TAG, "Failed to extract VIN from response");
    }
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Failed to read vehicle information");
    );
}

/**
 * @brief Complete OBD diagnostic example (coroutine)
 */
static es_async_t obd_complete_diagnostic_example(es_coro_t *coro) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "Starting OBD-II Diagnostic Example");
    
    // Initialize OBD connection
    if (obd_example_init() != ES_OBD_OK) {
        es_co_err;
    }
    
    // Check supported PIDs first
    es_co_await_ex(err, obd_check_supported_pids);
    
    es_co_sleep(1000);  // Wait 1 second
    
    // Read basic engine data
    es_co_await_ex(err, obd_read_basic_engine_data);
    
    es_co_sleep(1000);  // Wait 1 second
    
    // Read DTCs
    es_co_await_ex(err, obd_read_dtcs);
    
    es_co_sleep(1000);  // Wait 1 second
    
    // Read vehicle information
    es_co_await_ex(err, obd_read_vehicle_info);
    
    ES_PRINTF_I(TAG, "OBD-II Diagnostic Example completed successfully");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "OBD-II Diagnostic Example failed");
    );
}

/**
 * @brief Continuous engine monitoring task (coroutine)
 */
static es_async_t obd_continuous_monitoring_task(es_coro_t *coro, void *ctx) {
    static uint32_t last_read_time = 0;
    
    es_co_begin(coro);
    
    // Initialize OBD connection
    if (obd_example_init() != ES_OBD_OK) {
        es_co_sleep(5000);  // Wait 5 seconds before retry
        es_co_exit;
    }
    
    while (1) {
        uint32_t current_time = es_os_get_tick_ms();
        
        // Read engine data every 2 seconds
        if (current_time - last_read_time >= 2000) {
            ES_PRINTF_I(TAG, "--- Engine Status Update ---");
            
            // Read RPM
            if (es_obd_read_current_data(coro, &g_obd_conn, ES_OBD_PID_ENGINE_RPM) == ES_ASYNC_DONE) {
                es_obd_parse_engine_data(&g_obd_conn, ES_OBD_PID_ENGINE_RPM);
            }
            
            es_co_yield;
            
            // Read Speed
            if (es_obd_read_current_data(coro, &g_obd_conn, ES_OBD_PID_VEHICLE_SPEED) == ES_ASYNC_DONE) {
                es_obd_parse_engine_data(&g_obd_conn, ES_OBD_PID_VEHICLE_SPEED);
            }
            
            es_co_yield;
            
            // Read Coolant Temperature
            if (es_obd_read_current_data(coro, &g_obd_conn, ES_OBD_PID_COOLANT_TEMP) == ES_ASYNC_DONE) {
                es_obd_parse_engine_data(&g_obd_conn, ES_OBD_PID_COOLANT_TEMP);
            }
            
            last_read_time = current_time;
        }
        
        es_co_yield;
    }
    
    es_co_end;
}

/**
 * @brief CAN message handler for OBD responses
 */
static void obd_can_message_handler(const es_can_msg_t *msg) {
    if (!g_obd_initialized) {
        return;
    }
    
    // Process OBD-II CAN messages (0x7E8-0x7EF range)
    if (msg->id >= ES_OBD_PHYSICAL_RX_BASE && msg->id <= (ES_OBD_PHYSICAL_RX_BASE + 7)) {
        es_obd_process_can_message(&g_obd_conn, msg);
    }
}

/**
 * @brief Initialize OBD example module
 */
void obd_example_module_init(void) {
    ES_PRINTF_I(TAG, "OBD Example Module Initialized");
    
    // Register CAN message handler for OBD responses
    // Note: This would typically be done through the vehicle module's event system
    // es_veh_register_can_handler(obd_can_message_handler);
}

/**
 * @brief Run one-time OBD diagnostic example
 */
void obd_example_run_diagnostic(void) {
    static es_coro_t diagnostic_coro = {0};
    
    // Run the complete diagnostic example
    obd_complete_diagnostic_example(&diagnostic_coro);
}

/**
 * @brief Start continuous OBD monitoring
 */
void obd_example_start_monitoring(void) {
    static es_coro_task_t monitoring_task;
    
    // Initialize and add monitoring task to scheduler
    es_scheduler_task_init(&monitoring_task, "obd_monitor", obd_continuous_monitoring_task, NULL);
    es_scheduler_task_add(es_scheduler_get_default(), &monitoring_task);
    
    ES_PRINTF_I(TAG, "OBD continuous monitoring started");
}
