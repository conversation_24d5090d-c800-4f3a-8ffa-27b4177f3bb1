/**
 * @file es_veh.c
 * @brief Vehicle CAN data processing module implementation
 * @date 2024/10/1
 */

#include <stdio.h>
#include <string.h>
#include "es_drv_can.h"
#include "es_list.h"
#include "es_coro.h"
#include "es_config.h"
#include "es_scheduler.h"
#include "es_log.h"
#include "es_drv_can.h"
#include <stdint.h>
#include <stdbool.h>
#include "es_veh.h"
#include "es_at_srv.h"
#include "es_pm.h"
#include "es.h"
#define TAG "VEH"

/**
 * @brief Vehicle CAN context structure
 */
typedef struct {
    bool inited;                         /* Vehicle module initialization state */
    es_coro_task_t rx_task;             /* CAN receive task */
} es_veh_ctx_t;

/* Vehicle context singleton instance */
static es_veh_ctx_t s_veh_ctx = {0};

static es_at_srv_sink_t at_srv_veh_sink = {0};

static es_async_t es_at_srv_veh_sink_func(es_coro_t *coro, const uint8_t *data, uint16_t len)
{
    es_co_begin(coro);
    es_co_await(es_can_coro_write, 0x444, data, len);
    es_co_end;
}

/**
 * @brief CAN message handler callback function
 * @param msg Pointer to received CAN message
 */
static void es_veh_can_msg_handler(const es_can_msg_t *msg)
{
    if (!msg) {
        return;
    }

    /* Log received CAN message */
    ES_LOGI(TAG, "CAN RX: ID=0x%08X, IDE=%d, RTR=%d, DLC=%d", 
             msg->id, msg->ide, msg->rtr, msg->dlc);
    
    if (!msg->rtr && msg->dlc > 0) {
        /* Log data for data frames */
        char data_str[32] = {0};
        int pos = 0;
        for (int i = 0; i < msg->dlc && i < 8; i++) {
            pos += snprintf(data_str + pos, sizeof(data_str) - pos, "%02X ", msg->data[i]);
        }
        ES_LOGI(TAG, "CAN Data: %s", data_str);
    }

    /* TODO: Add your CAN message processing logic here */
    /* Example: Parse specific CAN IDs, extract vehicle data, etc. */
    switch (msg->id) {
        case 0x123:  /* Example: Engine RPM */
            if (msg->dlc >= 2) {
                uint16_t rpm = (msg->data[0] << 8) | msg->data[1];
                ES_LOGI(TAG, "Engine RPM: %d", rpm);
            }
            break;
            
        case 0x456:  /* Example: Vehicle Speed */
            if (msg->dlc >= 1) {
                uint8_t speed = msg->data[0];
                ES_LOGI(TAG, "Vehicle Speed: %d km/h", speed);
            }
            break;
            
        default:
            /* Handle other CAN messages */
            break;
    }
}

/**
 * @brief CAN接收数据处理协程
 * @param coro 协程上下文
 * @param ctx 协程上下文参数
 * @return 协程返回值
 */
static es_async_t es_veh_rx_task_func(es_coro_t *coro, void *ctx)
{
    es_can_msg_t can_msg;
    int ret;

    es_co_begin(coro);

    while (1) {
        es_co_yield;

        /* 检查是否有可用的CAN数据 */
        es_co_wait(es_can_available() > 0);

        /* 读取CAN消息 */
        ret = es_can_read(&can_msg);
        if (ret > 0) {
            //if is 0x333, then pass to at srv
            if (can_msg.id == 0x333) {
                es_at_srv_recv(at_srv_veh_sink.src_type, can_msg.data, can_msg.dlc);
            }
            else {
            /* 处理接收到的CAN消息 */
                // es_veh_can_msg_handler(&can_msg);
                es_scheduler_event_publish(es_scheduler_get_default(), EVENT_TYPE_CAN_MSG, &can_msg, sizeof(can_msg));
            }
        }

        // if(g_pm_ctx.lpm_dev_mask != 0) {
        //     g_pm_ctx.lpm_dev_mask |= ES_PM_LPM_DEV_CAN;
        //     ES_LOGI(TAG, "CAN LPM dev ready, mask: 0x%08X", g_pm_ctx.lpm_dev_mask);
        //     es_co_wait(g_pm_ctx.lpm_dev_mask == 0);
        //     ES_LOGI(TAG, "CAN LPM dev exit, mask: 0x%08X", g_pm_ctx.lpm_dev_mask);
        // }
    }

    es_co_end;
}

/**
 * @brief Initialize vehicle CAN module
 * @return 0 on success, non-zero on failure
 */
int es_veh_init(void)
{
    if (s_veh_ctx.inited) {
        return 0;
    }

    int ret = 0;

    /* 初始化CAN模块 */
    ret = es_can_init();
    if (ret != 0) {
        ES_LOGE(TAG, "CAN init failed: %d", ret);
        return -1;
    }

    at_srv_veh_sink.src_type = es_at_srv_alloc_src_type();
    at_srv_veh_sink.sink_func = es_at_srv_veh_sink_func;

    es_at_srv_add_sink(&at_srv_veh_sink);

    /* 初始化CAN接收任务 */
    s_veh_ctx.rx_task = (es_coro_task_t) {
        .name = "veh_can_rx",
        .func = es_veh_rx_task_func,
        .ctx = NULL
    };
    
    /* 将接收任务添加到默认调度器 */
    ret = es_scheduler_task_add(es_scheduler_get_default(), &s_veh_ctx.rx_task);
    if (ret < 0) {
        ES_LOGE(TAG, "Failed to add CAN RX task to scheduler: %d", ret);
        return -1;
    }

    s_veh_ctx.inited = true;
    ES_LOGI(TAG, "Vehicle CAN module initialized successfully");

    return 0;
}

/**
 * @brief Deinitialize vehicle CAN module
 */
void es_veh_deinit(void)
{
    if (s_veh_ctx.inited) {
        /* TODO: Remove task from scheduler if needed */
        s_veh_ctx.inited = false;
        ES_LOGI(TAG, "Vehicle CAN module deinitialized");
    }
}

/**
 * @brief Send CAN message
 * @param id CAN ID
 * @param ide IDE flag (0=standard, 1=extended)
 * @param rtr RTR flag (0=data frame, 1=remote frame)
 * @param dlc Data length code
 * @param data Pointer to data bytes
 * @return 0 on success, -1 on failure
 */
int es_veh_can_send(uint32_t id, uint8_t ide, uint8_t rtr, uint8_t dlc, const uint8_t *data)
{
    if (!s_veh_ctx.inited) {
        ES_LOGE(TAG, "Vehicle module not initialized");
        return -1;
    }

    es_can_msg_t msg = {
        .id = id,
        .ide = ide,
        .rtr = rtr,
        .dlc = dlc
    };

    /* Clear data buffer first */
    memset(msg.data, 0, sizeof(msg.data));
    
    /* Copy data if it's a data frame and data is provided */
    if (data && dlc > 0 && rtr == 0) {
        uint8_t copy_len = (dlc > 8) ? 8 : dlc;
        memcpy(msg.data, data, copy_len);
    }

    int ret = es_can_write(&msg);
    if (ret != 0) {
        ES_LOGE(TAG, "CAN send failed: %d", ret);
        return -1;
    }

    ES_LOGD(TAG, "CAN TX: ID=0x%08X, IDE=%d, RTR=%d, DLC=%d", id, ide, rtr, dlc);
    return 0;
}

/**
 * @brief Get CAN status information
 * @return CAN status flags
 */
uint32_t es_veh_get_can_status(void)
{
    if (!s_veh_ctx.inited) {
        return 0;
    }
    
    return es_can_get_status();
}

/**
 * @brief Get CAN error information
 * @param error_info Pointer to store error information
 * @return 0 on success, -1 on failure
 */
int es_veh_get_can_error_info(es_can_error_info_t *error_info)
{
    if (!s_veh_ctx.inited || !error_info) {
        return -1;
    }
    
    return es_can_get_error_info(error_info);
} 
