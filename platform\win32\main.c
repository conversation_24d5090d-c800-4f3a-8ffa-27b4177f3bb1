#include <stdio.h>
#include "es.h"
#include "es_drv_can.h"
#include "win32_pin.h"
#include <windows.h>

int es_lsm6dsl_init(void) { 
    return 0;
}

void test_can_functionality(void)
{
    printf("\n=== CAN Functionality Test ===\n");
    
    /* Initialize CAN */
    printf("Initializing CAN...\n");
    int result = es_can_init();
    if (result == 0) {
        printf("CAN initialized successfully!\n");
    } else {
        printf("CAN initialization failed!\n");
        return;
    }
    
    /* Wait a moment for simulation messages to be generated */
    printf("Waiting for simulated messages...\n");
    Sleep(200);
    
    /* Check status */
    printf("\nCAN Status: 0x%08X\n", es_can_get_status());
    printf("Available messages: %d\n", es_can_available());
    
    /* Read some messages */
    es_can_msg_t msg;
    for (int i = 0; i < 5; i++) {
        int read_result = es_can_read(&msg);
        if (read_result == 1) {
            printf("Message %d: ID=0x%X, IDE=%d, RTR=%d, DLC=%d, Data: ", 
                   i+1, msg.id, msg.ide, msg.rtr, msg.dlc);
            for (int j = 0; j < msg.dlc; j++) {
                printf("%02X ", msg.data[j]);
            }
            printf("\n");
        } else if (read_result == 0) {
            printf("No more messages available\n");
            break;
        } else {
            printf("Error reading message\n");
            break;
        }
    }
    
    /* Send a test message */
    printf("\nSending test message...\n");
    es_can_msg_t test_msg;
    test_msg.id = 0x123;
    test_msg.ide = 0;
    test_msg.rtr = 0;
    test_msg.dlc = 8;
    for (int i = 0; i < 8; i++) {
        test_msg.data[i] = 0xA0 + i;
    }
    
    result = es_can_write(&test_msg);
    if (result == 0) {
        printf("Test message sent successfully!\n");
    } else {
        printf("Failed to send test message!\n");
    }
    

}

int main(){
    printf("ES MCU Win32 Simulation Starting...\n");
    
    es_init();
    win32_pin_init();
    
    /* Test CAN functionality */
    test_can_functionality();
    
    printf("\nPress any key to exit...\n");
    getchar();
    
    return 0;
}

