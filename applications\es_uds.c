/**
 * @file es_uds.c
 * @brief UDS Client Implementation for OTA Services
 *
 * This module provides a UDS client implementation focused on:
 * - UDS OTA services according to ISO 14229-1
 * - Proper 0x78 NRC handling
 * - Coroutine-based async operations
 * - Uses separate ISO-TP transport layer
 *
 * <AUTHOR> MCU Framework
 * @date 2025-01-21
 * @version 1.0.0
 */

#include "es_uds.h"

#define TAG "UDS"

/* ========================================================================== */
/*                            LOG CONTROL MACROS                             */
/* ========================================================================== */

/**
 * @brief UDS debug log levels
 */
#ifndef ES_UDS_LOG_LEVEL
#define ES_UDS_LOG_LEVEL 1  // 0=None, 1=Error+Critical, 2=Info, 3=Debug, 4=Verbose
#endif

// Critical operations (always logged)
#define UDS_LOG_CRITICAL(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)

// Error conditions (level 1+)
#if ES_UDS_LOG_LEVEL >= 1
#define UDS_LOG_ERROR(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define UDS_LOG_ERROR(fmt, ...)
#endif

// Important operations (level 2+)
#if ES_UDS_LOG_LEVEL >= 2
#define UDS_LOG_INFO(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define UDS_LOG_INFO(fmt, ...)
#endif

// Debug information (level 3+)
#if ES_UDS_LOG_LEVEL >= 3
#define UDS_LOG_DEBUG(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define UDS_LOG_DEBUG(fmt, ...)
#endif

// Verbose frame-level logs (level 4+)
#if ES_UDS_LOG_LEVEL >= 4
#define UDS_LOG_VERBOSE(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define UDS_LOG_VERBOSE(fmt, ...)
#endif

/* ========================================================================== */
/*                            STATIC VARIABLES                               */
/* ========================================================================== */

static bool s_uds_initialized = false;

/* ========================================================================== */
/*                            UTILITY FUNCTIONS                              */
/* ========================================================================== */

/**
 * @brief Get current timestamp in milliseconds
 */
static inline uint32_t get_timestamp_ms(void) {
    return es_os_get_tick_ms();
}

/**
 * @brief Check if timeout has occurred
 */
static inline bool is_timeout(uint32_t start_time, uint32_t timeout_ms) {
    uint32_t current_time = get_timestamp_ms();

    /* Handle counter wraparound situation */
    if (current_time >= start_time) {
        return (current_time - start_time) >= timeout_ms;
    } else {
        /* Current time is less than start time, indicating wraparound occurred */
        return ((UINT32_MAX - start_time) + current_time + 1) >= timeout_ms;
    }
}



/* ========================================================================== */
/*                            MODULE FUNCTIONS                               */
/* ========================================================================== */

/**
 * @brief Initialize UDS module
 */
es_uds_error_t es_uds_init(void) {
    if (s_uds_initialized) {
        return ES_UDS_OK;
    }

    UDS_LOG_INFO("Initializing UDS module");
    s_uds_initialized = true;

    return ES_UDS_OK;
}

/**
 * @brief Initialize UDS connection
 */
es_uds_error_t es_uds_connection_init(es_uds_connection_t *conn, uint32_t tx_id, uint32_t rx_id, uint32_t functional_tx_id) {
    if (!conn) {
        return ES_UDS_ERR_INVALID_PARAM;
    }

    // Clear the entire structure
    memset(conn, 0, sizeof(es_uds_connection_t));

    // Initialize ISO-TP connection with UDS buffers
    es_isotp_error_t isotp_result = es_isotp_init(&conn->isotp, tx_id, rx_id,
                                                  conn->tx_buffer, ES_UDS_MAX_TX_BUFFER_SIZE,
                                                  conn->rx_buffer, ES_UDS_MAX_RX_BUFFER_SIZE);
    if (isotp_result != ES_ISOTP_OK) {
        return ES_UDS_ERR_NO_MEMORY;  // Map ISO-TP error to UDS error
    }

    conn->functional_tx_id = functional_tx_id;

    // Initialize UDS connection
    conn->current_session = ES_UDS_SESSION_DEFAULT;
    conn->p2_timeout = ES_UDS_DEFAULT_P2_TIMEOUT;
    conn->p2_star_timeout = ES_UDS_DEFAULT_P2_STAR_TIMEOUT;
    conn->max_transfer_block_size = ES_UDS_MAX_TX_BUFFER_SIZE - 2;  // Default max transfer block size
    conn->block_sequence_counter = 1;  // Initialize block sequence counter
    conn->transfer_offset = 0;  // Initialize transfer offset

    UDS_LOG_INFO("UDS connection initialized (TX: 0x%03X, RX: 0x%03X)", tx_id, rx_id);

    return ES_UDS_OK;
}

/**
 * @brief Process incoming CAN message
 */
es_uds_error_t es_uds_process_can_message(es_uds_connection_t *conn, const es_can_msg_t *msg) {
    if (!conn || !msg) {
        return ES_UDS_ERR_INVALID_PARAM;
    }

    // Delegate to ISO-TP layer
    es_isotp_error_t isotp_result = es_isotp_process_can_message(&conn->isotp, msg);

    // Map ISO-TP errors to UDS errors
    switch (isotp_result) {
        case ES_ISOTP_OK:
            return ES_UDS_OK;
        case ES_ISOTP_ERR_INVALID_PARAM:
            return ES_UDS_ERR_INVALID_PARAM;
        case ES_ISOTP_ERR_OVERFLOW:
            return ES_UDS_ERR_NEGATIVE_RESPONSE;  // Map to appropriate UDS error
        default:
            return ES_UDS_ERR_CAN_ERROR;
    }
}

/* ========================================================================== */
/*                             UDS FUNCTIONS                                 */
/* ========================================================================== */

/**
 * @brief Send UDS request (coroutine)
 */
static es_async_t es_uds_request(es_coro_t *coro, es_uds_connection_t *conn) {
    es_co_begin(coro);

    if (conn->waiting_for_response) {
        es_co_err;  // Already waiting for response
    }

    // Validate request buffer has been prepared
    if (UDS_REQUEST_LENGTH(conn) == 0) {
        UDS_LOG_ERROR("Request buffer not properly prepared - length is 0");
        es_co_err;
    }

    uint8_t service_id = UDS_REQUEST_BUFFER(conn)[0];

    // Store request info
    conn->current_service = service_id;
    conn->response_pending_count = 0;
    conn->waiting_for_response = true;
    conn->request_start_time = get_timestamp_ms();

    // Send request via ISO-TP
    es_co_await_ex(err, es_isotp_send, &conn->isotp);

    UDS_LOG_DEBUG("UDS request sent: SID=0x%02X, len=%d", service_id, UDS_REQUEST_LENGTH(conn));

    es_co_eee(
        UDS_LOG_ERROR("Failed to send UDS request");
        conn->waiting_for_response = false;
    );
}

/**
 * @brief Wait for UDS response with 0x78 handling (coroutine)
 */
static es_async_t es_uds_wait_response(es_coro_t *coro, es_uds_connection_t *conn) {
    es_co_begin(coro);

    if (!conn->waiting_for_response) {
        es_co_err;
    }

    conn->wait_using_p2_star = false;
    conn->wait_current_timeout = conn->p2_timeout;

    while (conn->waiting_for_response) {
        // Check timeout
        if (is_timeout(conn->request_start_time, conn->wait_current_timeout)) {
            UDS_LOG_ERROR("UDS response timeout (P2%s)", conn->wait_using_p2_star ? "*" : "");
            conn->waiting_for_response = false;
            es_co_err;
        }

        // Try to receive response
        es_co_await_ex(timeout_check, es_isotp_recv, &conn->isotp);

        if (UDS_RESPONSE_LENGTH(conn) < 1) {
            UDS_LOG_ERROR("Invalid UDS response length: %d", UDS_RESPONSE_LENGTH(conn));
            conn->waiting_for_response = false;
            es_co_err;
        }

        // Check if it's a negative response
        if (conn->isotp.rx_buffer[0] == 0x7F) {
            if (UDS_RESPONSE_LENGTH(conn) < 3) {
                UDS_LOG_ERROR("Invalid negative response length: %d", UDS_RESPONSE_LENGTH(conn));
                conn->waiting_for_response = false;
                es_co_err;
            }

            uint8_t requested_sid = conn->isotp.rx_buffer[1];
            uint8_t nrc = conn->isotp.rx_buffer[2];

            // Verify this is response to our request
            if (requested_sid != conn->current_service) {
                UDS_LOG_DEBUG("NRC for wrong service: expected=0x%02X, got=0x%02X",
                          conn->current_service, requested_sid);
                continue;  // Wait for correct response
            }

            conn->last_nrc = nrc;

            if (nrc == ES_UDS_NRC_RESPONSE_PENDING) {
                // 0x78 - Response pending, switch to P2* timeout
                conn->response_pending_count++;

                if (conn->response_pending_count > ES_UDS_MAX_RESPONSE_PENDING) {
                    UDS_LOG_ERROR("Too many 0x78 responses: %d", conn->response_pending_count);
                    conn->waiting_for_response = false;
                    es_co_err;
                }

                // Switch to P2* timeout and reset timer
                if (!conn->wait_using_p2_star) {
                    conn->wait_using_p2_star = true;
                    conn->wait_current_timeout = conn->p2_star_timeout;
                    UDS_LOG_INFO("Received 0x78, switching to P2* timeout (%d ms)",
                              conn->wait_current_timeout);
                } else {
                    UDS_LOG_DEBUG("Received additional 0x78 (%d/%d)",
                              conn->response_pending_count, ES_UDS_MAX_RESPONSE_PENDING);
                }

                conn->request_start_time = get_timestamp_ms();
                continue;  // Wait for actual response

            } else {
                // Other negative response
                UDS_LOG_ERROR("Negative response: SID=0x%02X, NRC=0x%02X", requested_sid, nrc);
                conn->waiting_for_response = false;
                // Note: response_length is now the same as UDS_RESPONSE_LENGTH(conn)
                es_co_err;
            }

        } else {
            // Positive response
            uint8_t response_sid = conn->isotp.rx_buffer[0];
            uint8_t expected_response_sid = conn->current_service + 0x40;

            if (response_sid != expected_response_sid) {
                UDS_LOG_DEBUG("Wrong response SID: expected=0x%02X, got=0x%02X",
                          expected_response_sid, response_sid);
                continue;  // Wait for correct response
            }

            // Valid positive response
            conn->waiting_for_response = false;
            // Note: response_length is now the same as UDS_RESPONSE_LENGTH(conn)

            UDS_LOG_DEBUG("Positive response received: SID=0x%02X, len=%d, 0x78_count=%d",
                      response_sid, UDS_RESPONSE_LENGTH(conn), conn->response_pending_count);
            break;
        }

        timeout_check:
        es_co_yield;
    }

    conn->waiting_for_response = false;
    es_co_end;
}

/**
 * @brief Send UDS request with error handling (coroutine)
 */
static es_async_t es_uds_request_ex(es_coro_t *coro, es_uds_connection_t *conn) {
    es_co_begin(coro);

    // Send request
    es_co_await_ex(err, es_uds_request, conn);

    // Wait for response
    es_co_await_ex(err, es_uds_wait_response, conn);

    es_co_eee(
        UDS_LOG_ERROR("UDS request_ex failed: SID=0x%02X", UDS_REQUEST_BUFFER(conn)[0]);
    );
}

/* ========================================================================== */
/*                           UDS OTA SERVICES                                */
/* ========================================================================== */

/**
 * @brief Diagnostic Session Control (coroutine)
 */
es_async_t es_uds_diagnostic_session_control(es_coro_t *coro, es_uds_connection_t *conn, es_uds_session_t session_type) {
    es_co_begin(coro);

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL;
    UDS_REQUEST_BUFFER(conn)[1] = session_type;
    UDS_REQUEST_LENGTH(conn) = 2;

    es_co_await_ex(err, es_uds_request_ex, conn);

    // Parse P2 and P2* timing parameters from response
    // UDS response format: [0x50][session_type][P2_high][P2_low][P2*_byte3][P2*_byte2][P2*_byte1][P2*_byte0]
    // P2 and P2* values are in units of 10ms (ISO 14229-1)
    if (UDS_RESPONSE_LENGTH(conn) >= 6) {
        // Extract P2Server_max (bytes 2-3, big-endian, in units of 10ms)
        uint16_t p2_server_max = (UDS_RESPONSE_BUFFER(conn)[2] << 8) | UDS_RESPONSE_BUFFER(conn)[3];
        conn->p2_timeout = p2_server_max * 10;  // Convert to milliseconds

        // Extract P2*Server_max (bytes 4-7, big-endian, in units of 10ms)
        if (UDS_RESPONSE_LENGTH(conn) >= 8) {
            uint32_t p2_star_server_max = ((uint32_t)UDS_RESPONSE_BUFFER(conn)[4] << 24) |
                                          ((uint32_t)UDS_RESPONSE_BUFFER(conn)[5] << 16) |
                                          ((uint32_t)UDS_RESPONSE_BUFFER(conn)[6] << 8) |
                                          ((uint32_t)UDS_RESPONSE_BUFFER(conn)[7]);
            conn->p2_star_timeout = p2_star_server_max * 10;  // Convert to milliseconds

            UDS_LOG_INFO("Session changed to: 0x%02X, P2=%dms, P2*=%dms",
                       session_type, conn->p2_timeout, conn->p2_star_timeout);
        } else {
            UDS_LOG_INFO("Session changed to: 0x%02X, P2=%dms (P2* not provided)",
                       session_type, conn->p2_timeout);
        }
    } else {
        UDS_LOG_INFO("Session changed to: 0x%02X (timing parameters not provided)", session_type);
    }

    // Update current session on success
    conn->current_session = session_type;

    es_co_eee(
        UDS_LOG_ERROR("Failed to change session to: 0x%02X", session_type);
    );
}

/**
 * @brief ECU Reset (coroutine)
 */
es_async_t es_uds_ecu_reset(es_coro_t *coro, es_uds_connection_t *conn, es_uds_reset_type_t reset_type) {
    es_co_begin(coro);

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_ECU_RESET;
    UDS_REQUEST_BUFFER(conn)[1] = reset_type;
    UDS_REQUEST_LENGTH(conn) = 2;

    es_co_await_ex(err, es_uds_request_ex, conn);

    UDS_LOG_INFO("ECU reset requested: type=0x%02X", reset_type);

    es_co_eee(
        UDS_LOG_ERROR("Failed to request ECU reset: type=0x%02X", reset_type);
    );
}

/**
 * @brief Security Access (coroutine)
 */
es_async_t es_uds_security_access(es_coro_t *coro, es_uds_connection_t *conn,
                                     uint8_t level, es_uds_security_access_key_callback_t key_callback,
                                     void *user_data) {
    es_co_begin(coro);

    if (!key_callback) {
        UDS_LOG_ERROR("Security access: key callback is required");
        es_co_err;
    }

    // Ensure level is odd for seed request
    if ((level & 0x01) == 0) {
        UDS_LOG_ERROR("Security access level must be odd: 0x%02X", level);
        es_co_err;
    }

    // Step 1: Request seed (use the provided odd level)
    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_SECURITY_ACCESS;
    UDS_REQUEST_BUFFER(conn)[1] = level;  // Use the provided odd level
    UDS_REQUEST_LENGTH(conn) = 2;

    es_co_await_ex(err, es_uds_request_ex, conn);

    // Check response format: should be [0x67][level][seed...]
    if (UDS_RESPONSE_LENGTH(conn) < 3 || UDS_RESPONSE_BUFFER(conn)[0] != (ES_UDS_SID_SECURITY_ACCESS + 0x40)) {
        UDS_LOG_ERROR("Invalid seed response format");
        es_co_err;
    }

    uint16_t seed_length = UDS_RESPONSE_LENGTH(conn) - 2;  // Exclude SID and level
    const uint8_t *seed_data = &UDS_RESPONSE_BUFFER(conn)[2];

    UDS_LOG_DEBUG("Security access seed received: level=0x%02X, seed_len=%d", level, seed_length);

    // Step 2: Generate key using callback directly into tx buffer
    // Use tx buffer starting from offset 2 (after SID and level)
    uint16_t max_key_length = ES_UDS_MAX_TX_BUFFER_SIZE - 2;
    uint16_t key_length = key_callback(level, seed_data, seed_length,
                                      &UDS_REQUEST_BUFFER(conn)[2], max_key_length, user_data);
    if (key_length == 0) {
        UDS_LOG_ERROR("Key generation failed");
        es_co_err;
    }

    if (key_length > max_key_length) {
        UDS_LOG_ERROR("Generated key too long: %d", key_length);
        es_co_err;
    }

    // Step 3: Send key (use level + 1 for even level)
    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_SECURITY_ACCESS;
    UDS_REQUEST_BUFFER(conn)[1] = level + 1;  // Use level + 1 for key sending
    UDS_REQUEST_LENGTH(conn) = 2 + key_length;

    es_co_await_ex(err, es_uds_request_ex, conn);

    UDS_LOG_INFO("Security access completed: seed_level=0x%02X, key_level=0x%02X, key_len=%d",
                level, level + 1, key_length);

    es_co_eee(
        UDS_LOG_ERROR("Failed security access: level=0x%02X", level);
    );
}

/**
 * @brief Tester Present (coroutine)
 */
es_async_t es_uds_tester_present(es_coro_t *coro, es_uds_connection_t *conn,
                                    bool suppress_response) {
    es_co_begin(coro);

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_TESTER_PRESENT;
    UDS_REQUEST_BUFFER(conn)[1] = suppress_response ? 0x80 : 0x00;
    UDS_REQUEST_LENGTH(conn) = 2;

    if (suppress_response) {
        // For suppressed response, just send the request
        es_co_await_ex(err, es_uds_request, conn);
    } else {
        // Normal request with response
        es_co_await_ex(err, es_uds_request_ex, conn);
    }

    UDS_LOG_DEBUG("Tester present sent (suppress=%d)", suppress_response);

    es_co_eee(
        UDS_LOG_ERROR("Failed to send tester present");
    );
}

/**
 * @brief Request Download (coroutine)
 */
es_async_t es_uds_request_download(es_coro_t *coro, es_uds_connection_t *conn,
                                      uint8_t data_format_id, uint8_t address_length_format,
                                      uint32_t memory_address, uint32_t memory_size) {
    uint8_t length_format = 0;
    uint8_t max_block_len_size = 0;
    es_co_begin(coro);

    // Extract address and size lengths from format
    uint8_t addr_len = (address_length_format >> 4) & 0x0F;
    uint8_t size_len = address_length_format & 0x0F;

    if (addr_len == 0 || addr_len > 4 || size_len == 0 || size_len > 4) {
        es_co_err;
    }

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_REQUEST_DOWNLOAD;
    UDS_REQUEST_BUFFER(conn)[1] = data_format_id;
    UDS_REQUEST_BUFFER(conn)[2] = address_length_format;
    UDS_REQUEST_LENGTH(conn) = 3;

    // Encode address (big-endian)
    for (int i = 0; i < addr_len; i++) {
        UDS_REQUEST_BUFFER(conn)[3 + i] = (memory_address >> (8 * (addr_len - 1 - i))) & 0xFF;
    }
    UDS_REQUEST_LENGTH(conn) += addr_len;

    // Encode size (big-endian)
    for (int i = 0; i < size_len; i++) {
        UDS_REQUEST_BUFFER(conn)[3 + addr_len + i] = (memory_size >> (8 * (size_len - 1 - i))) & 0xFF;
    }
    UDS_REQUEST_LENGTH(conn) += size_len;

    es_co_await_ex(err, es_uds_request_ex, conn);

    length_format = conn->isotp.rx_buffer[1];  // Extract length format from response buffer
    max_block_len_size = length_format & 0x0F;  // Extract max block length size from length format

    // Validate length format and response length
    if (max_block_len_size > 0 && max_block_len_size <= 4 &&
        UDS_RESPONSE_LENGTH(conn) >= (2 + max_block_len_size)) {

        // Extract maximum transfer block size (big-endian)
        uint32_t max_block_size = 0;
        for (int i = 0; i < max_block_len_size; i++) {
            max_block_size = (max_block_size << 8) | conn->isotp.rx_buffer[2 + i];
        }

        // Store the maximum transfer block size (limit to reasonable range)
        if (max_block_size > 0 && max_block_size <= ES_UDS_MAX_TX_BUFFER_SIZE - 2) {
            conn->max_transfer_block_size = (uint16_t)max_block_size;
        } else {
            // Use default safe size if server value is unreasonable
            conn->max_transfer_block_size = ES_UDS_MAX_TX_BUFFER_SIZE - 2;
        }

        UDS_LOG_INFO("Request download success: addr=0x%08X, size=%d, max_block=%d",
                    memory_address, memory_size, conn->max_transfer_block_size);
    } else {
        UDS_LOG_ERROR("Invalid response format for request download");
        es_co_err;
    }

    es_co_eee(
        UDS_LOG_ERROR("Failed request download");
    );
}

/**
 * @brief Transfer Data (coroutine)
 */
es_async_t es_uds_transfer_data(es_coro_t *coro, es_uds_connection_t *conn,
                                   es_uds_transfer_data_read_callback_t read_callback,
                                   void *user_data) {
    es_co_begin(coro);

    if (!read_callback) {
        es_co_err;
    }

    conn->block_sequence_counter = 1;  // Start from 1
    conn->transfer_offset = 0;  // Initialize transfer offset
    uint16_t data_length;

    // Loop until callback returns 0 bytes
    while (true) {
        // Call user callback to read data with current offset
        data_length = read_callback(conn->transfer_offset,
                                   &UDS_REQUEST_BUFFER(conn)[2],
                                   conn->max_transfer_block_size,
                                   user_data);

        // If no more data, break the loop
        if (data_length == 0) {
            break;
        }

        // Validate data length against server's maximum
        if (data_length > conn->max_transfer_block_size) {
            UDS_LOG_ERROR("Transfer data exceeds server max: %d > %d bytes",
                      data_length, conn->max_transfer_block_size);
            es_co_err;
        }

        // Prepare transfer data request
        UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_TRANSFER_DATA;
        UDS_REQUEST_BUFFER(conn)[1] = conn->block_sequence_counter;
        UDS_REQUEST_LENGTH(conn) = 2 + data_length;

        // Send the transfer data request
        es_co_await_ex(err, es_uds_request_ex, conn);

        UDS_LOG_DEBUG("Transfer data sent: block=%d, len=%d, offset=%d",
                  conn->block_sequence_counter, data_length, conn->transfer_offset);

        // Update transfer offset and block sequence counter
        conn->transfer_offset += data_length;
        conn->block_sequence_counter++;
        if (conn->block_sequence_counter == 0) {
            conn->block_sequence_counter = 1;
        }
    }

    UDS_LOG_INFO("Transfer data completed");

    es_co_eee(
        UDS_LOG_ERROR("Failed transfer data: block=%d", conn->block_sequence_counter);
    );
}

/**
 * @brief Request Transfer Exit (coroutine)
 */
es_async_t es_uds_request_transfer_exit(es_coro_t *coro, es_uds_connection_t *conn) {
    es_co_begin(coro);

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_REQUEST_TRANSFER_EXIT;
    UDS_REQUEST_LENGTH(conn) = 1;

    es_co_await_ex(err, es_uds_request_ex, conn);

    UDS_LOG_INFO("Request transfer exit completed");

    es_co_eee(
        UDS_LOG_ERROR("Failed request transfer exit");
    );
}

/**
 * @brief Routine Control (coroutine)
 */
es_async_t es_uds_routine_control(es_coro_t *coro, es_uds_connection_t *conn,
                                     uint8_t routine_control_type, uint16_t routine_id,
                                     const uint8_t *data, uint16_t length) {
    es_co_begin(coro);

    if (length > ES_UDS_MAX_TX_BUFFER_SIZE - 4) {
        es_co_err;
    }

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_ROUTINE_CONTROL;
    UDS_REQUEST_BUFFER(conn)[1] = routine_control_type;
    UDS_REQUEST_BUFFER(conn)[2] = (routine_id >> 8) & 0xFF;  // High byte
    UDS_REQUEST_BUFFER(conn)[3] = routine_id & 0xFF;         // Low byte
    UDS_REQUEST_LENGTH(conn) = 4;

    if (data && length > 0) {
        memcpy(&UDS_REQUEST_BUFFER(conn)[4], data, length);
        UDS_REQUEST_LENGTH(conn) += length;
    }

    es_co_await_ex(err, es_uds_request_ex, conn);

    UDS_LOG_INFO("Routine control: type=0x%02X, id=0x%04X, len=%d",
              routine_control_type, routine_id, length);

    es_co_eee(
        UDS_LOG_ERROR("Failed routine control: type=0x%02X, id=0x%04X",
                  routine_control_type, routine_id);
    );
}

/**
 * @brief Read Data By Identifier (coroutine)
 */
es_async_t es_uds_read_data_by_identifier(es_coro_t *coro, es_uds_connection_t *conn,
                                             uint16_t data_identifier) {
    es_co_begin(coro);

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_READ_DATA_BY_ID;
    UDS_REQUEST_BUFFER(conn)[1] = (data_identifier >> 8) & 0xFF;  // High byte
    UDS_REQUEST_BUFFER(conn)[2] = data_identifier & 0xFF;         // Low byte
    UDS_REQUEST_LENGTH(conn) = 3;

    es_co_await_ex(err, es_uds_request_ex, conn);

    UDS_LOG_INFO("Read data by identifier success: DID=0x%04X, response_len=%d",
                data_identifier, UDS_RESPONSE_LENGTH(conn));

    es_co_eee(
        UDS_LOG_ERROR("Failed to read data by identifier: DID=0x%04X", data_identifier);
    );
}

/**
 * @brief Write Data By Identifier (coroutine)
 */
es_async_t es_uds_write_data_by_identifier(es_coro_t *coro, es_uds_connection_t *conn,
                                              uint16_t data_identifier, const uint8_t *data, uint16_t data_length) {
    es_co_begin(coro);

    // Validate input parameters
    if (!data || data_length == 0) {
        UDS_LOG_ERROR("Invalid data parameters for write data by identifier");
        es_co_err;
    }

    // Check if request fits in buffer (3 bytes header + data)
    if (3 + data_length > ES_UDS_MAX_TX_BUFFER_SIZE) {
        UDS_LOG_ERROR("Write data too large: %d bytes (max: %d)",
                    data_length, ES_UDS_MAX_TX_BUFFER_SIZE - 3);
        es_co_err;
    }

    // Build request: [SID][DID_high][DID_low][data...]
    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_WRITE_DATA_BY_ID;
    UDS_REQUEST_BUFFER(conn)[1] = (data_identifier >> 8) & 0xFF;  // High byte
    UDS_REQUEST_BUFFER(conn)[2] = data_identifier & 0xFF;         // Low byte

    // Copy data to request buffer
    memcpy(&UDS_REQUEST_BUFFER(conn)[3], data, data_length);
    UDS_REQUEST_LENGTH(conn) = 3 + data_length;

    es_co_await_ex(err, es_uds_request_ex, conn);

    UDS_LOG_INFO("Write data by identifier success: DID=0x%04X, data_len=%d, response_len=%d",
                data_identifier, data_length, UDS_RESPONSE_LENGTH(conn));

    es_co_eee(
        UDS_LOG_ERROR("Failed to write data by identifier: DID=0x%04X, data_len=%d",
                    data_identifier, data_length);
    );
}

/**
 * @brief Communication Control (coroutine)
 */
es_async_t es_uds_communication_control(es_coro_t *coro, es_uds_connection_t *conn,
                                           es_uds_communication_control_type_t control_type,
                                           uint8_t communication_type) {
    es_co_begin(coro);

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_COMMUNICATION_CONTROL;
    UDS_REQUEST_BUFFER(conn)[1] = control_type;
    UDS_REQUEST_LENGTH(conn) = 2;

    // Add communication type parameter if provided
    if (communication_type != 0) {
        UDS_REQUEST_BUFFER(conn)[2] = communication_type;
        UDS_REQUEST_LENGTH(conn) = 3;
    }

    es_co_await_ex(err, es_uds_request_ex, conn);

    UDS_LOG_INFO("Communication control success: type=0x%02X, comm_type=0x%02X",
                control_type, communication_type);

    es_co_eee(
        UDS_LOG_ERROR("Failed communication control: type=0x%02X, comm_type=0x%02X",
                  control_type, communication_type);
    );
}

/**
 * @brief Control DTC Setting (coroutine)
 */
es_async_t es_uds_control_dtc_setting(es_coro_t *coro, es_uds_connection_t *conn,
                                         es_uds_control_dtc_setting_type_t dtc_setting_type,
                                         const uint8_t *dtc_setting_control_option_record,
                                         uint16_t record_length) {
    es_co_begin(coro);

    // Validate buffer space
    if (record_length > ES_UDS_MAX_TX_BUFFER_SIZE - 2) {
        es_co_err;
    }

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_CONTROL_DTC_SETTING;
    UDS_REQUEST_BUFFER(conn)[1] = dtc_setting_type;
    UDS_REQUEST_LENGTH(conn) = 2;

    // Add DTC setting control option record if provided
    if (dtc_setting_control_option_record && record_length > 0) {
        memcpy(&UDS_REQUEST_BUFFER(conn)[2], dtc_setting_control_option_record, record_length);
        UDS_REQUEST_LENGTH(conn) += record_length;
    }

    es_co_await_ex(err, es_uds_request_ex, conn);

    UDS_LOG_INFO("Control DTC setting success: type=0x%02X, record_len=%d",
                dtc_setting_type, record_length);

    es_co_eee(
        UDS_LOG_ERROR("Failed control DTC setting: type=0x%02X, record_len=%d",
                  dtc_setting_type, record_length);
    );
}

/**
 * @brief Read DTC Information (coroutine)
 */
es_async_t es_uds_read_dtc_information(es_coro_t *coro, es_uds_connection_t *conn,
                                          es_uds_read_dtc_sub_function_t sub_function,
                                          uint8_t dtc_status_mask,
                                          const uint8_t *dtc_mask_record,
                                          uint16_t record_length) {
    es_co_begin(coro);

    // Validate buffer space
    if (record_length > ES_UDS_MAX_TX_BUFFER_SIZE - 3) {
        es_co_err;
    }

    UDS_REQUEST_BUFFER(conn)[0] = ES_UDS_SID_READ_DTC_INFORMATION;
    UDS_REQUEST_BUFFER(conn)[1] = sub_function;
    UDS_REQUEST_LENGTH(conn) = 2;

    // Add DTC status mask for sub-functions that require it
    if (sub_function == ES_UDS_DTC_REPORT_NUMBER_OF_DTC_BY_STATUS_MASK ||
        sub_function == ES_UDS_DTC_REPORT_DTC_BY_STATUS_MASK ||
        sub_function == ES_UDS_DTC_REPORT_NUMBER_OF_DTC_BY_SEVERITY_MASK_RECORD ||
        sub_function == ES_UDS_DTC_REPORT_DTC_BY_SEVERITY_MASK_RECORD ||
        sub_function == ES_UDS_DTC_REPORT_MIRROR_MEMORY_DTC_BY_STATUS_MASK ||
        sub_function == ES_UDS_DTC_REPORT_NUMBER_OF_MIRROR_MEMORY_DTC_BY_STATUS_MASK ||
        sub_function == ES_UDS_DTC_REPORT_NUMBER_OF_EMISSIONS_OBD_DTC_BY_STATUS_MASK ||
        sub_function == ES_UDS_DTC_REPORT_EMISSIONS_OBD_DTC_BY_STATUS_MASK) {
        UDS_REQUEST_BUFFER(conn)[2] = dtc_status_mask;
        UDS_REQUEST_LENGTH(conn) = 3;
    }

    // Add additional DTC mask record if provided
    if (dtc_mask_record && record_length > 0) {
        memcpy(&UDS_REQUEST_BUFFER(conn)[UDS_REQUEST_LENGTH(conn)], dtc_mask_record, record_length);
        UDS_REQUEST_LENGTH(conn) += record_length;
    }

    es_co_await_ex(err, es_uds_request_ex, conn);

    UDS_LOG_INFO("Read DTC information success: sub_func=0x%02X, status_mask=0x%02X, response_len=%d",
                sub_function, dtc_status_mask, UDS_RESPONSE_LENGTH(conn));

    es_co_eee(
        UDS_LOG_ERROR("Failed read DTC information: sub_func=0x%02X, status_mask=0x%02X",
                  sub_function, dtc_status_mask);
    );
}

// /* ========================================================================== */
// /*                            UTILITY FUNCTIONS                              */
// /* ========================================================================== */

// /**
//  * @brief Get string representation of error code
//  */
// const char* es_uds_error_to_string(es_uds_error_t error) {
//     switch (error) {
//         case ES_UDS_OK: return "Success";
//         case ES_UDS_ERR_INVALID_PARAM: return "Invalid parameter";
//         case ES_UDS_ERR_TIMEOUT: return "Timeout";
//         case ES_UDS_ERR_BUSY: return "Busy";
//         case ES_UDS_ERR_NO_MEMORY: return "No memory";
//         case ES_UDS_ERR_CAN_ERROR: return "CAN error";
//         case ES_UDS_ERR_INVALID_FRAME: return "Invalid frame";
//         case ES_UDS_ERR_SEQUENCE_ERROR: return "Sequence error";
//         case ES_UDS_ERR_OVERFLOW: return "Overflow";
//         case ES_UDS_ERR_NEGATIVE_RESPONSE: return "Negative response";
//         case ES_UDS_ERR_RESPONSE_PENDING: return "Response pending";
//         default: return "Unknown error";
//     }
// }

// /**
//  * @brief Get string representation of NRC
//  */
// const char* es_uds_nrc_to_string(uint8_t nrc) {
//     switch (nrc) {
//         case ES_UDS_NRC_POSITIVE_RESPONSE: return "Positive response";
//         case ES_UDS_NRC_GENERAL_REJECT: return "General reject";
//         case ES_UDS_NRC_SERVICE_NOT_SUPPORTED: return "Service not supported";
//         case ES_UDS_NRC_INCORRECT_MESSAGE_LENGTH: return "Incorrect message length";
//         case ES_UDS_NRC_BUSY_REPEAT_REQUEST: return "Busy repeat request";
//         case ES_UDS_NRC_CONDITIONS_NOT_CORRECT: return "Conditions not correct";
//         case ES_UDS_NRC_REQUEST_SEQUENCE_ERROR: return "Request sequence error";
//         case ES_UDS_NRC_REQUEST_OUT_OF_RANGE: return "Request out of range";
//         case ES_UDS_NRC_SECURITY_ACCESS_DENIED: return "Security access denied";
//         case ES_UDS_NRC_RESPONSE_PENDING: return "Response pending";
//         default: return "Unknown NRC";
//     }
// }