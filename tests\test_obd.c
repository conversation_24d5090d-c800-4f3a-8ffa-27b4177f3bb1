/**
 * @file test_obd.c
 * @brief OBD-II Module Unit Tests
 *
 * This file contains unit tests for the OBD-II client module
 * to verify basic functionality and error handling.
 *
 * <AUTHOR> MCU Framework
 * @date 2025-01-22
 * @version 1.0.0
 */

#include "es_obd.h"
#include <assert.h>
#include <stdio.h>

#define TAG "OBD_TEST"

/* Test connection */
static es_obd_connection_t test_conn;

/* Mock CAN messages for testing */
static es_can_msg_t mock_response_msg;

/* ========================================================================== */
/*                            MOCK FUNCTIONS                                 */
/* ========================================================================== */

/**
 * @brief Mock function to simulate positive OBD response
 */
static void mock_positive_response(uint8_t service, uint8_t pid, const uint8_t *data, uint8_t data_len) {
    mock_response_msg.id = ES_OBD_PHYSICAL_RX_BASE;
    mock_response_msg.ide = 0;
    mock_response_msg.rtr = 0;
    mock_response_msg.dlc = 2 + data_len;
    
    mock_response_msg.data[0] = service + 0x40;  // Positive response
    mock_response_msg.data[1] = pid;
    
    if (data && data_len > 0) {
        memcpy(&mock_response_msg.data[2], data, data_len);
    }
}

/**
 * @brief Mock function to simulate negative OBD response
 */
static void mock_negative_response(uint8_t service, uint8_t nrc) {
    mock_response_msg.id = ES_OBD_PHYSICAL_RX_BASE;
    mock_response_msg.ide = 0;
    mock_response_msg.rtr = 0;
    mock_response_msg.dlc = 3;
    
    mock_response_msg.data[0] = 0x7F;    // Negative response
    mock_response_msg.data[1] = service; // Service ID
    mock_response_msg.data[2] = nrc;     // Negative response code
}

/* ========================================================================== */
/*                            TEST FUNCTIONS                                 */
/* ========================================================================== */

/**
 * @brief Test OBD initialization
 */
static void test_obd_init(void) {
    printf("Testing OBD initialization...\n");
    
    // Test valid initialization
    es_obd_error_t ret = es_obd_init(&test_conn, 0, false);
    assert(ret == ES_OBD_OK);
    
    // Test invalid parameters
    ret = es_obd_init(NULL, 0, false);
    assert(ret == ES_OBD_ERR_INVALID_PARAM);
    
    ret = es_obd_init(&test_conn, 8, false);  // Invalid ECU ID
    assert(ret == ES_OBD_ERR_INVALID_PARAM);
    
    printf("✓ OBD initialization tests passed\n");
}

/**
 * @brief Test OBD response parsing macros
 */
static void test_obd_response_parsing(void) {
    printf("Testing OBD response parsing...\n");
    
    // Mock RPM response: Service 01, PID 0x0C, RPM = 2000 (0x1F40 / 4)
    uint8_t rpm_data[] = {0x1F, 0x40};
    mock_positive_response(ES_OBD_SERVICE_01, ES_OBD_PID_ENGINE_RPM, rpm_data, sizeof(rpm_data));
    
    // Simulate receiving the response
    es_obd_process_can_message(&test_conn, &mock_response_msg);
    
    // Manually set response data for testing (normally done by ISO-TP)
    memcpy(test_conn.isotp.rx_buffer, mock_response_msg.data, mock_response_msg.dlc);
    test_conn.isotp.rx_length = mock_response_msg.dlc;
    
    // Test response parsing macros
    assert(OBD_RESPONSE_SERVICE(&test_conn) == ES_OBD_SERVICE_01);
    assert(OBD_RESPONSE_PID(&test_conn) == ES_OBD_PID_ENGINE_RPM);
    assert(OBD_RESPONSE_DATA_LENGTH(&test_conn) == 2);
    
    uint16_t rpm = OBD_EXTRACT_ENGINE_RPM(&test_conn);
    assert(rpm == 2000);
    
    printf("✓ OBD response parsing tests passed\n");
}

/**
 * @brief Test vehicle speed extraction
 */
static void test_vehicle_speed_extraction(void) {
    printf("Testing vehicle speed extraction...\n");
    
    // Mock speed response: Service 01, PID 0x0D, Speed = 80 km/h
    uint8_t speed_data[] = {80};
    mock_positive_response(ES_OBD_SERVICE_01, ES_OBD_PID_VEHICLE_SPEED, speed_data, sizeof(speed_data));
    
    // Simulate receiving the response
    memcpy(test_conn.isotp.rx_buffer, mock_response_msg.data, mock_response_msg.dlc);
    test_conn.isotp.rx_length = mock_response_msg.dlc;
    
    uint8_t speed = OBD_EXTRACT_VEHICLE_SPEED(&test_conn);
    assert(speed == 80);
    
    printf("✓ Vehicle speed extraction tests passed\n");
}

/**
 * @brief Test coolant temperature extraction
 */
static void test_coolant_temp_extraction(void) {
    printf("Testing coolant temperature extraction...\n");
    
    // Mock coolant temp response: Service 01, PID 0x05, Temp = 90°C (90 + 40 = 130)
    uint8_t temp_data[] = {130};
    mock_positive_response(ES_OBD_SERVICE_01, ES_OBD_PID_COOLANT_TEMP, temp_data, sizeof(temp_data));
    
    // Simulate receiving the response
    memcpy(test_conn.isotp.rx_buffer, mock_response_msg.data, mock_response_msg.dlc);
    test_conn.isotp.rx_length = mock_response_msg.dlc;
    
    int16_t temp = OBD_EXTRACT_COOLANT_TEMP(&test_conn);
    assert(temp == 90);
    
    printf("✓ Coolant temperature extraction tests passed\n");
}

/**
 * @brief Test DTC parsing
 */
static void test_dtc_parsing(void) {
    printf("Testing DTC parsing...\n");
    
    // Mock DTC response: Service 03, 2 DTCs: P0301, P0420
    uint8_t dtc_data[] = {2, 0x03, 0x01, 0x04, 0x20};  // Count + DTC1 + DTC2
    mock_positive_response(ES_OBD_SERVICE_03, 0x00, dtc_data, sizeof(dtc_data));
    
    // Simulate receiving the response
    memcpy(test_conn.isotp.rx_buffer, mock_response_msg.data, mock_response_msg.dlc);
    test_conn.isotp.rx_length = mock_response_msg.dlc;
    
    uint8_t dtc_count = OBD_GET_DTC_COUNT(&test_conn);
    assert(dtc_count == 2);
    
    uint8_t *dtc_data_ptr = OBD_GET_DTC_DATA(&test_conn);
    assert(dtc_data_ptr != NULL);
    
    // Test DTC to ASCII conversion
    char dtc_str[6];
    OBD_DTC_TO_ASCII(&dtc_data_ptr[0], dtc_str);
    printf("DTC 1: %s\n", dtc_str);
    
    OBD_DTC_TO_ASCII(&dtc_data_ptr[2], dtc_str);
    printf("DTC 2: %s\n", dtc_str);
    
    printf("✓ DTC parsing tests passed\n");
}

/**
 * @brief Test supported PIDs checking
 */
static void test_supported_pids(void) {
    printf("Testing supported PIDs checking...\n");
    
    // Mock supported PIDs response: Service 01, PID 0x00
    // Bit pattern: 0xBE1FA813 (supports PIDs 0x01, 0x03, 0x04, 0x05, 0x06, 0x07, 0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x13, 0x15, 0x1C, 0x1F, 0x20)
    uint8_t supported_data[] = {0xBE, 0x1F, 0xA8, 0x13};
    mock_positive_response(ES_OBD_SERVICE_01, ES_OBD_PID_SUPPORTED_PIDS_01_20, supported_data, sizeof(supported_data));
    
    // Simulate receiving the response
    memcpy(test_conn.isotp.rx_buffer, mock_response_msg.data, mock_response_msg.dlc);
    test_conn.isotp.rx_length = mock_response_msg.dlc;
    
    // Test PID support checking
    bool rpm_supported = es_obd_is_pid_supported(&test_conn, 0x00, ES_OBD_PID_ENGINE_RPM);
    bool speed_supported = es_obd_is_pid_supported(&test_conn, 0x00, ES_OBD_PID_VEHICLE_SPEED);
    bool coolant_supported = es_obd_is_pid_supported(&test_conn, 0x00, ES_OBD_PID_COOLANT_TEMP);
    
    assert(rpm_supported == true);    // PID 0x0C should be supported
    assert(speed_supported == true);  // PID 0x0D should be supported
    assert(coolant_supported == true); // PID 0x05 should be supported
    
    printf("✓ Supported PIDs checking tests passed\n");
}

/**
 * @brief Test VIN extraction
 */
static void test_vin_extraction(void) {
    printf("Testing VIN extraction...\n");
    
    // Mock VIN response: Service 09, Info Type 0x02
    const char *test_vin = "1HGBH41JXMN109186";
    uint8_t vin_data[18];
    vin_data[0] = 1;  // Message count
    memcpy(&vin_data[1], test_vin, 17);
    
    mock_positive_response(ES_OBD_SERVICE_09, ES_OBD_VIN_VEHICLE_IDENTIFICATION, vin_data, 18);
    
    // Simulate receiving the response
    memcpy(test_conn.isotp.rx_buffer, mock_response_msg.data, mock_response_msg.dlc);
    test_conn.isotp.rx_length = mock_response_msg.dlc;
    
    char vin_buffer[18];
    uint8_t vin_length = es_obd_extract_vin(&test_conn, vin_buffer);
    
    assert(vin_length == 17);
    assert(strcmp(vin_buffer, test_vin) == 0);
    
    printf("✓ VIN extraction tests passed\n");
}

/**
 * @brief Test negative response handling
 */
static void test_negative_response(void) {
    printf("Testing negative response handling...\n");
    
    // Mock negative response: Service not supported
    mock_negative_response(ES_OBD_SERVICE_01, ES_OBD_NRC_SERVICE_NOT_SUPPORTED);
    
    // Simulate receiving the response
    memcpy(test_conn.isotp.rx_buffer, mock_response_msg.data, mock_response_msg.dlc);
    test_conn.isotp.rx_length = mock_response_msg.dlc;
    
    // Check if it's recognized as negative response
    bool is_negative = (test_conn.isotp.rx_length >= 3 && test_conn.isotp.rx_buffer[0] == 0x7F);
    assert(is_negative == true);
    
    uint8_t nrc = test_conn.isotp.rx_buffer[2];
    assert(nrc == ES_OBD_NRC_SERVICE_NOT_SUPPORTED);
    
    printf("✓ Negative response handling tests passed\n");
}

/* ========================================================================== */
/*                            MAIN TEST FUNCTION                             */
/* ========================================================================== */

/**
 * @brief Run all OBD tests
 */
int main(void) {
    printf("=== OBD-II Module Unit Tests ===\n\n");
    
    // Initialize test environment
    memset(&test_conn, 0, sizeof(test_conn));
    memset(&mock_response_msg, 0, sizeof(mock_response_msg));
    
    // Run tests
    test_obd_init();
    test_obd_response_parsing();
    test_vehicle_speed_extraction();
    test_coolant_temp_extraction();
    test_dtc_parsing();
    test_supported_pids();
    test_vin_extraction();
    test_negative_response();
    
    printf("\n=== All OBD-II Tests Passed! ===\n");
    return 0;
}

/**
 * @brief Test runner for embedded environment
 */
void run_obd_tests(void) {
    printf("Running OBD-II module tests...\n");
    
    // Initialize test environment
    memset(&test_conn, 0, sizeof(test_conn));
    memset(&mock_response_msg, 0, sizeof(mock_response_msg));
    
    // Run critical tests
    test_obd_init();
    test_obd_response_parsing();
    test_vehicle_speed_extraction();
    test_coolant_temp_extraction();
    
    printf("OBD-II module tests completed successfully\n");
}
