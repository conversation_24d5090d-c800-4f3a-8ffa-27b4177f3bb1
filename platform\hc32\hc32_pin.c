/*
 * Copyright (c) 2006-2022, RT-Thread Development Team
 * Copyright (c) 2022, Xiaohua Semiconductor Co., Ltd.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2022-04-28     CDT          first version
 */


#include "hc32_pin.h"
#include "hc32_ll_gpio.h"
#include "hc32_ll_interrupts.h"
#include "hc32f4xx.h"
#include "hc32f460.h"
#include <stdbool.h>

#define PIN_MAX_NUM                     ((GPIO_PORT_H * 16) + (__CLZ(__RBIT(GPIO_PIN_02))) + 1)


#define ITEM_NUM(items)                 sizeof(items) / sizeof(items[0])


#define HC32_PIN_CONFIG(pin, callback, config)                                 \
    {                                                                          \
        .pinbit             = pin,                                             \
        .irq_callback       = callback,                                        \
        .irq_config         = config,                                          \
    }



#ifndef EXTINT0_IRQ_CONFIG
#define EXTINT0_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT0_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT0_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ0,                   \
    }
#endif /* EXTINT1_IRQ_CONFIG */

#ifndef EXTINT1_IRQ_CONFIG
#define EXTINT1_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT1_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT1_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ1,                   \
    }
#endif /* EXTINT1_IRQ_CONFIG */

#ifndef EXTINT2_IRQ_CONFIG
#define EXTINT2_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT2_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT2_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ2,                   \
    }
#endif /* EXTINT2_IRQ_CONFIG */

#ifndef EXTINT3_IRQ_CONFIG
#define EXTINT3_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT3_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT3_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ3,                   \
    }
#endif /* EXTINT3_IRQ_CONFIG */

#ifndef EXTINT4_IRQ_CONFIG
#define EXTINT4_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT4_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT4_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ4,                   \
    }
#endif /* EXTINT4_IRQ_CONFIG */

#ifndef EXTINT5_IRQ_CONFIG
#define EXTINT5_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT5_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT5_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ5,                   \
    }
#endif /* EXTINT5_IRQ_CONFIG */

#ifndef EXTINT6_IRQ_CONFIG
#define EXTINT6_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT6_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT6_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ6,                   \
    }
#endif /* EXTINT6_IRQ_CONFIG */

#ifndef EXTINT7_IRQ_CONFIG
#define EXTINT7_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT7_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT7_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ7,                   \
    }
#endif /* EXTINT7_IRQ_CONFIG */

#ifndef EXTINT8_IRQ_CONFIG
#define EXTINT8_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT8_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT8_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ8,                   \
    }
#endif /* EXTINT8_IRQ_CONFIG */

#ifndef EXTINT9_IRQ_CONFIG
#define EXTINT9_IRQ_CONFIG                                  \
    {                                                       \
        .irq_num    = BSP_EXTINT9_IRQ_NUM,                  \
        .irq_prio   = BSP_EXTINT9_IRQ_PRIO,                 \
        .int_src    = INT_SRC_PORT_EIRQ9,                   \
    }
#endif /* EXTINT9_IRQ_CONFIG */

#ifndef EXTINT10_IRQ_CONFIG
#define EXTINT10_IRQ_CONFIG                                 \
    {                                                       \
        .irq_num    = BSP_EXTINT10_IRQ_NUM,                 \
        .irq_prio   = BSP_EXTINT10_IRQ_PRIO,                \
        .int_src    = INT_SRC_PORT_EIRQ10,                  \
    }
#endif /* EXTINT10_IRQ_CONFIG */

#ifndef EXTINT11_IRQ_CONFIG
#define EXTINT11_IRQ_CONFIG                                 \
    {                                                       \
        .irq_num    = BSP_EXTINT11_IRQ_NUM,                 \
        .irq_prio   = BSP_EXTINT11_IRQ_PRIO,                \
        .int_src    = INT_SRC_PORT_EIRQ11,                  \
    }
#endif /* EXTINT11_IRQ_CONFIG */

#ifndef EXTINT12_IRQ_CONFIG
#define EXTINT12_IRQ_CONFIG                                 \
    {                                                       \
        .irq_num    = BSP_EXTINT12_IRQ_NUM,                 \
        .irq_prio   = BSP_EXTINT12_IRQ_PRIO,                \
        .int_src    = INT_SRC_PORT_EIRQ12,                  \
    }
#endif /* EXTINT12_IRQ_CONFIG */

#ifndef EXTINT13_IRQ_CONFIG
#define EXTINT13_IRQ_CONFIG                                 \
    {                                                       \
        .irq_num    = BSP_EXTINT13_IRQ_NUM,                 \
        .irq_prio   = BSP_EXTINT13_IRQ_PRIO,                \
        .int_src    = INT_SRC_PORT_EIRQ13,                  \
    }
#endif /* EXTINT13_IRQ_CONFIG */

#ifndef EXTINT14_IRQ_CONFIG
#define EXTINT14_IRQ_CONFIG                                 \
    {                                                       \
        .irq_num    = BSP_EXTINT14_IRQ_NUM,                 \
        .irq_prio   = BSP_EXTINT14_IRQ_PRIO,                \
        .int_src    = INT_SRC_PORT_EIRQ14,                  \
    }
#endif /* EXTINT14_IRQ_CONFIG */

#ifndef EXTINT15_IRQ_CONFIG
#define EXTINT15_IRQ_CONFIG                                 \
    {                                                       \
        .irq_num    = BSP_EXTINT15_IRQ_NUM,                 \
        .irq_prio   = BSP_EXTINT15_IRQ_PRIO,                \
        .int_src    = INT_SRC_PORT_EIRQ15,                  \
    }
#endif /* EXTINT15_IRQ_CONFIG */



static void extint0_irq_handler(void);
static void extint1_irq_handler(void);
static void extint2_irq_handler(void);
static void extint3_irq_handler(void);
static void extint4_irq_handler(void);
static void extint5_irq_handler(void);
static void extint6_irq_handler(void);
static void extint7_irq_handler(void);
static void extint8_irq_handler(void);
static void extint9_irq_handler(void);
static void extint10_irq_handler(void);
static void extint11_irq_handler(void);
static void extint12_irq_handler(void);
static void extint13_irq_handler(void);
static void extint14_irq_handler(void);
static void extint15_irq_handler(void);

static struct hc32_pin_irq_map pin_irq_map[] =
{
    HC32_PIN_CONFIG(GPIO_PIN_00, extint0_irq_handler,  EXTINT0_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_01, extint1_irq_handler,  EXTINT1_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_02, extint2_irq_handler,  EXTINT2_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_03, extint3_irq_handler,  EXTINT3_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_04, extint4_irq_handler,  EXTINT4_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_05, extint5_irq_handler,  EXTINT5_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_06, extint6_irq_handler,  EXTINT6_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_07, extint7_irq_handler,  EXTINT7_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_08, extint8_irq_handler,  EXTINT8_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_09, extint9_irq_handler,  EXTINT9_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_10, extint10_irq_handler, EXTINT10_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_11, extint11_irq_handler, EXTINT11_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_12, extint12_irq_handler, EXTINT12_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_13, extint13_irq_handler, EXTINT13_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_14, extint14_irq_handler, EXTINT14_IRQ_CONFIG),
    HC32_PIN_CONFIG(GPIO_PIN_15, extint15_irq_handler, EXTINT15_IRQ_CONFIG),
};

struct hc32_pin_irq_hdr pin_irq_hdr_tab[] =
{
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
    {-1, 0, NULL, NULL},
};

static void pin_irq_handler(uint16_t pinbit)
{
    int irqindex = -1;

    __disable_irq();
    if (SET == EXTINT_GetExtIntStatus(pinbit))
    {
        EXTINT_ClearExtIntStatus(pinbit);
        irqindex = __CLZ(__RBIT(pinbit));
        if (pin_irq_hdr_tab[irqindex].hdr)
        {
            pin_irq_hdr_tab[irqindex].hdr(pin_irq_hdr_tab[irqindex].args);
        }
    }
    __enable_irq();
}

static void extint0_irq_handler(void)
{       
    pin_irq_handler(pin_irq_map[0].pinbit);
}

static void extint1_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[1].pinbit);
}

static void extint2_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[2].pinbit);
}

static void extint3_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[3].pinbit);
}

static void extint4_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[4].pinbit);
}

static void extint5_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[5].pinbit);
}

static void extint6_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[6].pinbit);
}

static void extint7_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[7].pinbit);
}

static void extint8_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[8].pinbit);
}

static void extint9_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[9].pinbit);
}

static void extint10_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[10].pinbit);
}

static void extint11_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[11].pinbit);
}

static void extint12_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[12].pinbit);
}

static void extint13_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[13].pinbit);
}

static void extint14_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[14].pinbit);
}

static void extint15_irq_handler(void)
{
    pin_irq_handler(pin_irq_map[15].pinbit);
}

void es_pin_mode(uint16_t pin, uint8_t mode)
{
    stc_gpio_init_t stcGpioInit;

    if (pin >= PIN_MAX_NUM)
    {
        return;
    }

    GPIO_StructInit(&stcGpioInit);
    switch (mode)
    {
    case PIN_MODE_OUTPUT:
        stcGpioInit.u16PinDir        = PIN_DIR_OUT;
        stcGpioInit.u16PinOutputType = PIN_OUT_TYPE_CMOS;
        break;
    case PIN_MODE_INPUT:
        stcGpioInit.u16PinDir   = PIN_DIR_IN;
        break;
    case PIN_MODE_INPUT_PULLUP:
        stcGpioInit.u16PinDir   = PIN_DIR_IN;
        stcGpioInit.u16PullUp   = PIN_PU_ON;
        break;
    case PIN_MODE_INPUT_PULLDOWN:
        stcGpioInit.u16PinDir   = PIN_DIR_IN;
        stcGpioInit.u16PullUp   = PIN_PU_OFF;
        break;
    case PIN_MODE_OUTPUT_OD:
        stcGpioInit.u16PinDir        = PIN_DIR_OUT;
        stcGpioInit.u16PinOutputType = PIN_OUT_TYPE_NMOS;
        break;
    default:
        break;
    }
    GPIO_Init(GPIO_PORT(pin), GPIO_PIN(pin), &stcGpioInit);
}

void es_pin_write(uint16_t pin, uint8_t value)
{
    uint8_t  gpio_port;
    uint16_t gpio_pin;

    if (pin < PIN_MAX_NUM)
    {
        gpio_port = GPIO_PORT(pin);
        gpio_pin  = GPIO_PIN(pin);
        if (PIN_LOW == value)
        {
            GPIO_ResetPins(gpio_port, gpio_pin);
        }
        else
        {
            GPIO_SetPins(gpio_port, gpio_pin);
        }
    }
}

int es_pin_read(uint16_t pin)
{
    uint8_t  gpio_port;
    uint16_t gpio_pin;
    int value = PIN_LOW;

    if (pin < PIN_MAX_NUM)
    {
        gpio_port = GPIO_PORT(pin);
        gpio_pin  = GPIO_PIN(pin);
        if (PIN_RESET == GPIO_ReadInputPins(gpio_port, gpio_pin))
        {
            value = PIN_LOW;
        }
        else
        {
            value = PIN_HIGH;
        }
    }

    return value;
}

int es_pin_attach_irq(uint16_t pin, uint16_t mode, void (*hdr)(void *args), void *args)
{
    uint8_t level;
    int32_t irqindex = -1;

    if (pin >= PIN_MAX_NUM)
    {
        return -1;
    }
    irqindex = GPIO_PIN_INDEX(pin);
    if (irqindex >= ITEM_NUM(pin_irq_map))
    {
        return -1;
    }

    __disable_irq();
    if (pin_irq_hdr_tab[irqindex].pin  == pin  &&
            pin_irq_hdr_tab[irqindex].hdr  == hdr  &&
            pin_irq_hdr_tab[irqindex].mode == mode &&
            pin_irq_hdr_tab[irqindex].args == args)
    {
        __enable_irq();
        return 0;
    }
    if (pin_irq_hdr_tab[irqindex].pin != -1)
    {
        __enable_irq();
        return -1;
    }
    pin_irq_hdr_tab[irqindex].pin  = pin;
    pin_irq_hdr_tab[irqindex].hdr  = hdr;
    pin_irq_hdr_tab[irqindex].mode = mode;
    pin_irq_hdr_tab[irqindex].args = args;
    __enable_irq();

    return 0;
}

int es_pin_detach_irq(uint16_t pin)
{
    uint8_t level;
    int32_t irqindex = -1;

    if (pin >= PIN_MAX_NUM)
    {
        return -1;
    }
    irqindex = GPIO_PIN_INDEX(pin);
    if (irqindex >= ITEM_NUM(pin_irq_map))
    {
        return -1;
    }

    __disable_irq();
    if (pin_irq_hdr_tab[irqindex].pin == -1)
    {
        __enable_irq();
        return 0;
    }
    pin_irq_hdr_tab[irqindex].pin  = -1;
    pin_irq_hdr_tab[irqindex].hdr  = NULL;
    pin_irq_hdr_tab[irqindex].mode = 0;
    pin_irq_hdr_tab[irqindex].args = NULL;
    __enable_irq();

    return 0;
}

static void gpio_irq_config(uint8_t u8Port, uint16_t u16Pin, uint16_t u16ExInt)
{
    __IO uint16_t *PCRx;
    uint16_t pin_num;

    pin_num = __CLZ(__RBIT(u16Pin));
    PCRx = (__IO uint16_t *)((uint32_t)(&CM_GPIO->PCRA0) + ((uint32_t)u8Port * 0x40UL) + (pin_num * 4UL));
    MODIFY_REG16(*PCRx, GPIO_PCR_INTE, u16ExInt);
}

int es_pin_irq_enable(uint16_t pin, uint8_t enabled)
{
    struct hc32_pin_irq_map *irq_map;
    int32_t irqindex = -1;
    stc_extint_init_t stcExtIntInit;
    uint8_t  gpio_port;
    uint16_t gpio_pin;

    if ((pin >= PIN_MAX_NUM) || ((PIN_IRQ_ENABLE != enabled) && (PIN_IRQ_DISABLE != enabled)))
    {
        return -1;
    }
    irqindex = GPIO_PIN_INDEX(pin);
    if (irqindex >= ITEM_NUM(pin_irq_map))
    {
        return -1;
    }

    irq_map  = &pin_irq_map[irqindex];
    gpio_port = GPIO_PORT(pin);
    gpio_pin  = GPIO_PIN(pin);
    if (enabled == PIN_IRQ_ENABLE)
    {
        __disable_irq();
        if (pin_irq_hdr_tab[irqindex].pin == -1)
        {
            __enable_irq();
            return -1;
        }

        /* Exint config */
        EXTINT_StructInit(&stcExtIntInit);
        switch (pin_irq_hdr_tab[irqindex].mode)
        {
        case PIN_IRQ_MODE_RISING:
            stcExtIntInit.u32Edge = EXTINT_TRIG_RISING;
            break;
        case PIN_IRQ_MODE_FALLING:
            stcExtIntInit.u32Edge = EXTINT_TRIG_FALLING;
            break;
        case PIN_IRQ_MODE_RISING_FALLING:
            stcExtIntInit.u32Edge = EXTINT_TRIG_BOTH;
            break;
        case PIN_IRQ_MODE_LOW_LEVEL:
            stcExtIntInit.u32Edge = EXTINT_TRIG_LOW;
            break;
        }
        EXTINT_Init(gpio_pin, &stcExtIntInit);
        NVIC_EnableIRQ(irq_map->irq_config.irq_num);
        gpio_irq_config(gpio_port, gpio_pin, PIN_EXTINT_ON);
    }
    else
    {
        __disable_irq();
        gpio_irq_config(gpio_port, gpio_pin, PIN_EXTINT_OFF);
        NVIC_DisableIRQ(irq_map->irq_config.irq_num);
    }
    __enable_irq();

    return 0;
}
//
//uint16_t hc32_pin_get(const char *name)
//{
//    uint16_t pin = 0;
//    int hw_port_num, hw_pin_num = 0;
//    int i, name_len;
//
//    name_len = rt_strlen(name);
//    if ((name_len < 4) || (name_len >= 6))
//    {
//        return -1;
//    }
//    if ((name[0] != 'P') || (name[2] != '.'))
//    {
//        return -1;
//    }
//
//    if ((name[1] >= 'A') && (name[1] <= 'Z'))
//    {
//        hw_port_num = (int)(name[1] - 'A');
//    }
//    else
//    {
//        return -1;
//    }
//
//    for (i = 3; i < name_len; i++)
//    {
//        hw_pin_num *= 10;
//        hw_pin_num += name[i] - '0';
//    }
//    pin = PIN_NUM(hw_port_num, hw_pin_num);
//
//    return pin;
//}


int hc32_install_irq_handler(struct hc32_irq_config *irq_config,
                                  void (*irq_hdr)(void),
                                  bool irq_enable)
{
    int result = -1;
    stc_irq_signin_config_t stcIrqSignConfig;

    stcIrqSignConfig.enIRQn      = irq_config->irq_num;
    stcIrqSignConfig.enIntSrc    = irq_config->int_src;
    stcIrqSignConfig.pfnCallback = irq_hdr;
    if (LL_OK == INTC_IrqSignIn(&stcIrqSignConfig))
    {
        NVIC_ClearPendingIRQ(stcIrqSignConfig.enIRQn);
        NVIC_SetPriority(stcIrqSignConfig.enIRQn, irq_config->irq_prio);
        if (true == irq_enable)
        {
            NVIC_EnableIRQ(stcIrqSignConfig.enIRQn);
        }
        else
        {
            NVIC_DisableIRQ(stcIrqSignConfig.enIRQn);
        }
        result = 0;
    }
    return result;
}

int es_pin_init(void)
{
    static bool pin_init_flag = false;
    uint8_t u32MaxExtInt;

    if (pin_init_flag)
    {
        return 0;
    }

    /* register extint */
    u32MaxExtInt = ITEM_NUM(pin_irq_map);
    for (uint8_t i = 0; i < u32MaxExtInt; i++)
    {
        hc32_install_irq_handler(&pin_irq_map[i].irq_config, pin_irq_map[i].irq_callback, false);
    }

    pin_init_flag = true;   

    return 0;
}


