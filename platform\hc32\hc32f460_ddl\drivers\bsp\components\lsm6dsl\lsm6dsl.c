/**
 *******************************************************************************
 * @file  lsm6dsl.c
 * @brief This midware file provides firmware functions to LSM6DSL 6-axis IMU sensor.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-11-01       CDT             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2022-2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "lsm6dsl.h"

/**
 * @addtogroup BSP
 * @{
 */

/**
 * @addtogroup Components
 * @{
 */

/**
 * @defgroup LSM6DSL IMU Driver for LSM6DSL
 * @{
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/
/**
 * @defgroup LSM6DSL_Local_Macros LSM6DSL Local Macros
 * @{
 */
#define LSM6DSL_FLAG_DRDY_XL         (1UL << 0U)  /* Accelerometer data ready */
#define LSM6DSL_FLAG_DRDY_G          (1UL << 1U)  /* Gyroscope data ready */
#define LSM6DSL_FLAG_DRDY_TEMP       (1UL << 2U)  /* Temperature data ready */

/* Accel scale factor for different range */
#define LSM6DSL_ACCEL_SCALE_FACTOR_2G   (0.061f)  /* mg/LSB */
#define LSM6DSL_ACCEL_SCALE_FACTOR_4G   (0.122f)  /* mg/LSB */
#define LSM6DSL_ACCEL_SCALE_FACTOR_8G   (0.244f)  /* mg/LSB */
#define LSM6DSL_ACCEL_SCALE_FACTOR_16G  (0.488f)  /* mg/LSB */

/* Gyro scale factor for different range */
#define LSM6DSL_GYRO_SCALE_FACTOR_250   (8.75f)   /* mdps/LSB */
#define LSM6DSL_GYRO_SCALE_FACTOR_500   (17.5f)   /* mdps/LSB */
#define LSM6DSL_GYRO_SCALE_FACTOR_1000  (35.0f)   /* mdps/LSB */
#define LSM6DSL_GYRO_SCALE_FACTOR_2000  (70.0f)   /* mdps/LSB */

/**
 * @}
 */

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/
/**
 * @addtogroup LSM6DSL_Local_Functions LSM6DSL Local Functions
 * @{
 */

/**
 * @brief  Write to LSM6DSL register.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [in]  u8Reg                Register address.
 * @param  [in]  pu8Data              Pointer to the data buffer.
 * @param  [in]  u16Size              Number of bytes to write.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
static int32_t LSM6DSL_WriteReg(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, 
                                uint8_t u8Reg, const uint8_t *pu8Data, uint16_t u16Size)
{
    return pstcLsm6dslLL->WriteReg(u8Reg, pu8Data, u16Size);
}

/**
 * @brief  Read from LSM6DSL register.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [in]  u8Reg                Register address.
 * @param  [out] pu8Data              Pointer to the data buffer.
 * @param  [in]  u16Size              Number of bytes to read.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
static int32_t LSM6DSL_ReadReg(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, 
                               uint8_t u8Reg, uint8_t *pu8Data, uint16_t u16Size)
{
    return pstcLsm6dslLL->ReadReg(u8Reg, pu8Data, u16Size);
}

/**
 * @}
 */

/**
 * @defgroup LSM6DSL_Global_Functions LSM6DSL Global Functions
 * @{
 */
/**
 * @brief  Initializes LSM6DSL sensor.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [in]  enAccelScale         Accelerometer full scale.
 * @param  [in]  enGyroScale          Gyroscope full scale.
 * @param  [in]  enODR                Output data rate.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_Init(const stc_lsm6dsl_ll_t *pstcLsm6dslLL,
                     en_lsm6dsl_accel_scale_t enAccelScale,
                     en_lsm6dsl_gyro_scale_t enGyroScale,
                     en_lsm6dsl_odr_t enODR)
{
    uint8_t u8Value;
    int32_t i32Ret = LL_ERR_INVD_PARAM;
    uint8_t u8RegValue[2];

    if (pstcLsm6dslLL != NULL) {
        /* Initialize the low level hardware */
        pstcLsm6dslLL->Init();
        pstcLsm6dslLL->Delay(10U);

        /* Check device ID */
        i32Ret = LSM6DSL_GetDeviceID(pstcLsm6dslLL, &u8Value);
        if ((i32Ret == LL_OK) && (u8Value == LSM6DSL_WHO_AM_I_VALUE)) {
            /* Reset device */
            i32Ret = LSM6DSL_Reset(pstcLsm6dslLL);
            if (i32Ret == LL_OK) {
                /* Configure accelerometer */
                u8RegValue[0] = (uint8_t)(enODR | enAccelScale);
                i32Ret = LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL1_XL, u8RegValue, 1U);
                if (i32Ret == LL_OK) {
                    /* Configure gyroscope */
                    u8RegValue[0] = (uint8_t)(enODR | enGyroScale);
                    i32Ret = LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL2_G, u8RegValue, 1U);
                }
                if (i32Ret == LL_OK) {
                    /* Set block data update and auto address increment */
                    u8RegValue[0] = 0x04U;
                    i32Ret = LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL3_C, u8RegValue, 1U);
                }
            }
        }
    }

    return i32Ret;
}

/**
 * @brief  De-Initialize LSM6DSL.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 */
int32_t LSM6DSL_DeInit(const stc_lsm6dsl_ll_t *pstcLsm6dslLL)
{
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if (pstcLsm6dslLL != NULL) {
        /* Power down the device */
        uint8_t u8RegValue[2] = {0U, 0U};
        (void)LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL1_XL, u8RegValue, 1U);
        (void)LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL2_G, u8RegValue, 1U);

        /* De-initialize the low level hardware */
        pstcLsm6dslLL->DeInit();
        i32Ret = LL_OK;
    }

    return i32Ret;
}

/**
 * @brief  Read device ID.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [out] pu8ID                Pointer to the device ID value.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_GetDeviceID(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, uint8_t *pu8ID)
{
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if ((pstcLsm6dslLL != NULL) && (pu8ID != NULL)) {
        i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_WHO_AM_I, pu8ID, 1U);
    }

    return i32Ret;
}

/**
 * @brief  Reset the device.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_Reset(const stc_lsm6dsl_ll_t *pstcLsm6dslLL)
{
    uint8_t u8RegValue;
    int32_t i32Ret = LL_ERR_INVD_PARAM;
    volatile uint32_t u32Timeout;

    if (pstcLsm6dslLL != NULL) {
        /* Set BOOT bit to start reset procedure */
        u8RegValue = 0x80U;
        i32Ret = LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL3_C, &u8RegValue, 1U);
        if (i32Ret == LL_OK) {
            /* Wait for reset to complete */
            u32Timeout = LSM6DSL_TIMEOUT;
            do {
                pstcLsm6dslLL->Delay(1U);
                i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_CTRL3_C, &u8RegValue, 1U);
                u32Timeout--;
            } while ((i32Ret == LL_OK) && ((u8RegValue & 0x80U) != 0U) && (u32Timeout != 0UL));

            if ((i32Ret == LL_OK) && (u32Timeout == 0UL)) {
                i32Ret = LL_ERR_TIMEOUT;
            }
        }
    }

    return i32Ret;
}

/**
 * @brief  Read accelerometer data.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [out] pstcAccel            Pointer to acceleration data structure.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_ReadAccel(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, stc_lsm6dsl_axis_t *pstcAccel)
{
    uint8_t au8Data[6];
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if ((pstcLsm6dslLL != NULL) && (pstcAccel != NULL)) {
        /* Read accelerometer data */
        i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_OUTX_L_XL, au8Data, 6U);
        if (i32Ret == LL_OK) {
            /* Format the data */
            pstcAccel->x = (int16_t)((uint16_t)au8Data[1] << 8U) | au8Data[0];
            pstcAccel->y = (int16_t)((uint16_t)au8Data[3] << 8U) | au8Data[2];
            pstcAccel->z = (int16_t)((uint16_t)au8Data[5] << 8U) | au8Data[4];
        }
    }

    return i32Ret;
}

/**
 * @brief  Read gyroscope data.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [out] pstcGyro             Pointer to gyroscope data structure.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_ReadGyro(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, stc_lsm6dsl_axis_t *pstcGyro)
{
    uint8_t au8Data[6];
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if ((pstcLsm6dslLL != NULL) && (pstcGyro != NULL)) {
        /* Read gyroscope data */
        i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_OUTX_L_G, au8Data, 6U);
        if (i32Ret == LL_OK) {
            /* Format the data */
            pstcGyro->x = (int16_t)((uint16_t)au8Data[1] << 8U) | au8Data[0];
            pstcGyro->y = (int16_t)((uint16_t)au8Data[3] << 8U) | au8Data[2];
            pstcGyro->z = (int16_t)((uint16_t)au8Data[5] << 8U) | au8Data[4];
        }
    }

    return i32Ret;
}

/**
 * @brief  Read temperature data.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [out] pfTemp               Pointer to temperature value (Celsius).
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_ReadTemp(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, float *pfTemp)
{
    uint8_t au8Data[2];
    int16_t i16RawTemp;
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if ((pstcLsm6dslLL != NULL) && (pfTemp != NULL)) {
        /* Read temperature data */
        i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_OUT_TEMP_L, au8Data, 2U);
        if (i32Ret == LL_OK) {
            /* Format the data */
            i16RawTemp = (int16_t)((uint16_t)au8Data[1] << 8U) | au8Data[0];
            /* Temperature in degree Celsius: T = 25 + (Raw / 16) */
            *pfTemp = (float)i16RawTemp / 16.0f + 25.0f;
        }
    }

    return i32Ret;
}

/**
 * @brief  Read all sensor data (accelerometer, gyroscope, temperature).
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [out] pstcData             Pointer to sensor data structure.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_ReadSensor(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, stc_lsm6dsl_data_t *pstcData)
{
    uint8_t au8Data[14];  /* 2 bytes temp + 6 bytes gyro + 6 bytes accel */
    int16_t i16RawTemp;
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if ((pstcLsm6dslLL != NULL) && (pstcData != NULL)) {
        /* Read all sensor data starting from temperature data (14 bytes) */
        i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_OUT_TEMP_L, au8Data, 14U);
        if (i32Ret == LL_OK) {
            /* Format temperature data */
            i16RawTemp = (int16_t)((uint16_t)au8Data[1] << 8U) | au8Data[0];
            pstcData->temperature = (float)i16RawTemp / 16.0f + 25.0f;

            /* Format gyroscope data */
            pstcData->gyro.x = (int16_t)((uint16_t)au8Data[3] << 8U) | au8Data[2];
            pstcData->gyro.y = (int16_t)((uint16_t)au8Data[5] << 8U) | au8Data[4];
            pstcData->gyro.z = (int16_t)((uint16_t)au8Data[7] << 8U) | au8Data[6];

            /* Format accelerometer data */
            pstcData->accel.x = (int16_t)((uint16_t)au8Data[9] << 8U) | au8Data[8];
            pstcData->accel.y = (int16_t)((uint16_t)au8Data[11] << 8U) | au8Data[10];
            pstcData->accel.z = (int16_t)((uint16_t)au8Data[13] << 8U) | au8Data[12];
        }
    }

    return i32Ret;
}

/**
 * @brief  Set accelerometer full scale.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [in]  enAccelScale         New accelerometer full scale.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_SetAccelScale(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, en_lsm6dsl_accel_scale_t enAccelScale)
{
    uint8_t u8RegValue;
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if (pstcLsm6dslLL != NULL) {
        /* Read current register value */
        i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_CTRL1_XL, &u8RegValue, 1U);
        if (i32Ret == LL_OK) {
            /* Update scale bits */
            u8RegValue &= ~0x0CU;  /* Clear scale bits */
            u8RegValue |= (uint8_t)enAccelScale;
            /* Write back to register */
            i32Ret = LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL1_XL, &u8RegValue, 1U);
        }
    }

    return i32Ret;
}

/**
 * @brief  Set gyroscope full scale.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [in]  enGyroScale          New gyroscope full scale.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_SetGyroScale(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, en_lsm6dsl_gyro_scale_t enGyroScale)
{
    uint8_t u8RegValue;
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if (pstcLsm6dslLL != NULL) {
        /* Read current register value */
        i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_CTRL2_G, &u8RegValue, 1U);
        if (i32Ret == LL_OK) {
            /* Update scale bits */
            u8RegValue &= ~0x0CU;  /* Clear scale bits */
            u8RegValue |= (uint8_t)enGyroScale;
            /* Write back to register */
            i32Ret = LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL2_G, &u8RegValue, 1U);
        }
    }

    return i32Ret;
}

/**
 * @brief  Set output data rate.
 * @param  [in]  pstcLsm6dslLL        Pointer to a @ref stc_lsm6dsl_ll_t structure.
 * @param  [in]  enODR                New output data rate.
 * @retval int32_t:
 *           - LL_OK:                 No error occurred.
 *           - LL_ERR_INVD_PARAM:     Invalid parameter.
 *           - LL_ERR_TIMEOUT:        Communication timeout.
 */
int32_t LSM6DSL_SetODR(const stc_lsm6dsl_ll_t *pstcLsm6dslLL, en_lsm6dsl_odr_t enODR)
{
    uint8_t au8RegValue[2];
    int32_t i32Ret = LL_ERR_INVD_PARAM;

    if (pstcLsm6dslLL != NULL) {
        /* Read current accel register value */
        i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_CTRL1_XL, &au8RegValue[0], 1U);
        if (i32Ret == LL_OK) {
            /* Update ODR bits for accelerometer */
            au8RegValue[0] &= ~0xF0U;  /* Clear ODR bits */
            au8RegValue[0] |= (uint8_t)enODR;
            /* Write back to register */
            i32Ret = LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL1_XL, &au8RegValue[0], 1U);
        }

        if (i32Ret == LL_OK) {
            /* Read current gyro register value */
            i32Ret = LSM6DSL_ReadReg(pstcLsm6dslLL, LSM6DSL_CTRL2_G, &au8RegValue[1], 1U);
            if (i32Ret == LL_OK) {
                /* Update ODR bits for gyroscope */
                au8RegValue[1] &= ~0xF0U;  /* Clear ODR bits */
                au8RegValue[1] |= (uint8_t)enODR;
                /* Write back to register */
                i32Ret = LSM6DSL_WriteReg(pstcLsm6dslLL, LSM6DSL_CTRL2_G, &au8RegValue[1], 1U);
            }
        }
    }

    return i32Ret;
}

/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/ 