/**
 * @file uds_security_access_usage.c
 * @brief Simple usage example for the new UDS security access interface
 * <AUTHOR> MCU Team
 * @date 2024
 */

#include "es_uds.h"
#include "es_printf.h"

static const char *TAG = "UDS_SEC_USAGE";

/**
 * @brief Simple key generation callback
 * This demonstrates how to implement a key generation algorithm
 */
static uint16_t simple_key_generator(uint8_t level, const uint8_t *seed, uint16_t seed_length,
                                    uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
    // Validate input parameters
    if (!seed || seed_length == 0 || !key_buffer || max_key_length < 4) {
        ES_PRINTF_I(TAG, "Invalid parameters for key generation");
        return 0;
    }
    
    ES_PRINTF_I(TAG, "Generating key for level %d", level);
    ES_PRINTF_I(TAG, "Seed data (%d bytes): ", seed_length);
    for (uint16_t i = 0; i < seed_length && i < 8; i++) {
        ES_PRINTF_I(TAG, "%02X ", seed[i]);
    }
    if (seed_length > 8) {
        ES_PRINTF_I(TAG, "...");
    }
    ES_PRINTF_I(TAG, "");
    
    // Simple algorithm: XOR seed with a fixed pattern
    // In real applications, use proper cryptographic algorithms
    const uint8_t secret_pattern[] = {0x5A, 0xA5, 0x3C, 0xC3};
    uint16_t key_length = (seed_length > 4) ? 4 : seed_length;
    
    if (key_length > max_key_length) {
        key_length = max_key_length;
    }
    
    for (uint16_t i = 0; i < key_length; i++) {
        key_buffer[i] = seed[i] ^ secret_pattern[i % sizeof(secret_pattern)];
    }
    
    ES_PRINTF_I(TAG, "Generated key (%d bytes): ", key_length);
    for (uint16_t i = 0; i < key_length; i++) {
        ES_PRINTF_I(TAG, "%02X ", key_buffer[i]);
    }
    ES_PRINTF_I(TAG, "");
    
    return key_length;
}

/**
 * @brief Example of performing security access
 */
static es_async_t perform_security_access_example(es_coro_t *coro, es_uds_connection_t *conn) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Security Access Example ===");
    
    // Perform security access with level 1 (odd number)
    // This will:
    // 1. Send request with level 1 to get seed
    // 2. Call our callback to generate key from seed
    // 3. Send key with level 2 (1 + 1)
    es_co_await_ex(err, es_uds_security_access, conn, 1, simple_key_generator, NULL);
    
    ES_PRINTF_I(TAG, "Security access level 1 completed successfully!");
    ES_PRINTF_I(TAG, "ECU should now allow protected operations");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Security access failed");
    );
}

/**
 * @brief Complete workflow example
 */
static es_async_t complete_workflow_example(es_coro_t *coro) {
    static es_uds_connection_t uds_conn;
    
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Complete UDS Security Access Workflow ===");
    
    // Step 1: Initialize UDS connection
    es_uds_error_t result = es_uds_connection_init(&uds_conn, 0x7E0, 0x7E8);
    if (result != ES_UDS_OK) {
        ES_PRINTF_I(TAG, "Failed to initialize UDS connection: %d", result);
        es_co_err;
    }
    ES_PRINTF_I(TAG, "✓ UDS connection initialized");
    
    // Step 2: Enter programming session (usually required before security access)
    es_co_await_ex(err, es_uds_diagnostic_session_control, &uds_conn, ES_UDS_SESSION_PROGRAMMING);
    ES_PRINTF_I(TAG, "✓ Entered programming session");
    
    // Step 3: Perform security access
    es_co_await_ex(err, perform_security_access_example, &uds_conn);
    ES_PRINTF_I(TAG, "✓ Security access completed");
    
    // Step 4: Now you can perform protected operations
    // For example: ECU reset, firmware download, etc.
    ES_PRINTF_I(TAG, "✓ Ready for protected operations");
    
    ES_PRINTF_I(TAG, "=== Workflow completed successfully! ===");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Workflow failed at some step");
    );
}

/**
 * @brief Demonstration of different security levels
 */
static es_async_t multi_level_security_example(es_coro_t *coro, es_uds_connection_t *conn) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Multi-Level Security Access Example ===");
    
    // Level 1 security access
    ES_PRINTF_I(TAG, "Performing security access level 1...");
    es_co_await_ex(err, es_uds_security_access, conn, 1, simple_key_generator, NULL);
    ES_PRINTF_I(TAG, "✓ Level 1 security access completed");
    
    // Level 3 security access (higher security level)
    ES_PRINTF_I(TAG, "Performing security access level 3...");
    es_co_await_ex(err, es_uds_security_access, conn, 3, simple_key_generator, NULL);
    ES_PRINTF_I(TAG, "✓ Level 3 security access completed");
    
    ES_PRINTF_I(TAG, "All security levels unlocked!");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Multi-level security access failed");
    );
}

/**
 * @brief Main example function
 */
es_async_t uds_security_access_usage_main(es_coro_t *coro) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "Starting UDS Security Access Usage Examples");
    ES_PRINTF_I(TAG, "");
    ES_PRINTF_I(TAG, "Key features of the new interface:");
    ES_PRINTF_I(TAG, "- Level must be odd (1, 3, 5, etc.)");
    ES_PRINTF_I(TAG, "- Seed is requested automatically");
    ES_PRINTF_I(TAG, "- Callback generates key from seed");
    ES_PRINTF_I(TAG, "- Key is sent with level + 1");
    ES_PRINTF_I(TAG, "- No static variables, direct buffer usage");
    ES_PRINTF_I(TAG, "");
    
    // Run the complete workflow example
    es_co_await_ex(err, complete_workflow_example);
    
    ES_PRINTF_I(TAG, "All examples completed successfully!");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Examples failed");
    );
}

/* Usage notes:
 * 
 * 1. Always use odd levels (1, 3, 5, 7, etc.)
 * 2. The function will automatically:
 *    - Request seed using your odd level
 *    - Call your callback with the received seed
 *    - Send the generated key using level + 1
 * 
 * 3. Your callback should:
 *    - Validate input parameters
 *    - Generate key based on seed and level
 *    - Return the key length (0 = failure)
 * 
 * 4. Example usage:
 *    es_co_await_ex(err, es_uds_security_access, conn, 1, my_key_generator, NULL);
 */
