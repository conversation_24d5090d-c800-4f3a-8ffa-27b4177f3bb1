/**
 * @file es_utils.h
 * @brief Common utility functions header file
 */
#ifndef __ES_UTILS_H__
#define __ES_UTILS_H__

#include <stdint.h>
#include <stddef.h>
#include "es_md5.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DIGITAL_FILTER_HISTORY_MASK 0xFE      // History state mask (high 7 bits)
#define DIGITAL_FILTER_RESULT_MASK 0x01       // Filtered state mask (low 1 bit)

/**
 * @brief Set a specific bit in a bitmap
 * @param bitmap Bitmap array
 * @param bit Bit index to set
 */
#define ES_BIT_SET(bitmap, bit)   ((bitmap)[(bit)/8] |= (1 << ((bit) % 8)))

/**
 * @brief Clear a specific bit in a bitmap
 * @param bitmap Bitmap array
 * @param bit Bit index to clear
 */
#define ES_BIT_CLEAR(bitmap, bit) ((bitmap)[(bit)/8] &= ~(1 << ((bit) % 8)))


#define ES_CHECK(label, condition, fmt, ...) \
    do { \
        if((condition)) { \
            ES_LOGE(TAG, fmt, ##__VA_ARGS__); \
            goto label; \
        } \
    } while(0)

/**
 * @brief Test if a specific bit is set in a bitmap
 * @param bitmap Bitmap array
 * @param bit Bit index to test
 * @return Non-zero if bit is set, 0 otherwise
 */
#define ES_BIT_TEST(bitmap, bit)  ((bitmap)[(bit)/8] & (1 << ((bit) % 8)))

uint8_t es_utils_digital_filter(uint8_t history, uint8_t current_state, uint8_t threshold);

#ifdef __cplusplus
}
#endif

#endif /* __ES_UTILS_H__ */ 
