/**
 * @file urc_memory_independence_demo.c
 * @brief URC内存独立性演示
 * 
 * 本示例演示了改进后的URC内存管理机制：
 * - 取出URC项时重新分配独立内存
 * - 队列释放时立即释放原始内存
 * - 两个内存块完全独立，无任何关联
 */

#include "es_at_cli.h"
#include "es_log.h"
#include "es_mem.h"

#define TAG "URC_MEM_DEMO"

/**
 * @brief 演示URC内存独立性的函数
 */
void demonstrate_urc_memory_independence(void)
{
    ES_PRINTF_I(TAG, "=== URC Memory Independence Demo ===");
    
    // 模拟队列中的URC项
    es_at_cli_urc_item_t queue_item;
    const char *original_data = "+TEST:Hello World\r\n";
    int data_len = strlen(original_data);
    
    // 1. 模拟添加到队列时的内存分配
    ES_PRINTF_I(TAG, "Step 1: Allocating memory for queue item");
    queue_item.data = es_mem_alloc(data_len + 1);
    if (queue_item.data == NULL) {
        ES_PRINTF_I(TAG, "Failed to allocate memory for queue item");
        return;
    }
    
    memcpy(queue_item.data, original_data, data_len);
    queue_item.data[data_len] = '\0';
    queue_item.len = data_len;
    queue_item.handler = NULL; // 简化演示
    
    ES_PRINTF_I(TAG, "Queue item allocated at: %p", queue_item.data);
    ES_PRINTF_I(TAG, "Queue item data: %s", queue_item.data);
    
    // 2. 模拟取出URC项时的独立内存分配
    ES_PRINTF_I(TAG, "\nStep 2: Creating independent copy for processing");
    es_at_cli_urc_item_t processing_item;
    
    // 为处理项分配新的独立内存
    processing_item.handler = queue_item.handler;
    processing_item.len = queue_item.len;
    processing_item.data = es_mem_alloc(queue_item.len + 1);
    
    if (processing_item.data != NULL) {
        // 拷贝数据到新分配的内存
        memcpy(processing_item.data, queue_item.data, queue_item.len);
        processing_item.data[queue_item.len] = '\0';
        
        ES_PRINTF_I(TAG, "Processing item allocated at: %p", processing_item.data);
        ES_PRINTF_I(TAG, "Processing item data: %s", processing_item.data);
        ES_PRINTF_I(TAG, "Memory addresses are different: %s", 
                   (queue_item.data != processing_item.data) ? "YES" : "NO");
    } else {
        ES_PRINTF_I(TAG, "Failed to allocate memory for processing item");
        es_mem_free_safe((void**)&queue_item.data);
        return;
    }
    
    // 3. 模拟队列释放（立即释放原始内存）
    ES_PRINTF_I(TAG, "\nStep 3: Releasing queue memory immediately");
    ES_PRINTF_I(TAG, "Queue memory before release: %p", queue_item.data);
    es_mem_free_safe((void**)&queue_item.data);
    ES_PRINTF_I(TAG, "Queue memory after release: %p", queue_item.data);
    
    // 4. 验证处理项内存仍然有效
    ES_PRINTF_I(TAG, "\nStep 4: Verifying processing item is still valid");
    ES_PRINTF_I(TAG, "Processing item memory: %p", processing_item.data);
    ES_PRINTF_I(TAG, "Processing item data: %s", processing_item.data);
    ES_PRINTF_I(TAG, "Processing item is independent: %s", 
               (processing_item.data != NULL) ? "YES" : "NO");
    
    // 5. 模拟处理完成后释放处理项内存
    ES_PRINTF_I(TAG, "\nStep 5: Releasing processing memory after completion");
    ES_PRINTF_I(TAG, "Processing memory before release: %p", processing_item.data);
    es_mem_free_safe((void**)&processing_item.data);
    ES_PRINTF_I(TAG, "Processing memory after release: %p", processing_item.data);
    
    ES_PRINTF_I(TAG, "\n=== Demo Completed Successfully ===");
    ES_PRINTF_I(TAG, "Key Points:");
    ES_PRINTF_I(TAG, "1. Queue and processing items have independent memory");
    ES_PRINTF_I(TAG, "2. Queue memory is released immediately after copying");
    ES_PRINTF_I(TAG, "3. Processing memory remains valid until processing completes");
    ES_PRINTF_I(TAG, "4. No memory sharing or dependencies between queue and processing");
}

/**
 * @brief 内存独立性的关键优势
 */
void explain_memory_independence_benefits(void)
{
    ES_PRINTF_I(TAG, "\n=== Memory Independence Benefits ===");
    
    ES_PRINTF_I(TAG, "1. **完全解耦**:");
    ES_PRINTF_I(TAG, "   - 队列内存和处理内存完全独立");
    ES_PRINTF_I(TAG, "   - 队列操作不会影响正在处理的URC项");
    
    ES_PRINTF_I(TAG, "2. **并发安全**:");
    ES_PRINTF_I(TAG, "   - 可以在URC处理过程中安全清理队列");
    ES_PRINTF_I(TAG, "   - 支持队列重置而不影响当前处理");
    
    ES_PRINTF_I(TAG, "3. **内存安全**:");
    ES_PRINTF_I(TAG, "   - 避免野指针和双重释放");
    ES_PRINTF_I(TAG, "   - 明确的内存所有权");
    
    ES_PRINTF_I(TAG, "4. **错误隔离**:");
    ES_PRINTF_I(TAG, "   - 队列错误不会影响处理中的URC");
    ES_PRINTF_I(TAG, "   - 处理错误不会影响队列状态");
}

/**
 * @brief 演示任务
 */
static es_async_t memory_demo_task(es_coro_t *coro, void *ctx)
{
    es_co_begin(coro);
    
    while (1) {
        demonstrate_urc_memory_independence();
        es_co_sleep(5000);
        
        explain_memory_independence_benefits();
        es_co_sleep(5000);
    }
    
    es_co_end;
}

// 任务实例
static es_coro_task_t s_demo_task = {
    .name = "memory_demo",
    .func = memory_demo_task,
    .ctx = NULL
};

/**
 * @brief 初始化内存独立性演示
 */
int urc_memory_independence_demo_init(void)
{
    es_scheduler_task_add(es_scheduler_get_default(), &s_demo_task);
    ES_PRINTF_I(TAG, "URC Memory Independence Demo initialized");
    return 0;
}

/**
 * @brief 反初始化内存独立性演示
 */
void urc_memory_independence_demo_deinit(void)
{
    es_scheduler_task_remove(es_scheduler_get_default(), &s_demo_task);
    ES_PRINTF_I(TAG, "URC Memory Independence Demo deinitialized");
}
