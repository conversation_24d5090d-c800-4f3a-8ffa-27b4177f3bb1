/**
 * @file es_obd.c
 * @brief OBD-II (On-Board Diagnostics) Client Implementation
 *
 * This module provides OBD-II client functionality for automotive diagnostics
 * according to SAE J1979 and ISO 15031 standards.
 *
 * <AUTHOR> MCU Framework
 * @date 2025-01-22
 * @version 1.0.0
 */

#include "es_obd.h"

#define TAG "OBD"

/* ========================================================================== */
/*                            LOG CONTROL MACROS                             */
/* ========================================================================== */

// Critical operations (always logged)
#define OBD_LOG_CRITICAL(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)

// Error conditions (level 1+)
#if ES_OBD_LOG_LEVEL >= 1
#define OBD_LOG_ERROR(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define OBD_LOG_ERROR(fmt, ...)
#endif

// Important operations (level 2+)
#if ES_OBD_LOG_LEVEL >= 2
#define OBD_LOG_INFO(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define OBD_LOG_INFO(fmt, ...)
#endif

// Debug information (level 3+)
#if ES_OBD_LOG_LEVEL >= 3
#define OBD_LOG_DEBUG(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define OBD_LOG_DEBUG(fmt, ...)
#endif

// Verbose frame-level logs (level 4+)
#if ES_OBD_LOG_LEVEL >= 4
#define OBD_LOG_VERBOSE(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define OBD_LOG_VERBOSE(fmt, ...)
#endif

/* ========================================================================== */
/*                            UTILITY FUNCTIONS                              */
/* ========================================================================== */

/**
 * @brief Get current timestamp in milliseconds
 */
static inline uint32_t get_timestamp_ms(void) {
    return es_os_get_tick_ms();
}

/**
 * @brief Check if timeout has occurred
 */
static inline bool is_timeout(uint32_t start_time_ms, uint32_t timeout_ms) {
    uint32_t current_time_ms = get_timestamp_ms();
    
    if (current_time_ms >= start_time_ms) {
        return (current_time_ms - start_time_ms) >= timeout_ms;
    } else {
        return ((UINT32_MAX - start_time_ms) + current_time_ms + 1) >= timeout_ms;
    }
}

/**
 * @brief Check if response is negative
 */
static inline bool is_negative_response(const uint8_t *data, uint16_t length) {
    return (length >= 3 && data[0] == 0x7F);
}

/**
 * @brief Get negative response code
 */
static inline uint8_t get_negative_response_code(const uint8_t *data) {
    return data[2];  // Format: [0x7F][SID][NRC]
}

/**
 * @brief Validate positive response
 */
static inline bool is_valid_positive_response(const uint8_t *data, uint16_t length, uint8_t expected_service) {
    return (length >= 1 && data[0] == (expected_service + 0x40));
}

/* ========================================================================== */
/*                            MODULE FUNCTIONS                               */
/* ========================================================================== */

/**
 * @brief Initialize OBD-II connection
 */
es_obd_error_t es_obd_init(es_obd_connection_t *conn, uint8_t ecu_id, bool use_functional) {
    if (!conn) {
        return ES_OBD_ERR_INVALID_PARAM;
    }
    
    if (!use_functional && ecu_id > 7) {
        return ES_OBD_ERR_INVALID_PARAM;
    }
    
    // Clear the entire structure
    memset(conn, 0, sizeof(es_obd_connection_t));
    
    // Configure CAN IDs based on addressing mode
    uint32_t tx_id, rx_id;
    
    if (use_functional) {
        // Functional addressing (broadcast)
        tx_id = ES_OBD_FUNCTIONAL_ID;
        rx_id = ES_OBD_PHYSICAL_RX_BASE + ecu_id;  // Still need specific ECU for response
    } else {
        // Physical addressing
        tx_id = ES_OBD_PHYSICAL_TX_BASE + ecu_id;
        rx_id = ES_OBD_PHYSICAL_RX_BASE + ecu_id;
    }
    
    // Initialize ISO-TP connection
    es_isotp_error_t isotp_ret = es_isotp_init(&conn->isotp, tx_id, rx_id,
                                               conn->tx_buffer, sizeof(conn->tx_buffer),
                                               conn->rx_buffer, sizeof(conn->rx_buffer));
    
    if (isotp_ret != ES_ISOTP_OK) {
        OBD_LOG_ERROR("Failed to initialize ISO-TP connection: %d", isotp_ret);
        return ES_OBD_ERR_CAN_ERROR;
    }
    
    // Set OBD-II specific timeouts
    conn->isotp.timeout_ms = ES_OBD_DEFAULT_TIMEOUT_MS;
    
    OBD_LOG_INFO("OBD-II connection initialized (ECU: %d, Functional: %s, TX: 0x%03X, RX: 0x%03X)",
                 ecu_id, use_functional ? "Yes" : "No", tx_id, rx_id);
    
    return ES_OBD_OK;
}

/**
 * @brief Process incoming CAN message
 */
es_obd_error_t es_obd_process_can_message(es_obd_connection_t *conn, const es_can_msg_t *msg) {
    if (!conn || !msg) {
        return ES_OBD_ERR_INVALID_PARAM;
    }
    
    // Forward to ISO-TP layer
    es_isotp_error_t isotp_ret = es_isotp_process_can_message(&conn->isotp, msg);
    
    if (isotp_ret == ES_ISOTP_OK) {
        return ES_OBD_OK;
    } else if (isotp_ret == ES_ISOTP_ERR_OVERFLOW) {
        return ES_OBD_ERR_BUSY;
    } else {
        return ES_OBD_ERR_CAN_ERROR;
    }
}

/* ========================================================================== */
/*                            OBD-II HELPER FUNCTIONS                        */
/* ========================================================================== */

static es_async_t es_obd_wait_response(es_coro_t *coro, es_obd_connection_t *conn);

/**
 * @brief Send OBD-II request and wait for response (coroutine helper)
 */
static es_async_t es_obd_request_ex(es_coro_t *coro, es_obd_connection_t *conn) {
    es_co_begin(coro);
    
    if (conn->isotp.tx_length == 0) {
        es_co_err;
    }
    
    if (conn->isotp.state != ES_ISOTP_STATE_IDLE) {
        es_co_err;  // Connection busy
    }
    
    uint8_t service_id = conn->isotp.tx_buffer[0];
    
    // Store request info
    conn->current_service = service_id;
    conn->response_pending_count = 0;
    conn->waiting_for_response = true;
    conn->request_start_time = get_timestamp_ms();
    
    // Send request via ISO-TP
    es_co_await_ex(err, es_isotp_send, &conn->isotp);
    
    OBD_LOG_DEBUG("OBD request sent: SID=0x%02X, len=%d", service_id, conn->isotp.tx_length);
    
    // Wait for response
    es_co_await_ex(err, es_obd_wait_response, conn);
    
    OBD_LOG_DEBUG("OBD request complete: SID=0x%02X, response_len=%d", service_id, conn->isotp.rx_length);
    
    es_co_eee(
        OBD_LOG_ERROR("Failed to send OBD request");
        conn->waiting_for_response = false;
    );
}

/**
 * @brief Wait for OBD-II response with timeout handling (coroutine helper)
 */
static es_async_t es_obd_wait_response(es_coro_t *coro, es_obd_connection_t *conn) {
    es_co_begin(coro);
    
    if (!conn->waiting_for_response) {
        es_co_err;
    }
    
    // Wait for ISO-TP response
    es_co_await_ex(err, es_isotp_recv, &conn->isotp);
    
    // Check for negative response
    if (is_negative_response(conn->isotp.rx_buffer, conn->isotp.rx_length)) {
        uint8_t nrc = get_negative_response_code(conn->isotp.rx_buffer);
        conn->last_nrc = nrc;
        
        if (nrc == ES_OBD_NRC_RESPONSE_PENDING) {
            // Handle response pending (0x78)
            conn->response_pending_count++;
            if (conn->response_pending_count < 10) {  // Max 10 pending responses
                OBD_LOG_DEBUG("Response pending (0x78), count: %d", conn->response_pending_count);
                conn->request_start_time = get_timestamp_ms();  // Reset timeout
                es_co_await_ex(err, es_obd_wait_response, conn);  // Wait for actual response
            } else {
                OBD_LOG_ERROR("Too many response pending messages");
                es_co_err;
            }
        } else {
            OBD_LOG_ERROR("Negative response: SID=0x%02X, NRC=0x%02X", 
                         conn->current_service, nrc);
            es_co_err;
        }
    }
    
    // Validate positive response
    if (!is_valid_positive_response(conn->isotp.rx_buffer, conn->isotp.rx_length, conn->current_service)) {
        OBD_LOG_ERROR("Invalid positive response format");
        es_co_err;
    }
    
    conn->waiting_for_response = false;
    
    es_co_eee(
        OBD_LOG_ERROR("Failed to wait for OBD response");
        conn->waiting_for_response = false;
    );
}

/* ========================================================================== */
/*                            OBD-II SERVICE FUNCTIONS                       */
/* ========================================================================== */

/**
 * @brief Read current data (Service 01) (coroutine)
 */
es_async_t es_obd_read_current_data(es_coro_t *coro, es_obd_connection_t *conn, es_obd_pid_t pid) {
    es_co_begin(coro);
    
    // Build request: [Service][PID]
    conn->isotp.tx_buffer[0] = ES_OBD_SERVICE_01;
    conn->isotp.tx_buffer[1] = (uint8_t)pid;
    conn->isotp.tx_length = 2;
    
    conn->current_pid = (uint8_t)pid;
    
    es_co_await_ex(err, es_obd_request_ex, conn);
    
    OBD_LOG_INFO("Read current data success: PID=0x%02X, response_len=%d", 
                 pid, conn->isotp.rx_length);
    
    es_co_eee(
        OBD_LOG_ERROR("Failed to read current data: PID=0x%02X", pid);
    );
}

/**
 * @brief Read freeze frame data (Service 02) (coroutine)
 */
es_async_t es_obd_read_freeze_frame_data(es_coro_t *coro, es_obd_connection_t *conn, 
                                         es_obd_pid_t pid, uint8_t frame_number) {
    es_co_begin(coro);
    
    // Build request: [Service][PID][Frame Number]
    conn->isotp.tx_buffer[0] = ES_OBD_SERVICE_02;
    conn->isotp.tx_buffer[1] = (uint8_t)pid;
    conn->isotp.tx_buffer[2] = frame_number;
    conn->isotp.tx_length = 3;
    
    conn->current_pid = (uint8_t)pid;
    
    es_co_await_ex(err, es_obd_request_ex, conn);
    
    OBD_LOG_INFO("Read freeze frame data success: PID=0x%02X, frame=%d, response_len=%d", 
                 pid, frame_number, conn->isotp.rx_length);
    
    es_co_eee(
        OBD_LOG_ERROR("Failed to read freeze frame data: PID=0x%02X, frame=%d", pid, frame_number);
    );
}

/**
 * @brief Read stored DTCs (Service 03) (coroutine)
 */
es_async_t es_obd_read_stored_dtcs(es_coro_t *coro, es_obd_connection_t *conn) {
    es_co_begin(coro);
    
    // Build request: [Service]
    conn->isotp.tx_buffer[0] = ES_OBD_SERVICE_03;
    conn->isotp.tx_length = 1;
    
    es_co_await_ex(err, es_obd_request_ex, conn);
    
    OBD_LOG_INFO("Read stored DTCs success: response_len=%d", conn->isotp.rx_length);
    
    es_co_eee(
        OBD_LOG_ERROR("Failed to read stored DTCs");
    );
}

/**
 * @brief Clear DTCs and stored values (Service 04) (coroutine)
 */
es_async_t es_obd_clear_dtcs(es_coro_t *coro, es_obd_connection_t *conn) {
    es_co_begin(coro);
    
    // Build request: [Service]
    conn->isotp.tx_buffer[0] = ES_OBD_SERVICE_04;
    conn->isotp.tx_length = 1;
    
    es_co_await_ex(err, es_obd_request_ex, conn);
    
    OBD_LOG_INFO("Clear DTCs success: response_len=%d", conn->isotp.rx_length);
    
    es_co_eee(
        OBD_LOG_ERROR("Failed to clear DTCs");
    );
}

/**
 * @brief Read pending DTCs (Service 07) (coroutine)
 */
es_async_t es_obd_read_pending_dtcs(es_coro_t *coro, es_obd_connection_t *conn) {
    es_co_begin(coro);
    
    // Build request: [Service]
    conn->isotp.tx_buffer[0] = ES_OBD_SERVICE_07;
    conn->isotp.tx_length = 1;
    
    es_co_await_ex(err, es_obd_request_ex, conn);
    
    OBD_LOG_INFO("Read pending DTCs success: response_len=%d", conn->isotp.rx_length);
    
    es_co_eee(
        OBD_LOG_ERROR("Failed to read pending DTCs");
    );
}

/**
 * @brief Request vehicle information (Service 09) (coroutine)
 */
es_async_t es_obd_request_vehicle_info(es_coro_t *coro, es_obd_connection_t *conn, 
                                       es_obd_vin_pid_t info_type) {
    es_co_begin(coro);
    
    // Build request: [Service][Info Type]
    conn->isotp.tx_buffer[0] = ES_OBD_SERVICE_09;
    conn->isotp.tx_buffer[1] = (uint8_t)info_type;
    conn->isotp.tx_length = 2;
    
    conn->current_pid = (uint8_t)info_type;
    
    es_co_await_ex(err, es_obd_request_ex, conn);
    
    OBD_LOG_INFO("Request vehicle info success: info_type=0x%02X, response_len=%d", 
                 info_type, conn->isotp.rx_length);
    
    es_co_eee(
        OBD_LOG_ERROR("Failed to request vehicle info: info_type=0x%02X", info_type);
    );
}

/* ========================================================================== */
/*                            UTILITY FUNCTIONS                              */
/* ========================================================================== */

/**
 * @brief Parse and display engine data from current data response
 * @param conn Pointer to OBD connection
 * @param pid PID that was requested
 */
void es_obd_parse_engine_data(es_obd_connection_t *conn, es_obd_pid_t pid) {
    if (!conn || OBD_RESPONSE_LENGTH(conn) < 2) {
        return;
    }

    switch (pid) {
        case ES_OBD_PID_ENGINE_RPM: {
            uint16_t rpm = OBD_EXTRACT_ENGINE_RPM(conn);
            OBD_LOG_INFO("Engine RPM: %d RPM", rpm);
            break;
        }

        case ES_OBD_PID_VEHICLE_SPEED: {
            uint8_t speed = OBD_EXTRACT_VEHICLE_SPEED(conn);
            OBD_LOG_INFO("Vehicle Speed: %d km/h", speed);
            break;
        }

        case ES_OBD_PID_COOLANT_TEMP: {
            int16_t temp = OBD_EXTRACT_COOLANT_TEMP(conn);
            OBD_LOG_INFO("Coolant Temperature: %d°C", temp);
            break;
        }

        case ES_OBD_PID_ENGINE_LOAD: {
            uint8_t load = OBD_EXTRACT_ENGINE_LOAD(conn);
            OBD_LOG_INFO("Engine Load: %d%%", load);
            break;
        }

        case ES_OBD_PID_THROTTLE_POSITION: {
            uint8_t throttle = OBD_EXTRACT_THROTTLE_POSITION(conn);
            OBD_LOG_INFO("Throttle Position: %d%%", throttle);
            break;
        }

        case ES_OBD_PID_INTAKE_AIR_TEMP: {
            int16_t temp = OBD_EXTRACT_INTAKE_AIR_TEMP(conn);
            OBD_LOG_INFO("Intake Air Temperature: %d°C", temp);
            break;
        }

        case ES_OBD_PID_MAF_RATE: {
            uint16_t maf = OBD_EXTRACT_MAF_RATE(conn);
            OBD_LOG_INFO("MAF Rate: %d.%02d g/s", maf / 100, maf % 100);
            break;
        }

        case ES_OBD_PID_FUEL_PRESSURE: {
            uint16_t pressure = OBD_EXTRACT_FUEL_PRESSURE(conn);
            OBD_LOG_INFO("Fuel Pressure: %d kPa", pressure);
            break;
        }

        case ES_OBD_PID_INTAKE_MAP: {
            uint8_t map = OBD_EXTRACT_INTAKE_MAP(conn);
            OBD_LOG_INFO("Intake MAP: %d kPa", map);
            break;
        }

        default:
            OBD_LOG_DEBUG("Raw data for PID 0x%02X: %d bytes", pid, OBD_RESPONSE_DATA_LENGTH(conn));
            break;
    }
}

/**
 * @brief Parse and display DTCs from DTC response
 * @param conn Pointer to OBD connection
 */
void es_obd_parse_dtcs(es_obd_connection_t *conn) {
    if (!conn || OBD_RESPONSE_LENGTH(conn) < 2) {
        return;
    }

    uint8_t dtc_count = OBD_GET_DTC_COUNT(conn);
    uint8_t *dtc_data = OBD_GET_DTC_DATA(conn);

    OBD_LOG_INFO("DTC Count: %d", dtc_count);

    if (dtc_count > 0 && dtc_data) {
        for (uint8_t i = 0; i < dtc_count && (i * 2 + 1) < OBD_RESPONSE_DATA_LENGTH(conn); i++) {
            char dtc_str[6];
            OBD_DTC_TO_ASCII(&dtc_data[i * 2], dtc_str);
            OBD_LOG_INFO("DTC %d: %s (0x%02X%02X)", i + 1, dtc_str,
                        dtc_data[i * 2], dtc_data[i * 2 + 1]);
        }
    }
}

/**
 * @brief Check if specific PID is supported
 * @param conn Pointer to OBD connection (after supported PIDs request)
 * @param base_pid Base PID for the supported range (0x00, 0x20, 0x40, etc.)
 * @param target_pid Target PID to check
 * @return true if PID is supported, false otherwise
 */
bool es_obd_is_pid_supported(es_obd_connection_t *conn, uint8_t base_pid, uint8_t target_pid) {
    if (!conn || OBD_RESPONSE_DATA_LENGTH(conn) < 4) {
        return false;
    }

    // Calculate offset within the 32-PID range
    uint8_t pid_offset = target_pid - base_pid - 1;
    if (pid_offset >= 32) {
        return false;
    }

    uint8_t *supported_data = OBD_RESPONSE_DATA(conn);
    return OBD_IS_PID_SUPPORTED(supported_data, pid_offset);
}

/**
 * @brief Extract VIN from Service 09 response
 * @param conn Pointer to OBD connection
 * @param vin_buffer Buffer to store VIN string (minimum 18 bytes)
 * @return Length of VIN string, 0 on error
 */
uint8_t es_obd_extract_vin(es_obd_connection_t *conn, char *vin_buffer) {
    if (!conn || !vin_buffer || OBD_RESPONSE_DATA_LENGTH(conn) < 3) {
        return 0;
    }

    // VIN response format: [Service+0x40][Info Type][Message Count][VIN data...]
    uint8_t message_count = OBD_RESPONSE_DATA(conn)[0];
    uint8_t *vin_data = &OBD_RESPONSE_DATA(conn)[1];
    uint8_t vin_length = OBD_RESPONSE_DATA_LENGTH(conn) - 1;

    if (vin_length > 17) {
        vin_length = 17;  // VIN is max 17 characters
    }

    memcpy(vin_buffer, vin_data, vin_length);
    vin_buffer[vin_length] = '\0';

    return vin_length;
}
