/**
 * @file test_mem_v2_merge.c
 * @brief 内存管理器V2块合并功能测试
 * <AUTHOR> Assistant
 * @date 2025/1/21
 */

#include <stdio.h>
#include <string.h>
#include <assert.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdbool.h>
#include <stdlib.h>
#include "../applications/es_mem_v2.h"

// 简单的日志实现用于测试
void es_log_write(int level, const char *tag, bool to_flash, const char *fmt, ...) {
    va_list args;
    va_start(args, fmt);
    printf("[%s] ", tag);
    vprintf(fmt, args);
    printf("\n");
    va_end(args);
}

static void test_forward_merge_multiple(void) {
    printf("\n=== Testing Multiple Forward Merge ===\n");
    
    // 分配5个连续的小块
    void *ptrs[5];
    size_t block_size = 100;
    
    printf("Allocating 5 consecutive blocks of %zu bytes each...\n", block_size);
    for (int i = 0; i < 5; i++) {
        ptrs[i] = es_mem_v2_alloc(block_size);
        assert(ptrs[i] != NULL);
        printf("  Block %d: %p\n", i, ptrs[i]);
    }
    
    // 获取初始统计
    es_mem_v2_stats_t stats_before;
    es_mem_v2_get_stats(&stats_before);
    printf("\nBefore merge - Active blocks: %u\n", stats_before.active_blocks);
    
    // 释放前4个块（应该触发多重向前合并）
    printf("\nFreeing blocks 0, 1, 2, 3 (should trigger multiple forward merge)...\n");
    for (int i = 0; i < 4; i++) {
        printf("  Freeing block %d: %p\n", i, ptrs[i]);
        es_mem_v2_free(ptrs[i]);
    }
    
    // 获取合并后统计
    es_mem_v2_stats_t stats_after;
    es_mem_v2_get_stats(&stats_after);
    printf("\nAfter merge - Active blocks: %u\n", stats_after.active_blocks);
    
    // 验证合并效果：应该只剩1个活跃块
    assert(stats_after.active_blocks == 1);
    printf("✓ Multiple forward merge successful\n");
    
    // 尝试分配一个大块，应该能够利用合并后的空间
    size_t large_size = 4 * block_size;  // 4个块的大小
    void *large_ptr = es_mem_v2_alloc(large_size);
    if (large_ptr) {
        printf("✓ Successfully allocated large block (%zu bytes) using merged space\n", large_size);
        es_mem_v2_free(large_ptr);
    } else {
        printf("✗ Failed to allocate large block - merge may not be working correctly\n");
    }
    
    // 清理
    es_mem_v2_free(ptrs[4]);
    printf("✓ Test completed\n");
}

static void test_backward_merge_multiple(void) {
    printf("\n=== Testing Multiple Backward Merge ===\n");
    
    // 分配5个连续的小块
    void *ptrs[5];
    size_t block_size = 100;
    
    printf("Allocating 5 consecutive blocks of %zu bytes each...\n", block_size);
    for (int i = 0; i < 5; i++) {
        ptrs[i] = es_mem_v2_alloc(block_size);
        assert(ptrs[i] != NULL);
        printf("  Block %d: %p\n", i, ptrs[i]);
    }
    
    // 获取初始统计
    es_mem_v2_stats_t stats_before;
    es_mem_v2_get_stats(&stats_before);
    printf("\nBefore merge - Active blocks: %u\n", stats_before.active_blocks);
    
    // 从后往前释放块（应该触发多重向后合并）
    printf("\nFreeing blocks 4, 3, 2, 1 (should trigger multiple backward merge)...\n");
    for (int i = 4; i >= 1; i--) {
        printf("  Freeing block %d: %p\n", i, ptrs[i]);
        es_mem_v2_free(ptrs[i]);
    }
    
    // 获取合并后统计
    es_mem_v2_stats_t stats_after;
    es_mem_v2_get_stats(&stats_after);
    printf("\nAfter merge - Active blocks: %u\n", stats_after.active_blocks);
    
    // 验证合并效果：应该只剩1个活跃块
    assert(stats_after.active_blocks == 1);
    printf("✓ Multiple backward merge successful\n");
    
    // 尝试分配一个大块
    size_t large_size = 4 * block_size;
    void *large_ptr = es_mem_v2_alloc(large_size);
    if (large_ptr) {
        printf("✓ Successfully allocated large block (%zu bytes) using merged space\n", large_size);
        es_mem_v2_free(large_ptr);
    } else {
        printf("✗ Failed to allocate large block - merge may not be working correctly\n");
    }
    
    // 清理
    es_mem_v2_free(ptrs[0]);
    printf("✓ Test completed\n");
}

static void test_bidirectional_merge(void) {
    printf("\n=== Testing Bidirectional Merge ===\n");
    
    // 分配7个连续的小块
    void *ptrs[7];
    size_t block_size = 80;
    
    printf("Allocating 7 consecutive blocks of %zu bytes each...\n", block_size);
    for (int i = 0; i < 7; i++) {
        ptrs[i] = es_mem_v2_alloc(block_size);
        assert(ptrs[i] != NULL);
        printf("  Block %d: %p\n", i, ptrs[i]);
    }
    
    // 先释放中间的块
    printf("\nFreeing middle block (3)...\n");
    es_mem_v2_free(ptrs[3]);
    
    // 释放前面的块（应该触发向前合并）
    printf("Freeing blocks 2, 1 (should merge forward with block 3)...\n");
    es_mem_v2_free(ptrs[2]);
    es_mem_v2_free(ptrs[1]);
    
    // 释放后面的块（应该触发向后合并）
    printf("Freeing blocks 4, 5 (should merge backward with existing merged block)...\n");
    es_mem_v2_free(ptrs[4]);
    es_mem_v2_free(ptrs[5]);
    
    // 获取统计
    es_mem_v2_stats_t stats;
    es_mem_v2_get_stats(&stats);
    printf("\nAfter bidirectional merge - Active blocks: %u\n", stats.active_blocks);
    
    // 应该剩下2个活跃块（ptrs[0]和ptrs[6]）
    assert(stats.active_blocks == 2);
    printf("✓ Bidirectional merge successful\n");
    
    // 尝试分配一个大块，应该能够利用中间合并的空间
    size_t large_size = 5 * block_size;  // 5个块的大小
    void *large_ptr = es_mem_v2_alloc(large_size);
    if (large_ptr) {
        printf("✓ Successfully allocated large block (%zu bytes) using merged space\n", large_size);
        es_mem_v2_free(large_ptr);
    } else {
        printf("✗ Failed to allocate large block - bidirectional merge may not be working correctly\n");
    }
    
    // 清理
    es_mem_v2_free(ptrs[0]);
    es_mem_v2_free(ptrs[6]);
    printf("✓ Test completed\n");
}

static void test_fragmentation_and_merge(void) {
    printf("\n=== Testing Fragmentation and Merge Recovery ===\n");
    
    // 分配10个块
    void *ptrs[10];
    size_t block_size = 64;
    
    printf("Allocating 10 blocks of %zu bytes each...\n", block_size);
    for (int i = 0; i < 10; i++) {
        ptrs[i] = es_mem_v2_alloc(block_size);
        assert(ptrs[i] != NULL);
    }
    
    // 创建碎片：释放奇数位置的块
    printf("\nCreating fragmentation by freeing odd-numbered blocks...\n");
    for (int i = 1; i < 10; i += 2) {
        es_mem_v2_free(ptrs[i]);
        ptrs[i] = NULL;
    }
    
    es_mem_v2_stats_t stats_fragmented;
    es_mem_v2_get_stats(&stats_fragmented);
    printf("After fragmentation - Active blocks: %u\n", stats_fragmented.active_blocks);
    
    // 释放偶数位置的块，应该触发大量合并
    printf("\nFreeing even-numbered blocks (should trigger extensive merging)...\n");
    for (int i = 0; i < 10; i += 2) {
        if (ptrs[i]) {
            es_mem_v2_free(ptrs[i]);
        }
    }
    
    es_mem_v2_stats_t stats_merged;
    es_mem_v2_get_stats(&stats_merged);
    printf("After merge - Active blocks: %u\n", stats_merged.active_blocks);
    
    // 应该没有活跃块了
    assert(stats_merged.active_blocks == 0);
    printf("✓ Fragmentation recovery through merging successful\n");
    
    // 尝试分配一个大块，应该能够使用整个合并后的空间
    size_t large_size = 10 * block_size;
    void *large_ptr = es_mem_v2_alloc(large_size);
    if (large_ptr) {
        printf("✓ Successfully allocated very large block (%zu bytes) after defragmentation\n", large_size);
        es_mem_v2_free(large_ptr);
    } else {
        printf("✗ Failed to allocate large block after defragmentation\n");
    }
    
    printf("✓ Test completed\n");
}

static void test_merge_performance(void) {
    printf("\n=== Testing Merge Performance ===\n");
    
    const int num_blocks = 50;
    void *ptrs[num_blocks];
    size_t block_size = 100;
    
    printf("Allocating %d blocks for performance test...\n", num_blocks);
    for (int i = 0; i < num_blocks; i++) {
        ptrs[i] = es_mem_v2_alloc(block_size);
        assert(ptrs[i] != NULL);
    }
    
    // 测试大量连续释放的性能
    printf("Freeing all blocks in sequence (testing merge performance)...\n");
    
    es_mem_v2_stats_t stats_before;
    es_mem_v2_get_stats(&stats_before);
    
    for (int i = 0; i < num_blocks; i++) {
        es_mem_v2_free(ptrs[i]);
    }
    
    es_mem_v2_stats_t stats_after;
    es_mem_v2_get_stats(&stats_after);
    
    printf("Before: %u active blocks, After: %u active blocks\n", 
           stats_before.active_blocks, stats_after.active_blocks);
    
    // 应该没有活跃块
    assert(stats_after.active_blocks == 0);
    printf("✓ Performance test completed - all blocks merged successfully\n");
    
    // 验证可以分配一个大块
    size_t total_size = num_blocks * block_size;
    void *large_ptr = es_mem_v2_alloc(total_size);
    if (large_ptr) {
        printf("✓ Successfully allocated single large block (%zu bytes) after merging %d blocks\n", 
               total_size, num_blocks);
        es_mem_v2_free(large_ptr);
    } else {
        printf("✗ Failed to allocate large block after merging\n");
    }
    
    printf("✓ Test completed\n");
}

int main(void) {
    printf("=== Memory Allocator V2 Merge Test Suite ===\n");
    
    // 初始化
    int ret = es_mem_v2_init();
    assert(ret == 0);
    printf("✓ Memory allocator initialized\n");
    
    // 运行合并测试
    test_forward_merge_multiple();
    test_backward_merge_multiple();
    test_bidirectional_merge();
    test_fragmentation_and_merge();
    test_merge_performance();
    
    // 最终检查
    printf("\n=== Final Status ===\n");
    es_mem_v2_dump_stats();
    
    int leaks = es_mem_v2_check_leaks();
    if (leaks == 0) {
        printf("✓ No memory leaks detected\n");
    } else {
        printf("⚠ %d memory leaks detected\n", leaks);
    }
    
    // 反初始化
    ret = es_mem_v2_deinit();
    assert(ret == 0);
    printf("✓ Memory allocator deinitialized\n");
    
    printf("\n=== All Merge Tests Passed! ===\n");
    return 0;
}
