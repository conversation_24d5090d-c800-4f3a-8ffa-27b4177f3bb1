/**
 * @file es_isotp.c
 * @brief ISO-TP (ISO 15765-2) Transport Layer Implementation
 *
 * This module provides ISO-TP transport layer functionality for CAN-based
 * diagnostic communication according to ISO 15765-2 standard.
 *
 * <AUTHOR> MCU Framework
 * @date 2025-01-22
 * @version 1.1.0
 */

#include "es_isotp.h"
#include "es_ringobj.h"

#define TAG "ISOTP"

/* ========================================================================== */
/*                            LOG CONTROL MACROS                             */
/* ========================================================================== */

/**
 * @brief ISO-TP debug log levels
 */
#ifndef ES_ISOTP_LOG_LEVEL
#define ES_ISOTP_LOG_LEVEL 1  // 0=None, 1=Error+Critical, 2=Info, 3=Debug, 4=Verbose
#endif

// Critical operations (always logged)
#define ISOTP_LOG_CRITICAL(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)

// Error conditions (level 1+)
#if ES_ISOTP_LOG_LEVEL >= 1
#define ISOTP_LOG_ERROR(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define ISOTP_LOG_ERROR(fmt, ...)
#endif

// Important operations (level 2+)
#if ES_ISOTP_LOG_LEVEL >= 2
#define ISOTP_LOG_INFO(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define ISOTP_LOG_INFO(fmt, ...)
#endif

// Debug information (level 3+)
#if ES_ISOTP_LOG_LEVEL >= 3
#define ISOTP_LOG_DEBUG(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define ISOTP_LOG_DEBUG(fmt, ...)
#endif

// Verbose frame-level logs (level 4+)
#if ES_ISOTP_LOG_LEVEL >= 4
#define ISOTP_LOG_VERBOSE(fmt, ...) ES_PRINTF_I(TAG, fmt, ##__VA_ARGS__)
#else
#define ISOTP_LOG_VERBOSE(fmt, ...)
#endif

/* ========================================================================== */
/*                            UTILITY FUNCTIONS                              */
/* ========================================================================== */

/**
 * @brief Get current timestamp in milliseconds
 */
static inline uint32_t get_timestamp_ms(void) {
    return es_os_get_tick_ms();
}

/**
 * @brief Check if timeout has occurred
 */
static inline bool is_timeout(uint32_t start_time_ms, uint32_t timeout_ms) {
    uint32_t current_time_ms = get_timestamp_ms();

    /* Handle counter wraparound situation */
    if (current_time_ms >= start_time_ms) {
        return (current_time_ms - start_time_ms) >= timeout_ms;
    } else {
        /* Current time is less than start time, indicating wraparound occurred */
        return ((UINT32_MAX - start_time_ms) + current_time_ms + 1) >= timeout_ms;
    }
}

/**
 * @brief Extract ISO-TP frame type from CAN data
 */
static inline es_isotp_frame_type_t get_frame_type(const uint8_t *data) {
    return (es_isotp_frame_type_t)((data[0] >> 4) & 0x0F);
}

/**
 * @brief Extract data length from single frame
 */
static inline uint8_t get_sf_length(const uint8_t *data) {
    return data[0] & 0x0F;
}

/**
 * @brief Extract total length from first frame
 */
static inline uint16_t get_ff_length(const uint8_t *data) {
    return ((uint16_t)(data[0] & 0x0F) << 8) | data[1];
}

/**
 * @brief Extract sequence number from consecutive frame
 */
static inline uint8_t get_cf_sequence(const uint8_t *data) {
    return data[0] & 0x0F;
}

/**
 * @brief Extract flow control status
 */
static inline es_isotp_fc_status_t get_fc_status(const uint8_t *data) {
    return (es_isotp_fc_status_t)(data[0] & 0x0F);
}

/* ========================================================================== */
/*                            MODULE FUNCTIONS                               */
/* ========================================================================== */

/**
 * @brief Initialize ISO-TP connection with external buffers
 */
es_isotp_error_t es_isotp_init(es_isotp_connection_t *isotp_conn, uint32_t tx_id, uint32_t rx_id,
                               uint8_t *tx_buffer, uint16_t tx_buffer_size,
                               uint8_t *rx_buffer, uint16_t rx_buffer_size) {
    if (!isotp_conn || !tx_buffer || !rx_buffer) {
        return ES_ISOTP_ERR_INVALID_PARAM;
    }

    if (tx_buffer_size == 0 || rx_buffer_size == 0) {
        return ES_ISOTP_ERR_INVALID_PARAM;
    }

    // Clear the entire structure
    memset(isotp_conn, 0, sizeof(es_isotp_connection_t));

    // Initialize ISO-TP connection
    isotp_conn->tx_id = tx_id;
    isotp_conn->rx_id = rx_id;
    isotp_conn->state = ES_ISOTP_STATE_IDLE;
    isotp_conn->timeout_ms = ES_ISOTP_DEFAULT_TIMEOUT_MS;
    isotp_conn->block_size = 8;  // Default block size
    isotp_conn->separation_time = 0;  // Default separation time

    // Set buffer pointers and sizes
    isotp_conn->tx_buffer = tx_buffer;
    isotp_conn->tx_buffer_size = tx_buffer_size;
    isotp_conn->rx_buffer = rx_buffer;
    isotp_conn->rx_buffer_size = rx_buffer_size;

    // Initialize ring buffer for incoming CAN frames
    if (es_ringobj_init(&isotp_conn->rx_ring, isotp_conn->rx_ring_buffer,
                        ES_ISOTP_RING_BUFFER_SIZE, sizeof(es_can_msg_t)) != 0) {
        ISOTP_LOG_ERROR("Failed to initialize ring buffer");
        return ES_ISOTP_ERR_NO_MEMORY;
    }

    ISOTP_LOG_INFO("ISO-TP connection initialized (TX: 0x%03X, RX: 0x%03X, TX_BUF: %d, RX_BUF: %d)",
                   tx_id, rx_id, tx_buffer_size, rx_buffer_size);

    return ES_ISOTP_OK;
}

/**
 * @brief Process incoming CAN message
 */
es_isotp_error_t es_isotp_process_can_message(es_isotp_connection_t *conn, const es_can_msg_t *msg) {
    if (!conn || !msg) {
        return ES_ISOTP_ERR_INVALID_PARAM;
    }

    // Check if message is for this connection
    if (msg->id != conn->rx_id) {
        return ES_ISOTP_OK;  // Not for us, ignore
    }

    // Add message to ring buffer
    if (es_ringobj_put(&conn->rx_ring, msg) == 0) {
        ISOTP_LOG_ERROR("Ring buffer full, dropping CAN message");
        return ES_ISOTP_ERR_OVERFLOW;
    }

    return ES_ISOTP_OK;
}

/* ========================================================================== */
/*                            ISO-TP HELPER FUNCTIONS                        */
/* ========================================================================== */

/* Forward declarations for ISO-TP functions */
static es_async_t es_isotp_send_single_frame(es_coro_t *coro, es_isotp_connection_t *conn);
static es_async_t es_isotp_send_first_frame(es_coro_t *coro, es_isotp_connection_t *conn);
static es_async_t es_isotp_send_consecutive_frame(es_coro_t *coro, es_isotp_connection_t *conn);
static es_async_t es_isotp_send_flow_control(es_coro_t *coro, es_isotp_connection_t *conn,
                                             es_isotp_fc_status_t fc_status,
                                             uint8_t block_size, uint8_t separation_time);

/**
 * @brief Check for timeout and handle error
 */
static inline bool check_isotp_timeout(es_isotp_connection_t *conn, const char *operation) {
    if (is_timeout(conn->timeout_start_ms, conn->timeout_ms)) {
        ISOTP_LOG_ERROR("Timeout during %s", operation);
        conn->state = ES_ISOTP_STATE_ERROR;
        return true;
    }
    return false;
}

/**
 * @brief Convert separation time value to milliseconds according to ISO-TP standard
 * @param st_value Raw separation time value from FC frame
 * @return Separation time in milliseconds
 */
static uint32_t convert_separation_time_to_ms(uint8_t st_value) {
    if (st_value <= 0x7F) {
        // 0x00-0x7F: 0-127 milliseconds
        return st_value;
    } else if (st_value >= 0xF1 && st_value <= 0xF9) {
        // 0xF1-0xF9: 100-900 microseconds, convert to milliseconds (round up)
        uint32_t microseconds = (st_value - 0xF0) * 100;
        return (microseconds + 999) / 1000;  // Round up to next millisecond
    } else {
        // 0x80-0xF0: Reserved, treat as 0
        ISOTP_LOG_DEBUG("Reserved separation time value: 0x%02X, using 0ms", st_value);
        return 0;
    }
}

/**
 * @brief Process received flow control frame
 * @param conn ISO-TP connection
 * @param rx_msg Received CAN message
 * @return Flow control status, or -1 if not a valid FC frame
 */
static int process_flow_control_frame(es_isotp_connection_t *conn, const es_can_msg_t *rx_msg) {
    if (get_frame_type(rx_msg->data) != ES_ISOTP_FRAME_FC) {
        return -1;  // Not a flow control frame
    }

    es_isotp_fc_status_t fc_status = get_fc_status(rx_msg->data);

    if (fc_status == ES_ISOTP_FC_CTS) {
        // Continue to send
        conn->block_size = rx_msg->data[1];

        // Parse separation time according to ISO-TP standard
        uint8_t st_raw = rx_msg->data[2];
        uint32_t st_ms = convert_separation_time_to_ms(st_raw);
        conn->separation_time = (st_ms > 255) ? 255 : (uint8_t)st_ms;  // Clamp to uint8_t range

        conn->frames_sent_in_block = 0;
        conn->state = ES_ISOTP_STATE_SENDING;

        ISOTP_LOG_DEBUG("FC CTS: bs=%d, st_raw=0x%02X, st_ms=%d",
                      conn->block_size, st_raw, conn->separation_time);

        return ES_ISOTP_FC_CTS;
    } else if (fc_status == ES_ISOTP_FC_WAIT) {
        // Wait for next FC
        conn->timeout_start_ms = get_timestamp_ms();
        return ES_ISOTP_FC_WAIT;
    } else {
        // Overflow or abort
        ISOTP_LOG_ERROR("FC overflow/abort received");
        conn->state = ES_ISOTP_STATE_ERROR;
        return ES_ISOTP_FC_OVFLW;
    }
}

/**
 * @brief Wait for flow control frame (coroutine helper)
 * @param coro Coroutine context
 * @param conn ISO-TP connection
 * @param operation_name Operation name for logging
 * @return Coroutine return value
 */
static es_async_t wait_for_flow_control(es_coro_t *coro, es_isotp_connection_t *conn,
                                        const char *operation_name) {
    es_co_begin(coro);

    while (conn->state == ES_ISOTP_STATE_WAIT_FC) {
        // Check timeout
        if (check_isotp_timeout(conn, operation_name)) {
            es_co_err;
        }

        // Check for incoming FC frame
        if (es_ringobj_get(&conn->rx_ring, &conn->shared_can_msg) > 0) {
            int fc_result = process_flow_control_frame(conn, &conn->shared_can_msg);

            if (fc_result == ES_ISOTP_FC_CTS) {
                break;  // Continue to send
            } else if (fc_result == ES_ISOTP_FC_WAIT) {
                continue;  // Wait for next FC
            } else if (fc_result == ES_ISOTP_FC_OVFLW) {
                es_co_err;  // Overflow/abort
            }
            // If fc_result == -1, it's not an FC frame, continue waiting
        }

        es_co_yield;
    }

    es_co_end;
}

/**
 * @brief Send consecutive frames with flow control (coroutine helper)
 * @param coro Coroutine context
 * @param conn ISO-TP connection
 * @return Coroutine return value
 */
static es_async_t send_consecutive_frames(es_coro_t *coro, es_isotp_connection_t *conn) {
    es_co_begin(coro);

    while (conn->send_offset < conn->tx_length && conn->state == ES_ISOTP_STATE_SENDING) {
        // Check if we need to wait for next FC (block size control)
        if (conn->block_size > 0 && conn->frames_sent_in_block >= conn->block_size) {
            conn->state = ES_ISOTP_STATE_WAIT_FC;
            conn->timeout_start_ms = get_timestamp_ms();

            // Wait for next FC
            es_co_await_ex(err, wait_for_flow_control, conn, "next FC in block");
        }

        // Send consecutive frame
        es_co_await_ex(err, es_isotp_send_consecutive_frame, conn);

        // Use dlc to get the actual data length sent (dlc - 1 = data length)
        conn->frames_sent_in_block++;

        // Apply separation time if specified
        if (conn->separation_time > 0) {
            es_co_sleep(conn->separation_time);
        }
    }

    es_co_eee(
        ISOTP_LOG_ERROR("Failed to send consecutive frames");
    );
}

/**
 * @brief Wait for CAN message with timeout (coroutine helper)
 * @param coro Coroutine context
 * @param conn ISO-TP connection
 * @param rx_msg Pointer to store received message
 * @param operation_name Operation name for logging
 * @return Coroutine return value
 */
static es_async_t wait_for_can_message(es_coro_t *coro, es_isotp_connection_t *conn,
                                       es_can_msg_t *rx_msg, const char *operation_name) {
    es_co_begin(coro);

    while (1) {
        // Check timeout
        if (check_isotp_timeout(conn, operation_name)) {
            es_co_err;
        }

        // Check for incoming message
        if (es_ringobj_get(&conn->rx_ring, rx_msg) > 0) {
            break;  // Message received
        }

        es_co_yield;
    }

    es_co_end;
}

/**
 * @brief Process single frame reception
 * @param conn ISO-TP connection
 * @param rx_msg Received CAN message
 * @return 0 on success, -1 on error
 */
static int process_single_frame(es_isotp_connection_t *conn, const es_can_msg_t *rx_msg) {
    uint8_t frame_data_len = get_sf_length(rx_msg->data);

    if (frame_data_len == 0 || frame_data_len > 7 || frame_data_len > conn->rx_buffer_size) {
        ISOTP_LOG_ERROR("Invalid SF length: %d", frame_data_len);
        return -1;
    }

    memcpy(conn->rx_buffer, &rx_msg->data[1], frame_data_len);
    conn->rx_length = frame_data_len;
    conn->state = ES_ISOTP_STATE_IDLE;

    ISOTP_LOG_VERBOSE("Received SF: len=%d", frame_data_len);
    return 0;
}

/**
 * @brief Process first frame reception and send flow control
 * @param coro Coroutine context
 * @param conn ISO-TP connection
 * @param rx_msg Received CAN message
 * @return Coroutine return value
 */
static es_async_t process_first_frame(es_coro_t *coro, es_isotp_connection_t *conn, const es_can_msg_t *rx_msg) {
    es_co_begin(coro);

    conn->recv_total_length = get_ff_length(rx_msg->data);

    // Check if total length is valid and fits in receive buffer
    if (conn->recv_total_length <= 7) {
        ISOTP_LOG_ERROR("Invalid FF length (too small): %d", conn->recv_total_length);
        // Send FC overflow
        es_co_await_ex(err, es_isotp_send_flow_control, conn,
                      ES_ISOTP_FC_OVFLW, 0, 0);
        es_co_err;
    }

    if (conn->recv_total_length > conn->rx_buffer_size) {
        ISOTP_LOG_ERROR("FF length exceeds rx buffer size: %d > %d",
                      conn->recv_total_length, conn->rx_buffer_size);
        // Send FC overflow
        es_co_await_ex(err, es_isotp_send_flow_control, conn,
                      ES_ISOTP_FC_OVFLW, 0, 0);
        es_co_err;
    }

    // Copy first 6 bytes to connection's rx_buffer
    memcpy(conn->rx_buffer, &rx_msg->data[2], 6);
    conn->rx_length = 6;
    conn->recv_expected_sequence = 1;

    // Send FC continue
    es_co_await_ex(err, es_isotp_send_flow_control, conn,
                  ES_ISOTP_FC_CTS, conn->block_size, conn->separation_time);

    ISOTP_LOG_VERBOSE("Received FF: total_len=%d", conn->recv_total_length);

    es_co_eee(
        ISOTP_LOG_ERROR("Failed to process first frame");
    );
}

/**
 * @brief Process consecutive frame reception
 * @param conn ISO-TP connection
 * @param rx_msg Received CAN message
 * @return 0 on success, -1 on error
 */
static int process_consecutive_frame(es_isotp_connection_t *conn, const es_can_msg_t *rx_msg) {
    uint8_t sequence = get_cf_sequence(rx_msg->data);

    if (sequence != conn->recv_expected_sequence) {
        ISOTP_LOG_ERROR("Wrong CF sequence: expected=%d, got=%d",
                      conn->recv_expected_sequence, sequence);
        return -1;
    }

    // Calculate data length for this frame
    uint16_t remaining = conn->recv_total_length - conn->rx_length;
    uint8_t frame_data_len = (remaining > 7) ? 7 : remaining;

    // Copy data to connection's rx_buffer
    memcpy(&conn->rx_buffer[conn->rx_length], &rx_msg->data[1], frame_data_len);
    conn->rx_length += frame_data_len;

    // Update expected sequence
    conn->recv_expected_sequence = (conn->recv_expected_sequence + 1) & 0x0F;
    if (conn->recv_expected_sequence == 0) {
        conn->recv_expected_sequence = 1;  // Skip 0
    }

    ISOTP_LOG_VERBOSE("Received CF: seq=%d, len=%d, total=%d/%d",
              sequence, frame_data_len, conn->rx_length, conn->recv_total_length);
    return 0;
}

/**
 * @brief Receive consecutive frames until complete (coroutine helper)
 * @param coro Coroutine context
 * @param conn ISO-TP connection
 * @return Coroutine return value
 */
static es_async_t receive_consecutive_frames(es_coro_t *coro, es_isotp_connection_t *conn) {
    es_co_begin(coro);

    conn->recv_frames_in_block = 0;

    // Continue receiving consecutive frames
    while (conn->rx_length < conn->recv_total_length) {
        conn->timeout_start_ms = get_timestamp_ms();

        // Wait for consecutive frame
        es_co_await_ex(err, wait_for_can_message, conn, &conn->shared_can_msg, "CF");

        uint8_t frame_type = get_frame_type(conn->shared_can_msg.data);

        if (frame_type == ES_ISOTP_FRAME_CF) {
            if (process_consecutive_frame(conn, &conn->shared_can_msg) != 0) {
                es_co_err;
            }

            conn->recv_frames_in_block++;

            // Check if we need to send another flow control frame
            // This happens when we've received a full block and there's still more data to receive
            if (conn->block_size > 0 && conn->recv_frames_in_block >= conn->block_size &&
                conn->rx_length < conn->recv_total_length) {

                // Send FC continue for next block
                es_co_await_ex(err, es_isotp_send_flow_control, conn,
                              ES_ISOTP_FC_CTS, conn->block_size, conn->separation_time);

                conn->recv_frames_in_block = 0;  // Reset block counter

                ISOTP_LOG_DEBUG("Sent FC for next block, received=%d/%d",
                          conn->rx_length, conn->recv_total_length);
            }

        } else {
            ISOTP_LOG_ERROR("Expected CF but got frame type %d", frame_type);
            es_co_err;
        }
    }

    ISOTP_LOG_DEBUG("Multi-frame reception complete: %d bytes", conn->recv_total_length);

    es_co_eee(
        ISOTP_LOG_ERROR("Failed to receive consecutive frames");
    );
}

/* ========================================================================== */
/*                            ISO-TP FRAME FUNCTIONS                         */
/* ========================================================================== */

/**
 * @brief Send single frame (coroutine)
 */
static es_async_t es_isotp_send_single_frame(es_coro_t *coro, es_isotp_connection_t *conn) {
    es_co_begin(coro);

    // Prepare single frame
    conn->shared_can_msg.data[0] = (ES_ISOTP_FRAME_SF << 4) | conn->tx_length;
    memcpy(&conn->shared_can_msg.data[1], conn->tx_buffer, conn->tx_length);

    conn->shared_can_msg.dlc = conn->tx_length + 1;

    // Send via CAN
    es_co_await_ex(err, es_can_coro_write, conn->tx_id, conn->shared_can_msg.data, conn->shared_can_msg.dlc);

    ISOTP_LOG_VERBOSE("Sent SF: len=%d", conn->tx_length);

    es_co_eee(
        ISOTP_LOG_ERROR("Failed to send SF");
        conn->state = ES_ISOTP_STATE_ERROR;
    );
}

/**
 * @brief Send first frame (coroutine)
 */
static es_async_t es_isotp_send_first_frame(es_coro_t *coro, es_isotp_connection_t *conn) {
    es_co_begin(coro);

    // Prepare first frame
    conn->shared_can_msg.data[0] = (ES_ISOTP_FRAME_FF << 4) | ((conn->tx_length >> 8) & 0x0F);
    conn->shared_can_msg.data[1] = conn->tx_length & 0xFF;
    memcpy(&conn->shared_can_msg.data[2], conn->tx_buffer, 6);  // First 6 bytes of data

    conn->shared_can_msg.dlc = 8;

    // Update connection state
    conn->state = ES_ISOTP_STATE_WAIT_FC;
    conn->send_offset = 6;  // First 6 bytes sent in FF
    conn->sequence_number = 1;
    conn->timeout_start_ms = get_timestamp_ms();

    // Send via CAN
    es_co_await_ex(err, es_can_coro_write, conn->tx_id, conn->shared_can_msg.data, conn->shared_can_msg.dlc);

    ISOTP_LOG_VERBOSE("Sent FF: total_len=%d", conn->tx_length);

    es_co_eee(
        ISOTP_LOG_ERROR("Failed to send FF");
        conn->state = ES_ISOTP_STATE_ERROR;
    );
}

/**
 * @brief Send consecutive frame (coroutine)
 */
static es_async_t es_isotp_send_consecutive_frame(es_coro_t *coro, es_isotp_connection_t *conn) {
    es_co_begin(coro);

    // Calculate remaining bytes and frame data length
    uint16_t remaining_bytes = conn->tx_length - conn->send_offset;
    uint8_t frame_data_len = (remaining_bytes > 7) ? 7 : remaining_bytes;

    // Prepare consecutive frame
    conn->shared_can_msg.data[0] = (ES_ISOTP_FRAME_CF << 4) | (conn->sequence_number & 0x0F);
    memcpy(&conn->shared_can_msg.data[1], &conn->tx_buffer[conn->send_offset], frame_data_len);
    conn->shared_can_msg.dlc = frame_data_len + 1;

    // Send via CAN
    es_co_await_ex(err, es_can_coro_write, conn->tx_id, conn->shared_can_msg.data, conn->shared_can_msg.dlc);

    // Update sequence number (wrap around at 15)
    conn->sequence_number = (conn->sequence_number + 1) & 0x0F;
    if (conn->sequence_number == 0) {
        conn->sequence_number = 1;  // Skip 0, start from 1
    }

    ISOTP_LOG_VERBOSE("Sent CF: seq=%d, len=%d", conn->sequence_number - 1, conn->shared_can_msg.dlc - 1);

    conn->send_offset += (conn->shared_can_msg.dlc - 1);

    es_co_eee(
        ISOTP_LOG_ERROR("Failed to send CF");
        conn->state = ES_ISOTP_STATE_ERROR;
    );
}

/**
 * @brief Send flow control frame (coroutine)
 */
static es_async_t es_isotp_send_flow_control(es_coro_t *coro, es_isotp_connection_t *conn,
                                             es_isotp_fc_status_t fc_status,
                                             uint8_t block_size, uint8_t separation_time) {
    es_co_begin(coro);

    // Prepare flow control frame
    conn->shared_can_msg.data[0] = (ES_ISOTP_FRAME_FC << 4) | (fc_status & 0x0F);
    conn->shared_can_msg.data[1] = block_size;
    conn->shared_can_msg.data[2] = separation_time;

    conn->shared_can_msg.dlc = 3;

    // Send via CAN
    es_co_await_ex(err, es_can_coro_write, conn->tx_id, conn->shared_can_msg.data, conn->shared_can_msg.dlc);

    ISOTP_LOG_VERBOSE("Sent FC: status=%d, bs=%d, st=%d", fc_status, block_size, separation_time);

    es_co_eee(
        ISOTP_LOG_ERROR("Failed to send FC");
        conn->state = ES_ISOTP_STATE_ERROR;
    );
}

/* ========================================================================== */
/*                            PUBLIC ISO-TP FUNCTIONS                        */
/* ========================================================================== */

/**
 * @brief Send data via ISO-TP (coroutine)
 */
es_async_t es_isotp_send(es_coro_t *coro, es_isotp_connection_t *conn) {
    es_co_begin(coro);

    if (conn->tx_length == 0) {
        es_co_err;
    }

    if (conn->state != ES_ISOTP_STATE_IDLE) {
        es_co_err;  // Connection busy
    }

    // Validate data length
    if (conn->tx_length > conn->tx_buffer_size) {
        es_co_err;
    }

    conn->state = ES_ISOTP_STATE_SENDING;
    conn->timeout_start_ms = get_timestamp_ms();

    // Single frame or multi-frame?
    if (conn->tx_length <= 7) {
        // Single frame transmission
        es_co_await_ex(err, es_isotp_send_single_frame, conn);
        conn->state = ES_ISOTP_STATE_IDLE;
    } else {
        // Multi-frame transmission
        // Send first frame
        es_co_await_ex(err, es_isotp_send_first_frame, conn);

        // Wait for initial flow control
        es_co_await_ex(err, wait_for_flow_control, conn, "initial FC");

        // Send consecutive frames with flow control handling
        es_co_await_ex(err, send_consecutive_frames, conn);

        conn->state = ES_ISOTP_STATE_IDLE;
    }

    ISOTP_LOG_DEBUG("ISO-TP send complete: %d bytes", conn->tx_length);

    es_co_eee(
        ISOTP_LOG_ERROR("ISO-TP send failed");
        conn->state = ES_ISOTP_STATE_ERROR;
    );
}

/**
 * @brief Receive data via ISO-TP (coroutine)
 */
es_async_t es_isotp_recv(es_coro_t *coro, es_isotp_connection_t *conn) {
    es_co_begin(coro);

    if (conn->state != ES_ISOTP_STATE_IDLE) {
        es_co_err;  // Connection busy
    }

    conn->state = ES_ISOTP_STATE_RECEIVING;
    conn->timeout_start_ms = get_timestamp_ms();
    conn->rx_length = 0;

    // Wait for first frame (SF or FF)
    es_co_await_ex(err, wait_for_can_message, conn, &conn->shared_can_msg, "first frame");

    uint8_t frame_type = get_frame_type(conn->shared_can_msg.data);

    if (frame_type == ES_ISOTP_FRAME_SF) {
        // Single frame reception
        if (process_single_frame(conn, &conn->shared_can_msg) != 0) {
            es_co_err;
        }

    } else if (frame_type == ES_ISOTP_FRAME_FF) {
        // Multi-frame reception
        // Process first frame and send flow control
        es_co_await_ex(err, process_first_frame, conn, &conn->shared_can_msg);

        // Receive all consecutive frames
        es_co_await_ex(err, receive_consecutive_frames, conn);

        conn->rx_length = conn->recv_total_length;
        conn->state = ES_ISOTP_STATE_IDLE;

    } else {
        ISOTP_LOG_ERROR("Unexpected frame type: %d", frame_type);
        es_co_err;
    }

    ISOTP_LOG_DEBUG("ISO-TP receive complete: %d bytes", conn->rx_length);

    es_co_eee(
        ISOTP_LOG_ERROR("ISO-TP receive failed");
        conn->state = ES_ISOTP_STATE_ERROR;
    );
}
