/**
 * @file es_crc.c
 * @brief CRC calculation implementation
 */
#include "es_crc.h"

/**
 * @brief Calculate CRC32 checksum value
 * 
 * @param crc Initial CRC value, usually DEF_CRC_INIT_VALUE
 * @param buf Data buffer pointer
 * @param size Data length
 * @return uint32_t Calculated CRC32 value
 */
uint32_t es_crc32(uint32_t crc, const uint8_t *buf, size_t size)
{
    while (size--) {
        crc ^= *buf++;
        for (int k = 0; k < 8; k++) {
            crc = crc & 1 ? (crc >> 1) ^ 0xEDB88320 : crc >> 1;
        }
    }
    return crc;
} 
