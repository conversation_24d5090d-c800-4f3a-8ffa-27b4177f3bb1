/**
 * @file uds_security_access_example.c
 * @brief Example demonstrating the new UDS security access interface with callback
 * <AUTHOR> MCU Team
 * @date 2024
 */

#include "es_uds.h"
#include "es_printf.h"

static const char *TAG = "UDS_SEC_EXAMPLE";

/**
 * @brief Example security key generation callback
 * This is a simple demonstration - in real applications, use proper cryptographic algorithms
 */
static uint16_t security_key_generator(uint8_t level, const uint8_t *seed, uint16_t seed_length,
                                      uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
    // Validate parameters
    if (!seed || seed_length == 0 || !key_buffer || max_key_length < 4) {
        ES_PRINTF_I(TAG, "Invalid parameters for key generation");
        return 0;
    }
    
    ES_PRINTF_I(TAG, "Generating key for level %d with seed length %d", level, seed_length);
    
    // Simple algorithm for demonstration (DO NOT use in production)
    // In real implementation, use proper cryptographic functions like AES, DES, etc.
    const uint8_t secret_key[] = {0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x90};
    uint16_t key_length = (seed_length < 4) ? seed_length : 4;
    
    if (key_length > max_key_length) {
        key_length = max_key_length;
    }
    
    // XOR seed with secret key
    for (uint16_t i = 0; i < key_length; i++) {
        key_buffer[i] = seed[i] ^ secret_key[i % sizeof(secret_key)];
    }
    
    ES_PRINTF_I(TAG, "Generated %d-byte key", key_length);
    return key_length;
}

/**
 * @brief Advanced security key generation callback with algorithm selection
 */
static uint16_t advanced_key_generator(uint8_t level, const uint8_t *seed, uint16_t seed_length,
                                      uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
    // user_data can contain algorithm configuration
    uint32_t *algorithm_id = (uint32_t *)user_data;
    
    if (!seed || seed_length == 0 || !key_buffer || max_key_length < 4) {
        return 0;
    }
    
    ES_PRINTF_I(TAG, "Advanced key generation: level=%d, algorithm=%d", level, 
                algorithm_id ? *algorithm_id : 0);
    
    switch (algorithm_id ? *algorithm_id : 0) {
        case 1: // Algorithm 1: Simple XOR
            {
                const uint8_t pattern[] = {0x5A, 0xA5, 0x3C, 0xC3};
                uint16_t key_len = (seed_length > 4) ? 4 : seed_length;
                if (key_len > max_key_length) key_len = max_key_length;
                
                for (uint16_t i = 0; i < key_len; i++) {
                    key_buffer[i] = seed[i] ^ pattern[i];
                }
                return key_len;
            }
            
        case 2: // Algorithm 2: Byte rotation
            {
                uint16_t key_len = (seed_length > 8) ? 8 : seed_length;
                if (key_len > max_key_length) key_len = max_key_length;
                
                for (uint16_t i = 0; i < key_len; i++) {
                    key_buffer[i] = ((seed[i] << 1) | (seed[i] >> 7)) ^ 0xFF;
                }
                return key_len;
            }
            
        default: // Default algorithm
            {
                uint16_t key_len = (seed_length > 4) ? 4 : seed_length;
                if (key_len > max_key_length) key_len = max_key_length;
                
                for (uint16_t i = 0; i < key_len; i++) {
                    key_buffer[i] = seed[i] ^ 0xAA;
                }
                return key_len;
            }
    }
}

/**
 * @brief Example of basic security access
 */
static es_async_t basic_security_access_example(es_coro_t *coro, es_uds_connection_t *conn) {
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Basic Security Access Example ===");
    
    // Perform security access with simple key generator
    es_co_await_ex(err, es_uds_security_access, conn, 1, security_key_generator, NULL);
    
    ES_PRINTF_I(TAG, "Security access level 1 completed successfully!");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Security access failed");
    );
}

/**
 * @brief Example of advanced security access with algorithm selection
 */
static es_async_t advanced_security_access_example(es_coro_t *coro, es_uds_connection_t *conn) {
    static uint32_t algorithm_id = 2;  // Use algorithm 2
    
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Advanced Security Access Example ===");
    
    // Perform security access with advanced key generator (level 3 is odd)
    es_co_await_ex(err, es_uds_security_access, conn, 3, advanced_key_generator, &algorithm_id);
    
    ES_PRINTF_I(TAG, "Advanced security access level 3 completed successfully!");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Advanced security access failed");
    );
}

/**
 * @brief Complete security access workflow example
 */
static es_async_t complete_security_workflow_example(es_coro_t *coro) {
    static es_uds_connection_t uds_conn;
    
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "=== Complete Security Access Workflow ===");
    
    // Initialize UDS connection
    es_uds_error_t result = es_uds_connection_init(&uds_conn, 0x7E0, 0x7E8);
    if (result != ES_UDS_OK) {
        ES_PRINTF_I(TAG, "Failed to initialize UDS connection: %d", result);
        es_co_err;
    }
    
    // Step 1: Enter programming session
    es_co_await_ex(err, es_uds_diagnostic_session_control, &uds_conn, ES_UDS_SESSION_PROGRAMMING);
    ES_PRINTF_I(TAG, "Entered programming session");
    
    // Step 2: Perform security access
    es_co_await_ex(err, es_uds_security_access, &uds_conn, 1, security_key_generator, NULL);
    ES_PRINTF_I(TAG, "Security access completed");
    
    // Step 3: Now we can perform protected operations
    // ... (add your protected operations here)
    
    ES_PRINTF_I(TAG, "Complete security workflow finished successfully!");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Security workflow failed");
    );
}

/**
 * @brief Main example function
 */
es_async_t uds_security_access_example_main(es_coro_t *coro) {
    static es_uds_connection_t uds_conn;
    
    es_co_begin(coro);
    
    // Initialize UDS connection
    es_uds_error_t result = es_uds_connection_init(&uds_conn, 0x7E0, 0x7E8);
    if (result != ES_UDS_OK) {
        ES_PRINTF_I(TAG, "Failed to initialize UDS connection: %d", result);
        es_co_err;
    }
    
    // Run basic example
    es_co_await_ex(err, basic_security_access_example, &uds_conn);
    
    // Run advanced example
    es_co_await_ex(err, advanced_security_access_example, &uds_conn);
    
    // Run complete workflow
    es_co_await_ex(err, complete_security_workflow_example);
    
    ES_PRINTF_I(TAG, "All security access examples completed!");
    
    es_co_eee(
        ES_PRINTF_I(TAG, "Security access examples failed");
    );
}
