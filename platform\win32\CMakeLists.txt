cmake_minimum_required(VERSION 3.15)

# 禁用编译器检测（避免生成 CMakeCCompilerId.c）
set(CMAKE_DISABLE_SOURCE_CHANGES OFF)
set(CMAKE_DISABLE_IN_SOURCE_BUILD OFF)

# 设置工具链文件
set(CMAKE_TOOLCHAIN_FILE ${CMAKE_CURRENT_SOURCE_DIR}/vs2022.cmake)

# 项目名称
project(win32_app C CXX)

# 禁用警告4819 (关于包含非ANSI字符的文件编码警告)
if(MSVC)
    add_compile_options(/wd4819)
endif()

# 根据不同的构建类型设置不同的标志
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(STATUS "build type: Debug")
    add_compile_definitions(
        _DEBUG
    )
elseif(CMAKE_BUILD_TYPE STREQUAL "Release")
    message(STATUS "build type: Release")
    add_compile_definitions(
        NDEBUG
    )
endif()

# 项目版本
set(VERSION_MAJOR 1)
set(VERSION_MINOR 0)
set(VERSION_PATCH 0)

# 添加全局编译定义
add_compile_definitions(
    WIN32
    UNICODE
    _UNICODE
    VERSION_MAJOR=${VERSION_MAJOR}
    VERSION_MINOR=${VERSION_MINOR}
    VERSION_PATCH=${VERSION_PATCH}
)

# 包含目录 - 更新为新的目录结构
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/../../applications
    .
)

# 源文件 - 更新为新的目录结构
file(GLOB_RECURSE SOURCES 
    "${CMAKE_SOURCE_DIR}/../../applications/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.c"
)

# 排除CMakeCCompilerId.c文件和测试文件
list(FILTER SOURCES EXCLUDE REGEX ".*CMakeCCompilerId\\.c$")
list(FILTER SOURCES EXCLUDE REGEX ".*test_.*\\.c$")

# 资源文件
file(GLOB_RECURSE RESOURCES
    "res/*.ico"
    "res/*.bmp"
    "res/*.png"
)

# 生成可执行文件
add_executable(${PROJECT_NAME} WIN32 ${SOURCES} ${RESOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    gdi32.lib
    user32.lib
    shell32.lib
    comdlg32.lib
    ole32.lib
)

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)


# 输出构建信息
message(STATUS "project name: ${PROJECT_NAME}")
message(STATUS "project version: ${VERSION_MAJOR}.${VERSION_MINOR}.${VERSION_PATCH}")
message(STATUS "build directory: ${CMAKE_BINARY_DIR}")
message(STATUS "source directory: ${CMAKE_SOURCE_DIR}")
message(STATUS "compiler: ${CMAKE_C_COMPILER_ID} ${CMAKE_C_COMPILER_VERSION}") 