/**
 * @file es.h
 * @brief ES system core header file
 * @version 1.0.0
 * @date 2023-12-01
 * 
 * @copyright Copyright (c) 2023
 * 
 * This file contains core definitions and interface declarations of ES system.
 * 所有ES模块都应包含此头文件以获取基础类型和宏定义。
 */

#ifndef ES_H
#define ES_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif



/* Define event types */
#define EVENT_TYPE_BUTTON       1    /* Button event */
#define EVENT_TYPE_SENSOR       2    /* Sensor event */
#define EVENT_TYPE_TIMER        3    /* Timer event */
#define EVENT_TYPE_SYSTEM       4    /* System event */
#define EVENT_TYPE_EMERGENCY    5    /* Emergency event */
#define EVENT_TYPE_ALARM        6    /* Alarm event */
#define EVENT_TYPE_CAN_MSG      7    /* CAN message event */
#define EVENT_TYPE_ISOTP_RX     8    /* ISO-TP receive event */

int es_init(void);

#ifdef __cplusplus
}
#endif

#endif /* ES_H */
