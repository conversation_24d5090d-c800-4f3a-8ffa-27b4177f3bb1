/**
 * @file es_ringbuffer.h
 * @brief Generic ring buffer module
 * @date 2024/10/1
 */

#ifndef __ES_RINGBUFFER_H__
#define __ES_RINGBUFFER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/**
 * @brief Ring buffer structure definition
 * @note Key fields use volatile to ensure interrupt safety
 */
typedef struct {
    uint8_t *buffer;                 /**< Buffer pointer */
    uint16_t size;                   /**< Total buffer size, must be power of 2, max 4K */
    volatile uint16_t read_index;    /**< Read position index (volatile ensures interrupt safety) */
    volatile uint16_t write_index;   /**< Write position index (volatile ensures interrupt safety) */
    volatile uint16_t data_size;     /**< Current data size (volatile ensures interrupt safety) */
    uint16_t mask : 15;              /**< Mask value (size - 1), 15 bits sufficient for 4K-1=4095 */
    volatile uint16_t is_full : 1;   /**< Buffer full flag, 1 bit (volatile ensures interrupt safety) */
} es_ringbuffer_t;

/**
 * @brief Initialize ring buffer
 * @param rb Ring buffer pointer
 * @param buffer Buffer memory pointer
 * @param size Buffer size (must be power of 2)
 * @return 0 on success, -1 on failure
 */
int es_ringbuffer_init(es_ringbuffer_t *rb, uint8_t *buffer, uint32_t size);

/**
 * @brief Reset ring buffer state
 * @param rb Ring buffer pointer
 * @return 0 on success, -1 on failure
 */
int es_ringbuffer_reset(es_ringbuffer_t *rb);

/**
 * @brief Write data to ring buffer
 * @param rb Ring buffer pointer
 * @param data Data to write
 * @param len Data length
 * @return Actual bytes written
 */
uint32_t es_ringbuffer_write(es_ringbuffer_t *rb, const uint8_t *data, uint32_t len);

/**
 * @brief Force write data to ring buffer, overwrite old data if space is insufficient
 * @param rb Ring buffer pointer
 * @param data Data to write
 * @param len Data length
 * @return Actual bytes written
 */
uint32_t es_ringbuffer_write_force(es_ringbuffer_t *rb, const uint8_t *data, uint32_t len);

/**
 * @brief Read data from ring buffer
 * @param rb Ring buffer pointer
 * @param data Data receive buffer
 * @param len Maximum length to read
 * @return Actual bytes read
 */
uint32_t es_ringbuffer_read(es_ringbuffer_t *rb, uint8_t *data, uint32_t len);

/**
 * @brief Peek at ring buffer data without removing it
 * @param rb Ring buffer pointer
 * @param data Data receive buffer
 * @param len Maximum length to peek
 * @return Actual bytes peeked
 */
uint32_t es_ringbuffer_peek(es_ringbuffer_t *rb, uint8_t *data, uint32_t len);

/**
 * @brief Get available data amount in ring buffer
 * @param rb Ring buffer pointer
 * @return Available data amount
 */
uint32_t es_ringbuffer_available(es_ringbuffer_t *rb);

/**
 * @brief Get free space size in ring buffer
 * @param rb Ring buffer pointer
 * @return Free space size
 */
uint32_t es_ringbuffer_free_space(es_ringbuffer_t *rb);

/**
 * @brief Check if ring buffer is empty
 * @param rb Ring buffer pointer
 * @return true if empty, false if not empty
 */
bool es_ringbuffer_is_empty(es_ringbuffer_t *rb);

/**
 * @brief Check if ring buffer is full
 * @param rb Ring buffer pointer
 * @return true if full, false if not full
 */
bool es_ringbuffer_is_full(es_ringbuffer_t *rb);

/**
 * @brief Skip specified length of data in ring buffer
 * @param rb Ring buffer pointer
 * @param len Length to skip
 * @return Actual bytes skipped
 */
uint32_t es_ringbuffer_skip(es_ringbuffer_t *rb, uint32_t len);



#ifdef __cplusplus
}
#endif

#endif /* __ES_RINGBUFFER_H__ */ 
