# AT+MEM 指令使用说明

## 概述

AT+MEM 指令用于内存压力测试，提供了完整的内存管理测试接口。

## 指令格式

### 查询内存状态
```
AT+MEM?
```
**响应示例：**
```
+MEM: total=10240,used=128,free=10112,blocks=2
OK
```

### 内存压力测试
```
AT+MEM=STRESS,<count>,<size>
```
**参数：**
- `count`: 测试循环次数
- `size`: 每次分配的内存大小（字节）

**功能说明：**
- 执行指定次数的内存分配和释放操作
- 每次分配后写入测试数据，然后立即释放
- 每100次操作检查一次内存完整性
- 统计成功和失败次数

**响应示例：**
```
AT+MEM=STRESS,1000,256
+MEM: STRESS,count=1000,size=256,success=1000,fail=0,total_bytes=256000
OK
```

### 显示内存统计信息
```
AT+MEM=STATS
```
**功能：** 在日志中输出详细的内存统计信息

### 显示内存池状态
```
AT+MEM=DUMP
```
**功能：** 在日志中输出内存池的详细状态

### 内存完整性检查
```
AT+MEM=CHECK
```
**响应示例：**
```
+MEM: CHECK,OK
OK
```
或
```
+MEM: CHECK,FAILED
ERROR
```

### 内存碎片整理
```
AT+MEM=DEFRAG
```
**响应示例：**
```
+MEM: DEFRAG,merged=2
OK
```

### 内存泄漏检查
```
AT+MEM=LEAKS
```
**响应示例：**
```
+MEM: LEAKS,count=0
OK
```

## 测试示例

### 基本内存压力测试
```
AT+MEM?                           // 查看初始状态
AT+MEM=STRESS,100,64             // 小块内存压力测试：100次，每次64字节
AT+MEM=STATS                     // 查看详细统计
AT+MEM=CHECK                     // 检查内存完整性
```

### 大块内存压力测试
```
AT+MEM=STRESS,50,1024            // 大块内存压力测试：50次，每次1KB
AT+MEM=DUMP                      // 查看内存池状态
AT+MEM=DEFRAG                    // 进行碎片整理
AT+MEM=LEAKS                     // 检查内存泄漏
```

### 极限压力测试
```
AT+MEM=STRESS,1000,256           // 中等大小高频测试
AT+MEM=STRESS,10,2048            // 最大块测试（接近2KB限制）
AT+MEM=CHECK                     // 最终完整性检查
```

## 注意事项

1. **压力测试互斥**: 同一时间只能运行一个压力测试
2. **内存大小限制**: 单次分配不能超过 ES_MEM_V2_MAX_ALLOC (2KB)
3. **总内存限制**: 总内存池大小为 ES_MEM_V2_POOL_SIZE (10KB)
4. **完整性检查**: 每100次操作自动进行内存完整性检查
5. **日志输出**: STATS 和 DUMP 操作会在日志中输出详细信息，不会通过AT响应返回
6. **测试数据**: 压力测试会写入测试数据以验证内存读写正确性

## 错误码

- `ERROR:1` - 参数缺失
- `ERROR:2` - 无效命令
- `ERROR:3` - 无效参数
- `ERROR:4` - 操作失败
- `ERROR:5` - 不支持的操作

## 实现特性

- 使用协程实现，支持异步处理
- 自动跟踪测试分配的内存指针
- 提供完整的内存管理功能测试
- 支持内存完整性验证和泄漏检测
- 兼容现有的AT指令框架
