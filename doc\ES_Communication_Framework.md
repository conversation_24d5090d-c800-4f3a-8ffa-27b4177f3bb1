# ES 通信协议框架文档

## 1. 概述

ES 通信协议框架是 ES MCU Framework 的核心通信组件，提供了统一的协议处理架构和多种通信协议支持。该框架采用模块化设计，支持协议的动态注册和扩展，为车联网、物联网等应用场景提供了完整的通信解决方案。

### 1.1 主要特性

- **统一协议框架**: 提供统一的协议处理接口和数据结构
- **多协议支持**: 支持 TSP、BLE、串口、CAN 等多种通信协议
- **自动帧解析**: 支持协议帧的自动编码和解码
- **ISO-TP 支持**: 完整的 ISO-TP 协议实现，支持车载诊断
- **UDS 诊断**: 统一诊断服务实现，支持标准 UDS 协议
- **协程集成**: 与协程调度系统深度集成，支持异步通信

### 1.2 核心组件

- **es_frame.h/c**: 通信协议框架核心
- **es_isotp.h/c**: ISO-TP 协议实现
- **es_uds.h/c**: UDS 诊断服务实现
- **es_frame_v2_fs.h/c**: 协议框架 V2 文件系统支持

## 2. 协议框架架构

### 2.1 协议栈结构

```
应用层协议
├── TSP 协议 (车联网通信)
├── BLE 协议 (蓝牙低功耗)
├── UDS 协议 (统一诊断服务)
└── 自定义协议

传输层协议
├── ISO-TP (车载传输协议)
├── TCP/IP (网络传输)
└── 串口传输

物理层接口
├── CAN 总线
├── UART 串口
├── 蓝牙接口
└── 网络接口
```

### 2.2 帧结构定义

#### 2.2.1 TSP 协议帧

```c
#define TSP_FRAME_HEAD \
    X(head, 0x00, 0x00, M(x16, start) M(x16, len) M(s_arr(15), imei) \
      M(x8, type) M(x16, seq) M(x16, effect))

typedef struct {
    uint16_t start;     // 起始标志
    uint16_t len;       // 数据长度
    char imei[15];      // 设备IMEI
    uint8_t type;       // 消息类型
    uint16_t seq;       // 序列号
    uint16_t effect;    // 生效标志
} tsp_frame_head_t;
```

#### 2.2.2 BLE 协议帧

```c
#define BLE_FRAME_HEAD \
    X(ble_head, 0x00, 0x00, M(x16, start) M(x8, len) M(x8, crypto) M(x8, seq))

typedef struct {
    uint16_t start;     // 起始标志
    uint8_t len;        // 数据长度
    uint8_t crypto;     // 加密标志
    uint8_t seq;        // 序列号
} ble_frame_head_t;
```

### 2.3 帧信息结构

```c
typedef struct frame_info_t {
    const char *desc;           // 帧描述
    uint16_t size;              // 帧大小
    uint16_t meta;              // 帧元信息
    uint16_t cmd;               // 帧命令
    union {
        int (*encode)(unsigned char *buf, int size, int pos, const void *o);
        int (*decode)(const unsigned char *buf, int size, int pos, void *o);
    } codec;                    // 编解码函数
} frame_info_t;
```

## 3. ISO-TP 协议实现

### 3.1 ISO-TP 概述

ISO-TP (ISO 14229-2) 是车载网络传输协议，用于在 CAN 总线上传输长度超过 8 字节的数据。

### 3.2 帧类型

```c
typedef enum {
    ES_ISOTP_FRAME_TYPE_SINGLE = 0,     // 单帧
    ES_ISOTP_FRAME_TYPE_FIRST = 1,      // 首帧
    ES_ISOTP_FRAME_TYPE_CONSECUTIVE = 2, // 连续帧
    ES_ISOTP_FRAME_TYPE_FLOW_CONTROL = 3 // 流控帧
} es_isotp_frame_type_t;
```

### 3.3 连接结构

```c
typedef struct {
    uint32_t tx_id;             // 发送CAN ID
    uint32_t rx_id;             // 接收CAN ID
    uint8_t tx_data[8];         // 发送数据缓冲区
    uint8_t rx_data[4095];      // 接收数据缓冲区
    uint16_t rx_len;            // 接收数据长度
    uint16_t total_length;      // 总数据长度
    uint8_t sequence_number;    // 序列号
    uint8_t block_size;         // 块大小
    uint8_t separation_time;    // 分离时间
    es_isotp_state_t state;     // 连接状态
    uint32_t timeout;           // 超时时间
} es_isotp_connection_t;
```

### 3.4 主要API

```c
// 初始化ISO-TP连接
int es_isotp_init_connection(es_isotp_connection_t *conn, 
                            uint32_t tx_id, uint32_t rx_id);

// 发送数据
es_async_t es_isotp_send(es_coro_t *coro, es_isotp_connection_t *conn, 
                        const uint8_t *data, uint16_t length);

// 接收数据
es_async_t es_isotp_receive(es_coro_t *coro, es_isotp_connection_t *conn, 
                           uint8_t *data, uint16_t *length);

// 处理接收到的CAN帧
int es_isotp_handle_can_frame(es_isotp_connection_t *conn, 
                             uint32_t can_id, const uint8_t *data, uint8_t length);
```

### 3.5 使用示例

```c
// ISO-TP 发送示例
es_async_t isotp_send_task(es_coro_t *coro, void *ctx) {
    static es_isotp_connection_t conn;
    static uint8_t send_data[] = {0x22, 0xF1, 0x90}; // UDS读取数据
    
    es_co_begin;
    
    // 初始化连接
    es_isotp_init_connection(&conn, 0x7E0, 0x7E8);
    
    // 发送数据
    es_co_await(es_isotp_send(coro, &conn, send_data, sizeof(send_data)));
    
    ES_PRINTF_I("ISOTP", "Data sent successfully");
    
    es_co_end;
}
```

## 4. UDS 诊断服务

### 4.1 UDS 概述

UDS (Unified Diagnostic Services, ISO 14229-1) 是统一诊断服务协议，广泛应用于汽车电子控制单元的诊断。

### 4.2 服务标识符

```c
typedef enum {
    UDS_SID_DIAGNOSTIC_SESSION_CONTROL = 0x10,      // 诊断会话控制
    UDS_SID_ECU_RESET = 0x11,                       // ECU复位
    UDS_SID_SECURITY_ACCESS = 0x27,                 // 安全访问
    UDS_SID_COMMUNICATION_CONTROL = 0x28,           // 通信控制
    UDS_SID_TESTER_PRESENT = 0x3E,                  // 测试器在线
    UDS_SID_READ_DATA_BY_IDENTIFIER = 0x22,         // 按标识符读取数据
    UDS_SID_WRITE_DATA_BY_IDENTIFIER = 0x2E,        // 按标识符写入数据
    UDS_SID_ROUTINE_CONTROL = 0x31,                 // 例程控制
    UDS_SID_REQUEST_DOWNLOAD = 0x34,                // 请求下载
    UDS_SID_TRANSFER_DATA = 0x36,                   // 传输数据
    UDS_SID_REQUEST_TRANSFER_EXIT = 0x37,           // 请求传输退出
} uds_service_id_t;
```

### 4.3 UDS 客户端结构

```c
typedef struct {
    es_isotp_connection_t isotp_conn;   // ISO-TP连接
    uint8_t current_session;            // 当前会话类型
    uint8_t security_level;             // 安全级别
    uint32_t p2_timeout;                // P2超时时间
    uint32_t p2_star_timeout;           // P2*超时时间
    uds_state_t state;                  // UDS状态
} es_uds_client_t;
```

### 4.4 主要API

```c
// 初始化UDS客户端
int es_uds_client_init(es_uds_client_t *client, uint32_t tx_id, uint32_t rx_id);

// 诊断会话控制
es_async_t es_uds_diagnostic_session_control(es_coro_t *coro, 
                                            es_uds_client_t *client, uint8_t session_type);

// 安全访问
es_async_t es_uds_security_access(es_coro_t *coro, es_uds_client_t *client, 
                                 uint8_t level, const uint8_t *key, uint8_t key_len);

// 读取数据
es_async_t es_uds_read_data_by_identifier(es_coro_t *coro, es_uds_client_t *client, 
                                         uint16_t did, uint8_t *data, uint16_t *length);

// 写入数据
es_async_t es_uds_write_data_by_identifier(es_coro_t *coro, es_uds_client_t *client, 
                                          uint16_t did, const uint8_t *data, uint16_t length);

// 测试器在线
es_async_t es_uds_tester_present(es_coro_t *coro, es_uds_client_t *client);
```

### 4.5 使用示例

```c
// UDS 诊断示例
es_async_t uds_diagnostic_task(es_coro_t *coro, void *ctx) {
    static es_uds_client_t client;
    static uint8_t read_data[256];
    static uint16_t data_length;
    
    es_co_begin;
    
    // 初始化UDS客户端
    es_uds_client_init(&client, 0x7E0, 0x7E8);
    
    // 进入扩展诊断会话
    es_co_await(es_uds_diagnostic_session_control(coro, &client, 0x03));
    
    // 读取VIN码 (DID 0xF190)
    es_co_await(es_uds_read_data_by_identifier(coro, &client, 0xF190, 
                                              read_data, &data_length));
    
    if (data_length > 0) {
        ES_PRINTF_I("UDS", "VIN: %.*s", data_length, read_data);
    }
    
    // 保持测试器在线
    while (1) {
        es_co_await(es_uds_tester_present(coro, &client));
        
        // 等待2秒
        uint32_t start_time = es_get_timestamp();
        es_co_wait(es_get_timestamp() - start_time >= 2000);
    }
    
    es_co_end;
}
```

## 5. 协议注册和扩展

### 5.1 协议注册

```c
// 协议处理函数类型
typedef int (*protocol_handler_t)(const uint8_t *data, uint16_t length, 
                                 uint8_t *response, uint16_t *response_length);

// 注册协议处理器
int es_frame_register_protocol(uint8_t protocol_id, protocol_handler_t handler);

// 注销协议处理器
int es_frame_unregister_protocol(uint8_t protocol_id);
```

### 5.2 自定义协议示例

```c
// 自定义协议处理函数
int custom_protocol_handler(const uint8_t *data, uint16_t length, 
                           uint8_t *response, uint16_t *response_length) {
    // 解析协议数据
    if (length < 4) {
        return -1; // 数据长度不足
    }
    
    uint8_t cmd = data[0];
    uint16_t param = (data[1] << 8) | data[2];
    uint8_t checksum = data[3];
    
    // 校验和检查
    uint8_t calc_checksum = cmd + (param >> 8) + (param & 0xFF);
    if (calc_checksum != checksum) {
        return -2; // 校验和错误
    }
    
    // 处理命令
    switch (cmd) {
        case 0x01: // 读取状态
            response[0] = 0x81; // 响应码
            response[1] = get_device_status();
            *response_length = 2;
            break;
            
        case 0x02: // 设置参数
            set_device_parameter(param);
            response[0] = 0x82; // 响应码
            *response_length = 1;
            break;
            
        default:
            return -3; // 未知命令
    }
    
    return 0; // 成功
}

// 注册自定义协议
void register_custom_protocol(void) {
    es_frame_register_protocol(0x10, custom_protocol_handler);
}
```

## 6. 最佳实践

### 6.1 协议设计原则

1. **简洁性**: 协议设计应简洁明了，避免冗余字段
2. **可扩展性**: 预留扩展字段，支持协议版本升级
3. **可靠性**: 包含校验机制，确保数据传输可靠
4. **效率性**: 优化协议开销，提高传输效率

### 6.2 错误处理

1. **超时处理**: 设置合理的超时时间和重试机制
2. **错误码**: 定义完整的错误码体系
3. **日志记录**: 记录详细的通信日志便于调试
4. **异常恢复**: 实现协议状态的异常恢复机制

### 6.3 性能优化

1. **缓冲管理**: 合理管理发送和接收缓冲区
2. **并发处理**: 支持多个协议连接的并发处理
3. **内存优化**: 减少内存拷贝和动态分配
4. **协程调度**: 利用协程实现高效的异步通信

## 7. 故障排除

### 7.1 常见问题

1. **连接失败**: 检查CAN ID配置和总线状态
2. **数据丢失**: 检查流控参数和缓冲区大小
3. **超时错误**: 调整P2/P2*超时参数
4. **协议错误**: 检查帧格式和序列号

### 7.2 调试工具

1. **协议分析器**: 使用CAN分析仪监控总线通信
2. **日志分析**: 分析详细的通信日志
3. **状态监控**: 监控协议连接状态和统计信息
4. **测试工具**: 使用标准诊断工具进行测试

这个通信协议框架为 ES MCU Framework 提供了完整的通信解决方案，支持多种协议和应用场景，是构建复杂通信系统的重要基础。
