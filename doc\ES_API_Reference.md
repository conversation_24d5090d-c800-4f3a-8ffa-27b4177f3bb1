# ES MCU Framework API 参考手册

## 1. 概述

本文档提供了 ES MCU Framework 的完整 API 参考，包括所有核心模块的函数接口、数据结构和使用说明。开发者可以通过本文档快速查找和使用框架提供的各种功能。

### 1.1 API 分类

- **系统核心**: 系统初始化和基础服务
- **协程调度**: 协程和任务调度相关API
- **配置管理**: 参数配置和存储API
- **日志系统**: 日志记录和查询API
- **通信协议**: 协议处理和通信API
- **存储系统**: Flash和文件系统API
- **加密安全**: 加密算法和安全API
- **工具函数**: 实用工具和辅助API

### 1.2 命名规范

- **函数命名**: `es_module_function_name`
- **类型命名**: `es_module_type_t`
- **宏定义**: `ES_MODULE_MACRO_NAME`
- **常量定义**: `ES_MODULE_CONSTANT`

## 2. 系统核心 API

### 2.1 系统初始化

```c
/**
 * @brief 初始化ES系统
 * @return int 0表示成功，非0表示失败
 */
int es_init(void);

/**
 * @brief 获取系统版本信息
 * @return const char* 版本字符串
 */
const char *es_get_version(void);

/**
 * @brief 获取系统构建信息
 * @param build_info 构建信息结构体指针
 * @return int 0表示成功
 */
int es_get_build_info(es_build_info_t *build_info);
```

### 2.2 系统状态

```c
/**
 * @brief 获取系统运行时间
 * @return uint32_t 运行时间(毫秒)
 */
uint32_t es_get_uptime(void);

/**
 * @brief 获取系统时间戳
 * @return uint32_t 时间戳(毫秒)
 */
uint32_t es_get_timestamp(void);

/**
 * @brief 系统软复位
 */
void es_system_reset(void);
```

## 3. 协程调度 API

### 3.1 协程基础

```c
/**
 * @brief 协程函数类型定义
 */
typedef es_async_t (*es_coro_func_t)(es_coro_t *coro, void *ctx);

/**
 * @brief 分配子协程
 * @param parent 父协程指针
 * @return uint16_t 子协程ID，0表示失败
 */
uint16_t es_coro_alloc(es_coro_t *parent);

/**
 * @brief 释放子协程
 * @param parent 父协程指针
 */
void es_coro_free(es_coro_t *parent);

/**
 * @brief 获取协程指针
 * @param parent 父协程指针
 * @return es_coro_t* 协程指针
 */
es_coro_t* es_coro_get(es_coro_t *parent);
```

### 3.2 调度器管理

```c
/**
 * @brief 初始化协程调度器
 * @param scheduler 调度器实例
 */
void es_scheduler_init(es_scheduler_t *scheduler);

/**
 * @brief 获取默认调度器
 * @return es_scheduler_t* 默认调度器指针
 */
es_scheduler_t *es_scheduler_get_default(void);

/**
 * @brief 运行调度器
 * @param scheduler 调度器实例
 */
void es_scheduler_run(es_scheduler_t *scheduler);
```

### 3.3 任务管理

```c
/**
 * @brief 初始化协程任务
 * @param task 任务实例
 * @param name 任务名称
 * @param func 协程函数
 * @param ctx 上下文数据
 */
void es_scheduler_task_init(es_coro_task_t *task, const char *name, 
                           es_coro_func_t func, void *ctx);

/**
 * @brief 添加协程任务
 * @param scheduler 调度器实例
 * @param task 任务实例
 * @return int 0表示成功，-1表示失败
 */
int es_scheduler_task_add(es_scheduler_t *scheduler, es_coro_task_t *task);

/**
 * @brief 移除协程任务
 * @param scheduler 调度器实例
 * @param task 任务实例
 * @return int 0表示成功，-1表示失败
 */
int es_scheduler_task_remove(const es_scheduler_t *scheduler, es_coro_task_t *task);
```

### 3.4 定时器管理

```c
/**
 * @brief 初始化定时器
 * @param timer 定时器实例
 * @param period 定时周期(毫秒)
 * @param callback 回调函数
 * @param ctx 回调上下文
 * @param one_shot 是否单次触发
 */
void es_scheduler_timer_init(es_timer_t *timer, uint32_t period, 
                            es_timer_callback_t callback, void *ctx, bool one_shot);

/**
 * @brief 添加定时器
 * @param scheduler 调度器实例
 * @param timer 定时器实例
 * @return int 0表示成功，-1表示失败
 */
int es_scheduler_timer_add(es_scheduler_t *scheduler, es_timer_t *timer);

/**
 * @brief 移除定时器
 * @param scheduler 调度器实例
 * @param timer 定时器实例
 * @return int 0表示成功，-1表示失败
 */
int es_scheduler_timer_remove(es_scheduler_t *scheduler, es_timer_t *timer);

/**
 * @brief 启用/禁用定时器
 * @param timer 定时器实例
 * @param enable 是否启用
 */
void es_scheduler_timer_enable(es_timer_t *timer, bool enable);
```

## 4. 配置管理 API

### 4.1 系统管理

```c
/**
 * @brief 初始化配置系统
 * @return int 错误码
 */
int es_cfg_v2_init(void);

/**
 * @brief 保存所有参数到Flash
 * @return int 错误码
 */
int es_cfg_v2_save(void);

/**
 * @brief 从Flash加载参数
 * @return int 错误码
 */
int es_cfg_v2_load(void);

/**
 * @brief 检查系统是否已初始化
 * @return bool true表示已初始化
 */
bool es_cfg_v2_is_initialized(void);
```

### 4.2 参数操作

```c
/**
 * @brief 设置参数值
 * @param key 参数键值
 * @param value 参数值指针
 * @param value_len 参数值长度
 * @return int 错误码
 */
int es_cfg_v2_set(es_cfg_v2_key_t key, const void *value, uint16_t value_len);

/**
 * @brief 获取参数值
 * @param key 参数键值
 * @param value 输出缓冲区
 * @param value_len 缓冲区长度
 * @return int 实际读取长度，负数表示错误
 */
int es_cfg_v2_get(es_cfg_v2_key_t key, void *value, uint16_t value_len);

/**
 * @brief 获取参数长度
 * @param key 参数键值
 * @return int 参数长度，负数表示错误
 */
int es_cfg_v2_get_len(es_cfg_v2_key_t key);

/**
 * @brief 检查参数是否存在
 * @param key 参数键值
 * @return bool true表示存在
 */
bool es_cfg_v2_exists(es_cfg_v2_key_t key);
```

### 4.3 批量操作

```c
/**
 * @brief 按类别重置参数
 * @param category 参数类别
 * @param save 是否立即保存
 * @return int 错误码
 */
int es_cfg_v2_reset_by_category(es_cfg_v2_category_t category, bool save);

/**
 * @brief 重置所有参数
 * @param save 是否立即保存
 * @return int 错误码
 */
int es_cfg_v2_reset_all(bool save);

/**
 * @brief 获取参数数量
 * @return int 参数数量
 */
int es_cfg_v2_get_param_count(void);
```

## 5. 日志系统 API

### 5.1 日志记录

```c
/**
 * @brief 写入日志
 * @param level 日志级别
 * @param tag 日志标签
 * @param to_flash 是否写入Flash
 * @param fmt 格式字符串
 * @param ... 可变参数
 */
void es_log_write(es_log_level_t level, const char *tag, bool to_flash, 
                  const char *fmt, ...);

/**
 * @brief 打印十六进制数据
 * @param tag 日志标签
 * @param width 每行显示字节数
 * @param buf 数据缓冲区
 * @param size 数据大小
 */
void es_log_hex_dump(const char *tag, int width, const void *buf, int size);
```

### 5.2 日志控制

```c
/**
 * @brief 日志系统初始化
 * @return int 0表示成功
 */
int es_log_init(void);

/**
 * @brief 设置日志级别
 * @param level 日志级别
 */
void es_log_set_level(es_log_level_t level);

/**
 * @brief 获取日志级别
 * @return es_log_level_t 当前日志级别
 */
es_log_level_t es_log_get_level(void);

/**
 * @brief 启用/禁用日志
 * @param enable 是否启用
 */
void es_log_enable(bool enable);
```

### 5.3 日志输出器

```c
/**
 * @brief 添加日志输出器
 * @param sink 输出器实例
 * @return int 0表示成功
 */
int es_log_add_sink(es_log_sink_t *sink);

/**
 * @brief 移除日志输出器
 * @param sink 输出器实例
 * @return int 0表示成功
 */
int es_log_remove_sink(es_log_sink_t *sink);
```

## 6. 通信协议 API

### 6.1 ISO-TP 协议

```c
/**
 * @brief 初始化ISO-TP连接
 * @param conn 连接实例
 * @param tx_id 发送CAN ID
 * @param rx_id 接收CAN ID
 * @return int 0表示成功
 */
int es_isotp_init_connection(es_isotp_connection_t *conn, 
                            uint32_t tx_id, uint32_t rx_id);

/**
 * @brief 发送数据
 * @param coro 协程上下文
 * @param conn 连接实例
 * @param data 数据缓冲区
 * @param length 数据长度
 * @return es_async_t 协程状态
 */
es_async_t es_isotp_send(es_coro_t *coro, es_isotp_connection_t *conn, 
                        const uint8_t *data, uint16_t length);

/**
 * @brief 接收数据
 * @param coro 协程上下文
 * @param conn 连接实例
 * @param data 数据缓冲区
 * @param length 数据长度指针
 * @return es_async_t 协程状态
 */
es_async_t es_isotp_receive(es_coro_t *coro, es_isotp_connection_t *conn, 
                           uint8_t *data, uint16_t *length);
```

### 6.2 UDS 诊断

```c
/**
 * @brief 初始化UDS客户端
 * @param client 客户端实例
 * @param tx_id 发送CAN ID
 * @param rx_id 接收CAN ID
 * @return int 0表示成功
 */
int es_uds_client_init(es_uds_client_t *client, uint32_t tx_id, uint32_t rx_id);

/**
 * @brief 诊断会话控制
 * @param coro 协程上下文
 * @param client 客户端实例
 * @param session_type 会话类型
 * @return es_async_t 协程状态
 */
es_async_t es_uds_diagnostic_session_control(es_coro_t *coro, 
                                            es_uds_client_t *client, uint8_t session_type);

/**
 * @brief 按标识符读取数据
 * @param coro 协程上下文
 * @param client 客户端实例
 * @param did 数据标识符
 * @param data 数据缓冲区
 * @param length 数据长度指针
 * @return es_async_t 协程状态
 */
es_async_t es_uds_read_data_by_identifier(es_coro_t *coro, es_uds_client_t *client, 
                                         uint16_t did, uint8_t *data, uint16_t *length);
```

## 7. 存储系统 API

### 7.1 Flash 操作

```c
/**
 * @brief Flash初始化
 * @return int 0表示成功
 */
int es_flash_init(void);

/**
 * @brief 擦除扇区
 * @param sector_addr 扇区地址
 * @return int 0表示成功
 */
int es_flash_erase_sector(uint32_t sector_addr);

/**
 * @brief 写入数据
 * @param addr 地址
 * @param data 数据指针
 * @param len 数据长度
 * @return int 0表示成功
 */
int es_flash_write(uint32_t addr, const void *data, uint32_t len);

/**
 * @brief 读取数据
 * @param addr 地址
 * @param data 数据缓冲区
 * @param len 数据长度
 * @return int 0表示成功
 */
int es_flash_read(uint32_t addr, void *data, uint32_t len);
```

### 7.2 环形文件系统

```c
/**
 * @brief 初始化文件系统
 * @param fs 文件系统实例
 * @param partition 分区信息
 * @return int 0表示成功
 */
int ringfs_init(struct ringfs *fs, const struct ringfs_flash_partition *partition);

/**
 * @brief 格式化文件系统
 * @param fs 文件系统实例
 * @return int 0表示成功
 */
int ringfs_format(struct ringfs *fs);

/**
 * @brief 追加数据
 * @param fs 文件系统实例
 * @param file_id 文件ID
 * @param data 数据指针
 * @param len 数据长度
 * @return int 0表示成功
 */
int ringfs_append(struct ringfs *fs, uint16_t file_id, const void *data, uint16_t len);
```

## 8. 加密安全 API

### 8.1 AES 加密

```c
/**
 * @brief 初始化AES上下文
 * @param ctx AES上下文
 * @param key 密钥
 * @param key_bits 密钥长度(位)
 * @return int 0表示成功
 */
int es_aes_init(es_aes_context_t *ctx, const uint8_t *key, uint32_t key_bits);

/**
 * @brief AES ECB模式加密
 * @param ctx AES上下文
 * @param input 输入数据
 * @param output 输出数据
 * @return int 0表示成功
 */
int es_aes_encrypt_ecb(es_aes_context_t *ctx, const uint8_t *input, uint8_t *output);

/**
 * @brief AES CBC模式加密
 * @param ctx AES上下文
 * @param iv 初始向量
 * @param input 输入数据
 * @param output 输出数据
 * @param length 数据长度
 * @return int 0表示成功
 */
int es_aes_encrypt_cbc(es_aes_context_t *ctx, const uint8_t *iv, 
                       const uint8_t *input, uint8_t *output, uint32_t length);
```

### 8.2 MD5 哈希

```c
/**
 * @brief 初始化MD5上下文
 * @param ctx MD5上下文
 */
void es_md5_init(es_md5_context_t *ctx);

/**
 * @brief 更新MD5计算
 * @param ctx MD5上下文
 * @param input 输入数据
 * @param length 数据长度
 */
void es_md5_update(es_md5_context_t *ctx, const uint8_t *input, uint32_t length);

/**
 * @brief 完成MD5计算
 * @param ctx MD5上下文
 * @param digest 输出摘要
 */
void es_md5_final(es_md5_context_t *ctx, uint8_t digest[16]);

/**
 * @brief 一次性MD5计算
 * @param input 输入数据
 * @param length 数据长度
 * @param digest 输出摘要
 */
void es_md5(const uint8_t *input, uint32_t length, uint8_t digest[16]);
```

### 8.3 CRC 校验

```c
/**
 * @brief 计算CRC16
 * @param data 数据指针
 * @param length 数据长度
 * @return uint16_t CRC16值
 */
uint16_t es_crc16(const uint8_t *data, uint32_t length);

/**
 * @brief 计算CRC32
 * @param data 数据指针
 * @param length 数据长度
 * @return uint32_t CRC32值
 */
uint32_t es_crc32(const uint8_t *data, uint32_t length);

/**
 * @brief 更新CRC32
 * @param crc 当前CRC值
 * @param data 数据指针
 * @param length 数据长度
 * @return uint32_t 新的CRC值
 */
uint32_t es_crc32_update(uint32_t crc, const uint8_t *data, uint32_t length);
```

## 9. 工具函数 API

### 9.1 内存管理

```c
/**
 * @brief 内存管理初始化
 * @return int 0表示成功
 */
int es_mem_init(void);

/**
 * @brief 分配内存
 * @param size 内存大小
 * @return void* 内存指针，NULL表示失败
 */
void *es_malloc(size_t size);

/**
 * @brief 释放内存
 * @param ptr 内存指针
 */
void es_free(void *ptr);

/**
 * @brief 获取内存统计
 * @param stats 统计信息结构体
 */
void es_mem_get_stats(es_mem_stats_t *stats);
```

### 9.2 字符串工具

```c
/**
 * @brief 字符串转十六进制
 * @param str 字符串
 * @param hex 十六进制缓冲区
 * @param max_len 最大长度
 * @return int 转换长度
 */
int es_str_to_hex(const char *str, uint8_t *hex, int max_len);

/**
 * @brief 十六进制转字符串
 * @param hex 十六进制数据
 * @param len 数据长度
 * @param str 字符串缓冲区
 * @return int 转换长度
 */
int es_hex_to_str(const uint8_t *hex, int len, char *str);

/**
 * @brief 去除字符串首尾空格
 * @param str 字符串
 * @return char* 处理后的字符串
 */
char *es_str_trim(char *str);
```

## 10. 错误码定义

### 10.1 通用错误码

```c
#define ES_OK                    0      // 成功
#define ES_ERR_INVALID_PARAM    -1      // 无效参数
#define ES_ERR_NO_MEMORY        -2      // 内存不足
#define ES_ERR_TIMEOUT          -3      // 超时
#define ES_ERR_NOT_SUPPORTED    -4      // 不支持
#define ES_ERR_BUSY             -5      // 忙碌
#define ES_ERR_NOT_FOUND        -6      // 未找到
#define ES_ERR_ALREADY_EXISTS   -7      // 已存在
#define ES_ERR_IO               -8      // IO错误
#define ES_ERR_CHECKSUM         -9      // 校验错误
#define ES_ERR_NOT_INITIALIZED  -10     // 未初始化
```

### 10.2 模块特定错误码

各模块都定义了特定的错误码，详见各模块的头文件。

## 11. 使用注意事项

### 11.1 线程安全

- 大部分API不是线程安全的，需要在单线程环境或加锁保护下使用
- 协程调度器是协作式的，不需要考虑抢占式多线程问题
- 中断服务程序中应避免调用可能阻塞的API

### 11.2 内存管理

- 动态分配的内存必须及时释放
- 避免在中断服务程序中进行内存分配
- 使用内存池可以提高性能和减少碎片

### 11.3 错误处理

- 所有API调用都应检查返回值
- 使用统一的错误码进行错误处理
- 关键错误应记录到日志系统

这个API参考手册为开发者提供了完整的接口文档，便于快速查找和使用框架功能。
