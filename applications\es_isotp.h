/**
 * @file es_isotp.h
 * @brief ISO-TP (ISO 15765-2) Transport Layer Implementation
 *
 * This module provides ISO-TP transport layer functionality for CAN-based
 * diagnostic communication according to ISO 15765-2 standard.
 *
 * Features:
 * - Single Frame (SF) and Multi-Frame (FF/CF) transmission
 * - Flow Control (FC) frame handling
 * - Proper sequence number management
 * - Block size and separation time support
 * - Non-blocking coroutine-based async operations
 * - Ring buffer for incoming CAN message queuing
 *
 * <AUTHOR> MCU Framework
 * @date 2025-01-22
 * @version 1.1.0
 */

#ifndef ES_ISOTP_H
#define ES_ISOTP_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "es.h"
#include "es_coro.h"
#include "es_drv_can.h"
#include "es_ringobj.h"
#include "es_log.h"
#include "es_mem.h"
#include "es_drv_os.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================== */
/*                              CONFIGURATION                                */
/* ========================================================================== */

/** Default buffer sizes */
#ifndef ES_ISOTP_DEFAULT_TX_BUFFER_SIZE
#define ES_ISOTP_DEFAULT_TX_BUFFER_SIZE     4095
#endif

#ifndef ES_ISOTP_DEFAULT_RX_BUFFER_SIZE
#define ES_ISOTP_DEFAULT_RX_BUFFER_SIZE     4095
#endif

/** Ring buffer size for incoming CAN frames */
#ifndef ES_ISOTP_RING_BUFFER_SIZE
#define ES_ISOTP_RING_BUFFER_SIZE           8
#endif

/** Default timeout value (seconds) */
#define ES_ISOTP_DEFAULT_TIMEOUT_S          1

/** Default timeout value (milliseconds) */
#define ES_ISOTP_DEFAULT_TIMEOUT_MS         1000

/* ========================================================================== */
/*                              ERROR CODES                                  */
/* ========================================================================== */

/**
 * @brief ISO-TP error codes
 */
typedef enum {
    ES_ISOTP_OK = 0,                        /**< Success */
    ES_ISOTP_ERR_INVALID_PARAM = -1,        /**< Invalid parameter */
    ES_ISOTP_ERR_TIMEOUT = -2,              /**< Timeout occurred */
    ES_ISOTP_ERR_BUSY = -3,                 /**< Resource busy */
    ES_ISOTP_ERR_NO_MEMORY = -4,            /**< No memory available */
    ES_ISOTP_ERR_CAN_ERROR = -5,            /**< CAN communication error */
    ES_ISOTP_ERR_INVALID_FRAME = -6,        /**< Invalid ISO-TP frame */
    ES_ISOTP_ERR_SEQUENCE_ERROR = -7,       /**< Frame sequence error */
    ES_ISOTP_ERR_OVERFLOW = -8              /**< Buffer overflow */
} es_isotp_error_t;

/* ========================================================================== */
/*                              ISO-TP FRAMES                                */
/* ========================================================================== */

/**
 * @brief ISO-TP frame types
 */
typedef enum {
    ES_ISOTP_FRAME_SF = 0,                  /**< Single Frame */
    ES_ISOTP_FRAME_FF = 1,                  /**< First Frame */
    ES_ISOTP_FRAME_CF = 2,                  /**< Consecutive Frame */
    ES_ISOTP_FRAME_FC = 3                   /**< Flow Control Frame */
} es_isotp_frame_type_t;

/**
 * @brief ISO-TP flow control status
 */
typedef enum {
    ES_ISOTP_FC_CTS = 0,                    /**< Continue To Send */
    ES_ISOTP_FC_WAIT = 1,                   /**< Wait */
    ES_ISOTP_FC_OVFLW = 2                   /**< Overflow */
} es_isotp_fc_status_t;

/**
 * @brief ISO-TP connection states
 */
typedef enum {
    ES_ISOTP_STATE_IDLE = 0,                /**< Idle */
    ES_ISOTP_STATE_SENDING,                 /**< Sending data */
    ES_ISOTP_STATE_RECEIVING,               /**< Receiving data */
    ES_ISOTP_STATE_WAIT_FC,                 /**< Waiting for flow control */
    ES_ISOTP_STATE_ERROR                    /**< Error state */
} es_isotp_state_t;

/* ========================================================================== */
/*                            CONNECTION STRUCTURE                           */
/* ========================================================================== */

/**
 * @brief ISO-TP connection structure
 */
typedef struct {
    /* Configuration */
    uint32_t tx_id;                         /**< Transmit CAN ID */
    uint32_t rx_id;                         /**< Receive CAN ID */

    /* State management */
    es_isotp_state_t state;                 /**< Current state */
    uint32_t timeout_start_ms;              /**< Timeout start time (milliseconds) */
    uint32_t timeout_ms;                    /**< Timeout value (milliseconds) */

    /* Multi-frame transmission */
    uint8_t sequence_number;                /**< Current sequence number */
    uint16_t send_offset;                   /**< Current send offset (bytes already sent) */

    /* Flow control */
    uint8_t block_size;                     /**< Block size */
    uint8_t separation_time;                /**< Separation time */
    uint8_t frames_sent_in_block;           /**< Frames sent in current block */

    /* Buffers */
    uint8_t *tx_buffer;                     /**< Transmit buffer pointer */
    uint8_t *rx_buffer;                     /**< Receive buffer pointer */
    uint16_t tx_buffer_size;                /**< Transmit buffer size */
    uint16_t rx_buffer_size;                /**< Receive buffer size */
    uint16_t tx_length;                     /**< Transmit data length */
    uint16_t rx_length;                     /**< Receive data length */

    /* Shared CAN message buffer for ISO-TP operations */
    es_can_msg_t shared_can_msg;            /**< Shared CAN message buffer (send/receive) */

    /* ISO-TP receive state variables */
    uint8_t recv_expected_sequence;         /**< Expected sequence number */
    uint16_t recv_total_length;             /**< Total length for multi-frame receive */
    uint8_t recv_frames_in_block;           /**< Frames received in current block */

    /* Ring buffer for incoming CAN frames */
    es_ringobj_t rx_ring;                   /**< Receive ring buffer */
    es_can_msg_t rx_ring_buffer[ES_ISOTP_RING_BUFFER_SIZE]; /**< Ring buffer storage */
} es_isotp_connection_t;

/* ========================================================================== */
/*                             FUNCTION DECLARATIONS                         */
/* ========================================================================== */

/**
 * @brief Initialize ISO-TP connection with external buffers
 * @param isotp_conn Pointer to ISO-TP connection structure
 * @param tx_id Transmit CAN ID
 * @param rx_id Receive CAN ID
 * @param tx_buffer Pointer to transmit buffer
 * @param tx_buffer_size Size of transmit buffer
 * @param rx_buffer Pointer to receive buffer
 * @param rx_buffer_size Size of receive buffer
 * @return ES_ISOTP_OK on success, error code on failure
 */
es_isotp_error_t es_isotp_init(es_isotp_connection_t *isotp_conn, uint32_t tx_id, uint32_t rx_id,
                               uint8_t *tx_buffer, uint16_t tx_buffer_size,
                               uint8_t *rx_buffer, uint16_t rx_buffer_size);

/**
 * @brief Process incoming CAN message
 * @param conn Pointer to ISO-TP connection
 * @param msg Pointer to CAN message
 * @return ES_ISOTP_OK on success, error code on failure
 */
es_isotp_error_t es_isotp_process_can_message(es_isotp_connection_t *conn, const es_can_msg_t *msg);

/**
 * @brief Send data via ISO-TP (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to ISO-TP connection
 * @return Coroutine return value
 * @note Data to send should be pre-loaded in conn->tx_buffer with length in conn->tx_length
 */
es_async_t es_isotp_send(es_coro_t *coro, es_isotp_connection_t *conn);

/**
 * @brief Receive data via ISO-TP (coroutine)
 * @param coro Coroutine context
 * @param conn Pointer to ISO-TP connection
 * @return Coroutine return value
 * @note Received data is stored in conn->rx_buffer, length in conn->rx_length
 */
es_async_t es_isotp_recv(es_coro_t *coro, es_isotp_connection_t *conn);

#ifdef __cplusplus
}
#endif

#endif /* ES_ISOTP_H */
