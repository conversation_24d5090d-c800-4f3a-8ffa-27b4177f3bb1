/**
 * @file uds_ota_simplified_example.h
 * @brief Header for UDS OTA simplified interface example
 * @version 1.0.0
 * @date 2025-01-17
 */

#ifndef UDS_OTA_SIMPLIFIED_EXAMPLE_H
#define UDS_OTA_SIMPLIFIED_EXAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize and start the OTA example
 * @return 0 on success, negative error code on failure
 */
int uds_ota_simplified_example_init(void);

/**
 * @brief Deinitialize the OTA example
 */
void uds_ota_simplified_example_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* UDS_OTA_SIMPLIFIED_EXAMPLE_H */
