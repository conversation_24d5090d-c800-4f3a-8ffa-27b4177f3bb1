﻿/**
 * @file es_at_cli.h
 * @brief AT command line interface definition
 */

#ifndef ES_AT_CLI_H
#define ES_AT_CLI_H

#include <stdint.h>
#include <stdbool.h>
#include "es_coro.h"
#include "es_drv_uart.h"
#include "es_list.h"
#include "es_scheduler.h"

#define AT_CLI_CMD_MAX_LEN         128
#define AT_CLI_RSP_MAX_LEN         128
#define AT_CLI_URC_QUEUE_SIZE      4    /**< URC queue size, must be power of 2 */
#define AT_CLI_URC_DATA_MAX_LEN    128    /**< URC data maximum length */

struct es_at_cli;
typedef es_async_t(*es_at_cli_urc_handler_t)(es_coro_t *coro, struct es_at_cli* at_cli, const char* buf, int len);

/**
 * @brief AT command request status
 */
typedef enum {
    ES_AT_CLI_REQ_IDLE = 0,              /**< Idle state */
    ES_AT_CLI_REQ_EXECUTING,             /**< Executing */
    ES_AT_CLI_REQ_OK,                   /**< Execution completed */
    ES_AT_CLI_REQ_ERROR                  /**< Execution error */
} es_at_cli_req_state_t;


/**
 * @brief AT command request structure
 */
typedef struct es_at_cli_req es_at_cli_req_t;

struct es_at_cli_req {
    const uint8_t* cmd;                    /**< AT command */
    const char* end_mark;                  /**< End marker */
    uint32_t start_time_ms;                /**< Start time */
    uint32_t timeout_ms;                   /**< Timeout (milliseconds) */
    uint16_t cmd_len;                      /**< Command length */
    uint8_t try_times;                     /**< Retry count */
    uint8_t line_num;                      /**< Expected line count */
    uint8_t line_cnt;                      /**< Current line count */
    uint8_t state : 3;                     /**< Request state */
    uint8_t with_rsp : 1;                  /**< Whether response is needed */
};

/**
 * @brief URC description structure
 */
typedef struct {
    const char* prefix;
    const char* suffix;
    uint16_t prefix_len;
    uint16_t suffix_len;
    es_at_cli_urc_handler_t handler;
} es_at_cli_urc_desc_t;

#define ES_AT_CLI_URC_DESC(prefix, suffix, handler) \
    {prefix, suffix, sizeof(prefix) - 1, sizeof(suffix) - 1, handler}

/**
 * @brief URC data item structure
 */
typedef struct es_at_cli_urc_item {
    es_at_cli_urc_handler_t handler;       /**< URC handler function */
    char* data;                            /**< URC data pointer */
    uint16_t len;                          /**< Data length */
} es_at_cli_urc_item_t;

typedef struct es_at_cli_task_id_cfg {
    int rx_task_id;
    int urc_task_id;
} es_at_cli_task_id_cfg_t;

/**
 * @brief AT command line interface structure
 */
typedef struct es_at_cli {
    uint8_t rx_buf[128];                    /**< Receive buffer */
    uint16_t rx_buf_len;                    /**< Receive buffer length */
    uint16_t urc_descs_size;               /**< URC description array size */
    es_coro_lock_t rx_lock;                 /**< Receive lock */
    struct {
        es_at_cli_urc_item_t items[AT_CLI_URC_QUEUE_SIZE];  /**< URC item object pool */
        uint8_t read_index;              /**< Read position index */
        uint8_t write_index;             /**< Write position index */
        uint8_t count : 7;               /**< Current object count */
        uint8_t is_full : 1;             /**< Queue full flag */
    } urc_queue;

    uint8_t cmd_tx_buf[128];                /**< Command transmit buffer */
    uint8_t rsp_buf[128];                   /**< Response buffer */
    uint16_t cmd_tx_buf_len;                /**< Command transmit buffer length */
    uint16_t rsp_buf_len;                   /**< Response buffer length */
    
    es_at_cli_urc_handler_t urc_handler;    /**< URC handler function */
    es_uart_dev_t uart_dev;                 /**< UART device handle */

    es_at_cli_urc_item_t urc_item;          /**< Current URC item being processed (value type, decoupled from queue) */
    es_at_cli_req_t req;                    /**< AT command request */
    const es_at_cli_urc_desc_t* urc_descs;        /**< URC description array */

    es_coro_task_t rx_task;                 /**< Receive task */
    es_coro_task_t urc_task;                /**< URC processing task */

    uint32_t timeout_ms;                    /**< Default timeout (ms) */
    uint8_t try_times;                      /**< Default retry count */
    
} es_at_cli_t;

/**
 * @brief Initialize AT command line interface
 * @param at_cli AT command line interface instance
 * @param uart_config UART configuration
 * @param urc_descs URC description array
 * @param urc_descs_size URC description array size
 * @return Returns 0 on success, negative on failure
 */
int es_at_cli_init(es_at_cli_t *at_cli, const es_uart_config_t *uart_config, const es_at_cli_urc_desc_t *urc_descs, int urc_descs_size);

/**
 * @brief Deinitialize AT command line interface
 * @param at_cli AT command line interface instance
 * @return Returns 0 on success, negative on failure
 */
int es_at_cli_deinit(es_at_cli_t *at_cli);

/**
 * @brief Send AT command
 * @param coro Coroutine context
 * @param at_cli AT command line interface instance
 * @param req Request structure
 * @return Coroutine return value
 */
es_async_t es_at_cli_cmd_send(es_coro_t *coro, es_at_cli_t *at_cli, const es_at_cli_req_t* req, const char* fmt, ...);
#define es_at_cli_send(at_cli, data, timeout, retry, need_rsp) es_co_await(es_at_cli_cmd_send, (at_cli), &(es_at_cli_req_t){ \
        .cmd = (data), \
        .timeout_ms = (timeout), \
        .try_times = (retry), \
        .with_rsp = (need_rsp), \
    }, NULL);

/**
 * @brief Get line by keyword
 * @param req Request structure
 * @param kw Keyword
 * @return Line
 */
const char* es_at_cli_get_line_by_kw(const es_at_cli_t* at_cli, const char* kw);

/**
 * @brief Parse response by keyword
 * @param at_cli AT command line interface instance
 * @param keyword Keyword
 * @param resp_expr Response expression
 * @return Returns 0 on success, negative on failure
 */
int es_at_cli_parse_by_kw(const es_at_cli_t *at_cli, const char *keyword, const char *resp_expr, ...);

/**
 * @brief Generic AT command send macro definition
 * @param label Error jump label
 * @param at_cli AT command line interface instance
 * @param cmd AT command string
 * @param timeout Timeout (ms)
 * @param retry Retry count
 * @param need_rsp Whether response is needed
 */
#define es_at_cli_send_ex(label, at_cli, data, timeout, retry, need_rsp) \
    es_co_await_ex(label, es_at_cli_cmd_send, (at_cli), &(es_at_cli_req_t){ \
        .cmd = (data), \
        .timeout_ms = (timeout), \
        .try_times = (retry), \
        .with_rsp = (need_rsp), \
    }, NULL);

/**
 * @brief Formatted AT command send macro definition
 * @param label Error jump label
 * @param at_cli AT command line interface instance
 * @param timeout Timeout (ms)
 * @param retry Retry count
 * @param need_rsp Whether response is needed
 * @param fmt Format string
 * @param ... Variable arguments
 */
#define es_at_cli_fmt_send_ex(label, at_cli, timeout, retry, need_rsp, fmt, ...) \
    es_co_await_ex(label, es_at_cli_cmd_send, (at_cli), &(es_at_cli_req_t){ \
        .timeout_ms = (timeout), \
        .try_times = (retry), \
        .with_rsp = (need_rsp), \
    }, fmt, __VA_ARGS__);


#endif /* ES_AT_CLI_H */
