/**
 * @file es_drv_can.h
 * @brief CAN driver abstraction layer interface
 */

#ifndef __ES_DRV_CAN_H__
#define __ES_DRV_CAN_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

#include "es_coro.h"

/* CAN message structure */
typedef struct {
    uint32_t id;           /* CAN ID */
    uint8_t ide;           /* IDE flag: 0=standard, 1=extended */
    uint8_t rtr;           /* RTR flag: 0=data frame, 1=remote frame */
    uint8_t dlc;           /* Data Length Code */
    uint8_t data[8];       /* CAN data */
} es_can_msg_t;

/* CAN error information structure */
typedef struct {
    uint8_t arbitr_lost_pos;    /* Bit position where arbitration was lost */
    uint8_t error_type;         /* Error type code */
    uint8_t rx_error_count;     /* Receive error count */
    uint8_t tx_error_count;     /* Transmit error count */
} es_can_error_info_t;

/**
 * @brief Initialize CAN module
 * 
 * Initializes the CAN peripheral with default configuration, sets up GPIO pins,
 * interrupt handling, and receive buffer.
 * 
 * @return 0 on success, -1 on failure
 */
int es_can_init(void);

/**
 * @brief Read CAN message from receive buffer
 * 
 * Reads one CAN message from the internal ring buffer. This function is
 * non-blocking and returns immediately if no message is available.
 * 
 * @param msg Pointer to es_can_msg_t structure to store the received message
 * @return Number of messages read (0 if no message, 1 if message read), -1 on error
 */
int es_can_read(es_can_msg_t *msg);

/**
 * @brief Write CAN message to transmit buffer
 * 
 * Transmits a CAN message. This function may block if the transmit buffer is busy.
 * 
 * @param msg Pointer to es_can_msg_t structure containing the message to send
 * @return 0 on success, -1 on failure
 */
int es_can_write(const es_can_msg_t *msg);

/**
 * @brief Write CAN message to transmit buffer coro
 * 
 * Transmits a CAN message. This function may block if the transmit buffer is busy.
 * 
 * @param msg Pointer to es_can_msg_t structure containing the message to send
 * @return 0 on success, -1 on failure
 */
es_async_t es_can_coro_write(es_coro_t *coro, uint32_t id, const uint8_t *data, uint16_t len);

/**
 * @brief Check if CAN receive buffer has data available
 * 
 * Returns the number of complete CAN messages available in the receive buffer.
 * 
 * @return Number of available messages, -1 on error
 */
int es_can_available(void);

/**
 * @brief Get CAN status information
 * 
 * Returns the current CAN status information.
 * 
 * @return CAN status flags, 0 if not initialized
 */
uint32_t es_can_get_status(void);

/**
 * @brief Get CAN error information
 * 
 * Retrieves detailed error information including error counters and error types.
 * 
 * @param error_info Pointer to store error information structure
 * @return 0 on success, -1 on failure
 */
int es_can_get_error_info(es_can_error_info_t *error_info);

/**
 * @brief Debug function to check CAN configuration and status
 * 
 * Prints detailed CAN status information for debugging interrupt issues.
 * This function outputs status, filter settings, and buffer information.
 */
void es_can_debug_status(void);

/**
 * @brief Get number of messages pending in receive buffer
 * 
 * This is an alias for es_can_available() for better readability.
 * 
 * @return Number of CAN messages pending in buffer, -1 on error
 */
static inline int es_can_get_pending_count(void) {
    return es_can_available();
}

/**
 * @brief Test function for the new ring buffer implementation
 * 
 * Tests the es_ringobj_t functionality by writing and reading test messages.
 * Useful for verifying the ring buffer works correctly.
 */
void es_can_test_ringbuffer(void);

#ifdef __cplusplus
}
#endif

#endif /* __ES_DRV_CAN_H__ */ 
