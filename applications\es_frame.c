//
// Created by 67376 on 2022/12/30.
//
#include <string.h>
#include <stddef.h>
#include <time.h>
#include <stdio.h>
#include "es_frame.h"
#include "es_drv_os.h"
#include "es_time.h"
#include <stdint.h>
#include "es_log.h"
#include "es_utils.h"
#include "es_aes.h"
#include "es_des3.h"
#define TAG "frame"

uint8_t secret_key[24] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

//
// static int encode_int32_t(unsigned char *buffer, int size, int pos, const int32_t *value, int len) {
//    if (pos + sizeof(int32_t) > size) {
//        return -1;
//    }
//
//    int32_t v = *value;
//
//    buffer[pos + 0] = (v >> 24) & 0xff;
//    buffer[pos + 1] = (v >> 16) & 0xff;
//    buffer[pos + 2] = (v >> 8) & 0xff;
//    buffer[pos + 3] = v & 0xff;
//
//    return 4;
//}
//
// static int decode_int32_t(const unsigned char *buffer, int size, int pos, int32_t *value, int len) {
//    if (pos + sizeof(int32_t) > size) {
//        return -1;
//    }
//
//    *value = (buffer[pos + 0] << 24) | (buffer[pos + 1] << 16) | (buffer[pos + 2] << 8) | buffer[pos + 3];
//
//    return 4;
//}
//
// static int encode_int16_t(unsigned char *buffer, int size, int pos, const int16_t* value, int len) {
//    if (pos + sizeof(int16_t) > size) {
//        return -1;
//    }
//
//    int16_t v = *value;
//
//    buffer[pos + 0] = (v >> 8) & 0xff;
//    buffer[pos + 1] = v & 0xff;
//
//    return 2;
//}
//
// static int decode_int16_t(const unsigned char *buffer, int size, int pos, int16_t *value, int len) {
//    if (pos + sizeof(int16_t) > size) {
//        return -1;
//    }
//
//    *value = (short)((buffer[pos + 0] << 8) | buffer[pos + 1]);
//
//    return 2;
//}
//
// static int encode_int18_t(unsigned char *buffer, int size, int pos, const int8_t* value, int len) {
//    if (pos + sizeof(int8_t) > size) {
//        return -1;
//    }
//
//    buffer[pos] = *value;
//
//    return 1;
//}
//
// static int decode_int8_t(const unsigned char *buffer, int size, int pos, int8_t *value, int len) {
//    if (pos + sizeof(int8_t) > size) {
//        return -1;
//    }
//
//    *value = (char)buffer[pos];
//
//    return 1;
//}

static int encode_x32(unsigned char *buffer, int size, int pos, const uint32_t *value, int len)
{
    if (pos + 4 > size) {
        return -1;
    }

    uint32_t v = *value;

    buffer[pos + 0] = (v >> 24) & 0xff;
    buffer[pos + 1] = (v >> 16) & 0xff;
    buffer[pos + 2] = (v >> 8) & 0xff;
    buffer[pos + 3] = v & 0xff;

    return pos + 4;
}

static int decode_x32(const unsigned char *buffer, int size, int pos, uint32_t *value, int len)
{
    if (pos + 4 > size) {
        return -1;
    }

    *value = (buffer[pos + 0] << 24) | (buffer[pos + 1] << 16) | (buffer[pos + 2] << 8) | buffer[pos + 3];

    return pos + 4;
}

static int encode_x16(unsigned char *buffer, int size, int pos, const uint16_t *value, int len)
{
    if (pos + 2 > size) {
        return -1;
    }

    uint16_t v = *value;

    buffer[pos + 0] = (v >> 8) & 0xff;
    buffer[pos + 1] = v & 0xff;

    return pos + 2;
}

static int decode_x16(const unsigned char *buffer, int size, int pos, uint16_t *value, int len)
{
    if (pos + 2 > size) {
        return -1;
    }

    *value = (buffer[pos + 0] << 8) | buffer[pos + 1];

    return pos + 2;
}

static int encode_x8(unsigned char *buffer, int size, int pos, const uint8_t *value, int len)
{
    if (pos + 1 > size) {
        return -1;
    }

    buffer[pos] = *value;

    return pos + 1;
}

static int decode_x8(const unsigned char *buffer, int size, int pos, uint8_t *value, int len)
{
    if (pos + 1 > size) {
        return -1;
    }

    *value = buffer[pos];

    return pos + 1;
}

// static int decode_u24(const unsigned char *buffer, int size, int pos, uint32_t *value, int len) {

//    if (pos + 3 > size) {
//        return -1;
//    }

//    *value = (buffer[pos + 0] << 16) | (buffer[pos + 1] << 8) | buffer[pos + 2];

//    return 3;
//}

static int encode_x24(unsigned char *buffer, int size, int pos, const uint32_t *value, int len)
{

    if (pos + 3 > size) {
        return -1;
    }

    uint32_t v = *value;

    buffer[pos + 0] = (v >> 16) & 0xff;
    buffer[pos + 1] = (v >> 8) & 0xff;
    buffer[pos + 2] = v & 0xff;

    return pos + 3;
}

static int encode_c_str(unsigned char *buffer, int size, int pos, const char *value, int len)
{
    if (value == NULL) {
        return -1;
    }

    uint8_t s = (uint8_t)strlen(value);

    if (pos + s + 1 > size) {
        return -1;
    }

    buffer[pos + 0] = s;

    if (s > 0) memcpy(buffer + pos + 1, value, s);

    return pos + s + 1;
}

static int decode_c_str(const unsigned char *buffer, int size, int pos, char *value, int len)
{

    uint8_t s = 0;

    if ((pos = decode_x8(buffer, size, pos, &s, sizeof(uint8_t))) < 0) {
        return -1;
    }

    if (pos + s > size) {
        return -1;
    }

    if (s == 0) {
        value[0] = '\0';
    } else {
        memcpy(value, buffer + pos, s);
        value[s] = '\0';
    }

    return pos + s;
}

static int encode_c_arr(unsigned char *buffer, int size, int pos, const void *value, int len)
{
    if (value == NULL) {
        return -1;
    }

    uint8_t s           = *(uint8_t *)value;
    const uint8_t *data = (const uint8_t *)value + sizeof(uint8_t);

    if (pos + s > size) {
        return -1;
    }

    memcpy(buffer + pos, data, s);

    return pos + s;
}

//static int decode_c_arr(const unsigned char *buffer, int size, int pos, void *value, int len) {

//    if (pos >= size) {
//        return -1;
//    }

//    if(len - 1 < size - pos) {
//        len = len - 1;
//    } else {
//        len = size - pos;
//    }

//   uint8_t *data = (uint8_t*)value + sizeof(uint8_t);

//    *(uint8_t *)value = len;
//   memcpy(data, buffer + pos, len);

//   return pos + len;
//}

static int decode_s_arr(const unsigned char *buffer, int size, int pos, char *value, int len)
{

    if (pos + len > size) {
        return -1;
    }

    memcpy(value, buffer + pos, len);

    return pos + len;
}

static int encode_s_arr(unsigned char *buffer, int size, int pos, const void *value, int len)
{

    if (pos + len > size) {
        return -1;
    }

    memcpy(buffer + pos, value, len);

    return pos + len;
}

static int encode_f_arr(unsigned char *buffer, int size, int pos, const void *value, int len)
{

    uint16_t s = *(uint16_t *)value;
    ES_LOGE(TAG,"f_arr: %d\n", s);
    const uint8_t *data = (const uint8_t *)value + sizeof(uint16_t);

    if ((pos = encode_x16(buffer, size, pos, &s, sizeof(s))) < 0) {
        return -1;
    }

    if (pos + s > size) {
        return -1;
    }

    memcpy(buffer + pos, data, s);

    return pos + s;
}

static int encode_c_time(unsigned char *buffer, int size, int pos, const void *value, int len)
{
    uint32_t t = *(uint32_t *)value;

     uint8_t is_tick_time = 0;

     //check the high 1 bit if is 1
     if(t & 0x80000000) {
         is_tick_time = 1;

         //clear the high 1 bit
         t &= 0x7fffffff;
     }

     if(is_tick_time) {
         //cover to real time
         if(t < es_os_get_tick_ms()) {
             t = es_get_timestamp() - (es_os_get_tick_ms() - t) / 1000;
             ES_LOGE(TAG,"fix time %d", t);
         } else {
             //Fill directly with current time
             t = es_get_timestamp();
             ES_LOGE(TAG,"fix time %d", t);
         }
     }

    // if(t < XSYS_UTC_MIN_VALUE || t > XSYS_UTC_MAX_VALUE) {
    //     //Discard this data
    //     LOG_E("time out of range %d", t);
    // }

    return encode_x32(buffer, size, pos, &t, len);
}

static int encode_c_encode(unsigned char *buffer, int size, int pos, const void *value, int len)
{
    c_encode *obj = (c_encode *)value;
    if (obj == NULL) {
        return -1;
    }

    if ((pos = (*obj)(buffer, size, pos)) < 0) {
        return -1;
    }

    return pos;
}

static int encode_gps_t(unsigned char *buffer, int size, int pos, const void *value, int len)
{

    const gps_t *obj = (const gps_t *)value;

    //    buf_write_u32(buf, obj->time); // Timing time
    //
    //    buf_write_bit(buf, 7, 1, obj->src);        // Positioning source
    //    buf_write_bit(buf, 6, 1, obj->valid);      // Data validity
    //    buf_write_bit(buf, 5, 1, obj->f_lng);      // East/West longitude
    //    buf_write_bit(buf, 4, 1, obj->f_lat);      // North/South latitude
    //    buf_write_bit(buf, 0, 4, obj->satellites); // Number of positioning satellites
    //
    //    buf->len += 1;
    //
    //    uint32_t height = (uint32_t) (obj->height) + 10000;
    //
    //    buf_write_u16(buf, (height << 1) | ((obj->lon >> 24) & 0x01));
    //
    //    buf_write_u24(buf, obj->lon & 0x00ffffff);
    //    buf_write_u24(buf, obj->lat & 0x00ffffff);
    //    buf_write_u16(buf, obj->speed << 4 | ((obj->direction >> 8) & 0x03));
    //    buf_write_u8(buf, obj->direction & 0xff);
    //
    //    buf_write_u16(buf, obj->pdod << 4 | ((obj->hdop >> 8) & 0x04));
    //    buf_write_u8(buf, obj->hdop & 0xff);
    //
    //    buf_write_u16(buf, (obj->vdop << 4) | (obj->ign_status << 2) | (obj->is_turn_point ? 1 : 0) << 1);
    //
    //    buf_write_u16(buf, obj->hacc);
    //
    //    buf_write_u8(buf, 0x00);

    if ((pos = encode_x32(buffer, size, pos, &obj->time, sizeof(obj->time))) < 0) {
        return -1;
    }

    uint8_t tmp = 0;
    tmp |= obj->src << 7;
    tmp |= obj->valid << 6;
    tmp |= obj->f_lng << 5;
    tmp |= obj->f_lat << 4;
    tmp |= (obj->satellites > 15) ? 15 : obj->satellites;
    if ((pos = encode_x8(buffer, size, pos, &tmp, sizeof(tmp))) < 0) {
        return -1;
    }

    uint32_t height = (uint32_t)((obj->height) + 10000);
    uint16_t tmp2   = (height << 1) | ((obj->lon >> 24) & 0x01);
    if ((pos = encode_x16(buffer, size, pos, &tmp2, sizeof(tmp2))) < 0) {
        return -1;
    }

    uint32_t tmp3 = obj->lon & 0x00ffffff;
    if ((pos = encode_x24(buffer, size, pos, &tmp3, sizeof(tmp3))) < 0) {
        return -1;
    }

    uint32_t tmp4 = obj->lat & 0x00ffffff;
    if ((pos = encode_x24(buffer, size, pos, &tmp4, sizeof(tmp4))) < 0) {
        return -1;
    }

    uint16_t tmp5 = obj->speed << 4 | ((obj->direction >> 8) & 0x03);
    if ((pos = encode_x16(buffer, size, pos, &tmp5, sizeof(tmp5))) < 0) {
        return -1;
    }

    uint8_t tmp6 = obj->direction & 0xff;
    if ((pos = encode_x8(buffer, size, pos, &tmp6, sizeof(tmp6))) < 0) {
        return -1;
    }

    uint16_t tmp7 = obj->pdod << 4 | ((obj->hdop >> 8) & 0x04);
    if ((pos = encode_x16(buffer, size, pos, &tmp7, sizeof(tmp7))) < 0) {
        return -1;
    }

    uint8_t tmp8 = obj->hdop & 0xff;
    if ((pos = encode_x8(buffer, size, pos, &tmp8, sizeof(tmp8))) < 0) {
        return -1;
    }

    uint16_t tmp9 = (obj->vdop << 4) | (obj->ign_status << 2) | (obj->is_turn_point ? 1 : 0) << 1;
    if ((pos = encode_x16(buffer, size, pos, &tmp9, sizeof(tmp9))) < 0) {
        return -1;
    }

    uint16_t tmp10 = obj->hacc;
    if ((pos = encode_x16(buffer, size, pos, &tmp10, sizeof(tmp10))) < 0) {
        return -1;
    }

    uint8_t tmp11 = obj->signal_strength;
    if ((pos = encode_x8(buffer, size, pos, &tmp11, sizeof(tmp11))) < 0) {
        return -1;
    }

    return pos;
}

// static int encode_c_gps(unsigned char *buffer, int size, int pos, const void *value, int len)
// {

//     uint8_t cnt = *(uint8_t *)value;

//     if ((pos = encode_x8(buffer, size, pos, &cnt, sizeof(cnt))) < 0) {
//         return -1;
//     }

//     if (cnt == 0) {
//         return pos;
//     }

//     const gps_t *obj = (const gps_t *)((rt_uint8_t *)value + sizeof(uint8_t));

//     for (int i = 0; i < GPS_PACKAGE; i++) {
//         if ((pos = encode_gps_t(buffer, size, pos, obj + i, sizeof(gps_t))) < 0) {
//             return -1;
//         }
//     }

//     return pos;
// }

#define c_arr(S) c_arr
#define c_str(S) c_str
#define s_arr(S) s_arr
#define f_arr(S) f_arr
#define M(L, R) \
    if ((pos = CAT(encode_, L)(buf, size, pos, (const void *)&s->R, sizeof(s->R))) < 0) { return -1; }
#define X(S, CMD, META, ...)                                                         \
    static int CAT(encode_, S)(unsigned char *buf, int size, int pos, const void *o) \
    {                                                                                \
        const struct frame_tx_##S##_t *s = (const struct frame_tx_##S##_t *)o;       \
        __VA_ARGS__ return pos;                                                      \
    }
TSP_TX_FRAMES
TSP_FRAME_HEAD
#undef X

#define X(S, CMD, META, ...)                                                         \
    static int CAT(encode_, S)(unsigned char *buf, int size, int pos, const void *o) \
    {                                                                                \
        const struct ble_tx_##S##_t *s = (const struct ble_tx_##S##_t *)o;       \
        __VA_ARGS__ return pos;                                                      \
    }
BLE_TX_FRAMES
BLE_FRAME_HEAD
#undef X
#undef M

#define M(L, R, ...) \
    if ((pos = CAT(decode_, L)(buf, size, pos, (void *)&s->R, sizeof(s->R))) < 0) { return -1; };
#define X(S, CMD, META, ...)                                                         \
    static int CAT(decode_, S)(const unsigned char *buf, int size, int pos, void *o) \
    {                                                                                \
        struct frame_rx_##S##_t *s = (struct frame_rx_##S##_t *)o;                   \
        (void)s;                                                                     \
        __VA_ARGS__ return pos;                                                      \
    }
TSP_RX_FRAMES
TSP_FRAME_HEAD
#undef X

#define X(S, CMD, META, ...)                                                         \
    static int CAT(decode_, S)(const unsigned char *buf, int size, int pos, void *o) \
    {                                                                                \
        struct ble_rx_##S##_t *s = (struct ble_rx_##S##_t *)o;                   \
        (void)s;                                                                     \
        __VA_ARGS__ return pos;                                                      \
    }
BLE_RX_FRAMES
BLE_FRAME_HEAD
#undef X

#undef M
#undef c_arr
#undef c_str
#undef s_arr
#undef f_arr

static const frame_info_t frame_tx_info[] = {

#define X(name, id, META, ...) [frame_tx_type_##name] = {.desc = #name, .size = sizeof(struct frame_tx_##name##_t), .meta = (META), .cmd = (id), .codec.encode = encode_##name},
    TSP_TX_FRAMES
#undef X
};

static const frame_info_t ble_tx_info[] = {

#define X(name, id, META, ...) [ble_tx_type_##name] = {.desc = #name, .size = sizeof(struct ble_tx_##name##_t), .meta = (META), .cmd = (id), .codec.encode = encode_##name},
    BLE_TX_FRAMES
#undef X
};

static const frame_info_t frame_rx_info[] = {
#define X(name, id, META, ...) [frame_rx_type_##name] = {.desc = #name, .size = sizeof(struct frame_rx_##name##_t), .meta = (META), .cmd = (id), .codec.decode = decode_##name},
    TSP_RX_FRAMES
#undef X
};

static const struct frame_info_t ble_rx_info[] = {
#define X(name, id, META, ...) [ble_rx_type_##name] = {.desc = #name, .size = sizeof(struct ble_rx_##name##_t), .meta = (META), .cmd = (id), .codec.decode = decode_##name},
    BLE_RX_FRAMES
#undef X
};

const frame_info_t *frame_tx_get_info(uint8_t type)
{
    if (type <= frame_tx_type_min || type >= frame_tx_type_max) {
        return NULL;
    }
    return &frame_tx_info[type];
}

const frame_info_t *ble_tx_get_info(uint8_t type)
{
    if (type <= ble_tx_type_min || type >= ble_tx_type_max) {
        return NULL;
    }
    return &ble_tx_info[type];
}

const struct frame_info_t *ble_rx_get_info(uint8_t type) {
    if (type <= ble_rx_type_min || type >= ble_rx_type_max) {
        return NULL;
    }
    return &ble_rx_info[type];
}

int frame_tx_get_size(uint8_t type)
{
    if (type <= frame_tx_type_min || type >= frame_tx_type_max) {
        return 0;
    }
    return (int)(frame_tx_info[type].size + sizeof(struct frame_head_t));
}

int ble_tx_get_size(uint8_t type)
{
    if (type <= ble_tx_type_min || type >= ble_tx_type_max) {
        return 0;
    }
    return (int)(ble_tx_info[type].size + sizeof(struct frame_head_t));
}



static uint32_t adler32(const uint8_t *buf, uint16_t start, uint16_t len)
{
    uint32_t a = 1, b = 0;
    int i;
    for (i = start; i < start + len; i++) {
        a = (a + buf[i]) % 65521;
        b = (b + a) % 65521;
    }
    return (b << 16) | a;
}

static int check_crc(const uint8_t *data, int size, uint32_t effect)
{

    uint32_t crc; //= adler32(buf->data + 2, effective_len + 22);
    crc = adler32(data, 2, effect + 22);
    uint32_t cmp;

    decode_x32(data, size, (int)effect + 24, &cmp, sizeof(cmp));

    if (crc != cmp) {
        ES_LOGE(TAG,"check crc error, crc: %x, cmp: %x", crc, cmp);
        return -1;
    }

    return 0;
}

int frame_tx_encode(const struct frame_tx_t *frame, const uint8_t* device_id, uint8_t* data, int len) {

    if(frame->head.type <= frame_tx_type_min || frame->head.type >= frame_tx_type_max) {
        ES_LOGE(TAG,"mdm invalid frame type: %d", frame->head.type);
        return -1;
    }

    int pos = 0,  c = 0, m = 0;

    frame_info_t *info = (struct frame_info_t *) &frame_tx_info[frame->head.type];

    frame_tx_head_t head = {.start = 0x5566, .seq = frame->head.seq};
    memcpy(head.imei.arr, device_id, sizeof(head.imei.arr));
    head.type = info->meta & META_CONNECT ? 0x01 : (info->meta & META_DEV_RSP ? 0x04 : 0x03);

    if((pos=encode_head(data, len, pos, &head)) < 0) {
        return -1;
    }

    ES_LOGI(TAG,"encode frame: %s", info->desc);

    c = pos;

    if(info->meta & META_DATA || info->meta & META_DEV_RSP || info->meta & META_DEV_REQ) {
        if((pos=encode_x16(data, len, pos, &(uint16_t){info->cmd}, sizeof(uint16_t))) < 0) {
            return -1;
        }
    }

    if((pos=info->codec.encode(data, len, pos, &frame->value)) < 0) {
        return -1;
    }

    //Fill valid data length
    encode_x16(data, len, offsetof(frame_tx_head_t, effect), &(uint16_t){pos - c}, sizeof(uint16_t));

    int crc_pos = pos;
    //CRC placeholder
    if((pos=encode_x32(data, len, pos, &(uint32_t){0}, sizeof(uint32_t))) < 0) {
        return -1;
    }

    if ((m = (int)(pos - offsetof(frame_tx_head_t, type)) % 8) != 0) {
        for (int i = 0; i < 8 - m; i++) {
            if((pos=encode_x8(data, len, pos, &(uint8_t){0xff}, sizeof(i))) < 0) {
                return -1;
            }
        }
    }

    if((pos=encode_x16(data, len, pos, &(uint16_t){0xaaaa}, sizeof(uint16_t))) < 0) {
        return -1;
    }

    encode_x16(data, len, offsetof(frame_tx_head_t, len), &(uint16_t){pos}, sizeof(uint16_t));
    encode_x32(data, len, crc_pos, &(uint32_t){adler32(data, 2, crc_pos - 2)}, sizeof(uint32_t));

    ES_LOG_HEX(info->desc, 16, data, pos);

    uint8_t *pdata = data + offsetof(frame_tx_head_t, type);
    des3_ecb_encrypt(pdata, pdata, pos - 2 - offsetof(frame_tx_head_t, type), secret_key, 24);

    return pos;
}

typedef es_async_t (*net_rx_handler_t)(es_coro_t *coro, frame_rx_t *rx, frame_tx_t *tx, const uint8_t* data, int pos);

#define X(name, id, meta, ...) \
    ES_WEAK es_async_t name##_proc(es_coro_t *coro, frame_rx_t *rx, frame_tx_t *tx, const uint8_t* data, int pos) { \
        es_co_begin(coro); \
        es_co_end; \
    }
TSP_RX_FRAMES
#undef X

static const net_rx_handler_t net_rx_handlers[] = {
    #define X(name, id, meta, ...) [frame_rx_type_##name] = name##_proc,
    TSP_RX_FRAMES
    #undef X
};

es_async_t frame_rx_decode(es_coro_t *coro, frame_tx_t *tx, const uint8_t *data, int len) {

    static frame_rx_t rx_frame = {0};

    int pos = 0;
    uint16_t cmd;
	uint8_t *pdata;
	const  frame_info_t *f = NULL;
    frame_rx_head_t head = {0};

    es_co_begin(coro);

    ES_CHECK(err, (pos = decode_head(data, len, 0, &head)) < 0, "decode head failed");
    ES_CHECK(err, head.start != 0x5566, "check start failed: %x", head.start);
    ES_CHECK(err, head.len > len, "check len failed: %d, %d", head.len, len);
    ES_CHECK(err, data[head.len - 2] != 0xaa || data[head.len - 1] != 0xaa, "check end failed");

    // decrypt
    pdata = (uint8_t *)data + offsetof(frame_rx_head_t, type);
    des3_ecb_decrypt(pdata, pdata, head.len - 2 - offsetof(frame_rx_head_t, type), secret_key, 24);

    ES_CHECK(err, (pos = decode_head(data, len, 0, &head)) < 0, "decode head failed");
    ES_CHECK(err, check_crc(data, len, head.effect) != 0, "check crc failed");

    ES_LOG_HEX("decode", 16, (uint8_t*)data, head.len > 256 ? 256 : head.len);

    if(head.type != 0x02) {
        ES_CHECK(err, (pos=decode_x16(data, len, pos, &cmd, sizeof(cmd))) < 0, "decode cmd failed");
    }  

    if(head.type == 0x02) {
        f = &frame_rx_info[frame_rx_type_connack];
    } else if(head.type == 0x03) {
        for(int i = frame_rx_type_connack + 1; i < frame_rx_type_srv_rsp_common; i++) {
            f = &frame_rx_info[i];
            if(f->meta & META_SRV_REQ && f->cmd == cmd) {
                break;
            }
            f = NULL;
        }
    } else if(head.type == 0x04) {
        for(int i = frame_rx_type_connack + 1; i < frame_rx_type_srv_rsp_common; i++) {
            f = &frame_rx_info[i];
            if(f->meta & META_SRV_RSP && f->cmd == cmd) {
                break;
            }
            f = NULL;
        }
        if(f == NULL) {
            f = &frame_rx_info[frame_rx_type_srv_rsp_common];
        }
    } else {
        es_co_exit;
    }

    ES_CHECK(err, f == NULL, "invalid frame type: %d", head.type);

    if(f->cmd != 0xffff) {
        ES_LOGI(TAG,"recv frame: %s", f->desc);
    }

    rx_frame.head.type = (uint8_t)(f - frame_rx_info);
    rx_frame.head.seq = head.seq;
    //decode
    ES_CHECK(err, (pos=f->codec.decode(data, len, pos, &rx_frame.value)) < 0, "decode frame failed, type: %d: %s", rx_frame.head.type, f->desc);

    es_co_await(net_rx_handlers[rx_frame.head.type], &rx_frame, tx, data, pos);
    es_co_eee();
}


static const unsigned char  ble_aes_key[16]={'Q','S','D','f','a','g','Q','1','4','1','G','S','6','J','F','8'};//"QSDfagQ141GS6JF8";

static unsigned char checksum(const unsigned char *data, int len) {
    unsigned char bcc = 0;
    for (int i = 0; i < len; i++) {
        bcc ^= data[i];
    }
    return bcc;
}

typedef es_async_t (*ble_rx_handler_t)(es_coro_t *coro, ble_rx_t *rx, ble_tx_t *tx, const uint8_t* data, int pos);

//define all weak functions
#define X(name, id, meta, ...) \
    ES_WEAK es_async_t name##_proc(es_coro_t *coro, ble_rx_t *rx, ble_tx_t *tx, const uint8_t* data, int pos) { \
        es_co_begin(coro); \
        es_co_end; \
    }
    BLE_RX_FRAMES
#undef X

static const ble_rx_handler_t ble_rx_handlers[] = {
    #define X(name, id, meta, ...) [ble_rx_type_##name] = name##_proc,
    BLE_RX_FRAMES
    #undef X
};


es_async_t ble_rx_decode(es_coro_t *coro, ble_tx_t *tx, const uint8_t *payload, int size) {

    static ble_rx_t ble_rx_frame = {0};

    int pos = 0;
    uint16_t cmd;
    struct ble_rx_ble_head_t head = {0};
	const  frame_info_t *f = NULL;

    es_co_begin(coro);

    ES_CHECK(err, (pos = decode_ble_head(payload, size, 0, &head)) < 0, "decode head failed");
    ES_CHECK(err, head.start != 0x5555, "ble check start failed: %x", head.start);
    ES_CHECK(err, head.len > size, "ble check len failed: %d, %d", head.len, size);
    ES_CHECK(err, payload[head.len - 2] != 0xaa || payload[head.len - 1] != 0xaa, "ble check end failed");
    ES_CHECK(err, checksum(payload, head.len - 3) != payload[head.len - 3], "check bcc failed");
    ES_CHECK(err, (pos=decode_x16(payload, size, pos, &cmd, sizeof(cmd))) < 0, "decode cmd failed");
    // decrypt
    if(head.crypto == 0x02) {
        ES_CHECK(err, es_aes_decrypt_bytes((uint8_t *)payload + 7, (uint8_t *)payload + 7, head.len - 10, ble_aes_key) < 0, "ble decrypt failed");
    }
    
    for(int i = ble_rx_type_min + 1; i < ble_rx_type_max; i++) {
        f = &ble_rx_info[i];
        if(f->cmd == cmd) {
            break;
        }
        f = NULL;
    }

    ES_CHECK(err, f == NULL, "invalid frame type: %02x", cmd);

    ES_LOGI(TAG,"ble recv frame: %s", f->desc);
    //first set rx frame to 0
    memset(&ble_rx_frame, 0, sizeof(ble_rx_frame));
    ble_rx_frame.head.type = (uint8_t)(f - ble_rx_info);
    ble_rx_frame.head.seq = head.seq;
    tx->head.seq = head.seq;
    //decode
    ES_CHECK(err, (pos=f->codec.decode(payload, size, pos, &ble_rx_frame.value)) < 0, "decode frame failed, type: %d: %s", ble_rx_frame.head.type, f->desc);

    ES_LOGI(TAG,"decode", 16, (uint8_t*)payload, head.len);

    es_co_await(ble_rx_handlers[ble_rx_frame.head.type], &ble_rx_frame, tx, payload, pos);

    es_co_eee();
}


int ble_tx_encode(const ble_tx_t *frame, uint8_t* data, int len) {

    if(frame->head.type <= ble_tx_type_min || frame->head.type >= ble_tx_type_max) {
        ES_LOGE(TAG,"invalid frame type: %d", frame->head.type);
        return -1;
    }

    int pos = 0;
    int c = 0;

    frame_info_t *info = (struct frame_info_t *) &ble_tx_info[frame->head.type];

    ble_tx_ble_head_t head = {.start = 0x5555, .seq = (uint8_t)frame->head.seq, .crypto = 0x02};

    if((pos=encode_ble_head(data, len, pos, &head)) < 0) {
        return -1;
    }

    if(info->cmd != 0xffff) { //0xffff is common rsp frame
        //encode cmd
        if((pos=encode_x16(data, len, pos, &(uint16_t){info->cmd}, sizeof(uint16_t))) < 0) {
            return -1;
        }
    }

    c = pos;


    ES_LOGI(TAG,"encode frame: %s", info->desc);

    if((pos=info->codec.encode(data, len, pos, &frame->value)) < 0) {
        return -1;
    }

    ES_LOGI(TAG,info->desc, 16, data, pos);

    //crypto
    int size = es_aes_encrypt_bytes((uint8_t *)data + 7, (uint8_t *)data + 7, pos - 7, ble_aes_key);
    if(size < 0) {
        return -1;
    }

    if(info->cmd != 0xffff) {
        pos = c + size;
    } else {
        pos = c + size + 2;
    }

    encode_x8(data, len, offsetof(ble_tx_ble_head_t, len), &(uint8_t){pos + 3}, sizeof(uint8_t));

    uint8_t crc = checksum(data, pos);
    if((pos=encode_x8(data, len, pos, &crc, sizeof(crc))) < 0) {
        return -1;
    }

    if((pos=encode_x16(data, len, pos, &(uint16_t){0xaaaa}, sizeof(uint16_t))) < 0) {
        return -1;
    }

    return pos;
}

