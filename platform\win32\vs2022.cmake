set(CMAKE_SYSTEM_NAME Windows)
set(CMAKE_SYSTEM_PROCESSOR x86_64)

# 设置MSVC编译器
set(CMAKE_C_COMPILER "cl.exe")
set(CMAKE_CXX_COMPILER "cl.exe")

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Choose the type of build: Debug Release RelWithDebInfo MinSizeRel" FORCE)
endif()

# MSVC编译选项
set(MSVC_FLAGS "/nologo /W3 /EHsc /D_CRT_SECURE_NO_WARNINGS")

# Debug版本额外添加调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(MSVC_FLAGS "${MSVC_FLAGS} /Od /Zi /MDd")
else()
    set(MSVC_FLAGS "${MSVC_FLAGS} /O2 /MD")
endif()

# 设置C和C++编译标志
set(CMAKE_C_FLAGS "${MSVC_FLAGS}" CACHE STRING "" FORCE)
set(CMAKE_CXX_FLAGS "${MSVC_FLAGS}" CACHE STRING "" FORCE)

# 链接器标志
set(CMAKE_EXE_LINKER_FLAGS "/INCREMENTAL:NO /SUBSYSTEM:CONSOLE" CACHE STRING "" FORCE) 