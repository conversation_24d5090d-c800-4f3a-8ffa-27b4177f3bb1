/**
 * @file test_uds_polling_mechanism.c
 * @brief UDS轮询机制和事件处理重构测试
 * <AUTHOR>
 * @date 2025/1/17
 * @copyright Copyright (c) 2025
 */

#include "es_uds_client.h"
#include "es_coro.h"
#include "es_log.h"
#include <stdio.h>
#include <assert.h>
#include <string.h>

#define TAG "TEST_UDS_POLLING"

// 测试统计
static int test_passed = 0;
static int test_failed = 0;

// 测试宏
#define TEST_ASSERT(condition, message) \
    do { \
        if (condition) { \
            test_passed++; \
            printf("✓ PASS: %s\n", message); \
        } else { \
            test_failed++; \
            printf("✗ FAIL: %s\n", message); \
        } \
    } while(0)

#define TEST_ASSERT_NOT_NULL(ptr, message) TEST_ASSERT((ptr) != NULL, message)
#define TEST_ASSERT_EQUAL(expected, actual, message) \
    TEST_ASSERT((expected) == (actual), message)

// 模拟ISO-TP连接和数据
static es_isotp_connection_t mock_isotp_conn = {0};
static uint8_t mock_rx_data[256] = {0};
static uint16_t mock_rx_length = 0;
static bool mock_data_available = false;

// 模拟ISO-TP获取接收数据函数
es_isotp_error_t es_isotp_get_received_data(es_isotp_connection_t *conn,
                                           uint8_t *data,
                                           uint16_t max_length,
                                           uint16_t *actual_length)
{
    if (conn == NULL || data == NULL || actual_length == NULL) {
        return ES_ISOTP_ERR_INVALID_PARAM;
    }

    if (!mock_data_available || mock_rx_length == 0) {
        *actual_length = 0;
        return ES_ISOTP_ERR_NO_DATA;
    }

    if (max_length < mock_rx_length) {
        return ES_ISOTP_ERR_BUFFER_TOO_SMALL;
    }

    memcpy(data, mock_rx_data, mock_rx_length);
    *actual_length = mock_rx_length;

    // 清除模拟数据（模拟一次性读取）
    mock_data_available = false;
    mock_rx_length = 0;

    return ES_ISOTP_OK;
}

// 设置模拟接收数据
static void set_mock_rx_data(const uint8_t *data, uint16_t length)
{
    if (length <= sizeof(mock_rx_data)) {
        memcpy(mock_rx_data, data, length);
        mock_rx_length = length;
        mock_data_available = true;
    }
}

/**
 * @brief 测试ISO-TP轮询API
 */
static void test_isotp_polling_api(void)
{
    printf("\n--- Testing ISO-TP Polling API ---\n");
    
    es_isotp_connection_t conn = {0};
    uint8_t buffer[64];
    uint16_t length;
    
    // 测试无效参数
    es_isotp_error_t result = es_isotp_get_received_data(NULL, buffer, sizeof(buffer), &length);
    TEST_ASSERT_EQUAL(ES_ISOTP_ERR_INVALID_PARAM, result, "NULL connection should return error");
    
    result = es_isotp_get_received_data(&conn, NULL, sizeof(buffer), &length);
    TEST_ASSERT_EQUAL(ES_ISOTP_ERR_INVALID_PARAM, result, "NULL buffer should return error");
    
    result = es_isotp_get_received_data(&conn, buffer, sizeof(buffer), NULL);
    TEST_ASSERT_EQUAL(ES_ISOTP_ERR_INVALID_PARAM, result, "NULL length should return error");
    
    // 测试无数据情况
    mock_data_available = false;
    result = es_isotp_get_received_data(&conn, buffer, sizeof(buffer), &length);
    TEST_ASSERT_EQUAL(ES_ISOTP_ERR_NO_DATA, result, "No data should return ES_ISOTP_ERR_NO_DATA");
    
    // 测试有数据情况
    uint8_t test_data[] = {0x50, 0x01, 0x02, 0x03};
    set_mock_rx_data(test_data, sizeof(test_data));
    
    result = es_isotp_get_received_data(&conn, buffer, sizeof(buffer), &length);
    TEST_ASSERT_EQUAL(ES_ISOTP_OK, result, "Valid data should return ES_ISOTP_OK");
    TEST_ASSERT_EQUAL(sizeof(test_data), length, "Length should match test data");
    TEST_ASSERT_EQUAL(0, memcmp(buffer, test_data, length), "Data should match test data");
    
    // 测试缓冲区太小
    uint8_t large_data[100];
    memset(large_data, 0xAA, sizeof(large_data));
    set_mock_rx_data(large_data, sizeof(large_data));
    
    uint8_t small_buffer[10];
    result = es_isotp_get_received_data(&conn, small_buffer, sizeof(small_buffer), &length);
    TEST_ASSERT_EQUAL(ES_ISOTP_ERR_BUFFER_TOO_SMALL, result, "Small buffer should return error");
}

// 测试UDS轮询的协程
static es_async_t test_uds_polling_coro(es_coro_t *coro, int *result)
{
    es_co_begin(coro);
    
    // 创建UDS客户端连接
    es_uds_client_connection_t conn = {0};
    conn.isotp_conn = &mock_isotp_conn;
    
    // 设置缓冲区
    uint8_t tx_buffer[64];
    uint8_t rx_buffer[64];
    conn.tx_buffer = tx_buffer;
    conn.tx_buffer_size = sizeof(tx_buffer);
    conn.rx_buffer = rx_buffer;
    conn.rx_buffer_size = sizeof(rx_buffer);
    
    // 设置模拟正响应数据
    uint8_t positive_response[] = {0x50, 0x01}; // 诊断会话控制正响应
    set_mock_rx_data(positive_response, sizeof(positive_response));
    
    // 调用轮询函数
    es_co_await_ex(err, es_uds_client_poll_isotp_data, &conn);
    
    // 验证响应被正确处理
    if (conn.state == ES_UDS_CLIENT_STATE_RESPONSE_COMPLETE) {
        *result = 1; // 成功
    } else {
        *result = 0; // 失败
    }
    
    es_co_end;
    
err:
    *result = 0; // 错误
    es_co_err;
}

/**
 * @brief 测试UDS客户端轮询机制
 */
static void test_uds_client_polling(void)
{
    printf("\n--- Testing UDS Client Polling Mechanism ---\n");
    
    es_coro_t coro = {0};
    int result = 0;
    es_async_t status;
    
    // 运行测试协程
    do {
        status = test_uds_polling_coro(&coro, &result);
    } while (status == ES_ASYNC_YIELD || status == ES_ASYNC_WAIT);
    
    // 验证结果
    TEST_ASSERT_EQUAL(1, result, "UDS polling should work correctly");
}

/**
 * @brief 测试0x78响应挂起处理
 */
static void test_response_pending_handling(void)
{
    printf("\n--- Testing 0x78 Response Pending Handling ---\n");
    
    es_uds_client_connection_t conn = {0};
    conn.state = ES_UDS_CLIENT_STATE_WAITING_RESPONSE;
    conn.current_request.service_id = ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL;
    conn.p2_timeout = 1000;
    conn.p2_star_timeout = 5000;
    conn.max_response_pending = 10;
    
    // 设置缓冲区
    uint8_t rx_buffer[64];
    conn.rx_buffer = rx_buffer;
    conn.rx_buffer_size = sizeof(rx_buffer);
    
    // 测试0x78响应挂起
    uint8_t response_pending[] = {0x7F, ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL, 0x78};
    
    es_uds_client_process_response(&conn, response_pending, sizeof(response_pending));
    
    // 验证状态转换
    TEST_ASSERT_EQUAL(ES_UDS_CLIENT_STATE_WAITING_RESPONSE_PENDING, conn.state, 
                     "State should be WAITING_RESPONSE_PENDING after 0x78");
    TEST_ASSERT_EQUAL(1, conn.response_pending_count, "Response pending count should be 1");
    TEST_ASSERT_EQUAL(conn.p2_star_timeout, conn.current_timeout, "Timeout should switch to P2*");
    
    // 测试最终正响应
    uint8_t final_response[] = {0x50, 0x01}; // 正响应
    es_uds_client_process_response(&conn, final_response, sizeof(final_response));
    
    TEST_ASSERT_EQUAL(ES_UDS_CLIENT_STATE_RESPONSE_COMPLETE, conn.state, 
                     "State should be RESPONSE_COMPLETE after final response");
    TEST_ASSERT_EQUAL(0, conn.response_pending_count, "Response pending count should be reset");
}

/**
 * @brief 测试负响应处理
 */
static void test_negative_response_handling(void)
{
    printf("\n--- Testing Negative Response Handling ---\n");
    
    es_uds_client_connection_t conn = {0};
    conn.state = ES_UDS_CLIENT_STATE_WAITING_RESPONSE;
    conn.current_request.service_id = ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL;
    
    // 设置缓冲区
    uint8_t rx_buffer[64];
    conn.rx_buffer = rx_buffer;
    conn.rx_buffer_size = sizeof(rx_buffer);
    
    // 测试一般负响应（非0x78）
    uint8_t negative_response[] = {0x7F, ES_UDS_SID_DIAGNOSTIC_SESSION_CONTROL, 0x22}; // 条件不正确
    
    es_uds_client_process_response(&conn, negative_response, sizeof(negative_response));
    
    // 验证状态转换
    TEST_ASSERT_EQUAL(ES_UDS_CLIENT_STATE_ERROR_NEGATIVE_RESPONSE, conn.state, 
                     "State should be ERROR_NEGATIVE_RESPONSE");
    TEST_ASSERT_EQUAL(0x22, conn.last_negative_response_code, "NRC should be stored");
}

/**
 * @brief 运行所有测试
 */
void run_uds_polling_mechanism_tests(void)
{
    printf("=== UDS Polling Mechanism Tests ===\n");
    
    test_isotp_polling_api();
    test_uds_client_polling();
    test_response_pending_handling();
    test_negative_response_handling();
    
    printf("\n=== Test Results ===\n");
    printf("Passed: %d\n", test_passed);
    printf("Failed: %d\n", test_failed);
    printf("Total:  %d\n", test_passed + test_failed);
    
    if (test_failed == 0) {
        printf("✓ All tests passed!\n");
    } else {
        printf("✗ Some tests failed!\n");
    }
}

/**
 * @brief 主测试函数
 */
int main(void)
{
    // 初始化日志系统
    es_log_init();
    
    // 运行测试
    run_uds_polling_mechanism_tests();
    
    return test_failed;
}
