﻿#ifdef _WIN32

#include "es_drv_os.h"
#include <windows.h>
#include <time.h>  /* 添加时间相关头文件 */
#include <stdio.h>  /* 添加stdio.h来提供snprintf */

#if defined(_MSC_VER) && _MSC_VER < 1900
#define snprintf _snprintf
#endif

static LARGE_INTEGER s_freq;
static LARGE_INTEGER s_start;
static uint8_t s_init = 0;

// 设置当前时间戳（从1970-01-01 00:00:00开始的秒数）
void es_set_timestamp(uint32_t timestamp) {
    //do nothing
}

// 获取当前时间戳（原子读取）
uint32_t es_get_timestamp(void) {
    // get current time
    time_t now;
    time(&now);
    return (uint32_t)now + 8 * 3600; // UTC+8
}

void es_os_make_secret_key(void) {
    //do nothing
}

/**
 * @brief 初始化操作系统相关功能
 * @return int 0表示成功，非0表示失败
 */
int es_os_init(void)
{
    if (!s_init) {
        if (!QueryPerformanceFrequency(&s_freq)) {
            return -1;
        }
        QueryPerformanceCounter(&s_start);
        s_init = 1;
    }
    return 0;
}

static void es_os_win_init(void)
{
    if (!s_init) {
        es_os_init();
    }
}

uint32_t es_os_get_tick_ms(void)
{
    LARGE_INTEGER current;
    
    es_os_win_init();
    QueryPerformanceCounter(&current);
    
    return (uint32_t)((current.QuadPart - s_start.QuadPart) * 1000 / s_freq.QuadPart);
}

uint64_t es_os_get_tick_us(void)
{
    LARGE_INTEGER current;
    
    es_os_win_init();
    QueryPerformanceCounter(&current);
    
    return (uint64_t)((current.QuadPart - s_start.QuadPart) * 1000000 / s_freq.QuadPart);
}

void es_os_feed_wdt(void)
{
    /* Windows平台下无需实现喂狗 */
}

void es_os_msleep(uint32_t ms)
{
    Sleep(ms);
}

void es_os_us_delay(uint32_t us)
{
    Sleep(us / 1000);
}

void es_os_reboot(void) {
    //do nothing
}

#endif /* _WIN32 */ 