﻿/*
 * es_ota.c
 *
 * OTA module implementation
 */

#include "es_ota.h"
#include "es_flash.h"
#include "es_at_srv.h"
#include "es_log.h"
#include "es_coro.h"
#include "es_drv_os.h"
#include "es_ringbuffer.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdint.h>
#include "es_mem.h"
#include "es_scheduler.h"
#include "es_pm.h"

#define TAG "OTA"

/**
 * @brief Read firmware information
 *
 * @param fw_info Firmware information structure pointer
 * @return int 0 indicates success, -1 indicates failure
 */
int es_ota_read_fw_info(struct fw_info_t *fw_info) {
    const es_partition_t *download_part = es_partition_find("download");
    if(download_part == NULL) {
        return -1;
    }

    if(es_flash_read(download_part, 0, (uint8_t *)fw_info, sizeof(struct fw_info_t)) < 0) {
        return -1;
    }

    // check magic
    if(fw_info->magic != APP_FKG_MAGIC && fw_info->magic != BOOT_FKG_MAGIC && 
       fw_info->magic != ECU_FKG_MAGIC && fw_info->magic != BLE_FKG_MAGIC) {
        ES_LOGE(TAG, "magic check failed");
        return -1;
    }

    // check crc
    if(fw_info->hdr_crc != es_crc32(DEF_CRC_INIT_VALUE, (uint8_t *)fw_info, sizeof(struct fw_info_t) - 4)) {
        ES_LOGE(TAG, "crc check failed");
        return -1;
    }

    // check size
    if(fw_info->size > download_part->size - sizeof(struct fw_info_t) - sizeof(uint32_t)) {
        ES_LOGE(TAG, "size check failed");
        return -1;
    }

    //check random
    uint32_t random = 0;
    if(es_flash_read(download_part, fw_info->size + sizeof(struct fw_info_t), (uint8_t *)&random, sizeof(uint32_t)) < 0) {
        return -1;
    }

    if(random != fw_info->random) {
        ES_LOGE(TAG, "random check failed");
        return -1;
    }

    return 0;
}

/**
 * @brief Check if downloaded firmware is valid
 *
 * @return int 0 indicates valid, -1 indicates invalid
 */
int es_ota_download_fw_valid(void) {
    const es_partition_t *download_part = es_partition_find("download");
    if(download_part == NULL) {
        ES_LOGE(TAG, "download partition not found");
        return -1;
    }

    struct fw_info_t fw_info = {0};
    if(es_ota_read_fw_info(&fw_info) != 0) {
        ES_LOGE(TAG, "read fw info failed");
        return -1;
    }

    // check data crc
    uint32_t crc = DEF_CRC_INIT_VALUE;
    uint8_t buf[1024] = {0};
    int offset = sizeof(struct fw_info_t);
    int len = (int)fw_info.size;
    int read_len = 0;
    
    while(len > 0) {
        read_len = len > sizeof(buf) ? sizeof(buf) : len;
        if(es_flash_read(download_part, offset + fw_info.size - len, buf, read_len) < 0) {
            ES_LOGE(TAG, "read partition failed");
            return -1;
        }
        
        crc = es_crc32(crc, buf, read_len);
        
        len -= read_len;
        
        // feed watchdog
        es_os_feed_wdt();
    }

    if(crc != fw_info.crc) {
        ES_LOGE(TAG, "firmware crc check failed");
        return -1;
    }

    return 0;
}

typedef struct {
    uint32_t len;
    uint32_t max_block;
    const es_partition_t *partition;
    uint32_t cur;
    uint32_t offset;
    uint16_t read_len;
} at_ota_ctx_t;

// OTA start command handler function
es_async_t at_srv_cmd_ota_start_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    #define OTA_MAX_BLOCK_SIZE 512

    static at_ota_ctx_t *ota_ctx = NULL;
    static uint8_t buffer_data[OTA_MAX_BLOCK_SIZE+4] = {0}; // 4 bytes crc

    es_co_begin(coro);

    // Allocate memory
    if (ota_ctx == NULL) {
        ota_ctx = (at_ota_ctx_t *)es_mem_alloc(sizeof(at_ota_ctx_t));
        if (ota_ctx == NULL) {
            ES_LOGE(TAG, "Failed to allocate memory for OTA context");
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
            es_co_exit;
        }
        memset(ota_ctx, 0, sizeof(at_ota_ctx_t));
    }

    if (cmd_type == ES_AT_SRV_CMD_SET)
    {
        if (ctx->param_count < 3) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            goto cleanup;
        }
        
        // Parse length
        ota_ctx->len = strtol(ctx->params[1].param, NULL, 16);

        // Parse CRC32
        // crc32 = strtol(ctx->params[2].param, NULL, 16);

        // Optional parameter: maximum block size
        ota_ctx->max_block = 0;
        if (ctx->param_count >= 4) {
            ota_ctx->max_block = strtol(ctx->params[3].param, NULL, 10);
        }

        // Check parameter validity
        if (ota_ctx->len <= 0) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            goto cleanup;
        }

        // Set maximum block size
        if (ota_ctx->max_block <= 0) {
            ota_ctx->max_block = OTA_MAX_BLOCK_SIZE;
        }
        else {
            ota_ctx->max_block = ota_ctx->max_block > OTA_MAX_BLOCK_SIZE ? OTA_MAX_BLOCK_SIZE : ota_ctx->max_block;
        }

        // Find partition
        ota_ctx->partition = es_partition_find(ctx->params[0].param);
        if (ota_ctx->partition == NULL) {
            ES_LOGE(TAG, "Partition %s not found", ctx->params[0].param);
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            goto cleanup;
        }

        // Check if length exceeds partition size
        if (ota_ctx->len > ota_ctx->partition->size) {
            ES_LOGE(TAG, "Data length %d exceeds partition size %lu", ota_ctx->len, ota_ctx->partition->size);
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            goto cleanup;
        }

        // Erase partition
        if (es_flash_erase(ota_ctx->partition, 0, ota_ctx->len) < 0) {
            ES_LOGE(TAG, "Erase partition failed");
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
            goto cleanup;
        }

        coro->cnt = 0;
        ota_ctx->offset = 0;

        es_pm_set_task_enable(false);
        es_log_set_enabled(false);

        while (ota_ctx->len > 0) {
            ota_ctx->read_len = ota_ctx->len > ota_ctx->max_block ? ota_ctx->max_block : ota_ctx->len;

            // Send data request
            es_at_srv_fmt_send("+OTAREQ:%d,%d\r\n", ota_ctx->offset, ota_ctx->read_len);
            ota_ctx->cur = 0;

            while (ota_ctx->cur < ota_ctx->read_len + 4) {
                es_co_wait_timeout_ex(err, es_at_srv_read_data(buffer_data + ota_ctx->cur, 1) == 1, 3000);
                ota_ctx->cur += 1;
            }

            // Calculate CRC32 checksum
            const uint8_t *temp = buffer_data + ota_ctx->read_len;
            uint32_t crc_recv = (temp[0] << 24) | (temp[1] << 16) | (temp[2] << 8) | temp[3];
            uint32_t crc = es_crc32(DEF_CRC_INIT_VALUE, buffer_data, ota_ctx->read_len);

            ES_CHECK(err, crc != crc_recv, "CRC32 ERROR");
            ES_CHECK(err, es_flash_write(ota_ctx->partition, ota_ctx->offset, buffer_data, ota_ctx->read_len) < 0, "Write partition error");

            ota_ctx->offset += ota_ctx->read_len;
            ota_ctx->len -= ota_ctx->read_len;
            coro->cnt = 0;
            continue;

            err:
            if (++coro->cnt >= 3) {
                es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
                goto cleanup;
            }
        }
    } else if (cmd_type == ES_AT_SRV_CMD_QUERY) {
        // Query OTA status
        static uint32_t ota_status = 0;
        if(es_ota_download_fw_valid() == 0) {
            ota_status = 1;
        } else {
            ota_status = 0;
        }
        es_at_srv_fmt_send("+OTASTAT:%d\r\n", ota_status);
    }

    // Send success response
    es_at_srv_send_ok();

cleanup:
    // Free memory
    es_mem_free_safe((void **)&ota_ctx);
    es_pm_set_task_enable(true);
    es_log_set_enabled(true);

    es_co_end;
}

// Register AT command
int es_ota_init(void)
{
    // Initialize ring buffer
 
    ES_LOGI(TAG, "OTA module initialized");
    return 0;
} 
