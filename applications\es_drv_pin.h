#ifndef __ES_DRV_PIN_H__
#define __ES_DRV_PIN_H__

#include <stdint.h>

#define PIN_LOW                 0x00
#define PIN_HIGH                0x01

#define PIN_MODE_OUTPUT         0x00
#define PIN_MODE_INPUT          0x01
#define PIN_MODE_INPUT_PULLUP   0x02
#define PIN_MODE_INPUT_PULLDOWN 0x03
#define PIN_MODE_OUTPUT_OD      0x04

#define PIN_IRQ_MODE_RISING             0x00
#define PIN_IRQ_MODE_FALLING            0x01
#define PIN_IRQ_MODE_RISING_FALLING     0x02
#define PIN_IRQ_MODE_HIGH_LEVEL         0x03
#define PIN_IRQ_MODE_LOW_LEVEL          0x04

#define PIN_IRQ_DISABLE                 0x00
#define PIN_IRQ_ENABLE                  0x01

#define PIN_IRQ_PIN_NONE                -1

int es_pin_init(void);
void es_pin_mode(uint16_t pin, uint8_t mode);
void es_pin_write(uint16_t pin, uint8_t value);
int es_pin_read(uint16_t pin);
int es_pin_attach_irq(uint16_t pin, uint16_t mode, void (*hdr)(void *args), void *args);
int es_pin_detach_irq(uint16_t pin);
int es_pin_irq_enable(uint16_t pin, uint8_t enabled);

#endif /* __ES_DRV_PIN_H__ */ 
