@echo off
setlocal enabledelayedexpansion

:: 检查VS2022环境
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo find Visual Studio 2022...
    
    :: 检查VS2022安装路径
    set "VS2022_PATH="
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" (
        set "VS2022_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvarsall.bat" (
        set "VS2022_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvarsall.bat" (
        set "VS2022_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise"
    )
    
    if not defined VS2022_PATH (
        echo error: not found Visual Studio 2022 installation!
        echo please install Visual Studio 2022 or manually initialize the development environment and then run this script.
        exit /b 1
    )
    
    echo find Visual Studio 2022: !VS2022_PATH!
    echo initialize VS2022 development environment...
    call "!VS2022_PATH!\VC\Auxiliary\Build\vcvarsall.bat" x64
    echo VS2022 environment has been loaded
) else (
    echo VS development environment has been configured
)

:: 检查CMake
where cmake >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo error: not found CMake! please install CMake and ensure it is in the PATH.
    exit /b 1
)

:: 如果没有参数，显示选择菜单
if "%1"=="" (
    echo ===============================
    echo    select build type
    echo ===============================
    echo [1] Debug build - debug version
    echo [2] Release build - release version
    echo [3] MinSizeRel build - minimum volume version
    echo [4] RelWithDebInfo build - release version with debug information
    echo ===============================
    
    set /p BUILD_CHOICE="please input the choice (1-4): "
) else (
    set BUILD_CHOICE=%1
)

:: 根据选择设置构建类型
if "%BUILD_CHOICE%"=="1" (
    set BUILD_TYPE=Debug
) else if "%BUILD_CHOICE%"=="2" (
    set BUILD_TYPE=Release
) else if "%BUILD_CHOICE%"=="3" (
    set BUILD_TYPE=MinSizeRel
) else if "%BUILD_CHOICE%"=="4" (
    set BUILD_TYPE=RelWithDebInfo
) else (
    echo invalid choice!
    exit /b 1
)

:: 设置构建目录
set BUILD_DIR=build_%BUILD_TYPE%

:: 清理之前的构建目录
if exist %BUILD_DIR% (
    echo clean previous build...
    rd /s /q %BUILD_DIR%
)
mkdir %BUILD_DIR%

echo.
echo start %BUILD_TYPE% build...
echo build directory: %BUILD_DIR%
echo.

:: 进入构建目录并运行 CMake
cd %BUILD_DIR%

:: 使用正确的 CMake 参数设置构建类型
echo run CMake, build type: %BUILD_TYPE%
cmake -G "Visual Studio 17 2022" ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ^
    ..

:: 如果 CMake 配置成功，开始构建
if %ERRORLEVEL%==0 (
    echo.
    echo use %BUILD_TYPE% to configure build...
    cmake --build . --config %BUILD_TYPE%
    if %ERRORLEVEL%==0 (
        echo.
        echo ✅ build successfully!
        echo.
        if exist %BUILD_TYPE%\win32_app.exe (
            echo the program has been generated in: %BUILD_DIR%\%BUILD_TYPE%\win32_app.exe
            
            :: 复制可执行文件到根目录方便访问
            echo copy the executable file to the current directory...
            copy %BUILD_TYPE%\win32_app.exe ..\win32_app_%BUILD_TYPE%.exe
            echo the executable file has been copied to: ..\win32_app_%BUILD_TYPE%.exe
        ) else (
            echo ❌ warning: the executable file is not generated!
        )
    ) else (
        echo.
        echo ❌ build failed!
    )
) else (
    echo.
    echo ❌ CMake configuration failed!
)

cd ..

:: 运行选项
if %ERRORLEVEL%==0 (
    if exist %BUILD_DIR%\%BUILD_TYPE%\win32_app.exe (
        echo.
        echo ===============================
        echo    whether to run the generated program?
        echo ===============================
        echo [1] yes - run the program
        echo [2] no - exit
        echo ===============================
        
        set /p RUN_CHOICE="请输入选择 (1-2): "
        
        if "%RUN_CHOICE%"=="1" (
            echo run the program...
            %BUILD_DIR%\%BUILD_TYPE%\win32_app.exe
        ) else (
            echo build completed, the program is not running.
        )
    )
)

echo.
echo build process completed.
echo. 