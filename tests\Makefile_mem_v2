# Makefile for Memory V2 Test
# Usage: make -f Makefile_mem_v2

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g -O0
INCLUDES = -I../applications
TARGET = test_mem_v2_basic
SOURCES = test_mem_v2_basic.c ../applications/es_mem_v2.c

# Default target
all: $(TARGET)

# Build the test executable
$(TARGET): $(SOURCES)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET) $(SOURCES)

# Run the test
test: $(TARGET)
	./$(TARGET)

# Clean build artifacts
clean:
	rm -f $(TARGET)

# Debug build
debug: CFLAGS += -DDEBUG -DES_MEM_V2_DEBUG
debug: $(TARGET)

.PHONY: all test clean debug
