# Makefile for Ring Buffer Interrupt Safety Tests
# 环形缓冲区中断安全性测试编译文件

# 编译器设置
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g -O0 -DTEST_BUILD
INCLUDES = -I../applications -I../include -I.

# 源文件
RING_SOURCES = ../applications/es_ringobj.c ../applications/es_ringbuffer.c
TEST_SOURCES = test_ringbuffer_interrupt_safety.c
ALL_SOURCES = $(RING_SOURCES) $(TEST_SOURCES)

# 目标文件
TARGET = test_ringbuffer_interrupt_safety

# 默认目标
all: $(TARGET)

# 编译测试程序
$(TARGET): $(ALL_SOURCES)
	$(CC) $(CFLAGS) $(INCLUDES) -o $@ $^

# 运行测试
test: $(TARGET)
	./$(TARGET)

# 清理
clean:
	rm -f $(TARGET) *.o

# 显示帮助
help:
	@echo "可用目标："
	@echo "  all    - 编译测试程序"
	@echo "  test   - 编译并运行测试"
	@echo "  clean  - 清理生成的文件"
	@echo "  help   - 显示此帮助信息"

.PHONY: all test clean help
