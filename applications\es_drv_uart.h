/**
 * @file es_drv_uart.h
 * @brief UART Operation Abstraction Layer
 */

#ifndef __ES_DRV_UART_H__
#define __ES_DRV_UART_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "es_coro.h"

/**
 * @brief UART device handle type
 */
typedef void* es_uart_dev_t;

/**
 * @brief UART configuration structure
 */
typedef struct {
    const char* name;      /**< UART name */
    uint32_t baud_rate;    /**< Baud rate */
    uint8_t data_bits;     /**< Data bits */
    uint8_t stop_bits;     /**< Stop bits */
    uint8_t parity;        /**< Parity */
    uint8_t flow_control;  /**< Flow control */
} es_uart_config_t;

/**
 * @brief UART parity definitions
 */
enum es_uart_parity {
    ES_UART_PARITY_NONE,   /**< No parity */
    ES_UART_PARITY_ODD,    /**< Odd parity */
    ES_UART_PARITY_EVEN    /**< Even parity */
};

/**
 * @brief UART stop bits definitions
 */
enum es_uart_stop_bits {
    ES_UART_STOP_BITS_1,   /**< 1 stop bit */
    ES_UART_STOP_BITS_2    /**< 2 stop bits */
};

/**
 * @brief UART flow control definitions
 */
enum es_uart_flow_control {
    ES_UART_FLOW_CONTROL_NONE,     /**< No flow control */
    ES_UART_FLOW_CONTROL_RTS,      /**< RTS flow control */
    ES_UART_FLOW_CONTROL_CTS,      /**< CTS flow control */
    ES_UART_FLOW_CONTROL_RTS_CTS   /**< RTS/CTS flow control */
};

/**
 * @brief Open UART device
 * @param name Device name
 * @param config UART configuration
 * @return UART device handle, NULL indicates failure
 */
es_uart_dev_t es_uart_open(const es_uart_config_t *config);

/**
 * @brief Close UART device
 * @param dev UART device handle
 * @return 0 for success, -1 for failure
 */
int es_uart_close(es_uart_dev_t dev);

/**
 * @brief Read data from UART
 * @param dev UART device handle
 * @param buf Data buffer
 * @param size Number of bytes to read
 * @return Number of bytes actually read, -1 indicates failure
 */
int es_uart_read(es_uart_dev_t dev, void *buf, size_t size);

/**
 * @brief Write data to UART
 * @param dev UART device handle
 * @param buf Data buffer
 * @param size Number of bytes to write
 * @return Number of bytes actually written, -1 indicates failure
 */
int es_uart_write(es_uart_dev_t dev, const void *buf, size_t size);

/**
 * @brief Check if UART has data available to read
 * @param dev UART device handle
 * @param timeout_ms Timeout in milliseconds, 0 means no wait
 * @return Number of bytes available, 0 means no data, -1 indicates failure
 */
int es_uart_available(es_uart_dev_t dev, uint32_t timeout_ms);


es_async_t es_uart_coro_write(es_coro_t *coro, es_uart_dev_t dev, const uint8_t *buf, int size);


/**
 * @brief Flush UART buffer
 * @param dev UART device handle
 * @return 0 for success, -1 for failure
 */
int es_uart_flush(es_uart_dev_t dev);

#ifdef __cplusplus
}
#endif

#endif /* __ES_DRV_UART_H__ */ 
