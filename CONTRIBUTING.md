# Contributing to ES MCU Framework

感谢您对 ES MCU Framework 的贡献兴趣！我们欢迎各种形式的贡献，包括但不限于：

- 🐛 Bug 报告和修复
- ✨ 新功能建议和实现
- 📚 文档改进
- 🧪 测试用例添加
- 🎨 代码优化和重构

## 🚀 快速开始

### 开发环境准备

1. **克隆仓库**
   ```bash
   git clone https://github.com/your-org/es-mcu-framework.git
   cd es-mcu-framework
   ```

2. **安装依赖**
   - **硬件**: HC32F460 开发板
   - **工具链**: ARM GCC / Keil MDK / IAR EWARM
   - **构建工具**: SCons / CMake
   - **调试器**: J-Link / ST-Link

3. **构建项目**
   ```bash
   # HC32F460 平台
   scons
   
   # Win32 仿真平台
   cd platform/win32
   build.bat
   ```

## 📋 贡献流程

### 1. 报告问题

如果您发现了 bug 或想要建议新功能，请：

1. 检查 [Issues](https://github.com/your-org/es-mcu-framework/issues) 确认问题未被报告
2. 使用合适的 Issue 模板创建新的 Issue
3. 提供详细的问题描述和复现步骤

### 2. 提交代码

1. **Fork 项目**
   ```bash
   # 在 GitHub 上 Fork 项目，然后克隆您的 Fork
   git clone https://github.com/your-username/es-mcu-framework.git
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   # 或
   git checkout -b bugfix/issue-number
   ```

3. **进行开发**
   - 遵循项目的编码规范
   - 添加必要的测试
   - 更新相关文档

4. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **创建 Pull Request**
   - 在 GitHub 上创建 PR
   - 填写 PR 模板
   - 等待代码审查

## 📝 编码规范

### C 代码规范

1. **命名约定**
   ```c
   // 文件名：使用下划线分隔
   es_scheduler.h
   es_scheduler.c
   
   // 函数名：使用下划线分隔
   es_scheduler_init()
   es_coro_task_add()
   
   // 变量名：使用下划线分隔
   uint32_t task_count;
   bool is_initialized;
   
   // 宏定义：大写字母 + 下划线
   #define ES_SCHEDULER_MAX_TASKS  64
   #define ES_CORO_BEGIN(coro)     ...
   
   // 结构体：下划线分隔 + _t 后缀
   typedef struct es_scheduler es_scheduler_t;
   ```

2. **注释规范**
   ```c
   /**
    * @brief 初始化调度器
    * @param scheduler 调度器实例指针
    * @return 成功返回 0，失败返回错误码
    */
   int es_scheduler_init(es_scheduler_t *scheduler);
   ```

3. **代码格式**
   - 使用 4 个空格缩进
   - 大括号另起一行
   - 每行不超过 100 个字符
   - 使用 `.clang-format` 自动格式化

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型 (type):**
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更改
- `style`: 代码格式更改
- `refactor`: 重构代码
- `test`: 添加或修改测试
- `chore`: 构建过程或辅助工具的变动

**示例:**
```
feat(scheduler): add coroutine priority support

Add priority-based scheduling for coroutine tasks to improve
real-time response performance.

Closes #123
```

## 🧪 测试指南

### 单元测试

1. **添加测试**
   ```c
   // tests/unit/test_scheduler.c
   #include "unity.h"
   #include "es_scheduler.h"
   
   void test_scheduler_init(void) {
       es_scheduler_t scheduler;
       int result = es_scheduler_init(&scheduler);
       TEST_ASSERT_EQUAL(0, result);
   }
   ```

2. **运行测试**
   ```bash
   cd tests/unit
   make test
   ```

### 集成测试

1. **硬件在环测试**
   ```bash
   cd tests/integration
   python run_hardware_tests.py
   ```

2. **仿真测试**
   ```bash
   cd platform/win32
   ./run_simulation_tests.sh
   ```

## 📚 文档贡献

### 文档类型

1. **API 文档** - 在头文件中使用 Doxygen 注释
2. **用户指南** - `doc/` 目录下的 Markdown 文件
3. **开发指南** - 开发相关的技术文档
4. **示例代码** - `examples/` 目录下的示例

### 文档规范

- 使用 Markdown 格式
- 包含代码示例
- 添加图表和流程图（使用 Mermaid）
- 保持中英文对照

## 🏷️ 发布流程

### 版本号规范

使用 [Semantic Versioning](https://semver.org/):
- `MAJOR.MINOR.PATCH`
- 例如: `1.2.3`

### 发布检查清单

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] CHANGELOG.md 已更新
- [ ] 版本号已更新
- [ ] 标签已创建

## 📞 获取帮助

如果您在贡献过程中遇到问题，可以通过以下方式获取帮助：

- 📧 邮件: <EMAIL>
- 💬 讨论: [GitHub Discussions](https://github.com/your-org/es-mcu-framework/discussions)
- 📋 问题: [GitHub Issues](https://github.com/your-org/es-mcu-framework/issues)

## 📜 行为准则

请阅读我们的 [行为准则](CODE_OF_CONDUCT.md)，确保为所有参与者创造一个友好和包容的环境。

---

感谢您的贡献！🎉 