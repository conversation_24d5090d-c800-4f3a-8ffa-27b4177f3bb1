#include "hc32_ll_gpio.h"
#include "hc32_ll.h"
#include "hc32_ll_utility.h"
#include "hc32_ll_gpio.h"
#include "system_hc32f460.h"
#include "es_coro.h"
#include "es_at_srv.h"
#include "es_pm.h"
#include "hc32_ll_pwc.h"
#include "hc32_ll_def.h"
#include "hc32_ll_interrupts.h"
#include "hc32_pin.h"
#include "hc32_pm.h"

static uint32_t h32_pm_wuen = 0;

#define X(NAME, PIN, MODE, ISR_MODE, ACTIVE_LEVEL)              \
void NAME##_isr(void *args) {}
XPM_WKE_ITEMS
#undef X

const pm_en_cfg_t pm_en_pin_cfg[PM_EN_MAX] = {
#define X(NAME, PIN, MODE, ACTIVE_LEVEL, INIT_LEVEL) \
    [PM_EN_##NAME] = {.pin = (PIN), .mode = (MODE), .active_level = (ACTIVE_LEVEL), .init_level = (INIT_LEVEL)},
    XPM_EN_ITEMS
#undef X
};

const pm_wke_cfg_t pm_wke_pin_cfg[] = {
#define X(NAME, PIN, MODE, ISR_MODE, ACTIVE_LEVEL) \
    [PM_WKE_##NAME] = {.pin = (PIN), .mode = (MODE), .isr_mode = (ISR_MODE), .isr = NAME##_isr, .active_level = (ACTIVE_LEVEL)},
    XPM_WKE_ITEMS
#undef X
};

int hc32_pm_init(void)
{
    GPIO_SetDebugPort(GPIO_PIN_TDI | GPIO_PIN_TRST | GPIO_PIN_TDO, DISABLE);

    for (int i = 0; i < PM_EN_MAX; i++) {
        es_pin_mode(pm_en_pin_cfg[i].pin, pm_en_pin_cfg[i].mode);
        es_pin_write(pm_en_pin_cfg[i].pin, pm_en_pin_cfg[i].init_level);
    }

    DDL_DelayMS(500);
    hc32_pm_en_enable(PM_EN_BLE_NRST, 0);
    hc32_pm_en_enable(PM_EN_OUT_MDM_DTR, 0);

    for (int i = 0; i < sizeof(pm_wke_pin_cfg) / sizeof(pm_wke_pin_cfg[0]); ++i) {
        const pm_wke_cfg_t * const wke_cfg = &pm_wke_pin_cfg[i];
        if (wke_cfg->pin == PIN_IRQ_PIN_NONE) {
            continue;
        }
        es_pin_mode(wke_cfg->pin, wke_cfg->mode);
        es_pin_attach_irq(wke_cfg->pin, wke_cfg->isr_mode, wke_cfg->isr, NULL);
        es_pin_irq_enable(wke_cfg->pin, false);
    }

    return 0;
}

int hc32_pm_wke_enable(int wke_pin_type, int enable)
{
    if (wke_pin_type >= PM_WKE_MAX) {
        return -1;
    }
    const pm_wke_cfg_t *const wke_cfg = &pm_wke_pin_cfg[wke_pin_type];
    if (wke_cfg->pin == PIN_IRQ_PIN_NONE) {
        return -1;
    }
    
    if (enable) {
        es_pin_irq_enable(wke_cfg->pin, true);
        h32_pm_wuen |= GPIO_PIN(wke_cfg->pin);
    } else {
        es_pin_irq_enable(wke_cfg->pin, false);
        h32_pm_wuen &= ~GPIO_PIN(wke_cfg->pin);
    }
    return 0;
}

int hc32_pm_en_enable(int en_pin_type, int enable)
{
    const pm_en_cfg_t *const en_cfg = &pm_en_pin_cfg[en_pin_type];
    if (en_cfg->pin == PIN_IRQ_PIN_NONE) return -1;
    es_pin_write(en_cfg->pin, (enable != 0) == en_cfg->active_level);
    return 0;
}

int es_read_acc_state(void)
{
    //read the pin of acc
    return es_pin_read(pm_wke_pin_cfg[PM_WKE_ACC].pin) == PIN_LOW ? 1 : 0;
}


int es_pm_enter_lpm_stop(uint32_t wke_mask)
{
    hc32_pm_wke_enable(PM_WKE_ACC, 1);

    stc_pwc_stop_mode_config_t stop_cfg = {
            .u16Clock = PWC_STOP_CLK_KEEP,
            .u8StopDrv = PWC_STOP_DRV_HIGH,
            .u16FlashWait = PWC_STOP_FLASH_WAIT_ON,
    };
    (void)PWC_STOP_Config(&stop_cfg);

    if (PWC_PWRC2_DVS == (READ_REG8(CM_PWC->PWRC2) & PWC_PWRC2_DVS))
    {
        CLR_REG8_BIT(CM_PWC->PWRC1, PWC_PWRC1_STPDAS);
    }
    else
    {
        SET_REG8_BIT(CM_PWC->PWRC1, PWC_PWRC1_STPDAS);
    }

    INTC_WakeupSrcCmd(h32_pm_wuen, ENABLE);
    PWC_STOP_Enter(PWC_STOP_WFE_INT);
    INTC_WakeupSrcCmd(h32_pm_wuen, DISABLE);
    return 0;
}
