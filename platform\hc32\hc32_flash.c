/**
 * @file hc32_flash.c
 * @brief HC32F460 Flash 驱动实现
 */

#include "es_flash.h"
#include "hc32_ll_efm.h"
#include <string.h>

/* 定义 Flash 扇区大小 */
#define HC32_FLASH_SECTOR_SIZE     (8*1024)  /* 8KB */
#define HC32_FLASH_START_ADDR      (EFM_START_ADDR)
#define HC32_FLASH_END_ADDR        (EFM_END_ADDR)
#define HC32_FLASH_WRITE_GRAN      (4u)  /* 字写入粒度 */


/**
 * @brief 初始化 Flash
 * @return 0 成功，-1 失败
 */
static int es_hc32_flash_init(void)
{
    /* 设置 Flash 等待周期，根据系统时钟频率 */
    int32_t ret = EFM_SetWaitCycle(EFM_WAIT_CYCLE5);
    if (ret != LL_OK) {
        return -1;
    }
    
    /* 启用 Flash */
    EFM_Cmd(EFM_CHIP_ALL, ENABLE);
    
    /* 缓存使能 */
    EFM_CacheCmd(ENABLE);
    
    return 0;
}

/**
 * @brief 从 Flash 读取数据
 * @param addr 读取地址
 * @param data 数据缓冲区
 * @param len 数据长度
 * @return 0 成功，-1 失败
 */
static int es_hc32_flash_read(uint32_t addr, uint8_t *data, uint32_t len)
{
    int32_t ret;
    
    /* 地址范围检查 */
    if ((addr < HC32_FLASH_START_ADDR) || 
        ((addr + len) > (HC32_FLASH_END_ADDR + 1)) || 
        (data == NULL)) {
        return -1;
    }
    
    /* 调用 EFM 驱动读取数据 */
    ret = EFM_ReadByte(addr, data, len);
    if (ret != LL_OK) {
        return -1;
    }
    
    return len;
}

/**
 * @brief 向 Flash 写入数据
 * @param addr 写入地址
 * @param data 数据缓冲区
 * @param len 数据长度
 * @return 0 成功，-1 失败
 */
static int es_hc32_flash_write(uint32_t addr, const uint8_t *data, uint32_t len)
{
    int32_t ret;
    
    /* 地址范围检查 */
    if ((addr < HC32_FLASH_START_ADDR) || 
        ((addr + len) > (HC32_FLASH_END_ADDR + 1)) || 
        (data == NULL)) {
        return -1;
    }
    
    /* 检查是否字对齐 */
    if ((addr % HC32_FLASH_WRITE_GRAN) == 0 && (len % HC32_FLASH_WRITE_GRAN) == 0) {
        /* 使用序列编程模式效率更高 */
        ret = EFM_SequenceProgram(addr, (uint8_t *)data, len);
    } else {
        /* 使用标准编程模式 */
        ret = EFM_Program(addr, (uint8_t *)data, len);
    }
    
    if (ret != LL_OK) {
        return -1;
    }
    
    return len;
}

/**
 * @brief 擦除 Flash 扇区
 * @param sector_addr 扇区地址
 * @return 0 成功，-1 失败
 */
static int es_hc32_flash_erase_sector(uint32_t sector_addr)
{
    int32_t ret;
    
    /* 检查扇区地址是否对齐 */
    if ((sector_addr % HC32_FLASH_SECTOR_SIZE) != 0U ||
        (sector_addr < HC32_FLASH_START_ADDR) ||
        (sector_addr > HC32_FLASH_END_ADDR)) {
        return -1;
    }
    
    /* 调用 EFM 驱动擦除扇区 */
    ret = EFM_SectorErase(sector_addr);
    if (ret != LL_OK) {
        return -1;
    }
    
    return 0;
}

/* HC32F460 Flash 设备实例 */
const es_flash_dev_t hc32_flash_dev = {
    .name = "hc32_flash",
    .start_addr = HC32_FLASH_START_ADDR,
    .size = (HC32_FLASH_END_ADDR - HC32_FLASH_START_ADDR + 1),
    .sector_size = HC32_FLASH_SECTOR_SIZE,
    .write_gran = HC32_FLASH_WRITE_GRAN,
    
    .init = es_hc32_flash_init,
    .read = es_hc32_flash_read,
    .write = es_hc32_flash_write,
    .erase_sector = es_hc32_flash_erase_sector
};
