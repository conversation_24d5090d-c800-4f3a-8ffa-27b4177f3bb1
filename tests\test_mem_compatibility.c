/**
 * @file test_mem_compatibility.c
 * @brief Test compatibility layer for es_mem.h interface
 * <AUTHOR> Assistant
 * @date 2025/1/21
 */

#include <stdio.h>
#include <string.h>
#include <assert.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdbool.h>
#include <stdlib.h>
#include "../applications/es_mem.h"  // This now includes V2 implementation with compatibility layer

// Simple log implementation for testing
void es_log_write(int level, const char *tag, bool to_flash, const char *fmt, ...) {
    va_list args;
    va_start(args, fmt);
    printf("[%s] ", tag);
    vprintf(fmt, args);
    printf("\n");
    va_end(args);
}

static void test_basic_compatibility(void) {
    printf("\n=== Testing Basic Compatibility Interface ===\n");
    
    // Test basic allocation using old interface
    void *ptr1 = es_mem_alloc(64);
    assert(ptr1 != NULL);
    printf("✓ es_mem_alloc(64) succeeded: %p\n", ptr1);
    
    void *ptr2 = es_mem_alloc(128);
    assert(ptr2 != NULL);
    printf("✓ es_mem_alloc(128) succeeded: %p\n", ptr2);
    
    void *ptr3 = es_mem_alloc(256);
    assert(ptr3 != NULL);
    printf("✓ es_mem_alloc(256) succeeded: %p\n", ptr3);
    
    // Test writing data
    strcpy((char*)ptr1, "Hello");
    strcpy((char*)ptr2, "Memory");
    strcpy((char*)ptr3, "Compatibility");
    
    printf("✓ Data written: '%s', '%s', '%s'\n", 
           (char*)ptr1, (char*)ptr2, (char*)ptr3);
    
    // Test block size function
    int size1 = es_mem_ptr_block_size(ptr1);
    int size2 = es_mem_ptr_block_size(ptr2);
    int size3 = es_mem_ptr_block_size(ptr3);
    
    printf("✓ Block sizes: %d, %d, %d\n", size1, size2, size3);
    assert(size1 >= 64);
    assert(size2 >= 128);
    assert(size3 >= 256);
    
    // Test free
    es_mem_free(ptr1);
    es_mem_free(ptr2);
    es_mem_free(ptr3);
    printf("✓ All memory freed using es_mem_free\n");
}

static void test_safe_free_compatibility(void) {
    printf("\n=== Testing Safe Free Compatibility ===\n");
    
    void *ptr1 = es_mem_alloc(100);
    void *ptr2 = es_mem_alloc(200);
    
    assert(ptr1 != NULL && ptr2 != NULL);
    printf("✓ Allocated two blocks: %p, %p\n", ptr1, ptr2);
    
    // Test safe free
    es_mem_free_safe(&ptr1);
    es_mem_free_safe(&ptr2);
    
    assert(ptr1 == NULL && ptr2 == NULL);
    printf("✓ Safe free set pointers to NULL\n");
    
    // Test safe free with NULL pointer (should be safe)
    es_mem_free_safe(&ptr1);
    printf("✓ Safe free with NULL pointer handled correctly\n");
}

static void test_realloc_compatibility(void) {
    printf("\n=== Testing Realloc Compatibility ===\n");
    
    // Initial allocation
    void *ptr = es_mem_alloc(100);
    assert(ptr != NULL);
    printf("✓ Initial allocation: %p\n", ptr);
    
    // Write initial data
    strcpy((char*)ptr, "Initial data");
    printf("✓ Initial data: '%s'\n", (char*)ptr);
    
    // Expand memory
    ptr = es_mem_realloc(ptr, 200);
    assert(ptr != NULL);
    printf("✓ Expanded to 200 bytes: %p\n", ptr);
    printf("✓ Data preserved: '%s'\n", (char*)ptr);
    
    // Add more data
    strcat((char*)ptr, " - Extended");
    printf("✓ Extended data: '%s'\n", (char*)ptr);
    
    // Shrink memory
    ptr = es_mem_realloc(ptr, 50);
    assert(ptr != NULL);
    printf("✓ Shrunk to 50 bytes: %p\n", ptr);
    printf("✓ Data after shrink: '%.20s'\n", (char*)ptr);
    
    // Test realloc with NULL (should behave like malloc)
    void *ptr2 = es_mem_realloc(NULL, 150);
    assert(ptr2 != NULL);
    printf("✓ Realloc with NULL pointer: %p\n", ptr2);
    
    // Test realloc with size 0 (should behave like free)
    void *ptr3 = es_mem_realloc(ptr2, 0);
    assert(ptr3 == NULL);
    printf("✓ Realloc with size 0 freed memory\n");
    
    es_mem_free(ptr);
}

static void test_dump_status_compatibility(void) {
    printf("\n=== Testing Dump Status Compatibility ===\n");
    
    // Allocate some memory to show in status
    void *ptrs[5];
    for (int i = 0; i < 5; i++) {
        ptrs[i] = es_mem_alloc(100 + i * 50);
        assert(ptrs[i] != NULL);
    }
    
    printf("✓ Allocated 5 blocks for status demo\n");
    
    // Test dump status function
    printf("\nMemory status dump:\n");
    es_mem_dump_status();
    
    // Clean up
    for (int i = 0; i < 5; i++) {
        es_mem_free(ptrs[i]);
    }
    
    printf("\nMemory status after cleanup:\n");
    es_mem_dump_status();
}

static void test_error_conditions_compatibility(void) {
    printf("\n=== Testing Error Conditions Compatibility ===\n");
    
    // Test oversized allocation
    void *ptr = es_mem_alloc(ES_MEM_V2_MAX_ALLOC + 1);
    assert(ptr == NULL);
    printf("✓ Oversized allocation correctly rejected\n");
    
    // Test zero size allocation
    ptr = es_mem_alloc(0);
    assert(ptr == NULL);
    printf("✓ Zero size allocation correctly rejected\n");
    
    // Test invalid pointer for block size
    int size = es_mem_ptr_block_size((void*)0x12345678);
    assert(size == -1);
    printf("✓ Invalid pointer for block size returned -1\n");
    
    // Test free with invalid pointer
    es_mem_free((void*)0x12345678);
    printf("✓ Free with invalid pointer handled safely\n");
    
    // Test double free
    ptr = es_mem_alloc(100);
    assert(ptr != NULL);
    es_mem_free(ptr);
    es_mem_free(ptr);  // Should be detected and handled
    printf("✓ Double free detected and handled\n");
}

static void test_mixed_interface_usage(void) {
    printf("\n=== Testing Mixed Interface Usage ===\n");
    
    // Allocate using old interface
    void *ptr1 = es_mem_alloc(100);
    assert(ptr1 != NULL);
    printf("✓ Allocated using es_mem_alloc: %p\n", ptr1);
    
    // Allocate using new interface
    void *ptr2 = es_mem_v2_alloc(150);
    assert(ptr2 != NULL);
    printf("✓ Allocated using es_mem_v2_alloc: %p\n", ptr2);
    
    // Get block info using new interface for old allocation
    es_mem_v2_block_info_t info;
    int ret = es_mem_v2_get_block_info(ptr1, &info);
    assert(ret == 0);
    printf("✓ Got block info for old allocation: size=%u\n", info.size);
    
    // Get block size using old interface for new allocation
    int size = es_mem_ptr_block_size(ptr2);
    assert(size >= 150);
    printf("✓ Got block size for new allocation: %d\n", size);
    
    // Free using opposite interfaces
    es_mem_v2_free(ptr1);  // Free old allocation with new interface
    es_mem_free(ptr2);     // Free new allocation with old interface
    printf("✓ Cross-interface free operations successful\n");
}

static void test_stress_allocation(void) {
    printf("\n=== Testing Stress Allocation ===\n");

    const int max_allocations = 100;
    void *ptrs[max_allocations];
    int successful_allocs = 0;

    // Stress test: allocate many small blocks
    printf("Stress test: allocating %d blocks of varying sizes...\n", max_allocations);

    for (int i = 0; i < max_allocations; i++) {
        size_t size = 16 + (i % 200);  // Sizes from 16 to 215 bytes
        ptrs[i] = es_mem_alloc(size);

        if (ptrs[i]) {
            successful_allocs++;
            // Write pattern to verify data integrity
            memset(ptrs[i], (uint8_t)(i & 0xFF), size);
        } else {
            ptrs[i] = NULL;
            break;  // Stop when allocation fails
        }
    }

    printf("✓ Successfully allocated %d out of %d blocks\n", successful_allocs, max_allocations);

    // Verify data integrity
    int integrity_errors = 0;
    for (int i = 0; i < successful_allocs; i++) {
        if (ptrs[i]) {
            size_t size = 16 + (i % 200);
            uint8_t expected = (uint8_t)(i & 0xFF);
            uint8_t *data = (uint8_t*)ptrs[i];

            for (size_t j = 0; j < size; j++) {
                if (data[j] != expected) {
                    integrity_errors++;
                    break;
                }
            }
        }
    }

    if (integrity_errors == 0) {
        printf("✓ Data integrity verified for all allocated blocks\n");
    } else {
        printf("✗ Data integrity errors detected in %d blocks\n", integrity_errors);
    }

    // Random free pattern to test fragmentation handling
    printf("Freeing blocks in random pattern...\n");
    for (int i = 1; i < successful_allocs; i += 3) {  // Free every 3rd block
        if (ptrs[i]) {
            es_mem_free(ptrs[i]);
            ptrs[i] = NULL;
        }
    }

    // Try to allocate in the gaps
    int gap_allocs = 0;
    for (int i = 0; i < 20; i++) {
        void *gap_ptr = es_mem_alloc(32);
        if (gap_ptr) {
            gap_allocs++;
            es_mem_free(gap_ptr);
        }
    }

    printf("✓ Successfully allocated %d blocks in fragmented gaps\n", gap_allocs);

    // Clean up remaining allocations
    for (int i = 0; i < successful_allocs; i++) {
        if (ptrs[i]) {
            es_mem_free(ptrs[i]);
        }
    }

    printf("✓ Stress test completed\n");
}

static void test_boundary_conditions(void) {
    printf("\n=== Testing Boundary Conditions ===\n");

    // Test minimum allocation size
    printf("Testing minimum allocation sizes...\n");
    void *ptr1 = es_mem_alloc(1);
    void *ptr2 = es_mem_alloc(2);
    void *ptr3 = es_mem_alloc(3);
    void *ptr4 = es_mem_alloc(4);

    if (ptr1 && ptr2 && ptr3 && ptr4) {
        printf("✓ Minimum size allocations successful\n");

        // Verify alignment
        if (((uintptr_t)ptr1 % 4) == 0 && ((uintptr_t)ptr2 % 4) == 0 &&
            ((uintptr_t)ptr3 % 4) == 0 && ((uintptr_t)ptr4 % 4) == 0) {
            printf("✓ All small allocations are 4-byte aligned\n");
        } else {
            printf("✗ Alignment error in small allocations\n");
        }

        es_mem_free(ptr1);
        es_mem_free(ptr2);
        es_mem_free(ptr3);
        es_mem_free(ptr4);
    }

    // Test maximum allocation size
    printf("Testing maximum allocation size...\n");
    void *max_ptr = es_mem_alloc(ES_MEM_V2_MAX_ALLOC);
    if (max_ptr) {
        printf("✓ Maximum allocation (%d bytes) successful\n", ES_MEM_V2_MAX_ALLOC);

        // Write to entire block to verify it's usable
        memset(max_ptr, 0xAA, ES_MEM_V2_MAX_ALLOC);

        // Verify data
        uint8_t *data = (uint8_t*)max_ptr;
        bool data_ok = true;
        for (int i = 0; i < ES_MEM_V2_MAX_ALLOC; i++) {
            if (data[i] != 0xAA) {
                data_ok = false;
                break;
            }
        }

        if (data_ok) {
            printf("✓ Maximum allocation block is fully usable\n");
        } else {
            printf("✗ Data corruption in maximum allocation block\n");
        }

        es_mem_free(max_ptr);
    } else {
        printf("✗ Maximum allocation failed\n");
    }

    // Test oversized allocation
    printf("Testing oversized allocation rejection...\n");
    void *over_ptr = es_mem_alloc(ES_MEM_V2_MAX_ALLOC + 1);
    if (over_ptr == NULL) {
        printf("✓ Oversized allocation correctly rejected\n");
    } else {
        printf("✗ Oversized allocation unexpectedly succeeded\n");
        es_mem_free(over_ptr);
    }

    // Test zero size allocation
    printf("Testing zero size allocation...\n");
    void *zero_ptr = es_mem_alloc(0);
    if (zero_ptr == NULL) {
        printf("✓ Zero size allocation correctly rejected\n");
    } else {
        printf("✗ Zero size allocation unexpectedly succeeded\n");
        es_mem_free(zero_ptr);
    }

    // Test realloc boundary conditions
    printf("Testing realloc boundary conditions...\n");

    // Realloc from small to large
    void *realloc_ptr = es_mem_alloc(16);
    assert(realloc_ptr != NULL);
    strcpy((char*)realloc_ptr, "test");

    realloc_ptr = es_mem_realloc(realloc_ptr, ES_MEM_V2_MAX_ALLOC);
    if (realloc_ptr && strcmp((char*)realloc_ptr, "test") == 0) {
        printf("✓ Realloc from small to maximum size successful\n");
    } else {
        printf("✗ Realloc from small to maximum size failed\n");
    }

    // Realloc from large to small
    if (realloc_ptr) {
        realloc_ptr = es_mem_realloc(realloc_ptr, 8);
        if (realloc_ptr && strncmp((char*)realloc_ptr, "test", 4) == 0) {
            printf("✓ Realloc from large to small size successful\n");
        } else {
            printf("✗ Realloc from large to small size failed\n");
        }

        es_mem_free(realloc_ptr);
    }

    // Test realloc with NULL (should behave like malloc)
    void *null_realloc = es_mem_realloc(NULL, 100);
    if (null_realloc) {
        printf("✓ Realloc with NULL pointer works like malloc\n");
        es_mem_free(null_realloc);
    } else {
        printf("✗ Realloc with NULL pointer failed\n");
    }

    // Test realloc with size 0 (should behave like free)
    void *free_realloc = es_mem_alloc(100);
    assert(free_realloc != NULL);
    void *result = es_mem_realloc(free_realloc, 0);
    if (result == NULL) {
        printf("✓ Realloc with size 0 works like free\n");
    } else {
        printf("✗ Realloc with size 0 should return NULL\n");
        es_mem_free(result);
    }

    printf("✓ Boundary conditions testing completed\n");
}

static void test_memory_exhaustion(void) {
    printf("\n=== Testing Memory Exhaustion ===\n");

    void **ptrs = malloc(1000 * sizeof(void*));
    assert(ptrs != NULL);

    int alloc_count = 0;
    size_t total_allocated = 0;

    // Allocate until exhaustion
    printf("Allocating memory until exhaustion...\n");
    for (int i = 0; i < 1000; i++) {
        size_t size = 64 + (i % 128);  // Varying sizes
        ptrs[i] = es_mem_alloc(size);

        if (ptrs[i]) {
            alloc_count++;
            total_allocated += size;
            // Write pattern to verify allocation
            memset(ptrs[i], (uint8_t)(i & 0xFF), size);
        } else {
            break;
        }
    }

    printf("✓ Allocated %d blocks, total ~%zu bytes before exhaustion\n",
           alloc_count, total_allocated);

    // Verify that further allocations fail
    void *should_fail = es_mem_alloc(100);
    if (should_fail == NULL) {
        printf("✓ Additional allocation correctly failed when memory exhausted\n");
    } else {
        printf("✗ Unexpected allocation success when memory should be exhausted\n");
        es_mem_free(should_fail);
    }

    // Free some memory and try again
    printf("Freeing some memory and retrying allocation...\n");
    for (int i = 0; i < alloc_count; i += 5) {  // Free every 5th block
        if (ptrs[i]) {
            es_mem_free(ptrs[i]);
            ptrs[i] = NULL;
        }
    }

    // Try allocation again
    void *retry_ptr = es_mem_alloc(100);
    if (retry_ptr) {
        printf("✓ Allocation successful after freeing some memory\n");
        es_mem_free(retry_ptr);
    } else {
        printf("✗ Allocation still failed after freeing memory\n");
    }

    // Clean up all remaining allocations
    for (int i = 0; i < alloc_count; i++) {
        if (ptrs[i]) {
            es_mem_free(ptrs[i]);
        }
    }

    free(ptrs);
    printf("✓ Memory exhaustion test completed\n");
}

static void test_fragmentation_recovery(void) {
    printf("\n=== Testing Fragmentation Recovery ===\n");

    // Reinitialize memory pool for clean test
    es_mem_v2_deinit();
    es_mem_init();

    const int max_blocks = 50;
    void *ptrs[max_blocks];
    int num_blocks = 0;

    // Create fragmentation pattern
    printf("Creating fragmentation pattern...\n");

    // Allocate all blocks
    for (int i = 0; i < max_blocks; i++) {
        ptrs[i] = es_mem_alloc(100);
        if (ptrs[i] == NULL) {
            printf("Note: Could only allocate %d out of %d blocks due to memory constraints\n", i, max_blocks);
            break;
        }
        sprintf((char*)ptrs[i], "Block_%d", i);
        num_blocks++;
    }

    // Free every other block to create fragmentation
    for (int i = 1; i < num_blocks; i += 2) {
        es_mem_free(ptrs[i]);
        ptrs[i] = NULL;
    }

    printf("✓ Created fragmented memory pattern\n");

    // Try to allocate a large block (should fail due to fragmentation)
    void *large_ptr = es_mem_alloc(500);
    if (large_ptr == NULL) {
        printf("✓ Large allocation correctly failed in fragmented memory\n");
    } else {
        printf("✗ Large allocation unexpectedly succeeded in fragmented memory\n");
        es_mem_free(large_ptr);
    }

    // Free remaining blocks to allow merging
    printf("Freeing remaining blocks to test merging...\n");
    for (int i = 0; i < num_blocks; i += 2) {
        if (ptrs[i]) {
            // Verify data integrity before freeing
            char expected[20];
            sprintf(expected, "Block_%d", i);
            if (strcmp((char*)ptrs[i], expected) == 0) {
                // Data is intact
            } else {
                printf("✗ Data corruption detected in block %d\n", i);
            }
            es_mem_free(ptrs[i]);
        }
    }

    // Now try the large allocation again
    large_ptr = es_mem_alloc(1000);
    if (large_ptr) {
        printf("✓ Large allocation successful after defragmentation\n");
        es_mem_free(large_ptr);
    } else {
        printf("✗ Large allocation still failed after defragmentation\n");
    }

    printf("✓ Fragmentation recovery test completed\n");
}

static void test_concurrent_operations(void) {
    printf("\n=== Testing Concurrent-like Operations ===\n");

    // Simulate concurrent-like behavior with rapid alloc/free cycles
    printf("Simulating rapid allocation/deallocation cycles...\n");

    const int cycles = 1000;
    int success_count = 0;

    for (int i = 0; i < cycles; i++) {
        size_t size = 32 + (i % 64);
        void *ptr = es_mem_alloc(size);

        if (ptr) {
            // Write and verify data
            memset(ptr, (uint8_t)(i & 0xFF), size);

            // Immediate verification
            uint8_t *data = (uint8_t*)ptr;
            bool data_ok = true;
            for (size_t j = 0; j < size; j++) {
                if (data[j] != (uint8_t)(i & 0xFF)) {
                    data_ok = false;
                    break;
                }
            }

            if (data_ok) {
                success_count++;
            }

            es_mem_free(ptr);
        }
    }

    printf("✓ Completed %d successful rapid alloc/free cycles out of %d\n",
           success_count, cycles);

    // Test interleaved operations
    printf("Testing interleaved allocation patterns...\n");

    void *ptrs_a[10], *ptrs_b[10];

    // Interleave allocations
    for (int i = 0; i < 10; i++) {
        ptrs_a[i] = es_mem_alloc(64);
        ptrs_b[i] = es_mem_alloc(128);

        if (ptrs_a[i]) sprintf((char*)ptrs_a[i], "A_%d", i);
        if (ptrs_b[i]) sprintf((char*)ptrs_b[i], "B_%d", i);
    }

    // Interleave frees
    for (int i = 0; i < 10; i++) {
        if (ptrs_a[i]) {
            char expected[10];
            sprintf(expected, "A_%d", i);
            if (strcmp((char*)ptrs_a[i], expected) == 0) {
                // Data intact
            } else {
                printf("✗ Data corruption in ptrs_a[%d]\n", i);
            }
            es_mem_free(ptrs_a[i]);
        }

        if (ptrs_b[i]) {
            char expected[10];
            sprintf(expected, "B_%d", i);
            if (strcmp((char*)ptrs_b[i], expected) == 0) {
                // Data intact
            } else {
                printf("✗ Data corruption in ptrs_b[%d]\n", i);
            }
            es_mem_free(ptrs_b[i]);
        }
    }

    printf("✓ Interleaved operations completed successfully\n");
}

int main(void) {
    printf("=== Memory Compatibility Layer Test Suite ===\n");
    printf("Testing compatibility between es_mem.h and es_mem_v2.h interfaces\n");
    
    // Initialize using old interface
    int ret = es_mem_init();
    assert(ret == 0);
    printf("✓ Memory manager initialized using es_mem_init()\n");
    
    // Run compatibility tests
    test_basic_compatibility();
    test_safe_free_compatibility();
    test_realloc_compatibility();
    test_dump_status_compatibility();
    test_error_conditions_compatibility();
    test_mixed_interface_usage();

    // Run stress and boundary tests
    test_stress_allocation();
    test_boundary_conditions();
    test_memory_exhaustion();
    test_fragmentation_recovery();
    test_concurrent_operations();
    
    // Final status check
    printf("\n=== Final Status ===\n");
    es_mem_dump_status();
    
    // Check for leaks using new interface
    int leaks = es_mem_v2_check_leaks();
    if (leaks == 0) {
        printf("✓ No memory leaks detected\n");
    } else {
        printf("⚠ %d memory leaks detected\n", leaks);
    }
    
    // Deinitialize using new interface
    ret = es_mem_v2_deinit();
    assert(ret == 0);
    printf("✓ Memory manager deinitialized\n");
    
    printf("\n=== All Compatibility Tests Passed! ===\n");
    return 0;
}
