/**
 * @file win32_uart.c
 * @brief Windows平台串口操作实现
 */

#include "es_drv_uart.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <windows.h>

/**
 * @brief Windows平台串口设备结构体
 */
typedef struct {
    HANDLE hComm;
    DCB dcb;
    COMMTIMEOUTS timeouts;
    uint16_t tx_index;
    uint16_t tx_size;
} win_uart_dev_t;

/**
 * @brief 打开串口设备
 */
es_uart_dev_t es_uart_open(const es_uart_config_t *config)
{
    win_uart_dev_t *dev = (win_uart_dev_t *)malloc(sizeof(win_uart_dev_t));
    if (dev == NULL) {
        return NULL;
    }
    
    /* 打开串口 */
    char port_name[20];
    snprintf(port_name, sizeof(port_name), "\\\\.\\%s", config->name);
    
    dev->hComm = CreateFileA(port_name,
                            GENERIC_READ | GENERIC_WRITE,
                            0,
                            NULL,
                            OPEN_EXISTING,
                            FILE_ATTRIBUTE_NORMAL,
                            NULL);
    
    if (dev->hComm == INVALID_HANDLE_VALUE) {
        free(dev);
        return NULL;
    }
    
    /* 配置串口参数 */
    if (!GetCommState(dev->hComm, &dev->dcb)) {
        CloseHandle(dev->hComm);
        free(dev);
        return NULL;
    }
    
    /* 设置波特率 */
    memset(&dev->dcb, 0, sizeof(DCB));
    dev->dcb.DCBlength = sizeof(DCB);
    dev->dcb.BaudRate = config->baud_rate;
    dev->dcb.ByteSize = config->data_bits;
    
    switch (config->parity) {
        case ES_UART_PARITY_NONE:
            dev->dcb.Parity = NOPARITY;
            break;
        case ES_UART_PARITY_ODD:
            dev->dcb.Parity = ODDPARITY;
            break;
        case ES_UART_PARITY_EVEN:
            dev->dcb.Parity = EVENPARITY;
            break;
        default:
            dev->dcb.Parity = NOPARITY;
            break;
    }
    
    switch (config->stop_bits) {
        case ES_UART_STOP_BITS_1:
            dev->dcb.StopBits = ONESTOPBIT;
            break;
        case ES_UART_STOP_BITS_2:
            dev->dcb.StopBits = TWOSTOPBITS;
            break;
        default:
            dev->dcb.StopBits = ONESTOPBIT;
            break;
    }
    
    /* 流控制 */
    dev->dcb.fOutxCtsFlow = FALSE;
    dev->dcb.fRtsControl = RTS_CONTROL_DISABLE;
    dev->dcb.fBinary = TRUE;
    dev->dcb.fDtrControl = DTR_CONTROL_ENABLE;
    
    if (config->flow_control == ES_UART_FLOW_CONTROL_RTS_CTS) {
        dev->dcb.fOutxCtsFlow = TRUE;
        dev->dcb.fRtsControl = RTS_CONTROL_HANDSHAKE;
    }
    
    if (!SetCommState(dev->hComm, &dev->dcb)) {
        CloseHandle(dev->hComm);
        free(dev);
        return NULL;
    }
    
    /* 设置超时 */
    GetCommTimeouts(dev->hComm, &dev->timeouts);
    dev->timeouts.ReadIntervalTimeout = 50;
    dev->timeouts.ReadTotalTimeoutConstant = 50;
    dev->timeouts.ReadTotalTimeoutMultiplier = 10;
    dev->timeouts.WriteTotalTimeoutConstant = 50;
    dev->timeouts.WriteTotalTimeoutMultiplier = 10;
    SetCommTimeouts(dev->hComm, &dev->timeouts);
    dev->tx_index = 0;
    dev->tx_size  = 0;
    
    return dev;
}

/**
 * @brief 关闭串口设备
 */
int es_uart_close(es_uart_dev_t dev)
{
    win_uart_dev_t *win_dev = (win_uart_dev_t *)dev;
    if (win_dev == NULL) {
        return -1;
    }
    
    CloseHandle(win_dev->hComm);
    free(win_dev);
    
    return 0;
}

/**
 * @brief 从串口读取数据
 */
int es_uart_read(es_uart_dev_t dev, void *buf, size_t size)
{
    win_uart_dev_t *win_dev = (win_uart_dev_t *)dev;
    if (win_dev == NULL || buf == NULL || size == 0) {
        return -1;
    }
    
    DWORD bytes_read = 0;
    if (!ReadFile(win_dev->hComm, buf, (DWORD)size, &bytes_read, NULL)) {
        return -1;
    }
    
    return (int)bytes_read;
}

/**
 * @brief 向串口写入数据
 */
int es_uart_write(es_uart_dev_t dev, const void *buf, size_t size)
{
    win_uart_dev_t *win_dev = (win_uart_dev_t *)dev;
    if (win_dev == NULL || buf == NULL || size == 0) {
        return -1;
    }
    
    DWORD bytes_written = 0;
    if (!WriteFile(win_dev->hComm, buf, (DWORD)size, &bytes_written, NULL)) {
        return -1;
    }
    
    return (int)bytes_written;
}

es_async_t es_uart_coro_write(es_coro_t *coro, es_uart_dev_t dev, const uint8_t *buf, int size)
{
    win_uart_dev_t *win_dev = (win_uart_dev_t *)dev;
    if (win_dev == NULL || buf == NULL || size == 0) {
        return ES_ASYNC_ERROR;
    }

    DWORD bytes_written = 0;

    es_co_begin(coro);

    //wait until tx buffer is empty
    es_co_wait_timeout(win_dev->tx_index == 0, 3000);
    
    win_dev->tx_index = 0;
    win_dev->tx_size = size;

    while (win_dev->tx_index < win_dev->tx_size)
    {
        es_co_yield;
        if (WriteFile(win_dev->hComm, buf + win_dev->tx_index, (DWORD)(win_dev->tx_size - win_dev->tx_index), &bytes_written, NULL)) {
            win_dev->tx_index += (uint16_t)bytes_written;
        }
    }

    //flush
    FlushFileBuffers(win_dev->hComm);

    win_dev->tx_index = 0;
    win_dev->tx_size = 0;

    es_co_end;
}

/**
 * @brief 检查串口是否有数据可读
 */
int es_uart_available(es_uart_dev_t dev, uint32_t timeout_ms)
{
    win_uart_dev_t *win_dev = (win_uart_dev_t *)dev;
    if (win_dev == NULL) {
        return -1;
    }
    
    (void)timeout_ms;  /* 标记参数为已使用，避免警告 */
    
    COMSTAT status;
    DWORD errors;
    
    if (!ClearCommError(win_dev->hComm, &errors, &status)) {
        return -1;
    }
    
    return (int)status.cbInQue;
}

/**
 * @brief 刷新串口缓冲区
 */
int es_uart_flush(es_uart_dev_t dev)
{
    win_uart_dev_t *win_dev = (win_uart_dev_t *)dev;
    if (win_dev == NULL) {
        return -1;
    }
    
    if (!PurgeComm(win_dev->hComm, PURGE_RXCLEAR | PURGE_TXCLEAR)) {
        return -1;
    }
    
    return 0;
} 