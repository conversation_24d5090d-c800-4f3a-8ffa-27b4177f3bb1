#ifndef FRAME_QUEUE_H
#define FRAME_QUEUE_H

#include <stdbool.h>
#include <stdint.h>

typedef struct {
    uint8_t *buffer;  // Pre-allocated memory for storing frames
    uint32_t capacity : 11;      // Total size of the buffer
    uint32_t head : 11;
    uint32_t tail : 11;
    uint32_t full : 1;
    uint32_t is_ble : 1;
} frame_queue_t;

/**
 * @brief Initialize the queue with a pre-allocated buffer.
 * @param q The queue to initialize.
 * @param buffer The pre-allocated uint8_t buffer.
 * @param capacity The total capacity of the buffer.
 */
bool frame_queue_init(frame_queue_t *q, uint8_t *buffer, int capacity, int is_ble);

/**
 * @brief Enqueue a frame with explicit length
 * @param q The queue to push the frame into
 * @param frame The frame to push
 * @param len The length of the frame data
 */
bool frame_queue_push_by_len(frame_queue_t *q, const void *frame, int len);

/**
 * @brief Enqueue a frame using frame_size to determine length
 * @param q The queue to push the frame into
 * @param frame The frame to push
 */
bool frame_queue_push(frame_queue_t *q, const void *frame);

/**
 * @brief Dequeue a frame by using the first byte as the type to get the frame size.
 */
bool frame_queue_pop(frame_queue_t *q, void *frame);

/**
 * @brief Check if the queue is full.
 */
static inline bool frame_queue_is_empty(const frame_queue_t *q) {
    return (!q->full && (q->head == q->tail));
}

/**
 * @brief Clear the queue.
 */
static inline void frame_queue_clear(frame_queue_t *q) {
    q->head = 0;
    q->tail = 0;
    q->full = false;
}

/**
 * @brief Find a frame by type and return its start index in the queue.
 * @param q The queue
 * @param type The frame type to find
 * @return The start index if found, or -1 if not found.
 */
int frame_queue_find(frame_queue_t *q, uint8_t type);

/**
 * @brief Print queue information.
 * @param q The queue to print information for.
 */
void frame_queue_dump(const frame_queue_t *q);

#endif // FRAME_QUEUE_H
