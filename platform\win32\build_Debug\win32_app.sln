﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{AC9C3918-D321-3ACC-9026-7856F155DC67}"
	ProjectSection(ProjectDependencies) = postProject
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218} = {9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}
		{EC10C551-574F-3692-839A-F6D37401D627} = {EC10C551-574F-3692-839A-F6D37401D627}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{18DEC01B-7E7F-34B5-ADC5-B35994D8FAE1}"
	ProjectSection(ProjectDependencies) = postProject
		{AC9C3918-D321-3ACC-9026-7856F155DC67} = {AC9C3918-D321-3ACC-9026-7856F155DC67}
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218} = {9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "win32_app", "win32_app.vcxproj", "{EC10C551-574F-3692-839A-F6D37401D627}"
	ProjectSection(ProjectDependencies) = postProject
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218} = {9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AC9C3918-D321-3ACC-9026-7856F155DC67}.Debug|x64.ActiveCfg = Debug|x64
		{AC9C3918-D321-3ACC-9026-7856F155DC67}.Debug|x64.Build.0 = Debug|x64
		{AC9C3918-D321-3ACC-9026-7856F155DC67}.Release|x64.ActiveCfg = Release|x64
		{AC9C3918-D321-3ACC-9026-7856F155DC67}.Release|x64.Build.0 = Release|x64
		{AC9C3918-D321-3ACC-9026-7856F155DC67}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{AC9C3918-D321-3ACC-9026-7856F155DC67}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{AC9C3918-D321-3ACC-9026-7856F155DC67}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{AC9C3918-D321-3ACC-9026-7856F155DC67}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{18DEC01B-7E7F-34B5-ADC5-B35994D8FAE1}.Debug|x64.ActiveCfg = Debug|x64
		{18DEC01B-7E7F-34B5-ADC5-B35994D8FAE1}.Release|x64.ActiveCfg = Release|x64
		{18DEC01B-7E7F-34B5-ADC5-B35994D8FAE1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{18DEC01B-7E7F-34B5-ADC5-B35994D8FAE1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}.Debug|x64.ActiveCfg = Debug|x64
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}.Debug|x64.Build.0 = Debug|x64
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}.Release|x64.ActiveCfg = Release|x64
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}.Release|x64.Build.0 = Release|x64
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9D47A6CA-7BA9-3B35-A3E5-2DF38CC37218}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{EC10C551-574F-3692-839A-F6D37401D627}.Debug|x64.ActiveCfg = Debug|x64
		{EC10C551-574F-3692-839A-F6D37401D627}.Debug|x64.Build.0 = Debug|x64
		{EC10C551-574F-3692-839A-F6D37401D627}.Release|x64.ActiveCfg = Release|x64
		{EC10C551-574F-3692-839A-F6D37401D627}.Release|x64.Build.0 = Release|x64
		{EC10C551-574F-3692-839A-F6D37401D627}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EC10C551-574F-3692-839A-F6D37401D627}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{EC10C551-574F-3692-839A-F6D37401D627}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EC10C551-574F-3692-839A-F6D37401D627}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C0F863C3-EC13-348E-B652-4B1848584A8B}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
