#include "es_frame_q.h"
#include <string.h>
#include "es_log.h"
#include "es_frame.h"

#define TAG "fq"






static int frame_queue_free_space(const frame_queue_t *q) {
    if ( q->full ) {
        return 0;
    }
    if (q->head == q->tail && !q->full) {
        return q->capacity;
    }
    if (q->head < q->tail) {
        return q->capacity - (q->tail - q->head);
    } else {
        return (q->head - q->tail);
    }
}

bool frame_queue_init(frame_queue_t *q, uint8_t *buffer, int capacity, int is_ble) {
    if (capacity <= 0 || (capacity & (capacity - 1)) != 0) {
        ES_LOGE(TAG, "Invalid capacity size: %d. Must be a power of 2.", capacity);
        return false;
    }
    q->buffer = buffer;
    q->capacity = capacity;
    q->head = 0;
    q->tail = 0;
    q->full = 0;
    q->is_ble = is_ble;
    return true;
}

static const frame_info_t *frame_info(const frame_queue_t *q, uint8_t type) {
    return q->is_ble ? ble_tx_get_info(type) : frame_tx_get_info(type);
}

static int frame_size(const frame_queue_t *q, uint8_t type) {
    return q->is_ble ? ble_tx_get_size(type) : frame_tx_get_size(type);
}

static bool frame_queue_insert_at(frame_queue_t *q, int insert_idx, const uint8_t *data, int new_len) {
    if (!q || !data || new_len <= 0 || insert_idx < 0) {
        return false;
    }

    // Read existing frame length
    int old_len = q->buffer[insert_idx & (q->capacity - 1)]
                | (q->buffer[(insert_idx + 1) & (q->capacity - 1)] << 8);
    
    if (old_len <= 0) {
        ES_LOGE(TAG, "Invalid old frame length: %d", old_len);
        return false;
    }

    int old_total = old_len + 2;  // Include length bytes
    int new_total = new_len + 2;
    int diff = new_total - old_total;

    if (diff == 0) {
        // Equal size - replace content
        q->buffer[insert_idx & (q->capacity - 1)] = new_len & 0xFF;
        q->buffer[(insert_idx + 1) & (q->capacity - 1)] = (new_len >> 8) & 0xFF;
        for (int i = 0; i < new_len; i++) {
            q->buffer[(insert_idx + 2 + i) & (q->capacity - 1)] = data[i];
        }
        return true;
    }

    // Calculate the number of bytes we need to move
    int bytes_after = 0;
    int idx = (insert_idx + old_total) & (q->capacity - 1);
    while (idx != q->tail) {
        bytes_after++;
        idx = (idx + 1) & (q->capacity - 1);
    }

    if (diff < 0) {
        // New frame is smaller - free up space
        for (int i = 0; i < bytes_after; i++) {
            int src = (insert_idx + old_total + i) & (q->capacity - 1);
            int dst = (insert_idx + new_total + i) & (q->capacity - 1);
            q->buffer[dst] = q->buffer[src];
        }
        q->tail = (q->tail + diff + q->capacity) & (q->capacity - 1);
        q->full = false;
    } else {
        // New frame is larger - ensure enough space
        while (frame_queue_free_space(q) < diff) {
            frame_queue_pop(q, NULL); // Pop frames until enough space
        }
        for (int i = bytes_after - 1; i >= 0; i--) {
            int src = (insert_idx + old_total + i) & (q->capacity - 1);
            int dst = (insert_idx + new_total + i) & (q->capacity - 1);
            q->buffer[dst] = q->buffer[src];
        }
        q->tail = (q->tail + diff) & (q->capacity - 1);
        if (q->tail == q->head) {
            q->full = true;
        }
    }

    // Write new frame
    q->buffer[insert_idx & (q->capacity - 1)] = new_len & 0xFF;
    q->buffer[(insert_idx + 1) & (q->capacity - 1)] = (new_len >> 8) & 0xFF;
    for (int i = 0; i < new_len; i++) {
        q->buffer[(insert_idx + 2 + i) & (q->capacity - 1)] = data[i];
    }

    return true;
}

bool frame_queue_push_by_len(frame_queue_t *q, const void *frame, int len) {
    if (len + 2 > (int)q->capacity) {
        ES_LOGE(TAG, "Frame length exceeds queue capacity");
        return false;
    }

    const frame_info_t *info = frame_info(q, ((const uint8_t*)frame)[0]);
    if (!info) {
        return false;
    }
    if (info->meta & (META_DEV_REQ)) {
        int found = frame_queue_find(q, ((const uint8_t*)frame)[0]);
        if (found >= 0) {
            // Try to insert at the found position
            if (frame_queue_insert_at(q, found, (const uint8_t*)frame, len)) {
                return true;
            }
        }
    }

    int total = len + 2;  // two extra bytes for length
    while ( frame_queue_free_space(q) < total) {
        frame_queue_pop(q, NULL); // Pop and discard
    }
    // Store length in first 2 bytes
    q->buffer[q->tail & (q->capacity - 1)] = (uint8_t)(len & 0xFF);
    q->buffer[(q->tail + 1) & (q->capacity - 1)] = (uint8_t)((len >> 8) & 0xFF);
    // Copy frame data
    for (int i = 0; i < len; i++) {
        q->buffer[(q->tail + 2 + i) & (q->capacity - 1)] = ((const uint8_t*)frame)[i];
    }
    q->tail = (q->tail + total) & (q->capacity - 1);
    if (q->tail == q->head) {
        q->full = true;
    }
    return true;
}

bool frame_queue_push(frame_queue_t *q, const void *frame) {
    if (!frame) {
        return false;
    }
    int len = frame_size(q, ((const uint8_t*)frame)[0]);
    if (len <= 0 || len + 2 > (int)q->capacity) {
        ES_LOGE(TAG, "Frame length exceeds queue capacity or invalid length");
        return false;
    }
    return frame_queue_push_by_len(q, frame, len);
}

bool frame_queue_pop(frame_queue_t *q, void *frame) {
    if (frame_queue_is_empty(q)) {
        return false;
    }
    // Read length first
    int sz = q->buffer[q->head & (q->capacity - 1)]
           | (q->buffer[(q->head + 1) & (q->capacity - 1)] << 8);
    int offset = (q->head + 2) & (q->capacity - 1);
    if (frame != NULL) {
        for (int i = 0; i < sz; i++) {
            ((uint8_t*)frame)[i] = q->buffer[(offset + i) & (q->capacity - 1)];
        }
    }
    q->head = (q->head + 2 + sz) & (q->capacity - 1);
    q->full = false;
    return true;
}

int frame_queue_find(frame_queue_t *q, uint8_t type) {
    if (frame_queue_is_empty(q)) {
        return -1;
    }
    int idx = q->head;
    while (true) {
        uint16_t length = q->buffer[idx & (q->capacity - 1)]
                        | (q->buffer[(idx + 1) & (q->capacity - 1)] << 8);
        uint8_t curType = q->buffer[(idx + 2) & (q->capacity - 1)];
        if (curType == type) {
            return idx;
        }
        int next = (idx + 2 + length) & (q->capacity - 1);
        if (next == q->tail && !q->full) {
            break;
        }
        idx = next;
        if (idx == q->head) {
            break;
        }
    }
    return -1;
}

void frame_queue_dump(const frame_queue_t *q) {
    ES_LOGI(TAG, "Queue info: capacity=%d free=%d head=%d tail=%d full=%d",
             q->capacity, frame_queue_free_space(q), q->head, q->tail, q->full);
    if (frame_queue_is_empty(q)) {
        ES_LOGI(TAG, "No frames in queue");
        return;
    }
    int idx = q->head;
    while (true) {
        uint16_t length = q->buffer[idx & (q->capacity - 1)]
                        | (q->buffer[(idx + 1) & (q->capacity - 1)] << 8);
        uint8_t curType = q->buffer[(idx + 2) & (q->capacity - 1)];
        const frame_info_t *info = frame_info(q, curType);
        ES_LOGI(TAG, "Frame at idx=%d: type=0x%x, size=%d, name=%s",
                 idx, curType, length, info ? info->desc : "Unknown");
        int next = (idx + 2 + length) & (q->capacity - 1);
        if (next == q->tail && !q->full) {
            break;
        }
        idx = next;
        if (idx == q->head) {
            break;
        }
    }
}
