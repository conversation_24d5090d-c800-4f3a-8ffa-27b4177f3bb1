# ES 配置管理系统文档 (es_cfg_v2)

## 1. 概述

ES 配置管理系统 V2 (es_cfg_v2) 是 ES MCU Framework 的核心存储组件，提供了安全、可靠的参数存储和管理功能。该系统采用双扇区存储机制，支持参数分类管理、版本控制和数据完整性校验。

### 1.1 主要特性

- **双扇区存储**: 确保数据安全，防止写入过程中的数据丢失
- **参数分类**: 支持工厂参数和用户参数分类管理
- **版本控制**: 支持参数版本升级和兼容性检查
- **数据校验**: 使用 CRC32 校验确保数据完整性
- **类型安全**: 支持多种数据类型和范围校验
- **原子操作**: 参数更新操作具有原子性

### 1.2 核心概念

- **Key**: 16位参数键值，唯一标识一个参数
- **Category**: 参数类别（工厂参数/用户参数）
- **Type**: 参数数据类型（整数/字符串/二进制）
- **Metadata**: 参数元数据（默认值、范围、长度等）

## 2. 系统架构

### 2.1 存储结构

```
Flash 存储布局:
┌─────────────────┬─────────────────┐
│   Sector A      │   Sector B      │
│   (32KB)        │   (32KB)        │
├─────────────────┼─────────────────┤
│ Header + Data   │ Header + Data   │
└─────────────────┴─────────────────┘

扇区结构:
┌─────────────────┬─────────────────┬─────────────────┐
│     Header      │   Param Data    │   Free Space    │
│    (64 bytes)   │   (Variable)    │   (Variable)    │
└─────────────────┴─────────────────┴─────────────────┘
```

### 2.2 数据结构

#### 2.2.1 扇区头部

```c
typedef struct {
    uint32_t magic;         // 魔数 "ESCV"
    uint16_t version;       // 版本号
    uint16_t param_count;   // 参数数量
    uint32_t data_size;     // 数据大小
    uint32_t crc32;         // CRC32校验
    uint32_t timestamp;     // 时间戳
    uint8_t reserved[40];   // 保留字段
} es_cfg_v2_sector_header_t;
```

#### 2.2.2 参数条目

```c
typedef struct {
    es_cfg_v2_key_t key;           // 参数键值
    es_cfg_v2_value_type_t type;   // 数据类型
    uint16_t value_len;            // 数据长度
    es_cfg_v2_category_t category; // 参数类别
    uint8_t value[];               // 参数值
} es_cfg_v2_entry_t;
```

### 2.3 参数类型

```c
typedef enum {
    ES_CFG_V2_TYPE_UINT8    = 1,  // 8位无符号整数
    ES_CFG_V2_TYPE_UINT16,        // 16位无符号整数
    ES_CFG_V2_TYPE_UINT32,        // 32位无符号整数
    ES_CFG_V2_TYPE_INT8,          // 8位有符号整数
    ES_CFG_V2_TYPE_INT16,         // 16位有符号整数
    ES_CFG_V2_TYPE_INT32,         // 32位有符号整数
    ES_CFG_V2_TYPE_STRING,        // 字符串
    ES_CFG_V2_TYPE_BINARY,        // 二进制数据
} es_cfg_v2_value_type_t;
```

### 2.4 参数类别

```c
typedef enum {
    ES_CFG_V2_CATEGORY_FACTORY = 0x01,  // 工厂参数
    ES_CFG_V2_CATEGORY_USER    = 0x02,  // 用户参数
    ES_CFG_V2_CATEGORY_ALL     = 0x03,  // 所有参数
} es_cfg_v2_category_t;
```

## 3. 参数定义

### 3.1 参数定义宏

```c
#define ES_CFG_V2_PARAMS \
    X(device_id,     0x0001, ES_CFG_V2_TYPE_UINT32, 4, ES_CFG_V2_CATEGORY_FACTORY, \
      (&(es_cfg_v2_num_meta_t){.u={.u32={0x12345678, 0, 0xFFFFFFFF}}})) \
    X(device_name,   0x0010, ES_CFG_V2_TYPE_STRING, 32, ES_CFG_V2_CATEGORY_USER, \
      (&(es_cfg_v2_data_meta_t){"ES_Device", 9, 32})) \
    X(server_addr,   0x0011, ES_CFG_V2_TYPE_STRING, 64, ES_CFG_V2_CATEGORY_USER, \
      (&(es_cfg_v2_data_meta_t){ES_CFG_SERVER_ADDR, sizeof(ES_CFG_SERVER_ADDR)-1, 64})) \
    X(acc_threshold, 0x0020, ES_CFG_V2_TYPE_UINT8,  1, ES_CFG_V2_CATEGORY_USER, \
      (&(es_cfg_v2_num_meta_t){.u={.u8={30, 0, 100}}}))
```

### 3.2 元数据结构

#### 3.2.1 数值类型元数据

```c
typedef struct {
    union {
        struct { uint32_t def, min, max; } u32;
        struct { int32_t def, min, max; } i32;
        struct { uint16_t def, min, max; } u16;
        struct { int16_t def, min, max; } i16;
        struct { uint8_t def, min, max; } u8;
        struct { int8_t def, min, max; } i8;
    } u;
} es_cfg_v2_num_meta_t;
```

#### 3.2.2 数据类型元数据

```c
typedef struct {
    const void *def_data;    // 默认数据
    uint16_t def_len;        // 默认长度
    uint16_t max_len;        // 最大长度
} es_cfg_v2_data_meta_t;
```

## 4. 主要API

### 4.1 系统管理

```c
// 初始化配置系统
int es_cfg_v2_init(void);

// 保存所有参数到Flash
int es_cfg_v2_save(void);

// 从Flash加载参数
int es_cfg_v2_load(void);

// 获取系统状态
bool es_cfg_v2_is_initialized(void);
```

### 4.2 参数操作

```c
// 设置参数值
int es_cfg_v2_set(es_cfg_v2_key_t key, const void *value, uint16_t value_len);

// 获取参数值
int es_cfg_v2_get(es_cfg_v2_key_t key, void *value, uint16_t value_len);

// 获取参数长度
int es_cfg_v2_get_len(es_cfg_v2_key_t key);

// 检查参数是否存在
bool es_cfg_v2_exists(es_cfg_v2_key_t key);
```

### 4.3 批量操作

```c
// 按类别重置参数
int es_cfg_v2_reset_by_category(es_cfg_v2_category_t category, bool save);

// 重置所有参数
int es_cfg_v2_reset_all(bool save);

// 获取参数数量
int es_cfg_v2_get_param_count(void);
```

### 4.4 调试和诊断

```c
// 打印所有参数
void es_cfg_v2_dump_all(void);

// 打印指定类别参数
void es_cfg_v2_dump_category(es_cfg_v2_category_t category);

// 获取存储统计信息
void es_cfg_v2_get_stats(es_cfg_v2_stats_t *stats);
```

## 5. 使用示例

### 5.1 基本参数操作

```c
// 设置设备名称
const char *device_name = "MyDevice";
int ret = es_cfg_v2_set(0x0010, device_name, strlen(device_name));
if (ret != ES_CFG_V2_OK) {
    ES_LOGE("CFG", "Failed to set device name: %d", ret);
}

// 获取设备名称
char name_buffer[32];
ret = es_cfg_v2_get(0x0010, name_buffer, sizeof(name_buffer));
if (ret > 0) {
    name_buffer[ret] = '\0';  // 确保字符串结束
    ES_LOGI("CFG", "Device name: %s", name_buffer);
}

// 设置数值参数
uint8_t threshold = 50;
ret = es_cfg_v2_set(0x0020, &threshold, sizeof(threshold));
if (ret != ES_CFG_V2_OK) {
    ES_LOGE("CFG", "Failed to set threshold: %d", ret);
}

// 获取数值参数
uint8_t read_threshold;
ret = es_cfg_v2_get(0x0020, &read_threshold, sizeof(read_threshold));
if (ret == sizeof(read_threshold)) {
    ES_LOGI("CFG", "Threshold: %d", read_threshold);
}
```

### 5.2 批量操作

```c
// 重置用户参数到默认值
int ret = es_cfg_v2_reset_by_category(ES_CFG_V2_CATEGORY_USER, true);
if (ret != ES_CFG_V2_OK) {
    ES_LOGE("CFG", "Failed to reset user parameters: %d", ret);
} else {
    ES_LOGI("CFG", "User parameters reset successfully");
}

// 保存所有参数
ret = es_cfg_v2_save();
if (ret != ES_CFG_V2_OK) {
    ES_LOGE("CFG", "Failed to save parameters: %d", ret);
} else {
    ES_LOGI("CFG", "Parameters saved successfully");
}
```

### 5.3 参数验证

```c
// 检查参数是否存在
if (es_cfg_v2_exists(0x0020)) {
    ES_LOGI("CFG", "Parameter 0x0020 exists");
} else {
    ES_LOGW("CFG", "Parameter 0x0020 not found");
}

// 获取参数长度
int len = es_cfg_v2_get_len(0x0010);
if (len > 0) {
    ES_LOGI("CFG", "Parameter 0x0010 length: %d", len);
}
```

## 6. 错误码

```c
typedef enum {
    ES_CFG_V2_OK = 0,              // 成功
    ES_CFG_V2_ERR_NOT_INIT,        // 未初始化
    ES_CFG_V2_ERR_INVALID_KEY,     // 无效键值
    ES_CFG_V2_ERR_INVALID_VALUE,   // 无效值
    ES_CFG_V2_ERR_INVALID_LEN,     // 无效长度
    ES_CFG_V2_ERR_NO_SPACE,        // 存储空间不足
    ES_CFG_V2_ERR_FLASH_ERROR,     // Flash操作错误
    ES_CFG_V2_ERR_CRC_ERROR,       // CRC校验错误
    ES_CFG_V2_ERR_VERSION_ERROR,   // 版本错误
} es_cfg_v2_error_t;
```

## 7. 配置选项

### 7.1 编译时配置

```c
// 最大参数数量
#define ES_CFG_V2_MAX_PARAMS 256

// 扇区大小
#define ES_CFG_V2_SECTOR_SIZE (32 * 1024)

// 最大参数值长度
#define ES_CFG_V2_MAX_VALUE_LEN 512

// 启用调试输出
#define ES_CFG_V2_DEBUG 1
```

### 7.2 运行时配置

```c
// 设置自动保存间隔（毫秒）
void es_cfg_v2_set_auto_save_interval(uint32_t interval);

// 启用/禁用自动保存
void es_cfg_v2_enable_auto_save(bool enable);
```

## 8. 最佳实践

### 8.1 参数设计

1. **键值分配**: 使用有意义的键值范围分配
2. **类型选择**: 选择合适的数据类型减少存储空间
3. **默认值**: 设置合理的默认值和范围限制
4. **版本兼容**: 考虑参数升级的兼容性

### 8.2 性能优化

1. **批量操作**: 使用批量操作减少Flash写入次数
2. **缓存机制**: 频繁访问的参数可以缓存到RAM
3. **延迟保存**: 使用延迟保存机制减少Flash磨损
4. **压缩存储**: 对大量数据考虑压缩存储

### 8.3 可靠性保证

1. **双扇区**: 利用双扇区机制确保数据安全
2. **校验机制**: 使用CRC校验检测数据损坏
3. **错误处理**: 完善的错误处理和恢复机制
4. **备份策略**: 重要参数的备份和恢复策略

## 9. 故障排除

### 9.1 常见问题

1. **初始化失败**: 检查Flash分区配置和权限
2. **参数丢失**: 检查CRC校验和扇区完整性
3. **写入失败**: 检查Flash空间和写入权限
4. **版本不兼容**: 检查参数版本和升级逻辑

### 9.2 调试方法

1. **日志输出**: 启用详细的调试日志
2. **参数转储**: 使用转储功能检查参数状态
3. **统计信息**: 监控存储使用情况和性能指标
4. **Flash检查**: 使用工具检查Flash扇区状态

这个配置管理系统为 ES MCU Framework 提供了可靠的参数存储解决方案，是构建稳定嵌入式系统的重要基础。
