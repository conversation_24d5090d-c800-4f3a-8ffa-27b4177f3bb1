#include "es_flash.h"
#include "es_drv_os.h"
#include <stdint.h>
#include "ev_hc32f460_lqfp100_v2_w25qxx.h"

static int init(void)
{
    BSP_W25QXX_Init();
    return 0;
}

static int read(uint32_t addr, uint8_t *data, uint32_t len)
{

    int ret;
    // lock
    // rt_mutex_take(&w25qxx_mutex, RT_WAITING_FOREVER);
    // __disable_irq();
	 ret = (LL_OK == BSP_W25QXX_Read(addr, data, len)) ? 0 : -1;
    // unlock
    // rt_mutex_release(&w25qxx_mutex);
    // __enable_irq();

    return ret != 0 ? -1 : (int)len;

}

static int write(uint32_t addr, const uint8_t *data, uint32_t len)
{

    size_t data_size;
    uint16_t write_gran = 256;
    size_t total = len;

//    int ret;
    // lock
    // rt_mutex_take(&w25qxx_mutex, RT_WAITING_FOREVER);
    // __disable_irq();

    while (len) {
        /* make write align and calculate next write address */
        if (addr % write_gran != 0) {
            if (len > write_gran - (addr % write_gran)) {
                data_size = write_gran - (addr % write_gran);
            } else {
                data_size = len;
            }
        } else {
            if (len > write_gran) {
                data_size = write_gran;
            } else {
                data_size = len;
            }
        }

        if( LL_OK != BSP_W25QXX_Write(addr, data, data_size))
        {
            // unlock
            // rt_mutex_release(&w25qxx_mutex);
            // __enable_irq();
            return -1;
        }

        addr += data_size;
        data += data_size;
        len -= data_size;
        // app_feed_wdt();
        es_os_feed_wdt();
    }
    // unlock
    // rt_mutex_release(&w25qxx_mutex);
    // __enable_irq();

    return total;
}

static int erase(uint32_t sector_addr)
{
    // lock
    // rt_mutex_take(&w25qxx_mutex, RT_WAITING_FOREVER);
    // __disable_irq();

    if( BSP_W25QXX_EraseSector(sector_addr) != LL_OK)
    {
        // unlock
        // rt_mutex_release(&w25qxx_mutex);
        // __enable_irq();
        return -1;
    }

    // unlock
    // rt_mutex_release(&w25qxx_mutex);
    // __enable_irq();


    return 0;
}


//static sfud_flash_t sfud_dev = NULL;
es_flash_dev_t w25qxx_flash =
{
    // "w25qxx",
    // 0,
    // 4 * 1024 * 1024,
    // 4096,
    // {init, read, write, erase},
    // 1

    .name = "w25qxx",
    .start_addr = 0,
    .size = 4 * 1024 * 1024,
    .sector_size = 4096,
    .write_gran = 256,
    
    .init = init,
    .read = read,
    .write = write,
    .erase_sector = erase
};
