/**
 * @file mem_v2_demo.c
 * @brief 内存管理器V2演示程序
 * <AUTHOR> Assistant
 * @date 2025/1/21
 */

#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdbool.h>
#include "../applications/es_mem_v2.h"

// 简单的日志实现
void es_log_write(int level, const char *tag, bool to_flash, const char *fmt, ...) {
    va_list args;
    va_start(args, fmt);
    printf("[%s] ", tag);
    vprintf(fmt, args);
    printf("\n");
    va_end(args);
}

static void demo_basic_usage(void) {
    printf("\n=== 基本使用演示 ===\n");
    
    // 分配不同大小的内存
    void *ptr1 = es_mem_v2_alloc(64);
    void *ptr2 = es_mem_v2_alloc(128);
    void *ptr3 = es_mem_v2_alloc(256);
    
    if (ptr1 && ptr2 && ptr3) {
        printf("✓ 成功分配三个内存块\n");
        
        // 写入测试数据
        strcpy((char*)ptr1, "Hello");
        strcpy((char*)ptr2, "Memory");
        strcpy((char*)ptr3, "Allocator V2");
        
        printf("  数据: '%s', '%s', '%s'\n", 
               (char*)ptr1, (char*)ptr2, (char*)ptr3);
        
        // 释放内存
        es_mem_v2_free(ptr1);
        es_mem_v2_free(ptr2);
        es_mem_v2_free(ptr3);
        printf("✓ 内存已释放\n");
    }
}

static void demo_next_fit_strategy(void) {
    printf("\n=== Next Fit策略演示 ===\n");
    
    // 分配多个小块
    void *ptrs[8];
    printf("分配8个100字节的块:\n");
    for (int i = 0; i < 8; i++) {
        ptrs[i] = es_mem_v2_alloc(100);
        if (ptrs[i]) {
            printf("  块%d: %p\n", i, ptrs[i]);
        }
    }
    
    // 释放一些中间的块
    printf("\n释放块2, 4, 6:\n");
    es_mem_v2_free(ptrs[2]);
    es_mem_v2_free(ptrs[4]);
    es_mem_v2_free(ptrs[6]);
    
    // 再次分配，观察Next Fit策略
    printf("\n使用Next Fit策略重新分配:\n");
    void *new_ptr1 = es_mem_v2_alloc(50);
    void *new_ptr2 = es_mem_v2_alloc(50);
    void *new_ptr3 = es_mem_v2_alloc(50);
    
    printf("  新块1: %p\n", new_ptr1);
    printf("  新块2: %p\n", new_ptr2);
    printf("  新块3: %p\n", new_ptr3);
    
    // 清理
    for (int i = 0; i < 8; i++) {
        if (i != 2 && i != 4 && i != 6) {
            es_mem_v2_free(ptrs[i]);
        }
    }
    es_mem_v2_free(new_ptr1);
    es_mem_v2_free(new_ptr2);
    es_mem_v2_free(new_ptr3);
}

static void demo_alignment(void) {
    printf("\n=== 4字节对齐演示 ===\n");
    
    // 测试不同大小的对齐
    struct {
        size_t size;
        void *ptr;
    } test_cases[] = {
        {1, NULL}, {3, NULL}, {5, NULL}, {7, NULL}, 
        {9, NULL}, {15, NULL}, {17, NULL}, {33, NULL}
    };
    
    int count = sizeof(test_cases) / sizeof(test_cases[0]);
    
    printf("分配不同大小并检查对齐:\n");
    for (int i = 0; i < count; i++) {
        test_cases[i].ptr = es_mem_v2_alloc(test_cases[i].size);
        if (test_cases[i].ptr) {
            uintptr_t addr = (uintptr_t)test_cases[i].ptr;
            bool aligned = (addr % 4) == 0;
            printf("  大小%2zu -> %p (4字节对齐: %s)\n", 
                   test_cases[i].size, test_cases[i].ptr, 
                   aligned ? "✓" : "✗");
        }
    }
    
    // 清理
    for (int i = 0; i < count; i++) {
        if (test_cases[i].ptr) {
            es_mem_v2_free(test_cases[i].ptr);
        }
    }
}

static void demo_realloc(void) {
    printf("\n=== 重分配演示 ===\n");
    
    // 初始分配
    void *ptr = es_mem_v2_alloc(100);
    if (!ptr) {
        printf("✗ 初始分配失败\n");
        return;
    }
    
    // 写入数据
    strcpy((char*)ptr, "Initial data");
    printf("初始数据: '%s' (地址: %p)\n", (char*)ptr, ptr);
    
    // 扩大内存
    ptr = es_mem_v2_realloc(ptr, 200);
    if (ptr) {
        printf("扩大到200字节: '%s' (地址: %p)\n", (char*)ptr, ptr);
        strcat((char*)ptr, " - Extended");
        printf("扩展后数据: '%s'\n", (char*)ptr);
    }
    
    // 缩小内存
    ptr = es_mem_v2_realloc(ptr, 50);
    if (ptr) {
        printf("缩小到50字节: '%s' (地址: %p)\n", (char*)ptr, ptr);
    }
    
    es_mem_v2_free(ptr);
}

static void demo_statistics(void) {
    printf("\n=== 统计信息演示 ===\n");
    
    es_mem_v2_stats_t stats;
    
    // 初始状态
    es_mem_v2_get_stats(&stats);
    printf("初始状态:\n");
    printf("  总大小: %u 字节\n", stats.total_size);
    printf("  空闲: %u 字节\n", stats.free_size);
    printf("  活跃块: %u\n", stats.active_blocks);
    
    // 分配一些内存
    void *ptr1 = es_mem_v2_alloc(500);
    void *ptr2 = es_mem_v2_alloc(300);
    void *ptr3 = es_mem_v2_alloc(200);
    
    es_mem_v2_get_stats(&stats);
    printf("\n分配后:\n");
    printf("  已用: %u 字节\n", stats.used_size);
    printf("  空闲: %u 字节\n", stats.free_size);
    printf("  活跃块: %u\n", stats.active_blocks);
    printf("  分配次数: %u\n", stats.alloc_count);
    
    // 释放部分内存
    es_mem_v2_free(ptr2);
    
    es_mem_v2_get_stats(&stats);
    printf("\n释放一个块后:\n");
    printf("  已用: %u 字节\n", stats.used_size);
    printf("  空闲: %u 字节\n", stats.free_size);
    printf("  活跃块: %u\n", stats.active_blocks);
    printf("  释放次数: %u\n", stats.free_count);
    
    // 清理
    es_mem_v2_free(ptr1);
    es_mem_v2_free(ptr3);
}

static void demo_error_handling(void) {
    printf("\n=== 错误处理演示 ===\n");
    
    // 测试过大分配
    void *ptr = es_mem_v2_alloc(ES_MEM_V2_MAX_ALLOC + 1);
    printf("过大分配 (>2KB): %s\n", ptr ? "意外成功" : "✓ 正确拒绝");
    
    // 测试零大小分配
    ptr = es_mem_v2_alloc(0);
    printf("零大小分配: %s\n", ptr ? "意外成功" : "✓ 正确拒绝");
    
    // 测试无效指针释放
    printf("释放无效指针: ");
    es_mem_v2_free((void*)0x12345678);
    printf("✓ 安全处理\n");
    
    // 测试双重释放
    ptr = es_mem_v2_alloc(100);
    if (ptr) {
        es_mem_v2_free(ptr);
        printf("双重释放: ");
        es_mem_v2_free(ptr);
        printf("✓ 检测到并处理\n");
    }
}

int main(void) {
    printf("=== 内存管理器V2演示程序 ===\n");
    printf("内存池大小: %d KB\n", ES_MEM_V2_POOL_SIZE / 1024);
    printf("最大分配: %d KB\n", ES_MEM_V2_MAX_ALLOC / 1024);
    printf("对齐大小: %d 字节\n", ES_MEM_V2_ALIGN_SIZE);
    
    // 初始化
    if (es_mem_v2_init() != 0) {
        printf("✗ 内存管理器初始化失败\n");
        return -1;
    }
    printf("✓ 内存管理器初始化成功\n");
    
    // 运行演示
    demo_basic_usage();
    demo_next_fit_strategy();
    demo_alignment();
    demo_realloc();
    demo_statistics();
    demo_error_handling();
    
    // 最终统计
    printf("\n=== 最终状态 ===\n");
    es_mem_v2_dump_stats();
    
    // 检查内存泄漏
    int leaks = es_mem_v2_check_leaks();
    if (leaks == 0) {
        printf("✓ 无内存泄漏\n");
    } else {
        printf("⚠ 检测到 %d 个内存泄漏\n", leaks);
    }
    
    // 反初始化
    es_mem_v2_deinit();
    printf("✓ 内存管理器已清理\n");
    
    printf("\n=== 演示完成 ===\n");
    return 0;
}
