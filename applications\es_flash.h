// Empty header file to resolve compilation issues
/**
 * @file es_flash.h
 * @brief ES Flash Partition Management Module Interface Definition
 */
#ifndef ES_FLASH_H
#define ES_FLASH_H

#include <stdint.h>
#include <stdbool.h>


#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    const char *name;               /* Partition name */

    uint32_t start_addr;         /* Start address */
    uint32_t size;               /* Partition size */
    uint32_t sector_size;        /* Sector size */
    int write_gran;           /* Write granularity */
    
    /* Flash operation interface functions */
    int (*init)(void);
    int (*read)(uint32_t addr, uint8_t *data, uint32_t len);
    int (*write)(uint32_t addr, const uint8_t *data, uint32_t len);
    int (*erase_sector)(uint32_t sector_addr);
} es_flash_dev_t;

/* Partition information structure */
typedef struct {
    const char *name;               /* Partition name */
    const es_flash_dev_t *flash_dev;   /* Flash device reference */
    uint32_t start_addr;         /* Start address */
    uint32_t size;               /* Partition size */
} es_partition_t;

/**
 * @brief Initialize partition
 * @return 0 for success, -1 for failure
 */
int es_flash_init(void);

/**
 * @brief Read partition data
 * @param part Partition pointer
 * @param offset Offset
 * @param data Data pointer
 * @param len Data length
 * @return 0 for success, -1 for failure
 */
int es_flash_read(const es_partition_t *part, uint32_t offset, uint8_t *data, uint32_t len);

/**
 * @brief Write partition data
 * @param part Partition pointer
 * @param offset Offset
 * @param data Data pointer
 * @param len Data length
 * @return 0 for success, -1 for failure
 */
int es_flash_write(const es_partition_t *part, uint32_t offset, const uint8_t *data, uint32_t len);

/**
 * @brief Erase partition data
 * @param part Partition pointer
 * @param offset Offset
 * @param len Data length
 * @return 0 for success, -1 for failure
 */
int es_flash_erase(const es_partition_t *part, uint32_t offset, uint32_t len);

/**
 * @brief Find partition information by partition name
 * @param part_name Partition name
 * @return Partition pointer
 */
const es_partition_t* es_partition_find(const char *part_name);


#ifdef __cplusplus
}
#endif

#endif /* ES_PARTITION_H */
