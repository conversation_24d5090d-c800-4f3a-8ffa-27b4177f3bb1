/*
 * es_ota.h
 *
 * OTA module header file
 */

#ifndef __ES_OTA_H__
#define __ES_OTA_H__

#include <stdint.h>
#include "es_flash.h"
#include "es_at_srv.h"
#include "es_crc.h"  // For CRC functionality
#include "es_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Firmware package magic number definition */
#define APP_FKG_MAGIC 0x5A5A5A5A
#define BOOT_FKG_MAGIC 0x6A6A6A6A
#define ECU_FKG_MAGIC 0x7A7A7A7A
#define BLE_FKG_MAGIC 0x8A8A8A8A

// OTA context


#pragma pack(1)

struct fw_info_t {
    uint32_t magic;
    uint8_t version[32];
    uint8_t prod_code[24];
    uint32_t size;
    uint32_t crc;
    uint8_t reserved[32];
    uint32_t random;
    uint32_t hdr_crc;
};

#pragma pack()

/**
 * @brief Initialize OTA module
 *
 * @return int Returns 0 on success, other values indicate failure
 */
int es_ota_init(void);

/**
 * @brief Read firmware information
 *
 * @param fw_info Firmware information structure pointer
 * @return int Returns 0 on success, other values indicate failure
 */
int es_ota_read_fw_info(struct fw_info_t *fw_info);

/**
 * @brief Check if downloaded firmware is valid
 *
 * @return int Returns 0 if valid, -1 if invalid
 */
int es_ota_download_fw_valid(void);

#ifdef __cplusplus
}
#endif

#endif /* __ES_OTA_H__ */ 
