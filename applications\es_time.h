#ifndef ES_TIME_H
#define ES_TIME_H

#include <stdint.h>

// Time structure (compatible with C standard library tm structure)
struct es_tm {
    int tm_sec;   // Seconds [0-59]
    int tm_min;   // Minutes [0-59]
    int tm_hour;  // Hours [0-23]
    int tm_mday;  // Day of month [1-31]
    int tm_mon;   // Month [0-11]
    int tm_year;  // Year (since 1900)
    int tm_wday;  // Day of week [0-6] (can be ignored)
    int tm_yday;  // Day of year [0-365] (can be ignored)
    int tm_isdst; // Daylight saving time flag (usually ignored in bare metal systems)
};

// Set current timestamp (seconds since 1970-01-01 00:00:00)
void es_set_timestamp(uint32_t timestamp);

// Get current timestamp (atomic read)
uint32_t es_get_timestamp(void);

uint32_t es_mktime( struct es_tm * const tm);

// Convert timestamp to tm structure (optimized version)
struct es_tm * es_gmtime_r(const uint32_t *timestamp, struct es_tm *tm);

// Convert timestamp to string (optimized buffer operations)
void es_timestamp_to_str(uint32_t timestamp, char buf[20]);

#endif /* ES_TIME_H */
