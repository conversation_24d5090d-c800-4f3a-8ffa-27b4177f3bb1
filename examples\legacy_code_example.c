/**
 * @file legacy_code_example.c
 * @brief Example showing legacy es_mem.h code working with new es_mem_v2.h
 * <AUTHOR> Assistant
 * @date 2025/1/21
 */

#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdbool.h>

// This would have been: #include "es_mem.h"
// Now it's: #include "es_mem_v2.h" (with compatibility layer)
#include "es_mem_v2.h"

// Simple log implementation for example
void es_log_write(int level, const char *tag, bool to_flash, const char *fmt, ...) {
    va_list args;
    va_start(args, fmt);
    printf("[%s] ", tag);
    vprintf(fmt, args);
    printf("\n");
    va_end(args);
}

/**
 * @brief Legacy function that uses old es_mem interface
 * This function was written for the old es_mem.h and works unchanged
 */
void legacy_memory_operations(void) {
    printf("\n=== Legacy Memory Operations ===\n");
    
    // Initialize memory system (old interface)
    es_mem_init();
    printf("✓ Memory system initialized\n");
    
    // Allocate some memory blocks (old interface)
    void *buffer1 = es_mem_alloc(256);
    void *buffer2 = es_mem_alloc(512);
    void *buffer3 = es_mem_alloc(128);
    
    if (buffer1 && buffer2 && buffer3) {
        printf("✓ Allocated three buffers: 256, 512, 128 bytes\n");
        
        // Use the memory
        strcpy((char*)buffer1, "Legacy buffer 1");
        strcpy((char*)buffer2, "Legacy buffer 2 - larger content");
        strcpy((char*)buffer3, "Legacy buffer 3");
        
        printf("✓ Data written to buffers\n");
        
        // Check block sizes (old interface)
        int size1 = es_mem_ptr_block_size(buffer1);
        int size2 = es_mem_ptr_block_size(buffer2);
        int size3 = es_mem_ptr_block_size(buffer3);
        
        printf("✓ Block sizes: %d, %d, %d bytes\n", size1, size2, size3);
        
        // Reallocate one buffer (old interface)
        buffer2 = es_mem_realloc(buffer2, 1024);
        if (buffer2) {
            printf("✓ Reallocated buffer2 to 1024 bytes\n");
            printf("✓ Data preserved: '%.30s'\n", (char*)buffer2);
        }
        
        // Show memory status (old interface)
        printf("\nMemory status:\n");
        es_mem_dump_status();
        
        // Free memory (old interface)
        es_mem_free(buffer1);
        es_mem_free_safe((void**)&buffer2);  // Safe free
        es_mem_free(buffer3);
        
        printf("✓ All memory freed\n");
        
        // Verify buffer2 was set to NULL by safe free
        if (buffer2 == NULL) {
            printf("✓ Safe free set pointer to NULL\n");
        }
    } else {
        printf("✗ Failed to allocate memory\n");
    }
}

/**
 * @brief Modern function that uses new es_mem_v2 interface
 * This function demonstrates new features available in V2
 */
void modern_memory_operations(void) {
    printf("\n=== Modern Memory Operations ===\n");
    
    // Use new interface for advanced features
    void *ptr = es_mem_v2_alloc(300);
    if (ptr) {
        printf("✓ Allocated 300 bytes using new interface\n");
        
        // Get detailed block information
        es_mem_v2_block_info_t info;
        if (es_mem_v2_get_block_info(ptr, &info) == 0) {
            printf("✓ Block info: ptr=%p, size=%u, actual_size=%u, allocated=%s\n",
                   info.ptr, info.size, info.actual_size, 
                   info.allocated ? "true" : "false");
        }
        
        // Get comprehensive statistics
        es_mem_v2_stats_t stats;
        if (es_mem_v2_get_stats(&stats) == 0) {
            printf("✓ Memory stats: total=%u, used=%u, free=%u, active_blocks=%u\n",
                   stats.total_size, stats.used_size, stats.free_size, stats.active_blocks);
        }
        
        // Check memory integrity
        int integrity = es_mem_v2_verify_integrity();
        printf("✓ Memory integrity check: %s\n", 
               integrity == 0 ? "PASSED" : "FAILED");
        
        es_mem_v2_free(ptr);
    }
    
    // Check for memory leaks
    int leaks = es_mem_v2_check_leaks();
    printf("✓ Memory leak check: %d leaks detected\n", leaks);
}

/**
 * @brief Demonstrate mixed usage of old and new interfaces
 */
void mixed_interface_demo(void) {
    printf("\n=== Mixed Interface Demo ===\n");
    
    // Allocate using old interface
    void *old_ptr = es_mem_alloc(200);
    printf("✓ Allocated using old interface: %p\n", old_ptr);
    
    // Allocate using new interface
    void *new_ptr = es_mem_v2_alloc(250);
    printf("✓ Allocated using new interface: %p\n", new_ptr);
    
    // Use new interface functions on old allocation
    es_mem_v2_block_info_t info;
    if (es_mem_v2_get_block_info(old_ptr, &info) == 0) {
        printf("✓ Got info for old allocation: size=%u\n", info.size);
    }
    
    // Use old interface functions on new allocation
    int size = es_mem_ptr_block_size(new_ptr);
    printf("✓ Got size for new allocation: %d\n", size);
    
    // Cross-interface free operations
    es_mem_v2_free(old_ptr);  // Free old allocation with new interface
    es_mem_free(new_ptr);     // Free new allocation with old interface
    
    printf("✓ Cross-interface operations successful\n");
}

/**
 * @brief Simulate legacy application code
 */
void simulate_legacy_application(void) {
    printf("\n=== Simulating Legacy Application ===\n");
    
    // This is how a typical legacy application might use memory
    typedef struct {
        char name[32];
        int id;
        float value;
    } data_record_t;
    
    // Allocate array of records
    int num_records = 10;
    data_record_t *records = (data_record_t*)es_mem_alloc(sizeof(data_record_t) * num_records);
    
    if (records) {
        printf("✓ Allocated array for %d records\n", num_records);
        
        // Initialize records
        for (int i = 0; i < num_records; i++) {
            snprintf(records[i].name, sizeof(records[i].name), "Record_%d", i);
            records[i].id = i + 1000;
            records[i].value = i * 3.14f;
        }
        
        printf("✓ Initialized %d records\n", num_records);
        
        // Expand array
        num_records = 15;
        records = (data_record_t*)es_mem_realloc(records, sizeof(data_record_t) * num_records);
        
        if (records) {
            printf("✓ Expanded array to %d records\n", num_records);
            
            // Initialize new records
            for (int i = 10; i < num_records; i++) {
                snprintf(records[i].name, sizeof(records[i].name), "Record_%d", i);
                records[i].id = i + 1000;
                records[i].value = i * 3.14f;
            }
            
            printf("✓ Initialized additional records\n");
            
            // Show some data
            printf("Sample records:\n");
            for (int i = 0; i < 3; i++) {
                printf("  %s: id=%d, value=%.2f\n", 
                       records[i].name, records[i].id, records[i].value);
            }
        }
        
        // Clean up
        es_mem_free(records);
        printf("✓ Freed record array\n");
    } else {
        printf("✗ Failed to allocate record array\n");
    }
}

int main(void) {
    printf("=== Legacy Code Compatibility Example ===\n");
    printf("This example shows how legacy es_mem.h code works unchanged with es_mem_v2.h\n");
    
    // Run legacy operations (unchanged code)
    legacy_memory_operations();
    
    // Run modern operations (new features)
    modern_memory_operations();
    
    // Demonstrate mixed usage
    mixed_interface_demo();
    
    // Simulate real legacy application
    simulate_legacy_application();
    
    // Final status
    printf("\n=== Final Status ===\n");
    es_mem_dump_status();
    
    printf("\n=== Example Completed Successfully ===\n");
    printf("Legacy code works unchanged while new features are available!\n");
    
    return 0;
}
