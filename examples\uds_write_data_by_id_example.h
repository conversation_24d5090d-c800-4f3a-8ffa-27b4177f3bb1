/**
 * @file uds_write_data_by_id_example.h
 * @brief Header for UDS Write Data By Identifier example
 * <AUTHOR> MCU Team
 * @date 2025-01-20
 */

#ifndef UDS_WRITE_DATA_BY_ID_EXAMPLE_H
#define UDS_WRITE_DATA_BY_ID_EXAMPLE_H

#include "es_coroutine.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Main example function for UDS Write Data By Identifier
 * @param coro Coroutine context
 * @return Coroutine return value
 * 
 * This function demonstrates:
 * - Basic write data by identifier operations
 * - Writing different data types (multi-byte, single byte)
 * - Write and read verification
 * - Proper error handling
 */
es_async_t uds_write_data_by_id_example_main(es_coro_t *coro);

#ifdef __cplusplus
}
#endif

#endif /* UDS_WRITE_DATA_BY_ID_EXAMPLE_H */
