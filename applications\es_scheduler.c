﻿/**
 * @file scheduler.c
 * @brief Coroutine scheduler implementation
 */

#include "es_scheduler.h"
#include "es_log.h"
#include "es_at_srv.h"
#include "es_mem.h"
#include "es_drv_os.h"
#include "es_coro.h"
#include "es_button.h"
#include <string.h>

#define TAG "SCHR"

/* Default scheduler instance */
static es_scheduler_t s_default_scheduler = {0};

/**
 * @brief Initialize coroutine scheduler
 */
void es_scheduler_init(es_scheduler_t *scheduler)
{
    if (scheduler == NULL) {
        return;
    }
    
    memset(scheduler, 0, sizeof(es_scheduler_t));
    es_init_list_head(&scheduler->tasks);
    es_init_list_head(&scheduler->timers);
    es_init_list_head(&scheduler->delayed_delete_timers);
    es_init_list_head(&scheduler->delayed_add_timers);
    es_init_list_head(&scheduler->event_subscribers);
    
    scheduler->in_process_timers = 0;
    scheduler->inited = true;
}

/**
 * @brief Get default scheduler
 */
es_scheduler_t *es_scheduler_get_default(void)
{
    if (!s_default_scheduler.inited) {
        es_scheduler_init(&s_default_scheduler);
    }
    
    return &s_default_scheduler;
}

/**
 * @brief Add coroutine task
 */
int es_scheduler_task_add(es_scheduler_t *scheduler, es_coro_task_t *task)
{
    if (scheduler == NULL || task == NULL || !scheduler->inited) {
        return -1;
    }

        
    /* Initialize task's coroutine context */
    es_coro_init(&task->coro);
    task->coro.enabled = true;
    
    /* Add to list */
    es_init_list_head(&task->node);
    es_list_add(&task->node, &scheduler->tasks);
    
    return 0;
}

/**
 * @brief Remove coroutine task
 */
int es_scheduler_task_remove(const es_scheduler_t *scheduler, es_coro_task_t *task)
{
    if (scheduler == NULL || !scheduler->inited) {
        return -1;
    }
    
    /* Remove node directly from list */
    es_list_del(&task->node);
    return 0;
}

/**
 * @brief Initialize timer
 */
void es_scheduler_timer_init(es_timer_t *timer, uint32_t period, es_timer_callback_t callback, void *ctx, bool one_shot)
{
    if (timer == NULL || callback == NULL) {
        return;
    }
    
    memset(timer, 0, sizeof(es_timer_t));
    timer->period = period;
    timer->callback = callback;
    timer->ctx = ctx;
    timer->one_shot = one_shot ? 1 : 0;
    timer->state = ES_TIMER_INACTIVE;
    timer->scheduler = NULL;
    timer->need_free = 0;  /* Default: no need to free memory */
    
    /* Initialize timer's coroutine context */
    es_coro_timeout_init(&timer->coro);
    
    /* Initialize doubly linked list node */
    es_init_list_head(&timer->node);
}

/**
 * @brief Add timer to scheduler
 */
int es_scheduler_timer_add(es_scheduler_t *scheduler, es_timer_t *timer)
{
    if (scheduler == NULL || timer == NULL || !scheduler->inited) {
        return -1;
    }
    
    /* Record owning scheduler */
    timer->scheduler = scheduler;
    
    /* Check if in timer processing */
    if (scheduler->in_process_timers) {
        /* If in timer processing, add to delayed add list */
        es_init_list_head(&timer->node);
        es_list_add(&timer->node, &scheduler->delayed_add_timers);
    } else {
        /* Add directly to timer list */
        es_init_list_head(&timer->node);
        es_list_add(&timer->node, &scheduler->timers);
    }
    
    return 0;
}

/**
 * @brief Remove timer from scheduler
 *
 * Only mark timer as deleted, actual removal operation is performed during next timer processing
 */
int es_scheduler_timer_remove(es_scheduler_t *scheduler, es_timer_t *timer)
{
    if (scheduler == NULL || timer == NULL || !scheduler->inited) {
        return -1;
    }

    /* Only set state to deleted, actual removal operation is performed in timer processing */
    timer->state = ES_TIMER_DELETED;
    
    return 0;
}

static int es_scheduler_timer_start_internal(es_scheduler_t *scheduler, es_timer_t *timer, bool immediate_run)
{
    if (scheduler == NULL || timer == NULL || !scheduler->inited) {
        return -1;
    }
    
    if (timer->state == ES_TIMER_DELETED) {
        /* Timer has been deleted, cannot start */
        return -1;
    }
    
    /* If timer is not assigned to scheduler, add to specified scheduler */
    if (timer->scheduler == NULL) {
        /* Call add function */
        es_scheduler_timer_add(scheduler, timer);
    } else if (timer->scheduler != scheduler) {
        /* Timer already belongs to other scheduler, cannot start */
        return -1;
    }
    
    /* Reset timer's coroutine context */
    es_coro_timeout_init(&timer->coro);
    
    /* Set next trigger time */
    timer->next_trigger_time = es_os_get_tick_ms() + (immediate_run ? 0 : timer->period);
    timer->state = ES_TIMER_ACTIVE;
    
    return 0;
    
}

/**
 * @brief Start timer and choose whether to execute immediately once
 */
int es_scheduler_timer_start_immediate(es_scheduler_t *scheduler, es_timer_t *timer)
{
    return es_scheduler_timer_start_internal(scheduler, timer, true);
}

/**
 * @brief Start timer (compatibility interface)
 */
int es_scheduler_timer_start(es_scheduler_t *scheduler, es_timer_t *timer)
{
    /* Call new interface, do not execute immediately */
    return es_scheduler_timer_start_internal(scheduler, timer, false);
}

/**
 * @brief Stop timer
 */
int es_scheduler_timer_stop(const es_scheduler_t *scheduler, es_timer_t *timer)
{
    if (scheduler == NULL || timer == NULL || !scheduler->inited) {
        return -1;
    }
    
    if (timer->state == ES_TIMER_DELETED) {
        /* Timer has been deleted, cannot stop */
        return -1;
    }
    
    timer->state = ES_TIMER_INACTIVE;
    return 0;
}

/**
 * @brief Reset timer
 */
int es_scheduler_timer_reset(const es_scheduler_t *scheduler, es_timer_t *timer)
{
    if (scheduler == NULL || timer == NULL || !scheduler->inited) {
        return -1;
    }
    
    if (timer->state == ES_TIMER_DELETED) {
        /* Timer has been deleted, cannot reset */
        return -1;
    }
    
    /* Reset timer's coroutine context */
    es_coro_timeout_init(&timer->coro);
    
    /* Reset next trigger time */
    timer->next_trigger_time = es_os_get_tick_ms() + timer->period;
    timer->state = ES_TIMER_ACTIVE;
    
    return 0;
}

/**
 * @brief Modify timer period
 */
int es_scheduler_timer_change_period(const es_scheduler_t *scheduler, es_timer_t *timer, uint32_t period)
{
    if (scheduler == NULL || timer == NULL || !scheduler->inited) {
        return -1;
    }
    
    if (timer->state == ES_TIMER_DELETED) {
        /* Timer has been deleted, cannot modify period */
        return -1;
    }
    
    timer->period = period;
    return es_scheduler_timer_reset(scheduler, timer);
}

/**
 * @brief Process timers
 * @return Number of triggered timers
 */
static int es_process_timers(es_scheduler_t *scheduler)
{
    
    int triggered = 0;
    uint32_t current_time = es_os_get_tick_ms();
    
    /* Set timer processing flag */
    scheduler->in_process_timers = 1;
    
    /* Iterate through timer list */
    struct es_list_head *pos, *n;
    es_list_for_each_safe(pos, n, &scheduler->timers) {
        es_timer_t *timer = es_list_entry(pos, es_timer_t, node);
        
        /* Check timer state, if deleted move to delayed delete list */
        if (timer->state == ES_TIMER_DELETED) {
            /* Remove from current timer list */
            es_list_del(pos);
            
            /* Add to delayed delete list */
            es_init_list_head(&timer->node);
            es_list_add(&timer->node, &scheduler->delayed_delete_timers);
            continue;
        }
        
        /* Check if immediate execution is needed */
        if (timer->state == ES_TIMER_ACTIVE && (int32_t)(current_time - timer->next_trigger_time) >= 0) {
            /* Execute timer callback */
            es_async_t ret = timer->callback(&timer->coro.coro, timer, timer->ctx);
            
            /* Check timer state */
            if (timer->state == ES_TIMER_DELETED) {
                /* Timer was marked for deletion in callback, will be moved to delayed delete list later */
                continue;
            }
            
            /* Handle coroutine return value */
            if (ret == ES_ASYNC_DONE || ret == ES_ASYNC_ERROR) {
                /* Coroutine completed or error occurred */
                if (timer->one_shot) {
                    /* One-shot timer, stop */
                    timer->state = timer->need_free ? ES_TIMER_DELETED : ES_TIMER_INACTIVE;
                } else {
                    /* Periodic timer, reset coroutine context */
                    es_coro_timeout_init(&timer->coro);
                    timer->next_trigger_time = current_time + timer->period;
                }
            }
            triggered++;
        }
    }
    
    /* Process delayed delete timers */
    es_list_for_each_safe(pos, n, &scheduler->delayed_delete_timers) {
        es_timer_t *timer = es_list_entry(pos, es_timer_t, node);
        
        /* Remove from delayed delete list */
        es_list_del(pos);
        
        /* Clear scheduler reference */
        timer->scheduler = NULL;
        
        /* If memory needs to be freed, free it safely */
        if (timer->need_free) {
            es_mem_free_safe((void **)&timer);
        }
    }
    
    /* Process delayed add timers */
    es_list_for_each_safe(pos, n, &scheduler->delayed_add_timers) {
        es_timer_t *timer = es_list_entry(pos, es_timer_t, node);
        
        /* Remove from delayed add list */
        es_list_del(pos);
        
        /* Add to timer list */
        es_init_list_head(&timer->node);
        es_list_add(&timer->node, &scheduler->timers);
    }
    
    /* Clear timer processing flag */
    scheduler->in_process_timers = 0;
    
    return triggered;
}

/**
 * @brief Process tasks
 * @return Number of executed tasks
 */
static int es_process_tasks(const es_scheduler_t *scheduler)
{
    int executed = 0;
    
    /* Iterate through list to execute tasks */
    struct es_list_head *pos, *n;
    es_list_for_each_safe(pos, n, &scheduler->tasks) {
        es_coro_task_t *task = es_list_entry(pos, es_coro_task_t, node);
        
        /* Check if task is valid and enabled */
        if (task->func == NULL || !task->coro.enabled) {
            continue;
        }
        
        /* Execute coroutine */
        task->func(&task->coro, task->ctx);
        executed++;
    }
    
    return executed;
}

/**
 * @brief Schedule once
 */
int es_scheduler_run_once(es_scheduler_t *scheduler)
{
    if (scheduler == NULL || !scheduler->inited) {
        return 0;
    }
    
    int run_count = 0;
    
    /* Process timers */
    run_count += es_process_timers(scheduler);
    
    /* Process tasks */
    run_count += es_process_tasks(scheduler);
    
    return run_count;
}

/**
 * @brief Allocate and initialize a timer
 */
es_timer_t *es_scheduler_timer_alloc(uint32_t period, es_timer_callback_t callback, void *ctx, bool one_shot)
{
    if (callback == NULL) {
        return NULL;
    }
    
    /* Allocate timer memory */
    es_timer_t *timer = (es_timer_t *)es_mem_alloc(sizeof(es_timer_t));
    if (timer == NULL) {
        return NULL;
    }
    
    /* Initialize timer */
    es_scheduler_timer_init(timer, period, callback, ctx, one_shot);
    
    /* Set to auto-free */
    timer->need_free = 1;
    
    return timer;
}


/**
 * @brief Run scheduler main loop
 */
void es_scheduler_run(es_scheduler_t *scheduler)
{
    if (scheduler == NULL || !scheduler->inited) {
        return;
    }
    
    while (1) {
        es_scheduler_run_once(scheduler);
        
        /* Can add delay to reduce CPU usage */
        /* In actual environment, may need to determine delay length based on next task execution time */
        //es_os_delay(1);
    }
}

/**
 * @brief Initialize event subscriber
 */
void es_scheduler_event_subscriber_init(es_event_subscriber_t *subscriber, 
                                        es_event_type_t event_type,
                                        es_event_handler_t handler, 
                                        void *handler_ctx)
{
    if (subscriber == NULL || handler == NULL) {
        return;
    }
    
    memset(subscriber, 0, sizeof(es_event_subscriber_t));
    subscriber->event_type = event_type;
    subscriber->handler = handler;
    subscriber->handler_ctx = handler_ctx;
    
    /* Initialize list node */
    es_init_list_head(&subscriber->node);
}

/**
 * @brief Subscribe to event
 */
int es_scheduler_event_subscribe(es_scheduler_t *scheduler, es_event_subscriber_t *subscriber)
{
    if (scheduler == NULL || subscriber == NULL || !scheduler->inited) {
        return -1;
    }
    
    /* Add to subscriber list */
    es_list_add(&subscriber->node, &scheduler->event_subscribers);
    
    return 0;
}

/**
 * @brief Unsubscribe from event
 */
int es_scheduler_event_unsubscribe(const es_scheduler_t *scheduler, es_event_subscriber_t *subscriber)
{
    if (scheduler == NULL || subscriber == NULL || !scheduler->inited) {
        return -1;
    }
    
    /* Remove from subscriber list */
    es_list_del(&subscriber->node);
    
    return 0;
}

/**
 * @brief Synchronously publish event (using pointer data, trigger processing immediately)
 */
int es_scheduler_event_publish(es_scheduler_t *scheduler, es_event_type_t event_type, void *data, uint16_t data_len)
{
    if (scheduler == NULL || !scheduler->inited) {
        return -1;
    }
    
    /* Create temporary event data */
    es_event_t event = {
        .type = event_type,
        .data_len = data_len,
        .use_dynamic_alloc = 0,  /* Synchronous events do not use dynamic allocation */
        .u.data = data
    };
    
    /* Iterate through all subscribers and process immediately */
    struct es_list_head *pos;
    es_list_for_each(pos, &scheduler->event_subscribers) {
        es_event_subscriber_t *subscriber = es_list_entry(pos, es_event_subscriber_t, node);
        
        /* Check if event type matches (0 means subscribe to all events) */
        if (subscriber->event_type != 0 && subscriber->event_type != event.type) {
            continue;
        }
        
        /* Call event handler */
        subscriber->handler(&event, subscriber->handler_ctx);
    }
    
    return 0;
}

/**
 * @brief Synchronously publish event (using 32-bit value data, trigger processing immediately)
 */
int es_scheduler_event_publish_value(es_scheduler_t *scheduler, es_event_type_t event_type, uint32_t value)
{
    if (scheduler == NULL || !scheduler->inited) {
        return -1;
    }
    
    /* Create temporary event data */
    es_event_t event = {
        .type = event_type,
        .data_len = 0,  /* When using value data, length is 0 */
        .use_dynamic_alloc = 0,  /* Synchronous events do not use dynamic allocation */
        .u.value = value  /* Use union's value field */
    };
    
    /* Iterate through all subscribers and process immediately */
    struct es_list_head *pos;
    es_list_for_each(pos, &scheduler->event_subscribers) {
        es_event_subscriber_t *subscriber = es_list_entry(pos, es_event_subscriber_t, node);
        
        /* Check if event type matches (0 means subscribe to all events) */
        if (subscriber->event_type != 0 && subscriber->event_type != event.type) {
            continue;
        }
        
        /* Call event handler */
        subscriber->handler(&event, subscriber->handler_ctx);
    }
    
    return 0;
}

