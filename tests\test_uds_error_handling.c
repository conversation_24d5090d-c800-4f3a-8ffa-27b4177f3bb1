/**
 * @file test_uds_error_handling.c
 * @brief UDS错误处理测试
 * <AUTHOR>
 * @date 2025/1/17
 * @copyright Copyright (c) 2025
 */

#include "es_uds_client.h"
#include "es_uds_ota.h"
#include "es_coro.h"
#include "es_log.h"
#include <stdio.h>
#include <assert.h>
#include <string.h>

#define TAG "TEST_UDS_ERR"

// 测试统计
static int test_passed = 0;
static int test_failed = 0;

// 测试宏
#define TEST_ASSERT(condition, message) \
    do { \
        if (condition) { \
            test_passed++; \
            printf("✓ PASS: %s\n", message); \
        } else { \
            test_failed++; \
            printf("✗ FAIL: %s\n", message); \
        } \
    } while(0)

#define TEST_ASSERT_NOT_NULL(ptr, message) TEST_ASSERT((ptr) != NULL, message)
#define TEST_ASSERT_EQUAL(expected, actual, message) \
    TEST_ASSERT((expected) == (actual), message)

// 模拟错误的CAN写入函数
static es_async_t mock_can_write_error(es_coro_t *coro, uint32_t id, const uint8_t *data, uint8_t len)
{
    es_co_begin(coro);
    
    // 模拟CAN写入错误
    ES_PRINTF_E(TAG, "Mock CAN write error");
    es_co_err;
    
    es_co_end;
}

// 模拟正常的CAN写入函数
static es_async_t mock_can_write_success(es_coro_t *coro, uint32_t id, const uint8_t *data, uint8_t len)
{
    es_co_begin(coro);
    
    // 模拟成功的CAN写入
    ES_PRINTF_I(TAG, "Mock CAN write success");
    
    es_co_end;
}

// 测试协程错误传播
static es_async_t test_error_propagation_coro(es_coro_t *coro, int *result)
{
    es_co_begin(coro);
    
    // 调用会出错的函数
    es_co_await_ex(err, mock_can_write_error);
    
    // 如果到达这里，说明错误没有被正确传播
    *result = 0;
    es_co_end;
    
err:
    // 错误被正确传播到这里
    *result = 1;
    ES_PRINTF_I(TAG, "Error correctly propagated");
    es_co_err;
}

/**
 * @brief 测试协程错误传播机制
 */
static void test_coroutine_error_propagation(void)
{
    printf("\n--- Testing Coroutine Error Propagation ---\n");
    
    es_coro_t coro = {0};
    int result = 0;
    es_async_t status;
    
    // 运行测试协程
    do {
        status = test_error_propagation_coro(&coro, &result);
    } while (status == ES_ASYNC_YIELD || status == ES_ASYNC_WAIT);
    
    // 验证错误被正确传播
    TEST_ASSERT_EQUAL(ES_ASYNC_ERROR, status, "Coroutine should return error status");
    TEST_ASSERT_EQUAL(1, result, "Error should be propagated to error handler");
}

// 测试UDS客户端错误处理的协程
static es_async_t test_uds_client_error_coro(es_coro_t *coro, int *error_handled)
{
    es_co_begin(coro);
    
    // 模拟UDS客户端连接
    es_uds_client_connection_t conn = {0};
    conn.state = ES_UDS_CLIENT_STATE_IDLE;
    
    // 尝试发送诊断会话控制请求（会失败，因为没有实际的ISO-TP连接）
    es_co_await_ex(err, es_uds_client_diagnostic_session_control, &conn, 
                   ES_UDS_SESSION_PROGRAMMING, NULL, NULL);
    
    // 如果到达这里，说明错误没有被处理
    *error_handled = 0;
    es_co_end;
    
err:
    // 错误被正确处理
    *error_handled = 1;
    ES_PRINTF_I(TAG, "UDS client error correctly handled");
    es_co_err;
}

/**
 * @brief 测试UDS客户端错误处理
 */
static void test_uds_client_error_handling(void)
{
    printf("\n--- Testing UDS Client Error Handling ---\n");
    
    es_coro_t coro = {0};
    int error_handled = 0;
    es_async_t status;
    
    // 运行测试协程
    do {
        status = test_uds_client_error_coro(&coro, &error_handled);
    } while (status == ES_ASYNC_YIELD || status == ES_ASYNC_WAIT);
    
    // 验证错误被正确处理
    TEST_ASSERT_EQUAL(ES_ASYNC_ERROR, status, "UDS client should return error status");
    TEST_ASSERT_EQUAL(1, error_handled, "UDS client error should be handled");
}

// 测试UDS OTA错误处理的协程
static es_async_t test_uds_ota_error_coro(es_coro_t *coro, int *error_handled)
{
    es_co_begin(coro);
    
    // 创建一个简单的OTA工作流
    es_uds_ota_workflow_t workflow = {0};
    workflow.initialized = 1;
    workflow.step_count = 1;
    
    // 添加一个会失败的步骤
    es_uds_ota_step_t step = {0};
    step.name = "test_step";
    step.execute = NULL; // 这会导致失败
    workflow.steps = &step;
    
    // 执行工作流（应该立即失败）
    es_co_await_ex(err, es_uds_ota_execute, &workflow);
    
    // 如果到达这里，说明错误没有被处理
    *error_handled = 0;
    es_co_end;
    
err:
    // 错误被正确处理
    *error_handled = 1;
    ES_PRINTF_I(TAG, "UDS OTA error correctly handled");
    es_co_err;
}

/**
 * @brief 测试UDS OTA错误处理
 */
static void test_uds_ota_error_handling(void)
{
    printf("\n--- Testing UDS OTA Error Handling ---\n");

    es_coro_t coro = {0};
    int error_handled = 0;
    es_async_t status;

    // 运行测试协程
    do {
        status = test_uds_ota_error_coro(&coro, &error_handled);
    } while (status == ES_ASYNC_YIELD || status == ES_ASYNC_WAIT);

    // 验证错误被正确处理
    TEST_ASSERT_EQUAL(ES_ASYNC_ERROR, status, "UDS OTA should return error status");
    TEST_ASSERT_EQUAL(1, error_handled, "UDS OTA error should be handled");
}

// 测试超时直接退出的协程
static es_async_t test_timeout_direct_exit_coro(es_coro_t *coro, int *timeout_handled)
{
    es_co_begin(coro);

    // 模拟UDS客户端连接
    es_uds_client_connection_t conn = {0};
    conn.state = ES_UDS_CLIENT_STATE_WAITING_RESPONSE;
    conn.p2_timeout = 100; // 短超时用于测试
    conn.timeout_start_time = es_time_get_ms() - 200; // 模拟已经超时

    // 模拟超时处理（这应该设置错误状态而不调用回调）
    if (es_time_get_ms() - conn.timeout_start_time > conn.p2_timeout) {
        conn.state = ES_UDS_CLIENT_STATE_TIMEOUT_P2;
        conn.p2_timeouts++;
        *timeout_handled = 1;
        es_co_err; // 直接错误退出
    }

    // 如果到达这里，说明超时没有被正确处理
    *timeout_handled = 0;
    es_co_end;

err:
    // 超时被正确处理并直接退出
    ES_PRINTF_I(TAG, "Timeout correctly handled with direct exit");
    es_co_err;
}

/**
 * @brief 测试超时直接退出机制
 */
static void test_timeout_direct_exit(void)
{
    printf("\n--- Testing Timeout Direct Exit ---\n");

    es_coro_t coro = {0};
    int timeout_handled = 0;
    es_async_t status;

    // 运行测试协程
    do {
        status = test_timeout_direct_exit_coro(&coro, &timeout_handled);
    } while (status == ES_ASYNC_YIELD || status == ES_ASYNC_WAIT);

    // 验证超时被正确处理并直接退出
    TEST_ASSERT_EQUAL(ES_ASYNC_ERROR, status, "Timeout should cause direct error exit");
    TEST_ASSERT_EQUAL(1, timeout_handled, "Timeout should be detected and handled");
}

/**
 * @brief 运行所有测试
 */
void run_uds_error_handling_tests(void)
{
    printf("=== UDS Error Handling Tests ===\n");

    test_coroutine_error_propagation();
    test_uds_client_error_handling();
    test_uds_ota_error_handling();
    test_timeout_direct_exit();

    printf("\n=== Test Results ===\n");
    printf("Passed: %d\n", test_passed);
    printf("Failed: %d\n", test_failed);
    printf("Total:  %d\n", test_passed + test_failed);

    if (test_failed == 0) {
        printf("✓ All tests passed!\n");
    } else {
        printf("✗ Some tests failed!\n");
    }
}

/**
 * @brief 主测试函数
 */
int main(void)
{
    // 初始化日志系统
    es_log_init();
    
    // 运行测试
    run_uds_error_handling_tests();
    
    return test_failed;
}
