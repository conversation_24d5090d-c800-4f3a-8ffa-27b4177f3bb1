{"version": 1, "frames": [{"cmd": 1001, "data": [{"id": 1939, "signals": [{"id": 101, "start_bit": 0, "len": 8}, {"id": 102, "start_bit": 8, "len": 16}]}], "rule": {"cycle": 1000, "delay": 100, "start_cond": {"type": "constant", "value": 100}}}, {"cmd": 1002, "data": [{"id": 1110, "signals": [{"id": 201, "start_bit": 0, "len": 32}]}], "rule": {"cycle": 2000, "delay": 0, "start_cond": {"type": "signal", "value": 101}}}, {"cmd": 1003, "data": [{"id": 1929, "signals": [{"id": 301, "start_bit": 0, "len": 16}]}], "rule": {"cycle": 500, "delay": 50, "start_cond": {"type": "event", "value": 201}}}, {"cmd": 1004, "data": [{"id": 1234, "signals": [{"id": 401, "start_bit": 0, "len": 8}]}], "rule": {"cycle": 1500, "delay": 200, "start_cond": {"type": "message_time", "value": 1939}}}, {"cmd": 1005, "data": [{"id": 5678, "signals": [{"id": 501, "start_bit": 0, "len": 16}]}], "rule": {"cycle": 3000, "delay": 0, "start_cond": {"type": "system_time", "value": null}}}, {"cmd": 1006, "data": [{"id": 9999, "signals": [{"id": 601, "start_bit": 0, "len": 8}]}], "rule": {"cycle": 800, "delay": 100, "start_cond": {"type": "io", "value": 5}}}, {"cmd": 1007, "data": [{"id": 1111, "signals": [{"id": 701, "start_bit": 0, "len": 32}]}], "rule": {"cycle": 1200, "delay": 150, "start_cond": {"type": "compare", "value": {"operator": ">", "left": {"type": "signal", "value": 101}, "right": {"type": "constant", "value": 50}}}}}, {"cmd": 1008, "data": [{"id": 2222, "signals": [{"id": 801, "start_bit": 0, "len": 16}]}], "rule": {"cycle": 2500, "delay": 300, "start_cond": {"type": "logic", "value": {"operator": "and", "operands": [{"type": "signal", "value": 101}, {"type": "io", "value": 3}]}}}}, {"cmd": 1009, "data": [{"id": 3333, "signals": [{"id": 901, "start_bit": 0, "len": 8}]}], "rule": {"cycle": 1800, "delay": 250, "start_cond": {"type": "arithmetic", "value": {"operator": "+", "left": {"type": "signal", "value": 201}, "right": {"type": "constant", "value": 10}}}}}, {"cmd": 1010, "data": [{"id": 4444, "signals": [{"id": 1001, "start_bit": 0, "len": 16}]}], "rule": {"cycle": 2200, "delay": 400, "start_cond": {"type": "bitwise", "value": {"operator": "&", "left": {"type": "signal", "value": 301}, "right": {"type": "constant", "value": 255}}}}}, {"cmd": 1011, "data": [{"id": 5555, "signals": [{"id": 1101, "start_bit": 0, "len": 8}]}], "rule": {"cycle": 1000, "delay": 0, "start_cond": {"type": "compare", "value": {"operator": ">=", "left": {"type": "arithmetic", "value": {"operator": "*", "left": {"type": "signal", "value": 101}, "right": {"type": "constant", "value": 2}}}, "right": {"type": "constant", "value": 100}}}}}, {"cmd": 1012, "data": [{"id": 6666, "signals": [{"id": 1201, "start_bit": 0, "len": 16}]}], "rule": {"cycle": 1500, "delay": 100, "start_cond": {"type": "logic", "value": {"operator": "or", "operands": [{"type": "compare", "value": {"operator": "==", "left": {"type": "signal", "value": 201}, "right": {"type": "constant", "value": 0}}}, {"type": "io", "value": 7}]}}}}, {"cmd": 1013, "data": [{"id": 7777, "signals": [{"id": 1301, "start_bit": 0, "len": 32}]}], "rule": {"cycle": 3000, "delay": 500, "start_cond": {"type": "logic", "value": {"operator": "not", "operands": [{"type": "compare", "value": {"operator": "!=", "left": {"type": "bitwise", "value": {"operator": ">>", "left": {"type": "signal", "value": 301}, "right": {"type": "constant", "value": 4}}}, "right": {"type": "constant", "value": 15}}}]}}}}, {"cmd": 1014, "data": [{"id": 8888, "signals": [{"id": 1401, "start_bit": 0, "len": 8}]}], "rule": {"cycle": 800, "delay": 200, "start_cond": {"type": "bitwise", "value": {"operator": "~", "left": {"type": "signal", "value": 401}}}}}]}