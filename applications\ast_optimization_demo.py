#!/usr/bin/env python3
"""
AST可视化和优化演示程序

展示编译器的AST生成、可视化和优化功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from c_to_vm_compiler import Compiler

def demo_constant_folding():
    """常量折叠优化演示"""
    print("🔧 常量折叠优化演示")
    print("=" * 40)
    
    c_code = """
    int main() {
        register int a = 5 + 3;           // 8
        register int b = 10 * 2 - 5;      // 15
        register int c = (4 << 2) | 1;    // 17
        register int d = ~0xFF & 0x0F;    // 0
        return a + b + c + d;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=True)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def demo_algebraic_simplification():
    """代数简化优化演示"""
    print("🧮 代数简化优化演示")
    print("=" * 40)
    
    c_code = """
    int main() {
        register int x = 42;
        register int a = x + 0;      // x
        register int b = x * 1;      // x
        register int c = x * 0;      // 0
        register int d = x & 0;      // 0
        register int e = x | 0;      // x
        register int f = x ^ 0;      // x
        return a + b + c + d + e + f;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=True)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def demo_dead_code_elimination():
    """死代码消除演示"""
    print("💀 死代码消除演示")
    print("=" * 40)
    
    c_code = """
    int main() {
        register int result = 10;
        
        if (1) {
            result = result + 5;
        } else {
            result = 999;  // 死代码
        }
        
        if (0) {
            result = 888;  // 死代码
        }
        
        while (0) {
            result = 777;  // 死代码
        }
        
        return result;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=True)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def demo_vm_syscalls():
    """VM系统调用演示"""
    print("🔌 VM系统调用演示")
    print("=" * 40)
    
    c_code = """
    int main() {
        // 获取当前时间
        register int current_time = get_time();
        
        // 获取事件值
        register int event_status = get_event(42);
        
        // 获取IO电平
        register int pin_level = get_io(5);
        
        // 获取信号值 (消息索引1, 信号索引0)
        register int signal_value = get_signal(1, 0);
        
        // 获取消息时间戳
        register int msg_timestamp = get_msg_time(0);
        
        // 组合结果
        register int result = current_time + event_status;
        result = result + pin_level + signal_value;
        
        if (msg_timestamp > 0) {
            result = result + msg_timestamp;
        }
        
        return result;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=True)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def demo_complex_optimization():
    """复杂优化演示"""
    print("🎯 复杂优化演示")
    print("=" * 40)
    
    c_code = """
    int main() {
        register int base = 10;
        
        // 多层常量折叠
        register int calc1 = (5 + 3) * (2 + 2) - 1;  // 31
        
        // 混合代数简化
        register int calc2 = base * 1 + 0 - 0;  // base
        
        // 条件优化
        if (calc1 > 30) {  // 常量条件 31 > 30 = true
            calc2 = calc2 + calc1;
        } else {
            calc2 = 0;  // 死代码
        }
        
        // 无效循环消除
        while (0) {
            calc2 = calc2 * 2;  // 死代码
        }
        
        // 系统调用
        register int time_val = get_time();
        
        return calc2 + time_val;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=True)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    
    # 对比未优化版本
    print("\n--- 优化对比 ---")
    compiler_no_opt = Compiler(enable_optimization=False, show_ast=False)
    bytecode_no_opt = compiler_no_opt.compile(c_code)
    
    print(f"未优化字节码: {len(bytecode_no_opt)} bytes")
    print(f"优化后字节码: {len(bytecode)} bytes")
    savings = len(bytecode_no_opt) - len(bytecode)
    percentage = (savings / len(bytecode_no_opt)) * 100 if len(bytecode_no_opt) > 0 else 0
    print(f"节省: {savings} bytes ({percentage:.1f}%)")
    print()

def demo_ast_structure():
    """AST结构演示"""
    print("🌳 AST结构演示")
    print("=" * 40)
    
    c_code = """
    int main() {
        register int a = 5;
        register int b = 10;
        
        if (a < b) {
            register int temp = a + b;
            a = temp * 2;
        }
        
        while (a > 0) {
            a = a - 1;
        }
        
        return a;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    # 只显示AST，不编译
    compiler = Compiler(enable_optimization=False, show_ast=True)
    compiler.compile(c_code)
    print()

def main():
    print("🚀 AST可视化和优化演示")
    print("=" * 60)
    print()
    
    try:
        demo_ast_structure()
        demo_constant_folding()
        demo_algebraic_simplification()
        demo_dead_code_elimination()
        demo_vm_syscalls()
        demo_complex_optimization()
        
        print("🎉 所有演示完成!")
        print("\n📋 支持的优化类型:")
        print("  ✅ 常量折叠 (Constant Folding)")
        print("  ✅ 代数简化 (Algebraic Simplification)")
        print("  ✅ 死代码消除 (Dead Code Elimination)")
        print("  ✅ 条件分支优化 (Branch Optimization)")
        
        print("\n🔌 支持的VM系统调用:")
        print("  ✅ get_time() - 获取当前时间")
        print("  ✅ get_event(id) - 获取事件值")
        print("  ✅ get_io(pin) - 获取IO电平")
        print("  ✅ get_signal(msg_idx, sig_idx) - 获取信号值")
        print("  ✅ get_msg_time(msg_idx) - 获取消息时间戳")
        
    except Exception as e:
        print(f"❌ 演示错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
