﻿/**
 * @file es_utils.c
 * @brief Common utility functions implementation
 */
#include "es_utils.h"
#include <string.h>

uint8_t es_utils_digital_filter(uint8_t history, uint8_t current_state, uint8_t threshold) {
    uint8_t result = history & DIGITAL_FILTER_RESULT_MASK;
    uint8_t count = (history & DIGITAL_FILTER_HISTORY_MASK) >> 1;
    
    if (current_state == result) {
        if (count < threshold) {
            count++;
        }
    } else {
        if (count > 0) {
            count--;
        } else {
            result = current_state;
        }
    }
    
    return ((count << 1) & DIGITAL_FILTER_HISTORY_MASK) | (result & DIGITAL_FILTER_RESULT_MASK);
}
