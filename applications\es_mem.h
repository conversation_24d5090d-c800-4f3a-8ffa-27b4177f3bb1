/**
 * @file es_mem_v2.h
 * @brief Memory Management Module V2 - Next Fit Strategy Implementation
 * <AUTHOR> Assistant
 * @date 2025/1/21
 * @copyright Copyright (c) 2025
 */

#ifndef ES_MEM_V2_H
#define ES_MEM_V2_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

// Memory pool configuration
#define ES_MEM_V2_POOL_SIZE     (10 * 1024)    // 10KB memory pool
#define ES_MEM_V2_MAX_ALLOC     (2 * 1024)     // Maximum allocation 2KB
#define ES_MEM_V2_ALIGN_SIZE    4               // 4-byte alignment
#define ES_MEM_V2_HEADER_SIZE   4               // Metadata header 4 bytes

// Alignment macro
#define ES_MEM_V2_ALIGN(size) (((size) + ES_MEM_V2_ALIGN_SIZE - 1) & ~(ES_MEM_V2_ALIGN_SIZE - 1))

// Memory block header structure - using bit fields compressed to 4 bytes
// Since 10KB requires 14 bits, we use 14 bits to store size (in 4-byte units)
typedef struct {
    uint32_t current_size : 14;    // Current block size/4 (0-16383*4 = 64KB range)
    uint32_t prev_size : 14;       // Previous block size/4 (0-16383*4 = 64KB range)
    uint32_t allocated : 1;        // Allocation status (0=free, 1=allocated)
    uint32_t reserved : 3;         // Reserved bits
} es_mem_v2_header_t;

// Memory statistics information
typedef struct {
    uint32_t total_size;           // Total memory size
    uint32_t used_size;            // Used memory
    uint32_t free_size;            // Free memory
    uint32_t largest_free;         // Largest free block
    uint32_t alloc_count;          // Allocation count
    uint32_t free_count;           // Free count
    uint32_t alloc_fail_count;     // Allocation failure count
    uint32_t active_blocks;        // Active block count
} es_mem_v2_stats_t;

// Memory block information
typedef struct {
    void *ptr;                     // Block pointer
    uint32_t size;                 // Requested size
    uint32_t actual_size;          // Actual size
    bool allocated;                // Allocation status
} es_mem_v2_block_info_t;

/**
 * @brief Initialize memory manager V2
 *
 * @return int 0=success, <0=failure
 */
int es_mem_v2_init(void);

/**
 * @brief Deinitialize memory manager V2
 *
 * @return int 0=success, <0=failure
 */
int es_mem_v2_deinit(void);

/**
 * @brief Allocate memory - Next Fit strategy
 *
 * @param size Requested memory size
 * @return void* Allocated memory pointer, NULL on failure
 */
void* es_mem_v2_alloc(size_t size);

/**
 * @brief Allocate and zero memory
 *
 * @param num Number of elements
 * @param size Size of each element
 * @return void* Allocated memory pointer, NULL on failure
 */
void* es_mem_v2_calloc(size_t num, size_t size);

/**
 * @brief Reallocate memory
 *
 * @param ptr Original memory pointer
 * @param size New size
 * @return void* New memory pointer, NULL on failure
 */
void* es_mem_v2_realloc(void *ptr, size_t size);

/**
 * @brief Free memory
 *
 * @param ptr Memory pointer to free
 */
void es_mem_v2_free(void *ptr);

/**
 * @brief Safely free memory and set pointer to NULL
 *
 * @param ptr Pointer to memory pointer
 */
void es_mem_v2_free_safe(void **ptr);

/**
 * @brief Get memory statistics
 *
 * @param stats Statistics structure pointer
 * @return int 0=success, <0=failure
 */
int es_mem_v2_get_stats(es_mem_v2_stats_t *stats);

/**
 * @brief Print memory statistics
 */
void es_mem_v2_dump_stats(void);

/**
 * @brief Print memory pool status
 */
void es_mem_v2_dump_pools(void);

/**
 * @brief Get memory block information
 *
 * @param ptr Memory pointer
 * @param info Block information structure pointer
 * @return int 0=success, <0=failure
 */
int es_mem_v2_get_block_info(void *ptr, es_mem_v2_block_info_t *info);

/**
 * @brief Check memory integrity
 *
 * @return int 0=intact, <0=corrupted
 */
int es_mem_v2_verify_integrity(void);

/**
 * @brief Check for memory leaks
 *
 * @return int Number of leaked blocks
 */
int es_mem_v2_check_leaks(void);

/**
 * @brief Memory defragmentation
 *
 * @return int Number of merged blocks
 */
int es_mem_v2_defragment(void);

// Debug version allocation functions
#ifdef ES_MEM_V2_DEBUG
void* es_mem_v2_alloc_debug(size_t size, const char *file, int line);
void* es_mem_v2_calloc_debug(size_t num, size_t size, const char *file, int line);
void* es_mem_v2_realloc_debug(void *ptr, size_t size, const char *file, int line);

#define es_mem_v2_alloc(size) es_mem_v2_alloc_debug(size, __FILE__, __LINE__)
#define es_mem_v2_calloc(num, size) es_mem_v2_calloc_debug(num, size, __FILE__, __LINE__)
#define es_mem_v2_realloc(ptr, size) es_mem_v2_realloc_debug(ptr, size, __FILE__, __LINE__)
#endif

// =============================================================================
// Compatibility layer for es_mem.h interface
// =============================================================================

/**
 * @brief Initialize memory manager (compatibility function)
 *
 * @return int 0=success, <0=failure
 */
static inline int es_mem_init(void) {
    return es_mem_v2_init();
}

/**
 * @brief Allocate memory (compatibility function)
 *
 * @param size Requested memory size
 * @return void* Allocated memory pointer, NULL on failure
 */
static inline void* es_mem_alloc(size_t size) {
    return es_mem_v2_alloc(size);
}

/**
 * @brief Reallocate memory (compatibility function)
 *
 * @param ptr Original memory pointer
 * @param size New size
 * @return void* New memory pointer, NULL on failure
 */
static inline void* es_mem_realloc(void *ptr, size_t size) {
    return es_mem_v2_realloc(ptr, size);
}

/**
 * @brief Free memory (compatibility function)
 *
 * @param ptr Memory pointer to free
 */
static inline void es_mem_free(void *ptr) {
    es_mem_v2_free(ptr);
}

/**
 * @brief Safely free memory and set pointer to NULL (compatibility function)
 *
 * @param ptr Pointer to memory pointer
 */
static inline void es_mem_free_safe(void **ptr) {
    es_mem_v2_free_safe(ptr);
}

/**
 * @brief Get memory block size (compatibility function)
 *
 * @param ptr Memory pointer
 * @return int Block size in bytes, -1 on error
 */
static inline int es_mem_ptr_block_size(void *ptr) {
    es_mem_v2_block_info_t info;
    if (es_mem_v2_get_block_info(ptr, &info) == 0) {
        return (int)info.size;
    }
    return -1;
}

/**
 * @brief Print memory status (compatibility function)
 */
static inline void es_mem_dump_status(void) {
    es_mem_v2_dump_stats();
}

// Legacy compatibility macros
#define ES_MEM_H  // Prevent inclusion of old es_mem.h

// =============================================================================
// AT Command Support
// =============================================================================


#ifdef __cplusplus
}
#endif

#endif /* ES_MEM_V2_H */
