//
// Created by lxy on 2025/1/14.
// ES Log Storage - Ring buffer log storage with flash persistence implementation
//

#include "es_log_v2_fs.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

#ifndef ES_LOG_V2_TESTING
#include "es_flash.h"
#include "es_drv_os.h"
#include "es_at_srv.h"
#include "es_mem.h"
#include "es_log.h"
#endif

#define ES_LOG_V2_ALIGN_SIZE            4
#define ES_LOG_V2_SLOT_STATUS_EMPTY          0xFFFFFFFF
#define ES_LOG_V2_SLOT_STATUS_USED           0xFFFF0000

#define TAG "LOG_V2"

// Forward declarations
static int scan_write_slot(uint32_t sector_id);

// Global context
static es_log_v2_context_t s_log_ctx = {0};



#ifndef ES_LOG_V2_TESTING

static es_log_sink_t log_v2_sink = {0};

/* Log redirection callback function */
static void es_log_v2_sink(es_log_level_t level, bool to_flash, const char *log_msg, uint32_t len)
{
    //level must > ES_LOG_LEVEL_INFO
    if(level >= ES_LOG_LEVEL_DEBUG || !to_flash || !s_log_ctx.initialized) {
        return;
    }
    /* Write logs to Flash storage */
    es_log_v2_write(log_msg, len);
}

// Static variables for flash interface implementation
static const es_partition_t *log_partition = NULL;
static uint32_t timestamp_counter = 0;

// Implementation of external flash interface functions using es_flash
int log_flash_read(uint32_t addr, void *data, uint32_t len) {
    if (!log_partition) {
        return -1;
    }
    
    // Convert absolute address to partition offset
    // uint32_t offset = addr - s_log_ctx.flash_base;
    return es_flash_read(log_partition, addr, (uint8_t*)data, len) > 0 ? 0 : -1;
}

int log_flash_write(uint32_t addr, const void *data, uint32_t len) {
    if (!log_partition) {
        return -1;
    }
    
    // Convert absolute address to partition offset
    // uint32_t offset = addr - s_log_ctx.flash_base;
    return es_flash_write(log_partition, addr, (const uint8_t*)data, len) > 0 ? 0 : -1;
}

int log_flash_erase_sector(uint32_t addr) {
    if (!log_partition) {
        return -1;
    }
    
    // Convert absolute address to partition offset
    // uint32_t offset = addr - s_log_ctx.flash_base;
    uint32_t sector_size = log_partition->flash_dev->sector_size;
    
    return es_flash_erase(log_partition, addr, sector_size);
}

uint32_t log_flash_get_sector_size(void) {
    if (!log_partition) {
        return 4096; // Default sector size
    }
    return log_partition->flash_dev->sector_size;
}

uint32_t log_flash_get_log_base_addr(void) {
    if (!log_partition) {
        return 0;
    }
    return 0;
}

uint32_t log_flash_get_log_size(void) {
    if (!log_partition) {
        return 0;
    }
    return log_partition->size;
}

uint32_t log_get_timestamp(void) {
    return ++timestamp_counter;
}

// Initialize log partition
static int init_log_partition(void) {
    if (log_partition) {
        return 0; // Already initialized
    }
    
    // Initialize flash system
    int ret = es_flash_init();
    if (ret != 0) {
        return ret;
    }
    
    // Find log partition
    log_partition = es_partition_find("log");
    if (!log_partition) {
        return -1;
    }
    
    return 0;
}
#endif

// CRC32 calculation (same as es_cfg_v2)
static inline uint32_t log_crc32_calc(const void *data, uint16_t len) {
    uint32_t crc = DEF_LOG_CRC_INIT_VALUE;
    const uint8_t *ptr = (const uint8_t *)data;
    while (len--) {
        crc ^= *ptr++;
        for (int k = 0; k < 8; k++) {
            crc = crc & 1 ? (crc >> 1) ^ 0xEDB88320 : crc >> 1;
        }
    }
    return crc;
}

// Helper functions
static inline uint32_t align_up(uint32_t value, uint32_t alignment) {
    return (value + alignment - 1) & ~(alignment - 1);
}

static inline uint32_t get_sector_addr(uint32_t sector_id) {
    return s_log_ctx.flash_base + sector_id * s_log_ctx.sector_size;
}

static inline uint32_t get_next_sector(uint32_t sector_id) {
    return (sector_id + 1) % s_log_ctx.sector_count;
}

// Calculate slots per sector
static inline uint32_t get_slots_per_sector(void) {
    uint32_t usable_size = s_log_ctx.sector_size - align_up(sizeof(es_log_v2_sector_header_t), ES_LOG_V2_ALIGN_SIZE);
    return usable_size / align_up(sizeof(es_log_v2_entry_t), ES_LOG_V2_ALIGN_SIZE);
}

static inline uint32_t get_slot_addr(uint32_t sector_id, uint32_t slot_id) {
    return get_sector_addr(sector_id) + align_up(sizeof(es_log_v2_sector_header_t), ES_LOG_V2_ALIGN_SIZE) + slot_id * align_up(sizeof(es_log_v2_entry_t), ES_LOG_V2_ALIGN_SIZE);
}

static inline uint32_t get_entry_size(void) {
    return align_up(sizeof(es_log_v2_entry_t), ES_LOG_V2_ALIGN_SIZE);
}

// Verify sector header CRC
static bool verify_sector_header_crc(const es_log_v2_sector_header_t *header) {
    if (!header) {
        return false;
    }
    
    uint32_t calc_crc = log_crc32_calc(header, sizeof(es_log_v2_sector_header_t) - sizeof(header->crc32));
    return calc_crc == header->crc32;
}

// Write sector header
static int write_sector_header(uint32_t sector_id) {

    es_log_v2_sector_header_t header = {0};

    //check if sector is empty, if not, erase it, read first 4 bytes to check if it is empty
    uint32_t status = 0;
    if (log_flash_read(get_sector_addr(sector_id), &status, sizeof(status)) == 0) {
        if (status != ES_LOG_V2_SLOT_STATUS_EMPTY) {
            if (log_flash_erase_sector(get_sector_addr(sector_id)) != ES_LOG_V2_OK) {
                return ES_LOG_V2_ERR_FLASH_ERROR;
            }
        }
    }

    if(s_log_ctx.max_write_count == 0xFFFFFFFF) {
        s_log_ctx.max_write_count = 1;
    } else {
        s_log_ctx.max_write_count++;
    }

    header.magic = ES_LOG_V2_MAGIC;
    header.write_count = s_log_ctx.max_write_count;
    header.crc32 = log_crc32_calc(&header, sizeof(header) - sizeof(header.crc32));
    
    uint32_t sector_addr = get_sector_addr(sector_id);
    int ret =  log_flash_write(sector_addr, &header, sizeof(header));
    if (ret != 0) {
        return ret;
    }

    return ES_LOG_V2_OK;
}

// Read sector header with retry
static int read_sector_header(uint32_t sector_id, es_log_v2_sector_header_t *header) {
    if (!header) {
        return ES_LOG_V2_ERR_INVALID_PARAM;
    }
    
    uint32_t sector_addr = get_sector_addr(sector_id);
    int ret = -1;
    
    for (int retry = 0; retry < ES_LOG_V2_RETRY_COUNT; retry++) {
        ret = log_flash_read(sector_addr, header, sizeof(*header));
        if (ret == 0) {
            break;
        }
    }
    
    return ret;
}

// Find current read/write sectors and their slot positions by scanning all sectors
static int scan_sectors(void) {
    uint32_t max_write_count = 0;
    uint32_t min_write_count = 0xFFFFFFFF;
    uint32_t write_sector = 0;
    uint32_t read_sector = 0;
    bool found_valid = false;
    uint32_t i = 0;
    // Find sectors with highest and lowest write counts
    for (i = 0; i < s_log_ctx.sector_count; i++) {
        es_log_v2_sector_header_t header = {0};
        if (read_sector_header(i, &header) != 0) {
            continue;
        }
        
        // Check if sector is valid
        if (header.magic != ES_LOG_V2_MAGIC) {
            continue;
        }
        
        if (!verify_sector_header_crc(&header)) {
            continue;
        }
        
        found_valid = true;
        
        // Find sector with maximum write count (current write sector)
        if (header.write_count > max_write_count) {
            max_write_count = header.write_count;
            write_sector = i;
        }

        //find sector with minimum write count (current read sector)
        if (header.write_count < min_write_count) {
            min_write_count = header.write_count;
            read_sector = i;
        }
    }
    
    if (!found_valid) {
        // No valid sectors found, initialize first sector
        s_log_ctx.write_sector = 0;
        s_log_ctx.read_sector = 0;
        s_log_ctx.write_slot = 0;  // Write slot in write sector
        s_log_ctx.read_slot = 0;   // Read slot in read sector
        s_log_ctx.max_write_count = 0;
        return ES_LOG_V2_OK;
    }
    
    // Set current read and write sectors
    s_log_ctx.write_sector = write_sector;
    s_log_ctx.read_sector = read_sector;
    s_log_ctx.max_write_count = max_write_count;
    
    return ES_LOG_V2_OK;
}

// Scan write sector to find current write slot
static int scan_write_slot(uint32_t sector_id) {
    s_log_ctx.write_slot = 0;
    uint32_t slot = 0;
    for (slot = 0; slot < s_log_ctx.slots_per_sector; slot++) {
        uint32_t slot_addr = get_slot_addr(sector_id, slot);
        uint32_t status = 0;
        if (log_flash_read(slot_addr, &status, sizeof(status)) != 0) {
            return ES_LOG_V2_ERR_FLASH_ERROR;
        }
        
        if (status == ES_LOG_V2_SLOT_STATUS_EMPTY) {
            break;
        }
    }

    s_log_ctx.write_slot = slot;
    return ES_LOG_V2_OK;
}

// Allocate new sector for writing
static int allocate_new_write_sector(void) {
    uint32_t new_sector = get_next_sector(s_log_ctx.write_sector);
    uint32_t new_sector_addr = get_sector_addr(new_sector);
    
    // Check if new write sector conflicts with current read sector
    // If so, move read sector to next sector to avoid read/write conflict
    if (new_sector == s_log_ctx.read_sector) {
        s_log_ctx.read_sector = get_next_sector(s_log_ctx.read_sector);
        s_log_ctx.read_slot = 0;  // Reset read slot to beginning of new sector
    }
    
    // erase new sector
    if (log_flash_erase_sector(new_sector_addr) != 0) {
        return ES_LOG_V2_ERR_FLASH_ERROR;
    }

    //write header to new sector
    if(write_sector_header(new_sector) != ES_LOG_V2_OK) {
        return ES_LOG_V2_ERR_FLASH_ERROR;
    }
    
    s_log_ctx.write_sector = new_sector;
    s_log_ctx.write_slot = 0;  // Reset to slot 0
    
    return ES_LOG_V2_OK;
}

// Write log entry to flash
static int write_entry_to_flash(es_log_v2_entry_t *entry) {
    
    // Check if current sector has enough space
    if (s_log_ctx.write_slot >= s_log_ctx.slots_per_sector) {
        int ret = allocate_new_write_sector();
        if (ret != ES_LOG_V2_OK) {
            return ret;
        }
    }
    uint32_t write_addr = get_slot_addr(s_log_ctx.write_sector, s_log_ctx.write_slot);

    entry->status = ES_LOG_V2_SLOT_STATUS_USED;
    
    // Write entry to flash
    if (log_flash_write(write_addr, entry, get_entry_size()) != 0) {
        return ES_LOG_V2_ERR_FLASH_ERROR;
    }
    
    // Move to next write slot
    s_log_ctx.write_slot++;
    
    return ES_LOG_V2_OK;
}

// Public API implementation

int es_log_v2_init(void) {
    if (s_log_ctx.initialized) {
        return ES_LOG_V2_OK;
    }
    
    memset(&s_log_ctx, 0, sizeof(s_log_ctx));
    
#ifndef ES_LOG_V2_TESTING
    // Initialize log partition
    if (init_log_partition() != 0) {
        return ES_LOG_V2_ERR_FLASH_ERROR;
    }
#endif
    
    s_log_ctx.flash_base = log_flash_get_log_base_addr();
    s_log_ctx.flash_size = log_flash_get_log_size();
    s_log_ctx.sector_size = log_flash_get_sector_size();
    s_log_ctx.sector_count = s_log_ctx.flash_size / s_log_ctx.sector_size;
    s_log_ctx.slots_per_sector = get_slots_per_sector();
    
    if (s_log_ctx.sector_count < 2) {
        return ES_LOG_V2_ERR_NO_SPACE;
    }
    
    if (s_log_ctx.sector_size < align_up(sizeof(es_log_v2_sector_header_t), ES_LOG_V2_ALIGN_SIZE) + get_entry_size()) {
        return ES_LOG_V2_ERR_NO_SPACE;
    }
    
    if (s_log_ctx.slots_per_sector == 0) {
        return ES_LOG_V2_ERR_NO_SPACE;
    }
    
    s_log_ctx.initialized = true;
    #ifndef ES_LOG_V2_TESTING
    if(es_log_v2_load() != ES_LOG_V2_OK) {
        s_log_ctx.initialized = false;
        return ES_LOG_V2_ERR_FLASH_ERROR;
    }
    #endif

    return ES_LOG_V2_OK;
}

int es_log_v2_load(void) {
    if (!s_log_ctx.initialized) {
        return ES_LOG_V2_ERR_INVALID_PARAM;
    }
    
    if(scan_sectors() != ES_LOG_V2_OK || scan_write_slot(s_log_ctx.write_sector) != ES_LOG_V2_OK) {
        return ES_LOG_V2_ERR_FLASH_ERROR;
    }

    //if write slot is 0, check sector header, if not valid, erase sector and write header
    if(s_log_ctx.write_slot == 0) {
        es_log_v2_sector_header_t header = {0};
        if(read_sector_header(s_log_ctx.write_sector, &header) != ES_LOG_V2_OK) {
            return ES_LOG_V2_ERR_FLASH_ERROR;
        }
        if(header.magic != ES_LOG_V2_MAGIC || !verify_sector_header_crc(&header)) {
            if(log_flash_erase_sector(get_sector_addr(s_log_ctx.write_sector)) != 0) {
                return ES_LOG_V2_ERR_FLASH_ERROR;
            }
            if(write_sector_header(s_log_ctx.write_sector) != ES_LOG_V2_OK) {
                return ES_LOG_V2_ERR_FLASH_ERROR;
            }
        }
    }

#ifndef ES_LOG_V2_TESTING
    //set log sink
    log_v2_sink.name = "log_v2";
    log_v2_sink.sink_func = es_log_v2_sink;
    es_log_add_sink(&log_v2_sink);
#endif

    return ES_LOG_V2_OK;
}

int es_log_v2_write(const void *data, uint16_t data_len) {
    if (!s_log_ctx.initialized || !data) {
        return ES_LOG_V2_ERR_INVALID_PARAM;
    }
    
    if (data_len == 0) {
        return ES_LOG_V2_OK;  // No-op for zero length
    }
    
    const uint8_t *src = (const uint8_t *)data;
    
    /* 检查缓存中是否有足够的空间 */
    if (s_log_ctx.cache.offset + data_len < ES_LOG_V2_SLOT_SIZE) {
        /* 直接将消息复制到缓存中 */
        memcpy(s_log_ctx.cache.data + s_log_ctx.cache.offset, src, data_len);
        s_log_ctx.cache.offset += data_len;
        /* 确保最后一个字节为\0 */
        s_log_ctx.cache.data[s_log_ctx.cache.offset] = '\0';
    } else {
        /* 缓存空间不足，先填充缓存，然后刷新到闪存 */
        while (data_len > 0) {
            uint16_t copy_len = (data_len < ES_LOG_V2_SLOT_SIZE - s_log_ctx.cache.offset - 1) ? 
                               data_len : ES_LOG_V2_SLOT_SIZE - s_log_ctx.cache.offset - 1;
            memcpy(s_log_ctx.cache.data + s_log_ctx.cache.offset, src, copy_len);
            s_log_ctx.cache.offset += copy_len;
            /* 确保最后一个字节为\0 */
            s_log_ctx.cache.data[s_log_ctx.cache.offset] = '\0';
            src += copy_len;
            data_len -= copy_len;
            
            if (s_log_ctx.cache.offset >= ES_LOG_V2_SLOT_SIZE - 1) {
                int ret = es_log_v2_flush();
                if (ret != ES_LOG_V2_OK) {
                    return ret;
                }
            }
            if (data_len == 0) {
                break;
            }
        }
    }
    
    return ES_LOG_V2_OK;
}

int es_log_v2_read(char *data) {
    if (!s_log_ctx.initialized || !data) {
        return ES_LOG_V2_ERR_INVALID_PARAM;
    }

    //check if read slot is end of sector
    if (s_log_ctx.read_slot >= s_log_ctx.slots_per_sector) {
        if(s_log_ctx.read_sector == s_log_ctx.write_sector) {
            return ES_LOG_V2_ERR_NO_DATA;
        }
        //循环查找下一个有数据的sector
        uint32_t sector = get_next_sector(s_log_ctx.read_sector);
        for(; sector != s_log_ctx.write_sector; sector = get_next_sector(sector)) {
            uint32_t status = 0;
            if(log_flash_read(get_sector_addr(sector), &status, sizeof(status)) != 0) {
                continue;
            }
            if(status == ES_LOG_V2_SLOT_STATUS_EMPTY) {
                continue;
            }
            s_log_ctx.read_sector = sector;
            s_log_ctx.read_slot = 0;
            break;
        }
        if(sector == s_log_ctx.write_sector) {
            s_log_ctx.read_sector = s_log_ctx.write_sector;
            s_log_ctx.read_slot = 0;
        }
    }
    
    // Optimization: Check if read pointer has caught up with write pointer
    // If read sector equals write sector and read slot has reached write slot, no more data
    if (s_log_ctx.read_sector == s_log_ctx.write_sector && 
        s_log_ctx.read_slot >= s_log_ctx.write_slot) {
        return ES_LOG_V2_ERR_NO_DATA;
    }
    
    uint32_t slot_addr = get_slot_addr(s_log_ctx.read_sector, s_log_ctx.read_slot);
    es_log_v2_entry_t entry = {0};
    
    // Read with retry
    int ret = -1;
    for (int retry = 0; retry < ES_LOG_V2_RETRY_COUNT; retry++) {
        ret = log_flash_read(slot_addr, &entry, sizeof(entry));
        if (ret == 0) {
            break;
        }
    }
    
    if (ret != 0) {
        return ES_LOG_V2_ERR_FLASH_ERROR;
    }
    
    if (entry.status == ES_LOG_V2_SLOT_STATUS_EMPTY) {
        return ES_LOG_V2_ERR_NO_DATA;
    }   
    
    memcpy(data, entry.data, ES_LOG_V2_SLOT_SIZE);
    s_log_ctx.read_slot++;

    return ES_LOG_V2_OK;
}

int es_log_v2_rewind(void) {
    if (!s_log_ctx.initialized) {
        return ES_LOG_V2_ERR_INVALID_PARAM;
    }
    
    int ret = scan_sectors();
    if (ret != ES_LOG_V2_OK) {
        return ret;
    }

    // Reset read position to beginning
    s_log_ctx.read_slot = 0;

    return ES_LOG_V2_OK;
}


int es_log_v2_flush(void) {
    if (!s_log_ctx.initialized || s_log_ctx.cache.offset == 0) {
        return ES_LOG_V2_OK;
    }
    
    // Create entry from cache data
    es_log_v2_entry_t entry = {0};
    entry.status = ES_LOG_V2_SLOT_STATUS_USED;
    memcpy(entry.data, s_log_ctx.cache.data, s_log_ctx.cache.offset);
    
    // Write to flash
    int ret = write_entry_to_flash(&entry);
    if (ret != ES_LOG_V2_OK) {
        return ret;
    }
    
    // Clear cache
    memset(&s_log_ctx.cache, 0, sizeof(s_log_ctx.cache));
    
    return ES_LOG_V2_OK;
}

int es_log_v2_clear(void) {
    if (!s_log_ctx.initialized) {
        return ES_LOG_V2_ERR_INVALID_PARAM;
    }
    
    // Clear cache
    memset(&s_log_ctx.cache, 0, sizeof(s_log_ctx.cache));
    
    // Erase all sectors and reinitialize
    for (uint32_t i = 0; i < s_log_ctx.sector_count; i++) {
        uint32_t sector_addr = get_sector_addr(i);
        if (log_flash_erase_sector(sector_addr) != 0) {
            return ES_LOG_V2_ERR_FLASH_ERROR;
        }
    }
    
    // Reset context
    s_log_ctx.write_sector = 0;
    s_log_ctx.read_sector = 0;
    s_log_ctx.write_slot = 0;
    s_log_ctx.read_slot = 0;
    s_log_ctx.max_write_count = 0;
    
    // Write header to first sector
    if (write_sector_header(0) != ES_LOG_V2_OK) {
        return ES_LOG_V2_ERR_FLASH_ERROR;
    }
    
    return ES_LOG_V2_OK;
}

#ifndef ES_LOG_V2_TESTING
// AT command buffer for log v2
#define LOG_V2_MAX_ENTRY_SIZE   (ES_LOG_V2_SLOT_SIZE)
static char log_v2_buf[LOG_V2_MAX_ENTRY_SIZE * 2 - 1] = {0};

// State structure for AT+LOGV2 command
typedef struct {
    int16_t len;
    char *s;
    char *e;
    uint8_t i : 6;
    uint8_t search_in_progress : 1;
    uint8_t log_out_all : 1;
} log_v2_search_ctx_t;

/* AT+LOGV2? Command - Query all logs */
/* AT+LOGV2=<keyword> Command - Grep logs with keyword */
es_async_t at_srv_cmd_logv2_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    /* 使用es_mem分配的状态变量指针 */
    static log_v2_search_ctx_t *search_ctx = NULL;
    
    es_co_begin(coro);

    /* 初始化创建状态结构体 */
    if (search_ctx == NULL) {
        search_ctx = (log_v2_search_ctx_t *)es_mem_alloc(sizeof(log_v2_search_ctx_t));
        if (search_ctx == NULL) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
            es_co_exit;
        }
        memset(search_ctx, 0, sizeof(log_v2_search_ctx_t));
    }

    search_ctx->log_out_all = 0;
    
    if (!s_log_ctx.initialized) {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
        es_mem_free_safe((void **)&search_ctx);
        es_co_exit;
    }
    
    if (cmd_type == ES_AT_SRV_CMD_QUERY) {
       search_ctx->log_out_all = true;
       goto run_loop;
    } 
    else if (cmd_type == ES_AT_SRV_CMD_SET) {
       /* Check if params exist */
       if (ctx->param_count == 0) {
           es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
           es_mem_free_safe((void **)&search_ctx);
           es_co_exit;
       }
       
       search_ctx->log_out_all = 0;
       
       run_loop:
       search_ctx->len = 0;
       search_ctx->search_in_progress = 1;
       /* Fetch logs into buffer */
       memset(log_v2_buf, 0, sizeof(log_v2_buf));
       
       // Rewind to start reading from beginning
       es_log_v2_rewind();
       //disable log output
       es_log_set_enabled(false);
       
       while (search_ctx->search_in_progress) {
            es_co_yield;
           /* Fill buffer with as much data as possible */
           while (1) {
               if (es_log_v2_read(log_v2_buf + search_ctx->len) != ES_LOG_V2_OK) {
                   search_ctx->search_in_progress = 0;
                   break;
               }
               log_v2_buf[sizeof(log_v2_buf) - 1] = '\0'; /* Ensure null-terminated */
               search_ctx->len += LOG_V2_MAX_ENTRY_SIZE;
               /* Avoid buffer overflow */
               if (search_ctx->len + LOG_V2_MAX_ENTRY_SIZE > sizeof(log_v2_buf)) {
                   break;
               }
           }
           
           /* Process buffer line by line */
           search_ctx->s = log_v2_buf;
           
           while (1) {
              es_co_yield;
              search_ctx->e = search_ctx->s;
              while (*(search_ctx->e) != '\0' && *(search_ctx->e) != '\r' && *(search_ctx->e) != '\n') {
                  search_ctx->e++;
              }
              
              if (*(search_ctx->e) == '\0') {
                  break;
              }

              *(search_ctx->e) = '\0';

              if(search_ctx->e - search_ctx->s == 0) {
                  search_ctx->s = search_ctx->e + 1;
                  continue;
              }

              if(search_ctx->log_out_all) {
                  es_at_srv_fmt_send("%.*s\r\n", search_ctx->e - search_ctx->s, search_ctx->s);
              } else {
                for(search_ctx->i = 0; search_ctx->i < ctx->param_count; search_ctx->i++) {
                    if (strstr(search_ctx->s, ctx->params[search_ctx->i].param) != NULL) {
                        es_at_srv_fmt_send("%.*s\r\n", search_ctx->e - search_ctx->s, search_ctx->s);
                        break;
                    }
                }
              }
              
              search_ctx->s = search_ctx->e + 1;
              /* Feed watchdog during long operations */
              es_os_feed_wdt();
           }
           
           /* Move remaining partial line to start of buffer */
           if (search_ctx->s != log_v2_buf) {
               search_ctx->len = (int16_t)strlen(search_ctx->s);
               memmove(log_v2_buf, search_ctx->s, search_ctx->len);
               log_v2_buf[search_ctx->len] = '\0';
           } else {
               search_ctx->len = 0;
               memset(log_v2_buf, 0, sizeof(log_v2_buf));
           }
           
           /* If no more data and search is complete, exit */
           if (!search_ctx->search_in_progress) {
               break;
           }
       }
       
       /* Rewind for next search */
       es_log_v2_rewind();
       //enable log output
       es_log_set_enabled(true);
       es_at_srv_send_ok();
    }
    else {
       es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
    }
    
    /* 释放分配的内存 */
    es_mem_free_safe((void **)&search_ctx);
    
    es_co_end;
}

#define LOG_V2_SEEK_START 0
#define LOG_V2_SEEK_CUR   1  
#define LOG_V2_SEEK_END   2

/* State structure for AT+LOGV2SEEK command */
typedef struct log_v2_seek_ctx_t {
    int pos;
    int offset;
} log_v2_seek_ctx_t;

/* AT+LOGV2SEEK=<pos>,<offset> Command - Seek log position */
es_async_t at_srv_cmd_logv2seek_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    static log_v2_seek_ctx_t *seek_ctx = NULL;
    
    es_co_begin(coro);
    
    /* 初始化创建状态结构体 */
    if (seek_ctx == NULL) {
        seek_ctx = (log_v2_seek_ctx_t *)es_mem_alloc(sizeof(log_v2_seek_ctx_t));
        if (seek_ctx == NULL) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
            es_co_exit;
        }
        memset(seek_ctx, 0, sizeof(log_v2_seek_ctx_t));
    }
    
    if (!s_log_ctx.initialized) {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
        es_mem_free_safe((void **)&seek_ctx);
        es_co_exit;
    }
    
    if (cmd_type == ES_AT_SRV_CMD_SET) {
        /* Check if params exist */
        if (ctx->param_count < 2) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_mem_free_safe((void **)&seek_ctx);
            es_co_exit;
        }
        
        /* Get params */
        seek_ctx->pos = atoi(ctx->params[0].param);
        seek_ctx->offset = atoi(ctx->params[1].param);
        
        /* Validate position parameter */
        if (seek_ctx->pos < 0 || seek_ctx->pos > 2) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_mem_free_safe((void **)&seek_ctx);
            es_co_exit;
        }
        
        // For now, we only support rewinding to start (pos=0, offset=0)
        if (seek_ctx->pos == LOG_V2_SEEK_START && seek_ctx->offset == 0) {
            if (es_log_v2_rewind() != ES_LOG_V2_OK) {
                es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
                es_mem_free_safe((void **)&seek_ctx);
                es_co_exit;
            }
        } else {
            // Other seek operations not implemented yet
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_mem_free_safe((void **)&seek_ctx);
            es_co_exit;
        }
        
        es_at_srv_send_ok();
    }
    else if (cmd_type == ES_AT_SRV_CMD_QUERY) {
        /* Send current position information */
        es_at_srv_fmt_send("+LOGV2SEEK: %d,%d\r\n", s_log_ctx.read_sector, s_log_ctx.read_slot);
        es_at_srv_send_ok();
    }
    else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
    }
    
    /* 释放分配的内存 */
    es_mem_free_safe((void **)&seek_ctx);
    
    es_co_end;
}

/* AT+LOGV2CLR Command - Clear all logs */
es_async_t at_srv_cmd_logv2clr_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (!s_log_ctx.initialized) {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
        es_co_exit;
    }
    
    if (cmd_type == ES_AT_SRV_CMD_EXEC) {
        if (es_log_v2_clear() != ES_LOG_V2_OK) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
            es_co_exit;
        }
        
        es_at_srv_send_ok();
    }
    else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
    }
    
    es_co_end;
}

/* State structure for AT+LOGV2TEST command */
typedef struct log_v2_test_ctx_t {
    uint32_t count;
    uint32_t interval_ms;
    uint32_t current_count;
    uint32_t start_time;
    bool test_running;
} log_v2_test_ctx_t;

/* AT+LOGV2TEST=<count>,<interval_ms> Command - Stress test log writing */
es_async_t at_srv_cmd_logv2test_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    static log_v2_test_ctx_t *test_ctx = NULL;
    
    es_co_begin(coro);
    
    /* 初始化创建状态结构体 */
    if (test_ctx == NULL) {
        test_ctx = (log_v2_test_ctx_t *)es_mem_alloc(sizeof(log_v2_test_ctx_t));
        if (test_ctx == NULL) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
            es_co_exit;
        }
        memset(test_ctx, 0, sizeof(log_v2_test_ctx_t));
    }
    
    if (!s_log_ctx.initialized) {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
        es_mem_free_safe((void **)&test_ctx);
        es_co_exit;
    }
    
    if (cmd_type == ES_AT_SRV_CMD_SET) {
        /* Check if params exist */
        if (ctx->param_count < 2) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_mem_free_safe((void **)&test_ctx);
            es_co_exit;
        }
        
        /* Get params */
        test_ctx->count = (uint32_t)atoi(ctx->params[0].param);
        test_ctx->interval_ms = (uint32_t)atoi(ctx->params[1].param);
        
        /* Validate parameters */
        if (test_ctx->count == 0 || test_ctx->count > 10000) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_mem_free_safe((void **)&test_ctx);
            es_co_exit;
        }
        
        if (test_ctx->interval_ms > 60000) {  // Max 60 seconds interval
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_mem_free_safe((void **)&test_ctx);
            es_co_exit;
        }
        
        /* Initialize test */
        test_ctx->current_count = 0;
        test_ctx->start_time = es_os_get_tick_ms();
        test_ctx->test_running = true;
        
        es_at_srv_fmt_send("+LOGV2TEST: Starting stress test, count=%u, interval=%ums\r\n", 
                          test_ctx->count, test_ctx->interval_ms);
        
        /* Run stress test loop */
        while (test_ctx->test_running && test_ctx->current_count < test_ctx->count) {
            /* Write test log using ES_LOGI */
            ES_LOGI(TAG, "Test log entry #%u - timestamp=%u, data=0x%08X", 
                   test_ctx->current_count + 1, 
                   es_os_get_tick_ms(),
                   (unsigned int)(0xDEADBEEF + test_ctx->current_count));
            
            test_ctx->current_count++;
            
            /* Send progress update every 100 logs or at the end */
            if ((test_ctx->current_count % 100) == 0 || test_ctx->current_count == test_ctx->count) {
                es_at_srv_fmt_send("+LOGV2TEST: Progress %u/%u\r\n", 
                                  test_ctx->current_count, test_ctx->count);
            }
            
            /* Yield CPU to allow other tasks to run */
            es_co_yield;
            
            /* Feed watchdog during long operations */
            es_os_feed_wdt();
        }
        
        /* Flush any remaining cached logs */
        es_log_v2_flush();
        
        es_at_srv_send_ok();
    }
    else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
    }
    
    /* 释放分配的内存 */
    es_mem_free_safe((void **)&test_ctx);
    
    es_co_end;
}

#endif // ES_LOG_V2_TESTING

#ifdef ES_LOG_V2_TESTING
void es_log_v2_reset_state(void) {
    memset(&s_log_ctx, 0, sizeof(s_log_ctx));
}

es_log_v2_context_t* es_log_v2_get_context(void) {
    return &s_log_ctx;
}
#endif 
