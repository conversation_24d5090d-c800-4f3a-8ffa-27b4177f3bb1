//
// Created by lxy on 2025/1/14.
// ES Frame Storage - Ring buffer frame storage with flash persistence
//

#ifndef ES_FRAME_V2_STORAGE_H
#define ES_FRAME_V2_STORAGE_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// Configuration constants
#define ES_FRAME_V2_MAGIC              0x4652414D  // "FRAM"
#define ES_FRAME_V2_VERSION            0x0001
#define ES_FRAME_V2_ALIGN_SIZE         4
#define ES_FRAME_V2_RETRY_COUNT        10
#define DEF_FRAME_CRC_INIT_VALUE    0xFFFFFFFF
#define ES_FRAME_V2_MAX_DATA_SIZE      512

// Frame entry status
#define ES_FRAME_V2_STATUS_EMPTY (0xFFFFFFFFU)    // Empty slot
#define ES_FRAME_V2_STATUS_VALID (0xFFFF0000U)    // Valid frame
#define ES_FRAME_V2_STATUS_INVALID (0x00000000U)   // Invalid frame (read and marked)

// Error codes
enum {
    ES_FRAME_V2_OK = 0,
    ES_FRAME_V2_ERR_INVALID_PARAM = -1,
    ES_FRAME_V2_ERR_NO_SPACE = -2,
    ES_FRAME_V2_ERR_FLASH_ERROR = -3,
    ES_FRAME_V2_ERR_CRC_ERROR = -4,
    ES_FRAME_V2_ERR_NO_DATA = -5,
    ES_FRAME_V2_ERR_FRAME_TOO_LARGE = -6,
};

#pragma pack(push, 1)

// Flash sector header structure (same as es_log_storage)
typedef struct {
    uint32_t magic;         // Magic number for validation
    uint32_t write_count;   // Write counter for this sector
    uint32_t crc32;         // CRC32 of header (excluding this field)
} es_frame_v2_sector_header_t;

// Frame entry structure in flash
typedef struct {
    uint32_t status;        // Frame status (empty/valid/invalid)
    uint16_t data_len;      // Data length
    uint16_t reserved;      // Reserved for alignment
    uint32_t data_crc;      // CRC32 of data
    uint8_t data[];         // Variable length data
} es_frame_v2_entry_t;

#pragma pack(pop)

// Frame storage context
typedef struct {
    // Flash configuration
    uint32_t flash_base;
    uint32_t flash_size;
    uint32_t sector_size;
    uint32_t sector_count;
    
    // Current sectors
    uint32_t write_sector;      // Current write sector
    uint32_t read_sector;       // Current read sector
    uint32_t write_offset;      // Current write offset in sector
    uint32_t read_offset;       // Current read offset in sector
    
    uint32_t max_write_count;   // Max write count
    
    // State flags
    bool initialized;
} es_frame_v2_context_t;

// API Functions

/**
 * Initialize the frame storage system
 * @return ES_FRAME_V2_OK on success, error code on failure
 */
int es_frame_v2_init(void);

/**
 * Load existing frame data from flash
 * @return ES_FRAME_V2_OK on success, error code on failure
 */
int es_frame_v2_load(void);

/**
 * Push a frame to storage
 * @param data Frame data
 * @param data_len Data length
 * @return ES_FRAME_V2_OK on success, error code on failure
 */
int es_frame_v2_push(const void *data, uint16_t data_len);

/**
 * Pop a frame from storage
 * @param data Output buffer for data
 * @param data_len Input: buffer size, Output: actual data length
 * @return ES_FRAME_V2_OK on success, error code on failure
 */
int es_frame_v2_pop(void *data, uint16_t *data_len);

/**
 * Clear all frames
 * @return ES_FRAME_V2_OK on success, error code on failure
 */
int es_frame_v2_clear(void);

/**
 * Get frame count
 * @return Number of valid frames
 */
uint32_t es_frame_v2_count(void);

/**
 * Check if frame storage is empty
 * @return true if empty, false if contains valid frames
 */
bool es_frame_v2_is_empty(void);

// External flash interface functions (to be implemented by user)
extern int frame_flash_read(uint32_t addr, void *data, uint32_t len);
extern int frame_flash_write(uint32_t addr, const void *data, uint32_t len);
extern int frame_flash_erase_sector(uint32_t addr);
extern uint32_t frame_flash_get_sector_size(void);
extern uint32_t frame_flash_get_frame_base_addr(void);
extern uint32_t frame_flash_get_frame_size(void);

#ifdef ES_FRAME_V2_TESTING
// Test helper functions
void es_frame_v2_reset_state(void);
es_frame_v2_context_t* es_frame_v2_get_context(void);
#endif

#ifdef __cplusplus
}
#endif

#endif // ES_FRAME_V2_STORAGE_H 

