set(CMAKE_HOST_SYSTEM "Windows-10.0.26100")
set(CMAKE_HOST_SYSTEM_NAME "Windows")
set(CMAKE_HOST_SYSTEM_VERSION "10.0.26100")
set(CMAKE_HOST_SYSTEM_PROCESSOR "AMD64")

include("D:/es_mcu/platform/win32/vs2022.cmake")

set(CMAKE_SYSTEM "Windows")
set(CMAKE_SYSTEM_NAME "Windows")
set(CMAKE_SYSTEM_VERSION "")
set(CMAKE_SYSTEM_PROCESSOR "x86_64")

set(CMAKE_CROSSCOMPILING "TRUE")

set(CMAKE_SYSTEM_LOADED 1)
