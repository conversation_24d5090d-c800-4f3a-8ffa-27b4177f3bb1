/**
 * @file es_aes.h
 * @brief AES encryption/decryption utility functions
 */
#ifndef __ES_AES_H__
#define __ES_AES_H__

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* AES-128 constant definitions */
#define AES_BLOCK_SIZE     16  /* AES block size is 16 bytes */
#define AES_KEY_SIZE       16  /* AES-128 key size is 16 bytes */

/**
 * @brief AES-128 context structure
 */
typedef struct {
    uint8_t round_key[176];  /* AES-128 round keys, 11 rounds, 16 bytes per round */
    // ECB mode doesn't require IV field
} es_aes_ctx_t;

/**
 * @brief Initialize AES-128 encryption context
 * 
 * @param ctx AES context pointer
 * @param key 16-byte key data
 */
void es_aes_init(es_aes_ctx_t *ctx, const uint8_t key[AES_KEY_SIZE]);

/**
 * @brief AES-128 ECB mode encrypt single block
 * 
 * @param key 16-byte key data
 * @param plaintext 16-byte plaintext input
 * @param ciphertext 16-byte ciphertext output
 */
void es_aes_encrypt_block(const es_aes_ctx_t *ctx, const uint8_t plaintext[AES_BLOCK_SIZE], uint8_t ciphertext[AES_BLOCK_SIZE]);

/**
 * @brief AES-128 ECB mode decrypt single block
 * 
 * @param key 16-byte key data
 * @param ciphertext 16-byte ciphertext input
 * @param plaintext 16-byte plaintext output
 */
void es_aes_decrypt_block(const es_aes_ctx_t *ctx, const uint8_t ciphertext[AES_BLOCK_SIZE], uint8_t plaintext[AES_BLOCK_SIZE]);

/**
 * @brief Encrypt string using AES-128
 * 
 * @param key 16-byte key data
 * @param plaintext Plaintext string
 * @param ciphertext Ciphertext output buffer
 * @param length Plaintext string length
 * @return int Number of bytes after encryption
 * 
 * @note Output buffer size should be at least (length + AES_BLOCK_SIZE) bytes
 * @note Function automatically handles padding and block alignment
 */
int es_aes_encrypt_string(const uint8_t *key, const char *plaintext, uint8_t *ciphertext, int length);

/**
 * @brief Decrypt string using AES-128
 * 
 * @param key 16-byte key data
 * @param ciphertext Ciphertext data
 * @param plaintext Plaintext output buffer
 * @param length Ciphertext data length
 * @return int Decrypted string length, returns -1 on failure
 * 
 * @note Output buffer size should be at least the same as ciphertext length
 * @note Function automatically handles padding removal
 */
int es_aes_decrypt_string(const uint8_t *key, const uint8_t *ciphertext, char *plaintext, int length);

/**
 * @brief AES-128 ECB mode encrypt raw bytes (no automatic padding)
 * 
 * @param input Input data (length must be a multiple of 16)
 * @param output Output buffer (length same as input)
 * @param length Input data length (bytes, must be a multiple of 16)
 * @param key 16-byte key data
 * @return int Actual encrypted byte count, -1 on failure
 */
int es_aes_encrypt_bytes(uint8_t *input, uint8_t *output, int length, const uint8_t *key);

/**
 * @brief AES-128 ECB mode decrypt raw bytes (no automatic padding removal)
 * 
 * @param input Input data (length must be a multiple of 16)
 * @param output Output buffer (length same as input)
 * @param length Input data length (bytes, must be a multiple of 16)
 * @param key 16-byte key data
 * @return int Actual decrypted byte count, -1 on failure
 */
int es_aes_decrypt_bytes(uint8_t *input, uint8_t *output, int length, const uint8_t *key);

#ifdef __cplusplus
}
#endif

#endif /* __ES_AES_H__ */ 
