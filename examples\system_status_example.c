/**
 * @file system_status_example.c
 * @brief 系统状态监控模块使用示例
 * <AUTHOR>
 * @date 2025/1/15
 * @copyright Copyright (c) 2025
 */

#include "es_system_status.h"
#include "es_mem.h"
#include "es_log.h"
#include "es_drv_os.h"
#include <stdio.h>
#include <string.h>

#define TAG "SYS_STATUS_EXAMPLE"

/**
 * @brief 演示基本的系统状态获取
 */
static void demo_basic_status(void)
{
    ES_PRINTF_I(TAG, "\n=== Basic System Status Demo ===");
    
    // 获取并打印完整的系统状态
    es_sys_dump_status();
}

/**
 * @brief 演示内存状态监控
 */
static void demo_memory_monitoring(void)
{
    ES_PRINTF_I(TAG, "\n=== Memory Monitoring Demo ===");
    
    // 分配一些内存来演示内存使用变化
    void *ptrs[5];
    int sizes[] = {32, 64, 128, 64, 32};
    
    ES_PRINTF_I(TAG, "Initial memory status:");
    es_sys_dump_memory_status();
    
    // 分配内存
    ES_PRINTF_I(TAG, "\nAllocating memory blocks...");
    for (int i = 0; i < 5; i++) {
        ptrs[i] = es_mem_alloc(sizes[i]);
        if (ptrs[i]) {
            ES_PRINTF_I(TAG, "Allocated %d bytes at %p", sizes[i], ptrs[i]);
            // 写入一些数据
            memset(ptrs[i], 0x55 + i, sizes[i]);
        } else {
            ES_PRINTF_I(TAG, "Failed to allocate %d bytes", sizes[i]);
        }
    }
    
    ES_PRINTF_I(TAG, "\nMemory status after allocation:");
    es_sys_dump_memory_status();
    
    // 释放部分内存
    ES_PRINTF_I(TAG, "\nFreeing some memory blocks...");
    for (int i = 0; i < 3; i++) {
        if (ptrs[i]) {
            es_mem_free(ptrs[i]);
            ptrs[i] = NULL;
            ES_PRINTF_I(TAG, "Freed block %d", i);
        }
    }
    
    ES_PRINTF_I(TAG, "\nMemory status after partial free:");
    es_sys_dump_memory_status();
    
    // 释放剩余内存
    ES_PRINTF_I(TAG, "\nFreeing remaining memory blocks...");
    for (int i = 3; i < 5; i++) {
        if (ptrs[i]) {
            es_mem_free(ptrs[i]);
            ptrs[i] = NULL;
            ES_PRINTF_I(TAG, "Freed block %d", i);
        }
    }
    
    ES_PRINTF_I(TAG, "\nFinal memory status:");
    es_sys_dump_memory_status();
}

/**
 * @brief 演示时间信息监控
 */
static void demo_time_monitoring(void)
{
    ES_PRINTF_I(TAG, "\n=== Time Monitoring Demo ===");
    
    // 获取时间信息
    es_sys_time_info_t time_info;
    if (es_sys_get_time_info(&time_info) == 0) {
        ES_PRINTF_I(TAG, "System uptime: %u days, %u hours, %u minutes, %u seconds",
                   time_info.uptime_days, 
                   time_info.uptime_hours % 24,
                   time_info.uptime_minutes % 60, 
                   time_info.uptime_seconds % 60);
        ES_PRINTF_I(TAG, "Total uptime: %u ms", time_info.uptime_ms);
        ES_PRINTF_I(TAG, "Current timestamp: %u", time_info.current_timestamp);
    } else {
        ES_PRINTF_I(TAG, "Failed to get time information");
    }
    
    // 使用专用的打印函数
    es_sys_dump_time_status();
}

/**
 * @brief 演示健康状态监控
 */
static void demo_health_monitoring(void)
{
    ES_PRINTF_I(TAG, "\n=== Health Monitoring Demo ===");
    
    // 获取健康状态
    es_sys_health_status_t health = es_sys_get_health_status();
    ES_PRINTF_I(TAG, "Current system health: %s", es_sys_get_health_status_string(health));
    
    // 使用专用的打印函数
    es_sys_dump_health_status();
}

/**
 * @brief 演示详细的系统信息获取
 */
static void demo_detailed_info(void)
{
    ES_PRINTF_I(TAG, "\n=== Detailed System Info Demo ===");
    
    es_sys_status_info_t info;
    if (es_sys_get_status_info(&info) == 0) {
        ES_PRINTF_I(TAG, "Memory Manager Availability:");
        ES_PRINTF_I(TAG, "  V1: %s", info.memory_v1_available ? "Available" : "N/A");
        ES_PRINTF_I(TAG, "  V2: %s", info.memory_v2_available ? "Available" : "N/A");
        
        ES_PRINTF_I(TAG, "Memory Details:");
        ES_PRINTF_I(TAG, "  Total: %u bytes", info.memory.total_size);
        ES_PRINTF_I(TAG, "  Used: %u bytes (%.1f%%)", info.memory.used_size, info.memory.usage_percent);
        ES_PRINTF_I(TAG, "  Free: %u bytes", info.memory.free_size);
        ES_PRINTF_I(TAG, "  Active blocks: %u", info.memory.active_blocks);
        
        ES_PRINTF_I(TAG, "Time Details:");
        ES_PRINTF_I(TAG, "  Uptime: %u ms", info.time.uptime_ms);
        ES_PRINTF_I(TAG, "  Timestamp: %u", info.time.current_timestamp);
        
        ES_PRINTF_I(TAG, "Health: %s", es_sys_get_health_status_string(info.health));
    } else {
        ES_PRINTF_I(TAG, "Failed to get detailed system information");
    }
}

/**
 * @brief 演示周期性监控
 */
static void demo_periodic_monitoring(void)
{
    ES_PRINTF_I(TAG, "\n=== Periodic Monitoring Demo ===");
    
    ES_PRINTF_I(TAG, "Running periodic monitoring for 5 seconds...");
    
    uint32_t start_time = es_os_get_tick_ms();
    uint32_t last_print = start_time;
    
    while ((es_os_get_tick_ms() - start_time) < 5000) {
        uint32_t current_time = es_os_get_tick_ms();
        
        // 每秒打印一次状态
        if ((current_time - last_print) >= 1000) {
            ES_PRINTF_I(TAG, "\n--- Status at %u ms ---", current_time);
            
            // 获取内存信息
            es_sys_memory_info_t mem_info;
            if (es_sys_get_memory_info(&mem_info) == 0) {
                ES_PRINTF_I(TAG, "Memory: %u/%u bytes (%.1f%% used)", 
                           mem_info.used_size, mem_info.total_size, mem_info.usage_percent);
            }
            
            // 获取健康状态
            es_sys_health_status_t health = es_sys_get_health_status();
            ES_PRINTF_I(TAG, "Health: %s", es_sys_get_health_status_string(health));
            
            last_print = current_time;
        }
        
        // 短暂延时
        es_os_msleep(100);
    }
    
    ES_PRINTF_I(TAG, "Periodic monitoring completed");
}

/**
 * @brief 主函数
 */
int main(void)
{
    ES_PRINTF_I(TAG, "=== System Status Monitor Example ===");
    
    // 初始化内存管理器
    if (es_mem_init() != 0) {
        ES_PRINTF_I(TAG, "Failed to initialize memory manager");
        return -1;
    }
    
    // 初始化系统状态监控模块
    if (es_sys_status_init() != 0) {
        ES_PRINTF_I(TAG, "Failed to initialize system status monitor");
        return -1;
    }
    
    // 运行各种演示
    demo_basic_status();
    demo_memory_monitoring();
    demo_time_monitoring();
    demo_health_monitoring();
    demo_detailed_info();
    demo_periodic_monitoring();
    
    // 清理
    es_sys_status_deinit();
    
    ES_PRINTF_I(TAG, "\n=== Example completed ===");
    return 0;
}

/**
 * @brief 简化的演示函数，适合在其他应用中调用
 */
void system_status_quick_demo(void)
{
    ES_PRINTF_I(TAG, "=== Quick System Status ===");
    
    // 初始化（如果还没有初始化）
    es_sys_status_init();
    
    // 打印完整状态
    es_sys_dump_status();
}
