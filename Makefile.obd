# Makefile for OBD-II Module
# This makefile helps build and test the OBD-II module

# Compiler settings
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g -O0
INCLUDES = -I. -Iapplications -Iplatform/win32 -Iexamples -Itests

# Source files
OBD_SOURCES = applications/es_obd.c applications/es_isotp.c
TEST_SOURCES = tests/test_obd.c
EXAMPLE_SOURCES = examples/obd_example.c

# Object files
OBD_OBJECTS = $(OBD_SOURCES:.c=.o)
TEST_OBJECTS = $(TEST_SOURCES:.c=.o)
EXAMPLE_OBJECTS = $(EXAMPLE_SOURCES:.c=.o)

# Target executables
TEST_TARGET = test_obd
EXAMPLE_TARGET = obd_example

# Mock implementations for testing (simplified)
MOCK_SOURCES = platform/win32/win32_can.c \
               applications/es_drv_os.c \
               applications/es_mem.c \
               applications/es_log.c \
               applications/es_ringobj.c

# Default target
all: $(TEST_TARGET) $(EXAMPLE_TARGET)

# Test target
$(TEST_TARGET): $(TEST_OBJECTS) $(OBD_OBJECTS)
	@echo "Building OBD test executable..."
	$(CC) $(CFLAGS) $(INCLUDES) -o $@ $^ -DTEST_MODE

# Example target
$(EXAMPLE_TARGET): $(EXAMPLE_OBJECTS) $(OBD_OBJECTS)
	@echo "Building OBD example executable..."
	$(CC) $(CFLAGS) $(INCLUDES) -o $@ $^

# Object file compilation
%.o: %.c
	@echo "Compiling $<..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Run tests
test: $(TEST_TARGET)
	@echo "Running OBD-II module tests..."
	./$(TEST_TARGET)

# Run example
run-example: $(EXAMPLE_TARGET)
	@echo "Running OBD-II example..."
	./$(EXAMPLE_TARGET)

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -f $(OBD_OBJECTS) $(TEST_OBJECTS) $(EXAMPLE_OBJECTS)
	rm -f $(TEST_TARGET) $(EXAMPLE_TARGET)

# Install headers (copy to system include directory)
install-headers:
	@echo "Installing OBD headers..."
	mkdir -p /usr/local/include/es_mcu
	cp applications/es_obd.h /usr/local/include/es_mcu/
	cp applications/es_isotp.h /usr/local/include/es_mcu/

# Generate documentation
docs:
	@echo "Generating documentation..."
	doxygen Doxyfile.obd

# Code analysis
analyze:
	@echo "Running static code analysis..."
	cppcheck --enable=all --std=c99 $(OBD_SOURCES) $(TEST_SOURCES) $(EXAMPLE_SOURCES)

# Format code
format:
	@echo "Formatting code..."
	clang-format -i $(OBD_SOURCES) applications/es_obd.h $(TEST_SOURCES) $(EXAMPLE_SOURCES)

# Check for memory leaks (requires valgrind)
memcheck: $(TEST_TARGET)
	@echo "Running memory leak check..."
	valgrind --leak-check=full --show-leak-kinds=all ./$(TEST_TARGET)

# Performance profiling (requires gprof)
profile: CFLAGS += -pg
profile: $(TEST_TARGET)
	@echo "Running performance profiling..."
	./$(TEST_TARGET)
	gprof $(TEST_TARGET) gmon.out > profile_report.txt
	@echo "Profile report saved to profile_report.txt"

# Coverage analysis (requires gcov)
coverage: CFLAGS += --coverage
coverage: $(TEST_TARGET)
	@echo "Running coverage analysis..."
	./$(TEST_TARGET)
	gcov $(OBD_SOURCES)
	@echo "Coverage files generated (.gcov)"

# Help target
help:
	@echo "Available targets:"
	@echo "  all           - Build test and example executables"
	@echo "  test          - Build and run tests"
	@echo "  run-example   - Build and run example"
	@echo "  clean         - Clean build artifacts"
	@echo "  install-headers - Install headers to system"
	@echo "  docs          - Generate documentation"
	@echo "  analyze       - Run static code analysis"
	@echo "  format        - Format source code"
	@echo "  memcheck      - Check for memory leaks"
	@echo "  profile       - Run performance profiling"
	@echo "  coverage      - Run coverage analysis"
	@echo "  help          - Show this help message"

# Phony targets
.PHONY: all test run-example clean install-headers docs analyze format memcheck profile coverage help

# Dependencies
applications/es_obd.o: applications/es_obd.h applications/es_isotp.h
applications/es_isotp.o: applications/es_isotp.h
tests/test_obd.o: applications/es_obd.h
examples/obd_example.o: applications/es_obd.h

# Build configuration info
info:
	@echo "=== OBD-II Module Build Configuration ==="
	@echo "Compiler: $(CC)"
	@echo "Flags: $(CFLAGS)"
	@echo "Includes: $(INCLUDES)"
	@echo "Sources: $(OBD_SOURCES)"
	@echo "Test Sources: $(TEST_SOURCES)"
	@echo "Example Sources: $(EXAMPLE_SOURCES)"
	@echo "========================================="
