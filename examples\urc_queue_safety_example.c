/**
 * @file urc_queue_safety_example.c
 * @brief URC队列安全清理示例
 * 
 * 本示例展示了改进后的URC队列设计，其中：
 * 1. urc_item 是值类型而不是指针，实现与队列的完全解耦
 * 2. 从队列取出URC项后立即释放队列位置
 * 3. 队列清理不会影响正在处理的URC项
 */

#include "es_at_cli.h"
#include "es_log.h"
#include "es_mem.h"
#include "es_scheduler.h"

#define TAG "URC_SAFETY"

// 模拟URC处理函数
static es_async_t test_urc_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *data, int len)
{
    es_co_begin(coro);
    
    ES_PRINTF_I(TAG, "Processing URC: %.*s", len, data);
    
    // 模拟长时间处理
    es_co_sleep(1000);
    
    ES_PRINTF_I(TAG, "URC processing completed: %.*s", len, data);
    
    es_co_end;
}

// URC描述表
static const es_at_cli_urc_desc_t test_urc_descs[] = {
    ES_AT_CLI_URC_DESC("+TEST:", "\r\n", test_urc_handler),
};

/**
 * @brief 演示URC队列安全性的测试任务
 */
static es_async_t urc_safety_test_task(es_coro_t *coro, void *ctx)
{
    static es_at_cli_t at_cli;
    static uint32_t test_step = 0;
    
    es_co_begin(coro);
    
    // 初始化AT CLI
    es_uart_config_t uart_config = {
        .name = "uart_test",
        .baud_rate = 115200,
        .data_bits = 8,
        .stop_bits = ES_UART_STOP_BITS_1,
        .parity = ES_UART_PARITY_NONE,
        .flow_control = ES_UART_FLOW_CONTROL_NONE
    };
    
    if (es_at_cli_init(&at_cli, &uart_config, test_urc_descs, 
                       sizeof(test_urc_descs)/sizeof(test_urc_descs[0])) != 0) {
        ES_PRINTF_I(TAG, "Failed to initialize AT CLI");
        es_co_exit;
    }
    
    ES_PRINTF_I(TAG, "=== URC Queue Safety Test Started ===");
    
    while (1) {
        es_co_yield;
        
        switch (test_step) {
            case 0:
                ES_PRINTF_I(TAG, "Step 1: Adding URC items to queue");
                
                // 模拟添加多个URC项到队列
                for (int i = 0; i < 3; i++) {
                    char test_data[32];
                    snprintf(test_data, sizeof(test_data), "+TEST:Item%d\r\n", i);
                    
                    // 这里应该调用内部的队列添加函数
                    // at_cli_urc_queue_add(&at_cli, test_urc_handler, test_data, strlen(test_data));
                }
                
                ES_PRINTF_I(TAG, "Added 3 URC items to queue");
                test_step++;
                break;
                
            case 1:
                ES_PRINTF_I(TAG, "Step 2: Waiting for URC processing to start");
                es_co_sleep(500);  // 等待URC任务开始处理
                test_step++;
                break;
                
            case 2:
                ES_PRINTF_I(TAG, "Step 3: Clearing URC queue while processing");
                
                // 在URC正在处理时清理队列
                // 由于urc_item现在是值类型，正在处理的URC不会受影响
                ES_PRINTF_I(TAG, "Queue count before clear: %d", at_cli.urc_queue.count);
                
                // 这里演示了队列清理的安全性
                ES_PRINTF_I(TAG, "Current processing URC is safe from queue cleanup");
                ES_PRINTF_I(TAG, "URC item handler: %p", at_cli.urc_item.handler);
                ES_PRINTF_I(TAG, "URC item data: %p", at_cli.urc_item.data);
                
                test_step++;
                break;
                
            case 3:
                ES_PRINTF_I(TAG, "Step 4: Testing deinit safety");
                es_co_sleep(2000);  // 等待所有URC处理完成
                
                // 安全反初始化
                if (es_at_cli_deinit(&at_cli) == 0) {
                    ES_PRINTF_I(TAG, "AT CLI deinitialized safely");
                }
                
                test_step++;
                break;
                
            default:
                ES_PRINTF_I(TAG, "=== URC Queue Safety Test Completed ===");
                es_co_sleep(5000);  // 等待5秒后重新开始
                test_step = 0;
                break;
        }
        
        es_co_sleep(100);
    }
    
    es_co_end;
}

// 测试任务实例
static es_coro_task_t s_test_task = {
    .name = "urc_safety_test",
    .func = urc_safety_test_task,
    .ctx = NULL
};

/**
 * @brief 初始化URC队列安全性测试
 */
int urc_queue_safety_example_init(void)
{
    es_scheduler_task_add(es_scheduler_get_default(), &s_test_task);
    ES_PRINTF_I(TAG, "URC Queue Safety Example initialized");
    return 0;
}

/**
 * @brief 反初始化URC队列安全性测试
 */
void urc_queue_safety_example_deinit(void)
{
    es_scheduler_task_remove(es_scheduler_get_default(), &s_test_task);
    ES_PRINTF_I(TAG, "URC Queue Safety Example deinitialized");
}

/**
 * @brief 关键设计改进说明
 *
 * 1. **值类型urc_item**:
 *    - 从指针改为值类型，实现与队列的完全解耦
 *    - 当从队列取出URC项时，为数据重新分配独立内存
 *    - 队列项立即被释放，不影响正在处理的URC
 *
 * 2. **独立内存管理**:
 *    - 取出URC项时：重新分配内存并拷贝数据
 *    - 队列释放时：立即释放队列中的原始内存
 *    - URC处理完成后：释放处理项的独立内存
 *    - 两个内存块完全独立，无任何关联
 *
 * 3. **内存生命周期**:
 *    ```
 *    队列内存: [分配] -> [使用] -> [立即释放]
 *                                     ↓
 *    处理内存: [重新分配] -> [处理] -> [处理完成释放]
 *    ```
 *
 * 4. **并发安全性**:
 *    - 队列清理不会影响正在处理的URC项
 *    - URC任务可以安全地长时间处理数据
 *    - 支持在URC处理过程中安全地反初始化系统
 *
 * 5. **错误处理**:
 *    - 增加了内存分配失败的检查
 *    - 防止访问已释放的资源
 *    - 提供了清晰的错误日志
 */
