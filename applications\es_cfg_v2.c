//
// Created by lxy on 2025/1/14.
// ES Config V2 - Enhanced embedded parameter storage solution implementation
//

#include "es_cfg_v2.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

#ifndef ES_CFG_V2_TESTING
#include "es_flash.h"
#include "es_drv_os.h"
#include "es_at_srv.h"
#include "es_mem.h"
#include "es_at_def.h"
#include "es_coro.h"

// Static variables for flash interface implementation
static const es_partition_t *cfg_partition = NULL;

// Implementation of external flash interface functions using es_flash
int flash_read(uint32_t addr, void *data, uint32_t len) {
    if (!cfg_partition) {
        return -1;
    }
    
    // Convert absolute address to partition offset
    // uint32_t offset = addr - es_cfg_v2_flash_base;
    return es_flash_read(cfg_partition, addr, (uint8_t*)data, len) > 0 ? 0 : -1;
}

int flash_write(uint32_t addr, const void *data, uint32_t len) {
    if (!cfg_partition) {
        return -1;
    }
    
    // Convert absolute address to partition offset
    // uint32_t offset = addr - es_cfg_v2_flash_base;
    return es_flash_write(cfg_partition, addr, (const uint8_t*)data, len) > 0 ? 0 : -1;
}

int flash_erase_sector(uint32_t addr) {
    if (!cfg_partition) {
        return -1;
    }
    
    // Convert absolute address to partition offset
    // uint32_t offset = addr - es_cfg_v2_flash_base;
    uint32_t sector_size = cfg_partition->flash_dev->sector_size;
    
    return es_flash_erase(cfg_partition, addr, sector_size);
}

uint32_t flash_get_sector_size(void) {
    if (!cfg_partition) {
        return 4096; // Default sector size
    }
    return cfg_partition->flash_dev->sector_size;
}

uint32_t flash_get_cfg_base_addr(void) {
    if (!cfg_partition) {
        return 0;
    }
    return 0;
}

uint32_t flash_get_cfg_size(void) {
    if (!cfg_partition) {
        return 0;
    }
    return cfg_partition->size;
}

// Initialize config partition
static int init_cfg_partition(void) {
    if (cfg_partition) {
        return 0; // Already initialized
    }
    
    // Initialize flash system
    int ret = es_flash_init();
    if (ret != 0) {
        return ret;
    }
    
    // Find config partition (assuming it uses "download" partition)
    cfg_partition = es_partition_find("store");
    if (!cfg_partition) {
        return -1;
    }
    
    return 0;
}

#else
// Flash operation functions (to be implemented externally when testing)
extern int flash_read(uint32_t addr, void *data, uint32_t len);
extern int flash_write(uint32_t addr, const void *data, uint32_t len);
extern int flash_erase_sector(uint32_t addr);
extern uint32_t flash_get_sector_size(void);
extern uint32_t flash_get_cfg_base_addr(void);
extern uint32_t flash_get_cfg_size(void);
#endif

// CRC32 calculation
static inline uint32_t crc32_calc(const void *data, uint16_t len) {
    uint32_t crc = DEF_CRC_INIT_VALUE;
    const uint8_t *ptr = (const uint8_t *)data;
    while (len--) {
        crc ^= *ptr++;
        for (int k = 0; k < 8; k++) {
            crc = crc & 1 ? (crc >> 1) ^ 0xEDB88320 : crc >> 1;
        }
    }
    return crc;
}

// Internal constants
#define ES_CFG_V2_ENTRY_DELETED_MARKER  0xFFFF
#define ES_CFG_V2_ALIGN_SIZE            4
#define ES_CFG_V2_MAX_VALUE_SIZE        128
#define ES_CFG_V2_LOAD_RETRY_COUNT      10

// Global variables - parameter storage (static)
#define X(name, key, type, len, category, meta) static uint8_t es_cfg_v2_##name##_data[len] = {0};
ES_CFG_V2_PARAMS
#undef X

// Parameter metadata table
es_cfg_v2_param_t es_cfg_v2_param_table[ES_CFG_V2_PARAM_MAX] = {
#define X(name, key, type, len, category, meta) \
    {key, (uint8_t)(type), (uint8_t)(category), 0, len, meta, es_cfg_v2_##name##_data},
    ES_CFG_V2_PARAMS
#undef X
};


typedef struct {
    uint32_t write_count;
    uint32_t next_write_offset;
    uint8_t header_written : 1;
    uint8_t sector_id : 1;
    uint8_t reserved : 6;
} es_cfg_v2_active_sector_t;

// Internal state
static bool es_cfg_v2_initialized = false;
static uint32_t es_cfg_v2_flash_base = 0;
static uint32_t es_cfg_v2_flash_size = 0;
static uint32_t es_cfg_v2_sector_size = 0;

static es_cfg_v2_active_sector_t es_cfg_v2_active_sector = {0};

// Helper functions
static inline uint32_t align_up(uint32_t value, uint32_t alignment) {
    return (value + alignment - 1) & ~(alignment - 1);
}


static inline es_cfg_v2_param_t* find_param_by_key(es_cfg_v2_key_t key) {
    for (int i = 0; i < ES_CFG_V2_PARAM_MAX; i++) {
        if (es_cfg_v2_param_table[i].key == key) {
            return &es_cfg_v2_param_table[i];
        }
    }
    return NULL;
}

// Optimized version that leverages the fact that the first field in metadata is always the default value
static void set_default_value(es_cfg_v2_param_t *param) {
    
    switch (param->type) {
        case ES_CFG_V2_TYPE_UINT8:
        case ES_CFG_V2_TYPE_INT8:
        case ES_CFG_V2_TYPE_UINT16:
        case ES_CFG_V2_TYPE_INT16:
        case ES_CFG_V2_TYPE_UINT32:
        case ES_CFG_V2_TYPE_INT32: {
            // For all numeric types, the first field in metadata is the default value
            // We can directly copy from metadata since it stores the default in native type format
            memcpy(param->value_ptr, param->meta, param->value_len);
            break;
        }
        case ES_CFG_V2_TYPE_STRING:
        case ES_CFG_V2_TYPE_BINARY: {
            const es_cfg_v2_data_meta_t *meta = (const es_cfg_v2_data_meta_t*)param->meta;
            if (meta->def_data && meta->def_len > 0) {
                uint16_t copy_len = (meta->def_len < param->value_len) ? meta->def_len : param->value_len;
                memcpy(param->value_ptr, meta->def_data, copy_len);
                if (param->type == ES_CFG_V2_TYPE_STRING && copy_len < param->value_len) {
                    ((char*)param->value_ptr)[copy_len] = '\0';
                }
            } else {
                memset(param->value_ptr, 0, param->value_len);
            }
            break;
        }
        default:
            memset(param->value_ptr, 0, param->value_len);
            break;
    }
}

// Check if parameter value is equal to its default value
// Optimized version that leverages the fact that the first field in metadata is always the default value
static bool is_default_value(const es_cfg_v2_param_t *param) {
    switch (param->type) {
        case ES_CFG_V2_TYPE_UINT8:
        case ES_CFG_V2_TYPE_INT8:
        case ES_CFG_V2_TYPE_UINT16:
        case ES_CFG_V2_TYPE_INT16:
        case ES_CFG_V2_TYPE_UINT32:
        case ES_CFG_V2_TYPE_INT32: {
            // For all numeric types, the first field in metadata is the default value
            // We can directly compare memory since metadata stores the default in native type format
            return memcmp(param->value_ptr, param->meta, param->value_len) == 0;
        }
        case ES_CFG_V2_TYPE_STRING:
        case ES_CFG_V2_TYPE_BINARY: {
            const es_cfg_v2_data_meta_t *meta = (const es_cfg_v2_data_meta_t*)param->meta;
            if (meta->def_data && meta->def_len > 0) {
                uint16_t copy_len = (meta->def_len < param->value_len) ? meta->def_len : param->value_len;
                if (param->type == ES_CFG_V2_TYPE_STRING) {
                    return strncmp((char*)param->value_ptr, (char*)meta->def_data, copy_len) == 0;
                } else {
                    return memcmp(param->value_ptr, meta->def_data, copy_len) == 0;
                }
            } else {
                // Check if all bytes are zero
                for (int i = 0; i < param->value_len; i++) {
                    if (((uint8_t*)param->value_ptr)[i] != 0) {
                        return false;
                    }
                }
                return true;
            }
        }
        default:
            // Check if all bytes are zero
            for (int i = 0; i < param->value_len; i++) {
                if (((uint8_t*)param->value_ptr)[i] != 0) {
                    return false;
                }
            }
            return true;
    }
}

static inline uint32_t get_sector_addr(uint32_t sector_id) {
    return es_cfg_v2_flash_base + sector_id * es_cfg_v2_sector_size;
}

static inline uint32_t get_entry_size(uint16_t value_len) {
    return align_up(sizeof(es_cfg_v2_entry_t) + value_len, ES_CFG_V2_ALIGN_SIZE);
}

// Verify header CRC
static bool verify_header_crc(const es_cfg_v2_header_t *header) {
    if (!header) {
        return false;
    }
    
    // Calculate CRC of header excluding the header_crc32 field itself
    uint32_t calc_crc = crc32_calc(header, sizeof(es_cfg_v2_header_t) - sizeof(header->header_crc32));
    return calc_crc == header->header_crc32;
}


static int write_sector_header(void) {
    //set write count
    if(es_cfg_v2_active_sector.write_count == 0xFFFFFFFF) {
        es_cfg_v2_active_sector.write_count = 1; // Reset to 1 to avoid 0 (which indicates uninitialized)
    } else {
        es_cfg_v2_active_sector.write_count++;
    }
    es_cfg_v2_header_t header = {0};
    header.magic = ES_CFG_V2_MAGIC;
    header.factory_version = ES_CFG_V2_FACTORY_VERSION;
    header.user_version = ES_CFG_V2_USER_VERSION;
    header.write_count = es_cfg_v2_active_sector.write_count;
    header.header_crc32 = crc32_calc(&header, sizeof(header) - sizeof(header.header_crc32));
    int ret = flash_write(get_sector_addr(es_cfg_v2_active_sector.sector_id), &header, sizeof(header));
    if(ret != 0) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }
    es_cfg_v2_active_sector.next_write_offset = sizeof(es_cfg_v2_header_t);
    es_cfg_v2_active_sector.header_written = 1;
    return ES_CFG_V2_OK;
}

static inline int switch_active_sector(void) {

    es_cfg_v2_active_sector.sector_id = (es_cfg_v2_active_sector.sector_id + 1) & 0x01;
    //erase the current sector
    if(flash_erase_sector(get_sector_addr(es_cfg_v2_active_sector.sector_id)) != 0) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }
    //write header to the new sector
    if (write_sector_header() != 0) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }

    return ES_CFG_V2_OK;
}


// Helper function to write a single parameter entry to flash
static int write_flash_entry(const es_cfg_v2_param_t *param) {
    if (!param->value_ptr) {
        return ES_CFG_V2_ERR_INVALID_VALUE;
    }

    uint32_t entry_size = get_entry_size(param->value_len);

    //check if there is enough space in the current sector
    if(es_cfg_v2_active_sector.next_write_offset + entry_size > es_cfg_v2_sector_size) {
        if(es_cfg_v2_compact() != ES_CFG_V2_OK) {
            return ES_CFG_V2_ERR_FLASH_ERROR;
        }
    }

    uint8_t buf[ES_CFG_V2_MAX_VALUE_SIZE + sizeof(es_cfg_v2_entry_t)] = {0};
    es_cfg_v2_entry_t *entry = (es_cfg_v2_entry_t *)buf;
    entry->key = param->key;
    entry->value_len = param->value_len;
    entry->crc32 = crc32_calc(param->value_ptr, param->value_len);
    memcpy(buf + sizeof(es_cfg_v2_entry_t), param->value_ptr, param->value_len);

    uint32_t flash_addr = get_sector_addr(es_cfg_v2_active_sector.sector_id) + es_cfg_v2_active_sector.next_write_offset;

    //write entry to flash
    if (flash_write(flash_addr, buf, entry_size) != 0) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }

    //update next write offset
    es_cfg_v2_active_sector.next_write_offset += entry_size;

    return ES_CFG_V2_OK;
}

static int find_active_sector(void) {
    es_cfg_v2_header_t header0, header1;
    uint32_t sector0_addr = get_sector_addr(0);
    uint32_t sector1_addr = get_sector_addr(1);
    int ret0 = -1, ret1 = -1;
    
    // Read headers from both sectors with retry mechanism
    for (int retry = 0; retry < ES_CFG_V2_LOAD_RETRY_COUNT; retry++) {
        if (ret0 != 0) {
            ret0 = flash_read(sector0_addr, &header0, sizeof(header0));
        }
        if (ret1 != 0) {
            ret1 = flash_read(sector1_addr, &header1, sizeof(header1));
        }
        
        // If both reads are successful, break early
        if (ret0 == 0 && ret1 == 0) {
            break;
        }
        es_os_msleep(5);
    }
    
    bool sector0_valid = (ret0 == 0) && (header0.magic == ES_CFG_V2_MAGIC) && verify_header_crc(&header0);
    bool sector1_valid = (ret1 == 0) && (header1.magic == ES_CFG_V2_MAGIC) && verify_header_crc(&header1);

    es_cfg_v2_active_sector.header_written =  sector0_valid || sector1_valid;
    
    if (sector0_valid && sector1_valid) {
        // Both valid, use the one with higher write count
        if (header0.write_count >= header1.write_count) {
            es_cfg_v2_active_sector.write_count = header0.write_count;
            return 0;
        } else {
            es_cfg_v2_active_sector.write_count = header1.write_count;
            return 1;
        }
    } else if (sector0_valid) {
        es_cfg_v2_active_sector.write_count = header0.write_count;
        return 0;
    } else if (sector1_valid) {
        es_cfg_v2_active_sector.write_count = header1.write_count;
        return 1;
    } else {
        // Neither valid, use sector 0 as default
        es_cfg_v2_active_sector.write_count = 0;
        return 0;
    }
}

// Combined function to scan flash and load values in one pass with optimized batch reading
static int scan_and_load_flash(void) {
    uint32_t sector_addr = get_sector_addr(es_cfg_v2_active_sector.sector_id);
    uint32_t offset = sizeof(es_cfg_v2_header_t);
    es_cfg_v2_active_sector.next_write_offset = offset;
    
    #define BATCH_READ_SIZE 256
    uint8_t read_buffer[BATCH_READ_SIZE];
    uint32_t buffer_start_offset = 0;
    uint32_t buffer_valid_len = 0;
    bool buffer_valid = false;
    
    // Scan all entries and load values directly (later entries overwrite earlier ones)
    while (offset + sizeof(es_cfg_v2_entry_t) < es_cfg_v2_sector_size) {
        // Check if we need to read new data into buffer
        if (!buffer_valid || offset < buffer_start_offset || 
            offset >= buffer_start_offset + buffer_valid_len) {
            
            // Read 256 bytes starting from current offset
            buffer_start_offset = offset;
            uint32_t read_len = BATCH_READ_SIZE;
            if (buffer_start_offset + read_len > es_cfg_v2_sector_size) {
                read_len = es_cfg_v2_sector_size - buffer_start_offset;
            }
            
            // Try flash read up to 3 times
            int retry_count = 0;
            while (retry_count < 3) {
                if (flash_read(sector_addr + buffer_start_offset, read_buffer, read_len) == 0) {
                    break;
                }
                retry_count++;
                es_os_msleep(5);
            }
            if (retry_count >= 3) {
                // Flash read failed after all retries
                return ES_CFG_V2_ERR_FLASH_ERROR;
            }
            
            buffer_valid_len = read_len;
            buffer_valid = true;
        }
        
        // Check if buffer has enough data for entry header
        uint32_t buffer_offset = offset - buffer_start_offset;
        if (buffer_offset + sizeof(es_cfg_v2_entry_t) > buffer_valid_len) {
            // Not enough data for entry header, discard buffer and re-read from current offset
            buffer_valid = false;
            continue;
        }
        
        // Extract entry header from buffer
        es_cfg_v2_entry_t entry;
        memcpy(&entry, &read_buffer[buffer_offset], sizeof(entry));
        
        // Check if we've reached unwritten flash (all 0xFF)
        if (entry.key == 0xFFFF && entry.value_len == 0xFFFF && entry.crc32 == 0xFFFFFFFF) {
            break;
        }
        
        // Skip deleted entries, but process valid entries immediately
        if (entry.key != ES_CFG_V2_ENTRY_DELETED_MARKER) {
            // Validate entry before processing
            if (entry.value_len > ES_CFG_V2_MAX_VALUE_SIZE) {
                // Invalid value length - skip this entry but continue scanning
                uint32_t entry_size = get_entry_size(entry.value_len);
                
                // Safety check: ensure we don't go beyond sector boundary
                if (offset + entry_size > es_cfg_v2_sector_size) {
                    // This entry would go beyond sector boundary, stop scanning
                    break;
                }
                
                offset += entry_size;
                continue;
            }
            
            es_cfg_v2_param_t *param = find_param_by_key(entry.key);
            if (param) {
                // Check if buffer has enough data for complete entry (header + value)
                uint32_t value_buffer_offset = buffer_offset + sizeof(es_cfg_v2_entry_t);
                
                if (value_buffer_offset + entry.value_len <= buffer_valid_len) {
                    // Complete entry data is available in current buffer
                    uint32_t calc_crc = crc32_calc(&read_buffer[value_buffer_offset], entry.value_len);
                    if (calc_crc == entry.crc32) {
                        // Copy to parameter storage (later entries will overwrite earlier ones)
                        uint16_t copy_len = (entry.value_len < param->value_len) ? entry.value_len : param->value_len;
                        memcpy(param->value_ptr, &read_buffer[value_buffer_offset], copy_len);
                    }
                } else {
                    // Not enough data for complete entry, discard buffer and re-read from current offset
                    buffer_valid = false;
                    continue;
                }
            }
        }
        
        uint32_t entry_size = get_entry_size(entry.value_len);
        
        // Safety check: ensure we don't go beyond sector boundary
        if (offset + entry_size > es_cfg_v2_sector_size) {
            // This would go beyond sector boundary, stop scanning
            break;
        }
        
        es_cfg_v2_active_sector.next_write_offset = offset + entry_size;
        offset += entry_size;
    }
    
    return ES_CFG_V2_OK;
}

// Public API implementation

int es_cfg_v2_init(void) {
    if (es_cfg_v2_initialized) {
        return ES_CFG_V2_OK;
    }
    
#ifndef ES_CFG_V2_TESTING
    // Initialize config partition
    if (init_cfg_partition() != 0) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }
#endif
    
    es_cfg_v2_flash_base = flash_get_cfg_base_addr();
    es_cfg_v2_flash_size = flash_get_cfg_size();
    es_cfg_v2_sector_size = flash_get_sector_size();
    
    // Ensure we have at least 2 sectors
    if (es_cfg_v2_flash_size < 2 * es_cfg_v2_sector_size) {
        return ES_CFG_V2_ERR_NO_SPACE;
    }
    
    if (es_cfg_v2_sector_size < sizeof(es_cfg_v2_header_t)) {
        return ES_CFG_V2_ERR_NO_SPACE;
    }
    
    // Initialize all parameters to default values
    for (int i = 0; i < ES_CFG_V2_PARAM_MAX; i++) {
        set_default_value(&es_cfg_v2_param_table[i]);
    }


    
    es_cfg_v2_initialized = true;
    #ifndef ES_CFG_V2_TESTING
    // Load config from flash
    if (es_cfg_v2_load() != ES_CFG_V2_OK) {
        es_cfg_v2_initialized = false;
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }
#endif
    return ES_CFG_V2_OK;
}

int es_cfg_v2_load(void) {
    if (!es_cfg_v2_initialized) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }
    
    // Find active sector with retry mechanism built-in
    es_cfg_v2_active_sector.sector_id = find_active_sector();
    uint32_t sector_addr = get_sector_addr(es_cfg_v2_active_sector.sector_id);
    
    // Read and verify header with retry mechanism
    es_cfg_v2_header_t header = {0};
    int header_read_ret = -1;
    for (int retry = 0; retry < ES_CFG_V2_LOAD_RETRY_COUNT; retry++) {
        header_read_ret = flash_read(sector_addr, &header, sizeof(header));
        if (header_read_ret == 0) {
            break;
        }
        es_os_msleep(5);
    }
    
    if (header_read_ret != 0) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }
    
    // Check if flash is empty (first time use)
    if (header.magic == 0xFFFFFFFF) {
        if(write_sector_header() != ES_CFG_V2_OK) {
            return ES_CFG_V2_ERR_FLASH_ERROR;
        }
        return ES_CFG_V2_OK;
    }
    
    // Verify header
    if (header.magic != ES_CFG_V2_MAGIC || !verify_header_crc(&header)) {
        //erase sector and write header
        if(flash_erase_sector(sector_addr) != 0) {
            return ES_CFG_V2_ERR_FLASH_ERROR;
        }   
        if(write_sector_header() != ES_CFG_V2_OK) {
            return ES_CFG_V2_ERR_FLASH_ERROR;
        }
        return ES_CFG_V2_OK;
    }
    
    // Check version compatibility and reset parameters if needed
    bool factory_version_mismatch = (header.factory_version != ES_CFG_V2_FACTORY_VERSION);
    bool user_version_mismatch = (header.user_version != ES_CFG_V2_USER_VERSION);
    

    // Valid header found, mark as written
    es_cfg_v2_active_sector.header_written = 1;
    
    // Scan flash entries and load values in one pass
    int scan_result = scan_and_load_flash();
    if (scan_result != ES_CFG_V2_OK) {
        return scan_result;
    }
    
    // Reset parameters to default if version mismatch and perform compact
    if (factory_version_mismatch || user_version_mismatch) {
        if (factory_version_mismatch) {
            // Reset all factory parameters to default
            for (int i = 0; i < ES_CFG_V2_PARAM_MAX; i++) {
                es_cfg_v2_param_t *param = &es_cfg_v2_param_table[i];
                if (param->category == ES_CFG_V2_CATEGORY_FACTORY) {
                    set_default_value(param);
                }
            }
        }
        
        if (user_version_mismatch) {
            // Reset all user parameters to default
            for (int i = 0; i < ES_CFG_V2_PARAM_MAX; i++) {
                es_cfg_v2_param_t *param = &es_cfg_v2_param_table[i];
                if (param->category == ES_CFG_V2_CATEGORY_USER) {
                    set_default_value(param);
                }
            }
        }
        
        // Perform compact to clean up flash storage with updated versions
        int compact_result = es_cfg_v2_compact();
        if (compact_result != ES_CFG_V2_OK) {
            return compact_result;
        }
    }
    
    return ES_CFG_V2_OK;
}

int es_cfg_v2_get(es_cfg_v2_key_t key, void *value, uint16_t value_len) {
    if (!es_cfg_v2_initialized || !value) {
        return ES_CFG_V2_ERR_INVALID_VALUE;
    }
    
    const es_cfg_v2_param_t *param = find_param_by_key(key);
    if (!param) {
        return ES_CFG_V2_ERR_INVALID_KEY;
    }
    
    uint16_t copy_len = (value_len < param->value_len) ? value_len : param->value_len;
    memcpy(value, param->value_ptr, copy_len);
    
    return copy_len;
}

int es_cfg_v2_set(es_cfg_v2_key_t key, const void *value, uint16_t value_len) {
    if (!es_cfg_v2_initialized || !value) {
        return ES_CFG_V2_ERR_INVALID_VALUE;
    }
    
    es_cfg_v2_param_t *param = find_param_by_key(key);
    if (!param) {
        return ES_CFG_V2_ERR_INVALID_KEY;
    }
    
    if (value_len != param->value_len) {
        return ES_CFG_V2_ERR_INVALID_LEN;
    }

    if (!es_cfg_v2_validate(param, value, value_len)) {
        return ES_CFG_V2_ERR_INVALID_VALUE;
    }
    
    // Check if the new value is the same as current value - no operation needed
    if (memcmp(param->value_ptr, value, value_len) == 0) {
        return ES_CFG_V2_OK;
    }

    //save old value to temp buffer
    uint8_t temp_value[ES_CFG_V2_MAX_VALUE_SIZE] = {0};
    memcpy(temp_value, param->value_ptr, param->value_len);

    //set new value to param
    memcpy(param->value_ptr, value, value_len);

    //write new value to flash
    int ret = write_flash_entry(param);
    if (ret != ES_CFG_V2_OK) {
        //restore old value
        memcpy(param->value_ptr, temp_value, param->value_len);
        return ret;
    }
    
    return ES_CFG_V2_OK;
}

int es_cfg_v2_reset_to_default(es_cfg_v2_key_t key) {
    if (!es_cfg_v2_initialized) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }
    
    es_cfg_v2_param_t *param = find_param_by_key(key);
    if (!param) {
        return ES_CFG_V2_ERR_INVALID_KEY;
    }

    // Check if already at default value - no operation needed
    if (is_default_value(param)) {
        return ES_CFG_V2_OK;
    }
    
    // Get default value into temporary buffer
    uint8_t temp_value[ES_CFG_V2_MAX_VALUE_SIZE] = {0};
    memcpy(temp_value, param->value_ptr, param->value_len);  //save current value
    set_default_value(param);  //set to default temporarily
    
    //write default value to flash
    int ret = write_flash_entry(param);
    if (ret != ES_CFG_V2_OK) {
        //restore old value
        memcpy(param->value_ptr, temp_value, param->value_len);
        return ret;
    }   
    
    return ES_CFG_V2_OK;
}

int es_cfg_v2_reset_all(void) {
    if (!es_cfg_v2_initialized) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }

    bool any_reset = false;
    
    // Reset only non-default parameters
    for (int i = 0; i < ES_CFG_V2_PARAM_MAX; i++) {
        es_cfg_v2_param_t *param = &es_cfg_v2_param_table[i];
        if (!is_default_value(param)) {
            set_default_value(param);
            any_reset = true;
        }
    }

    if(any_reset) {
        if(es_cfg_v2_compact() != ES_CFG_V2_OK) {
            return ES_CFG_V2_ERR_FLASH_ERROR;
        }
    }

    return ES_CFG_V2_OK;
}

int es_cfg_v2_reset_by_category(es_cfg_v2_category_t category) {
    if (!es_cfg_v2_initialized) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }
    
    // Validate category
    if (category != ES_CFG_V2_CATEGORY_FACTORY && 
        category != ES_CFG_V2_CATEGORY_USER && 
        category != ES_CFG_V2_CATEGORY_ALL) {
        return ES_CFG_V2_ERR_INVALID_VALUE;
    }

    bool any_reset = false;
    
    // Reset parameters based on category
    for (int i = 0; i < ES_CFG_V2_PARAM_MAX; i++) {
        es_cfg_v2_param_t *param = &es_cfg_v2_param_table[i];
        if(param->category == category && !is_default_value(param)) {
            set_default_value(param);
            any_reset = true;
        }
    }

    if(any_reset) {
        if(es_cfg_v2_compact() != ES_CFG_V2_OK) {
            return ES_CFG_V2_ERR_FLASH_ERROR;
        }
    }

    return ES_CFG_V2_OK;
}

bool es_cfg_v2_validate(const es_cfg_v2_param_t *param, const void *value, uint16_t value_len) {
    if (!param || !value) {
        return false;
    }
    
    if (value_len != param->value_len) {
        return false;
    }
    
    switch (param->type) {
        case ES_CFG_V2_TYPE_UINT8: {
            const es_cfg_v2_num_meta_t *meta = (const es_cfg_v2_num_meta_t*)param->meta;
            uint8_t val = *(const uint8_t*)value;
            return val >= meta->u.u8.min && val <= meta->u.u8.max;
        }
        case ES_CFG_V2_TYPE_UINT16: {
            const es_cfg_v2_num_meta_t *meta = (const es_cfg_v2_num_meta_t*)param->meta;
            uint16_t val = *(const uint16_t*)value;
            return val >= meta->u.u16.min && val <= meta->u.u16.max;
        }
        case ES_CFG_V2_TYPE_UINT32: {
            const es_cfg_v2_num_meta_t *meta = (const es_cfg_v2_num_meta_t*)param->meta;
            uint32_t val = *(const uint32_t*)value;
            return val >= meta->u.u32.min && val <= meta->u.u32.max;
        }
        case ES_CFG_V2_TYPE_INT8: {
            const es_cfg_v2_num_meta_t *meta = (const es_cfg_v2_num_meta_t*)param->meta;
            int8_t val = *(const int8_t*)value;
            return val >= meta->u.i8.min && val <= meta->u.i8.max;
        }
        case ES_CFG_V2_TYPE_INT16: {
            const es_cfg_v2_num_meta_t *meta = (const es_cfg_v2_num_meta_t*)param->meta;
            int16_t val = *(const int16_t*)value;
            return val >= meta->u.i16.min && val <= meta->u.i16.max;
        }
        case ES_CFG_V2_TYPE_INT32: {
            const es_cfg_v2_num_meta_t *meta = (const es_cfg_v2_num_meta_t*)param->meta;
            int32_t val = *(const int32_t*)value;
            return val >= meta->u.i32.min && val <= meta->u.i32.max;
        }

        case ES_CFG_V2_TYPE_STRING: {
            const es_cfg_v2_data_meta_t *meta = (const es_cfg_v2_data_meta_t*)param->meta;
            return value_len <= meta->max_len;
        }
        case ES_CFG_V2_TYPE_BINARY: {
            const es_cfg_v2_data_meta_t *meta = (const es_cfg_v2_data_meta_t*)param->meta;
            return value_len <= meta->max_len;
        }
        default:
            return true;
    }
}


const es_cfg_v2_param_t* es_cfg_v2_get_param(es_cfg_v2_key_t key) {
    return find_param_by_key(key);
}

uint32_t es_cfg_v2_get_write_count(void) {
    return es_cfg_v2_active_sector.write_count;
}

// Parameter ID based API functions

int es_cfg_v2_get_by_name(es_cfg_v2_param_name_t name, void *value, uint16_t value_len) {
    if (!es_cfg_v2_initialized || !value) {
        return ES_CFG_V2_ERR_INVALID_VALUE;
    }
    
    if (name >= ES_CFG_V2_PARAM_MAX) {
        return ES_CFG_V2_ERR_INVALID_KEY;
    }
    
    es_cfg_v2_param_t *param = &es_cfg_v2_param_table[name];
    
    uint16_t copy_len = (value_len < param->value_len) ? value_len : param->value_len;
    memcpy(value, param->value_ptr, copy_len);
    
    return copy_len;
}

int es_cfg_v2_set_by_name(es_cfg_v2_param_name_t name, const void *value, uint16_t value_len) {
    if (name >= ES_CFG_V2_PARAM_MAX) {
        return ES_CFG_V2_ERR_INVALID_KEY;
    }

    // Use the existing es_cfg_v2_set interface for the actual implementation
    return es_cfg_v2_set(es_cfg_v2_param_table[name].key, value, value_len);
}

int es_cfg_v2_reset_to_default_by_name(es_cfg_v2_param_name_t name) {
    if (name >= ES_CFG_V2_PARAM_MAX) {
        return ES_CFG_V2_ERR_INVALID_KEY;
    }
    
    return es_cfg_v2_reset_to_default(es_cfg_v2_param_table[name].key);
}

const es_cfg_v2_param_t* es_cfg_v2_get_param_by_name(es_cfg_v2_param_name_t name) {
    if (name >= ES_CFG_V2_PARAM_MAX) {
        return NULL;
    }
    
    return &es_cfg_v2_param_table[name];
}

// Test helper function to reset initialization state (for unit testing only)
#ifdef ES_CFG_V2_TESTING
void es_cfg_v2_reset_init_state(void) {
    es_cfg_v2_initialized = false;
    es_cfg_v2_active_sector.write_count = 0;
    es_cfg_v2_active_sector.sector_id = 0;
    es_cfg_v2_active_sector.next_write_offset = 0;
    es_cfg_v2_active_sector.header_written = 0;
    
    // Reset all parameters to default values to simulate restart
    for (int i = 0; i < ES_CFG_V2_PARAM_MAX; i++) {
        set_default_value(&es_cfg_v2_param_table[i]);
    }
}
#endif

// Enhanced compact function with dual-sector safety
int es_cfg_v2_compact(void) {
    if (!es_cfg_v2_initialized) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }

    //save old sector id and switch to new sector
    uint8_t old_sector_id = es_cfg_v2_active_sector.sector_id;

    //switch to new sector
    if(switch_active_sector() != ES_CFG_V2_OK) {
        return ES_CFG_V2_ERR_FLASH_ERROR;
    }
    
    //write only non-default parameters to new sector
    for (int i = 0; i < ES_CFG_V2_PARAM_MAX; i++) {
        es_cfg_v2_param_t *param = &es_cfg_v2_param_table[i];
        if (is_default_value(param)) {
            continue; // Skip default values
        }
        
        //write entry to flash
        if(write_flash_entry(param) != ES_CFG_V2_OK) {
            return ES_CFG_V2_ERR_FLASH_ERROR;
        }
    }
    
    //erase the old sector
    uint32_t old_sector_addr = get_sector_addr(old_sector_id);
    if (flash_erase_sector(old_sector_addr) != 0) {
        // Even if old sector erase fails, we can continue with new sector
        // This provides better safety against power loss
    }
    
    return ES_CFG_V2_OK;
}

#ifndef ES_CFG_V2_TESTING

// AT command handlers for ES Config V2

/**
 * @brief AT+CFG2RST command handler - Reset all parameters to default
 * Usage: AT+CFG2RST
 */
es_async_t at_srv_cmd_cfg2rst_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_EXEC) {
        int ret = es_cfg_v2_reset_all();
        if (ret == ES_CFG_V2_OK) {
            es_at_srv_send_ok();
        } else {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
        }
    } else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_NOT_SUPPORTED);
    }
    
    es_co_end;
}

/**
 * @brief AT+CFG2FACTORY command handler - Reset factory parameters to default
 * Usage: AT+CFG2FACTORY
 */
es_async_t at_srv_cmd_cfg2factory_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_EXEC) {
        int ret = es_cfg_v2_reset_by_category(ES_CFG_V2_CATEGORY_FACTORY);
        if (ret == ES_CFG_V2_OK) {
            es_at_srv_send_ok();
        } else {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
        }
    } else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_NOT_SUPPORTED);
    }
    
    es_co_end;
}

/**
 * @brief AT+CFG2USER command handler - Reset user parameters to default
 * Usage: AT+CFG2USER
 */
es_async_t at_srv_cmd_cfg2user_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_EXEC) {
        int ret = es_cfg_v2_reset_by_category(ES_CFG_V2_CATEGORY_USER);
        if (ret == ES_CFG_V2_OK) {
            es_at_srv_send_ok();
        } else {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
        }
    } else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_NOT_SUPPORTED);
    }
    
    es_co_end;
}

/**
 * @brief Format parameter value for AT response based on type
 */
static void format_param_value_for_at(const es_cfg_v2_param_t *param, char *buffer, size_t buffer_size) {
    switch (param->type) {
        case ES_CFG_V2_TYPE_UINT8:
            snprintf(buffer, buffer_size, "%u", *(uint8_t*)param->value_ptr);
            break;
        case ES_CFG_V2_TYPE_UINT16:
            snprintf(buffer, buffer_size, "%u", *(uint16_t*)param->value_ptr);
            break;
        case ES_CFG_V2_TYPE_UINT32:
            snprintf(buffer, buffer_size, "%u", *(uint32_t*)param->value_ptr);
            break;
        case ES_CFG_V2_TYPE_INT8:
            snprintf(buffer, buffer_size, "%d", *(int8_t*)param->value_ptr);
            break;
        case ES_CFG_V2_TYPE_INT16:
            snprintf(buffer, buffer_size, "%d", *(int16_t*)param->value_ptr);
            break;
        case ES_CFG_V2_TYPE_INT32:
            snprintf(buffer, buffer_size, "%d", *(int32_t*)param->value_ptr);
            break;

        case ES_CFG_V2_TYPE_STRING:
            snprintf(buffer, buffer_size, "%s", (char*)param->value_ptr);
            break;
        case ES_CFG_V2_TYPE_BINARY: {
            // Format binary as hex string
            char *ptr = buffer;
            size_t remaining = buffer_size;
            uint8_t *data = (uint8_t*)param->value_ptr;
            for (int i = 0; i < param->value_len && remaining > 2; i++) {
                int written = snprintf(ptr, remaining, "%02X", data[i]);
                ptr += written;
                remaining -= written;
            }
            break;
        }
        default:
            snprintf(buffer, buffer_size, "unknown");
            break;
    }
}

/**
 * @brief Parse value from AT command parameter based on type
 */
static int parse_param_value_from_at(const es_cfg_v2_param_t *param, const char *param_str, int param_len, void *value_buffer) {
    switch (param->type) {
        case ES_CFG_V2_TYPE_UINT8: {
            uint32_t val = atoi(param_str);
            *(uint8_t*)value_buffer = (uint8_t)val;
            break;
        }
        case ES_CFG_V2_TYPE_UINT16: {
            uint32_t val = atoi(param_str);
            *(uint16_t*)value_buffer = (uint16_t)val;
            break;
        }
        case ES_CFG_V2_TYPE_UINT32: {
            uint32_t val = atoi(param_str);
            *(uint32_t*)value_buffer = val;
            break;
        }
        case ES_CFG_V2_TYPE_INT8: {
            int32_t val = atoi(param_str);
            *(int8_t*)value_buffer = (int8_t)val;
            break;
        }
        case ES_CFG_V2_TYPE_INT16: {
            int32_t val = atoi(param_str);
            *(int16_t*)value_buffer = (int16_t)val;
            break;
        }
        case ES_CFG_V2_TYPE_INT32: {
            int32_t val = atoi(param_str);
            *(int32_t*)value_buffer = val;
            break;
        }

        case ES_CFG_V2_TYPE_STRING: {
            // Ensure null termination and length check
            int copy_len = (param_len < param->value_len - 1) ? param_len : param->value_len - 1;
            memcpy(value_buffer, param_str, copy_len);
            ((char*)value_buffer)[copy_len] = '\0';
            break;
        }
        case ES_CFG_V2_TYPE_BINARY: {
            // Parse hex string to binary
            if (param_len % 2 != 0) {
                return -1; // Invalid hex string length
            }
            int binary_len = param_len / 2;
            if (binary_len > param->value_len) {
                return -1; // Too much data
            }
            
            uint8_t *data = (uint8_t*)value_buffer;
            for (int i = 0; i < binary_len; i++) {
                char byte_str[3] = {param_str[i*2], param_str[i*2+1], 0};
                uint32_t byte_val;
                if (sscanf(byte_str, "%x", &byte_val) != 1) {
                    return -1; // Invalid hex character
                }
                data[i] = (uint8_t)byte_val;
            }
            break;
        }
        default:
            return -1;
    }
    return 0;
}

/**
 * @brief AT+CFG2 command handler - Configuration parameter operations
 * Usage: 
 *   AT+CFG2?              - Query all parameters
 *   AT+CFG2=key           - Query specific parameter by key
 *   AT+CFG2=key,value     - Set parameter value
 */
es_async_t at_srv_cmd_cfg2_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    int key;
    static int i = 0;
    static const es_cfg_v2_param_t *param = NULL;
    static char value_str[64] = {0};
    
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_SET) {
        if (ctx->param_count == 0) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_PARAM_MISSING);
            es_co_exit;
        }
        
        // Parse key parameter (hex format)
        if (sscanf((const char*)ctx->params[0].param, "%x", &key) != 1) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_co_exit;
        }
        
        param = es_cfg_v2_get_param(key);
        if (param == NULL) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
            es_co_exit;
        }
        
        if (ctx->param_count == 1) {
            // Query specific parameter: AT+CFG2=key
            format_param_value_for_at(param, value_str, sizeof(value_str));
            es_at_srv_fmt_send("+CFG2:%04x,%s\r\n", param->key, value_str);
            es_at_srv_send_ok();
            es_co_exit;
        } else if (ctx->param_count == 2) {
            // Set parameter: AT+CFG2=key,value
            uint8_t value_buffer[64] = {0};
            
            if (parse_param_value_from_at(param, (const char*)ctx->params[1].param, 
                                         ctx->params[1].len, value_buffer) != 0) {
                es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
                es_co_exit;
            }
            
            int ret = es_cfg_v2_set(key, value_buffer, param->value_len);
            if (ret == ES_CFG_V2_OK) {
                es_at_srv_send_ok();
            } else if (ret == ES_CFG_V2_ERR_INVALID_VALUE) {
                // Get constraint information for error message
                if (param->type == ES_CFG_V2_TYPE_UINT8) {
                    es_at_srv_fmt_send("ERROR: Range(%u-%u)\r\n", ((const es_cfg_v2_num_meta_t*)param->meta)->u.u8.min, ((const es_cfg_v2_num_meta_t*)param->meta)->u.u8.max);
                } else if (param->type == ES_CFG_V2_TYPE_UINT16) {
                    es_at_srv_fmt_send("ERROR: Range(%u-%u)\r\n", ((const es_cfg_v2_num_meta_t*)param->meta)->u.u16.min, ((const es_cfg_v2_num_meta_t*)param->meta)->u.u16.max);
                } else if (param->type == ES_CFG_V2_TYPE_UINT32) {
                    es_at_srv_fmt_send("ERROR: Range(%u-%u)\r\n", ((const es_cfg_v2_num_meta_t*)param->meta)->u.u32.min, ((const es_cfg_v2_num_meta_t*)param->meta)->u.u32.max);
                } else if (param->type == ES_CFG_V2_TYPE_INT8) {
                    es_at_srv_fmt_send("ERROR: Range(%d-%d)\r\n", ((const es_cfg_v2_num_meta_t*)param->meta)->u.i8.min, ((const es_cfg_v2_num_meta_t*)param->meta)->u.i8.max);
                } else if (param->type == ES_CFG_V2_TYPE_INT16) {
                    es_at_srv_fmt_send("ERROR: Range(%d-%d)\r\n", ((const es_cfg_v2_num_meta_t*)param->meta)->u.i16.min, ((const es_cfg_v2_num_meta_t*)param->meta)->u.i16.max);
                } else if (param->type == ES_CFG_V2_TYPE_INT32) {
                    es_at_srv_fmt_send("ERROR: Range(%d-%d)\r\n", ((const es_cfg_v2_num_meta_t*)param->meta)->u.i32.min, ((const es_cfg_v2_num_meta_t*)param->meta)->u.i32.max);

                } else if (param->type == ES_CFG_V2_TYPE_STRING || param->type == ES_CFG_V2_TYPE_BINARY) {
                    es_at_srv_fmt_send("ERROR: Max len(%u)\r\n", ((const es_cfg_v2_data_meta_t*)param->meta)->max_len);
                } else {
                    es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_PARAM);
                }
            } else {
                es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
            }
            es_co_exit;
        }
    } else if (cmd_type == ES_AT_SRV_CMD_QUERY) {
        // Query all parameters: AT+CFG2?
        for (i = 0; i < ES_CFG_V2_PARAM_MAX; i++) {
            param = &es_cfg_v2_param_table[i];
            format_param_value_for_at(param, value_str, sizeof(value_str));
            es_at_srv_fmt_send("+CFG2:%04x,%s,%s\r\n", param->key, value_str, (param->category == ES_CFG_V2_CATEGORY_FACTORY) ? "F" : "U");
        }
        es_at_srv_send_ok();
    } else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_NOT_SUPPORTED);
    }
    
    es_co_end;
}

/**
 * @brief AT+CFG2COMPACT command handler - Compact flash storage
 * Usage: AT+CFG2COMPACT
 */
es_async_t at_srv_cmd_cfg2compact_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_EXEC) {
        int ret = es_cfg_v2_compact();
        if (ret == ES_CFG_V2_OK) {
            es_at_srv_send_ok();
        } else {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
        }
    } else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_NOT_SUPPORTED);
    }
    
    es_co_end;
} 

#endif
