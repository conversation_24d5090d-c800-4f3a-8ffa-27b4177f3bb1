#ifndef __ES_BUTTON_H__
#define __ES_BUTTON_H__

#include <stdint.h>
#include "es_drv_pin.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Button event types */
typedef enum {
    ES_BUTTON_EVENT_NONE = 0,
    ES_BUTTON_EVENT_PRESS,          /* Press */
    ES_BUTTON_EVENT_RELEASE,        /* Release */
    ES_BUTTON_EVENT_SINGLE_CLICK,   /* Single click */
    ES_BUTTON_EVENT_DOUBLE_CLICK,   /* Double click */
    ES_BUTTON_EVENT_LONG_PRESS,     /* Long press */
    ES_BUTTON_EVENT_LONG_RELEASE,   /* Long press release */
} es_button_event_t;

/* Button states */
typedef enum {
    ES_BUTTON_STATE_IDLE = 0,
    ES_BUTTON_STATE_PRESSED,
    ES_BUTTON_STATE_WAIT_RELEASE,
    ES_BUTTON_STATE_WAIT_DOUBLE,
    ES_BUTTON_STATE_LONG_PRESSED,
} es_button_state_t;

/* Button callback function type */
typedef void (*es_button_callback_t)(uint16_t pin, es_button_event_t event, void *user_data);

/* Button configuration structure */
typedef struct {
    uint16_t pin;                       /* GPIO pin */
    uint16_t debounce_time;             /* Debounce time (ms) */
    uint16_t double_click_time;         /* Double click interval time (ms) */
    uint16_t long_press_time;           /* Long press time (ms) */
    es_button_callback_t callback;      /* Event callback function */
    void *user_data;                    /* User data */
    uint8_t active_level;               /* Active level (PIN_LOW/PIN_HIGH) */
} es_button_config_t;

/* Button control block */
typedef struct es_button {
    uint16_t pin;                       /* GPIO pin */
    uint16_t debounce_time;             /* Debounce time (ms) */
    uint16_t double_click_time;         /* Double click interval time (ms) */
    uint16_t long_press_time;           /* Long press time (ms) */
    es_button_callback_t callback;      /* Event callback function */
    void *user_data;                    /* User data */
    uint32_t press_time;                /* Press timestamp */
    uint32_t release_time;              /* Release timestamp */
    uint8_t active_level    : 1;        /* Active level (0/1) */
    uint8_t state           : 3;        /* Current state (0-4) */
    uint8_t is_active       : 1;        /* Whether active */
    uint8_t is_used         : 1;        /* Whether used */
    uint8_t reserved        : 2;        /* Reserved bits */
} es_button_t;

/* Default configuration parameters */
#define ES_BUTTON_DEFAULT_DEBOUNCE_TIME     20      /* 20ms */
#define ES_BUTTON_DEFAULT_DOUBLE_CLICK_TIME 300     /* 300ms */
#define ES_BUTTON_DEFAULT_LONG_PRESS_TIME   1000    /* 1000ms */

/* Maximum button count */
#define ES_BUTTON_MAX_COUNT                 8       /* Support up to 8 buttons */

/**
 * @brief Initialize button module
 * @return 0: success, -1: failure
 */
int es_button_init(void);

/**
 * @brief Create button
 * @param config Button configuration
 * @return Button handle, NULL indicates failure
 */
es_button_t *es_button_create(const es_button_config_t *config);

/**
 * @brief Delete button
 * @param button Button handle
 * @return 0: success, -1: failure
 */
int es_button_delete(es_button_t *button);

/**
 * @brief Enable button
 * @param button Button handle
 * @return 0: success, -1: failure
 */
int es_button_enable(es_button_t *button);

/**
 * @brief Disable button
 * @param button Button handle
 * @return 0: success, -1: failure
 */
int es_button_disable(es_button_t *button);

/**
 * @brief Get current timestamp (milliseconds)
 * @return Timestamp
 */
uint32_t es_button_get_tick(void);

#ifdef __cplusplus
}
#endif

#endif /* __ES_BUTTON_H__ */ 
