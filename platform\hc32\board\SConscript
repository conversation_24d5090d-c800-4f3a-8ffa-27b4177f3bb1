import os
import rtconfig
from building import *

Import('SDK_LIB')

cwd = GetCurrentDir()

# add general drivers
src = Split('''
''')

path =  [cwd]

startup_path_prefix = SDK_LIB

if rtconfig.PLATFORM in ['gcc']:
    src += [startup_path_prefix + '/hc32f460_ddl/drivers/cmsis/Device/HDSC/hc32f4xx/Source/GCC/startup_hc32f460.S']
elif rtconfig.PLATFORM in ['armcc', 'armclang']:
    src += [startup_path_prefix + '/hc32f460_ddl/drivers/cmsis/Device/HDSC/hc32f4xx/Source/ARM/startup_hc32f460.s']
elif rtconfig.PLATFORM in ['iccarm']:
    src += [startup_path_prefix + '/hc32f460_ddl/drivers/cmsis/Device/HDSC/hc32f4xx/Source/IAR/startup_hc32f460.s']

# CPPDEFINES = ['HC32F460', '__DEBUG']
CPPDEFINES = ['HC32F460']
group = DefineGroup('Drivers', src, depend = [''], CPPPATH = path, CPPDEFINES = CPPDEFINES)

Return('group')
