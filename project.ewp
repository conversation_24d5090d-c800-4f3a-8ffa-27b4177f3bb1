<project>
  <fileVersion>2</fileVersion>
  <configuration>
    <name>Debug</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>General</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>24</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>ExePath</name>
          <state>build\iar\debug\Exe</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>build\iar\debug\Obj</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>build\iar\debug\List</state>
        </option>
        <option>
          <name>GEndianMode</name>
          <state>0</state>
        </option>
        <option>
          <name>Input variant</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>Input description</name>
          <state>Automatic choice of formatter.</state>
        </option>
        <option>
          <name>Output variant</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>Output description</name>
          <state>Automatic choice of formatter.</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>OGCoreOrChip</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>Use the normal configuration of the C/C++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>6.50.1.4445</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state>7.70.1.11471</state>
        </option>
        <option>
          <name>GeneralEnableMisra</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVerbose</name>
          <state>0</state>
        </option>
        <option>
          <name>OGChipSelectEditMenu</name>
          <state>Default	None</state>
        </option>
        <option>
          <name>GenLowLevelInterface</name>
          <state>1</state>
        </option>
        <option>
          <name>GEndianModeBE</name>
          <state>1</state>
        </option>
        <option>
          <name>OGBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>GenStdoutInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>GeneralMisraVer</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules04</name>
          <version>0</version>
          <state>011111011111111110111111111011011101111011111010110110011110101111110111111111111101111111111001101111110011110001111111011011111111111111111</state>
        </option>
        <option>
          <name>RTConfigPath2</name>
          <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
        </option>
        <option>
          <name>GBECoreSlave</name>
          <version>24</version>
          <state>35</state>
        </option>
        <option>
          <name>OGUseCmsis</name>
          <state>0</state>
        </option>
        <option>
          <name>OGUseCmsisDspLib</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibThreads</name>
          <state>0</state>
        </option>
        <option>
          <name>CoreVariant</name>
          <version>24</version>
          <state>39</state>
        </option>
        <option>
          <name>GFPUDeviceSlave</name>
          <state>Default	None</state>
        </option>
        <option>
          <name>FPU2</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>NrRegs</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>NEON</name>
          <state>0</state>
        </option>
        <option>
          <name>GFPUCoreSlave2</name>
          <version>24</version>
          <state>39</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>31</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCDefines</name>
          <state>__DEBUG</state>
          <state>CLOCKS_PER_SEC=RT_TICK_PER_SECOND</state>
          <state>RT_USING_DLIBC</state>
          <state>RT_USING_LIBC</state>
          <state>_DLIB_ADD_EXTRA_SYMBOLS=0</state>
          <state>HC32F460</state>
          <state>__RTTHREAD__</state>
          <state>USE_DDL_DRIVER</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state />
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state />
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state />
        </option>
        <option>
          <name>CCDiagError</name>
          <state />
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>1</version>
          <state>00000000</state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>IEndianMode</name>
          <state>1</state>
        </option>
        <option>
          <name>IProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state />
        </option>
        <option>
          <name>CCLangConformance</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSignedPlainChar</name>
          <state>1</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagWarnAreErr</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state />
        </option>
        <option>
          <name>CompilerMisraOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIncludePath2</name>
          <state />
          <state>$PROJ_DIR$\.</state>
          <state>$PROJ_DIR$\rt-thread\components\drivers\include</state>
          <state>$PROJ_DIR$\applications</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\compilers\common\extension</state>
          <state>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\cmsis\Include</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\compilers\common\extension\fcntl\octal</state>
          <state>$PROJ_DIR$\board</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\posix\ipc</state>
          <state>$PROJ_DIR$\rt-thread\libcpu\arm\cortex-m4</state>
          <state>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\inc</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\posix\io\stdio</state>
          <state>$PROJ_DIR$\board\config</state>
          <state>$PROJ_DIR$\rt-thread\components\finsh</state>
          <state>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\cmsis\Device\HDSC\hc32f4xx\Include</state>
          <state>$PROJ_DIR$\rt-thread\libcpu\arm\common</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\posix\io\poll</state>
          <state>$PROJ_DIR$\libraries\hc32_drivers</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\compilers\common\include</state>
          <state>$PROJ_DIR$\board\ports</state>
          <state>$PROJ_DIR$\rt-thread\include</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCodeSection</name>
          <state>.text</state>
        </option>
        <option>
          <name>IInterwork2</name>
          <state>0</state>
        </option>
        <option>
          <name>IProcessorMode2</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CompilerMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>CompilerMisraRules04</name>
          <version>0</version>
          <state>011111011111111110111111111011011101111011111011110110011111101111110111111111111101111111111001101111110111110001111111011111111111111111111</state>
        </option>
        <option>
          <name>CCPosIndRopi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndNoDynInit</name>
          <state>0</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCppDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccExceptions</name>
          <state>1</state>
        </option>
        <option>
          <name>IccRTTI</name>
          <state>1</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptimizationNoSizeConstraints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategySlave</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>CCGuardCalls</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>AEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state />
        </option>
        <option>
          <name>AWarnRange1</name>
          <state />
        </option>
        <option>
          <name>AWarnRange2</name>
          <state />
        </option>
        <option>
          <name>ADebug</name>
          <state>1</state>
        </option>
        <option>
          <name>AltRegisterNames</name>
          <state>0</state>
        </option>
        <option>
          <name>ADefines</name>
          <state />
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AOutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>AMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsEdit</name>
          <state>100</state>
        </option>
        <option>
          <name>AIgnoreStdInclude</name>
          <state>0</state>
        </option>
        <option>
          <name>AUserIncludes</name>
          <state />
        </option>
        <option>
          <name>AExtraOptionsCheckV2</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptionsV2</name>
          <state />
        </option>
        <option>
          <name>AsmNoLiteralPool</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>OBJCOPY</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OOCOutputFile</name>
          <state>rtthread.bin</state>
        </option>
        <option>
          <name>OOCOutputFormat</name>
          <version>3</version>
          <state>3</state>
        </option>
        <option>
          <name>OCOutputOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCCommandLineProducer</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCObjCopyEnable</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <extensions />
        <cmdline />
        <hasPrio>0</hasPrio>
      </data>
    </settings>
    <settings>
      <name>BICOMP</name>
      <archiveVersion>0</archiveVersion>
      <data />
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <prebuild />
        <postbuild />
      </data>
    </settings>
    <settings>
      <name>ILINK</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>17</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IlinkOutputFile</name>
          <state>rtthread.out</state>
        </option>
        <option>
          <name>IlinkIcfFile</name>
          <state>$PROJ_DIR$\board\linker_scripts\link.icf</state>
        </option>
        <option>
          <name>IlinkLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XLinkMisraHandler</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkInputFileSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkDebugInfoEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkKeepSymbols</name>
          <state />
        </option>
        <option>
          <name>IlinkRawBinaryFile</name>
          <state />
        </option>
        <option>
          <name>IlinkRawBinarySymbol</name>
          <state />
        </option>
        <option>
          <name>IlinkRawBinarySegment</name>
          <state />
        </option>
        <option>
          <name>IlinkRawBinaryAlign</name>
          <state />
        </option>
        <option>
          <name>IlinkDefines</name>
          <state />
        </option>
        <option>
          <name>IlinkConfigDefines</name>
          <state />
        </option>
        <option>
          <name>IlinkMapFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogFile</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInitialization</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogModule</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogSection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogVeneer</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkIcfFileSlave</name>
          <state />
        </option>
        <option>
          <name>IlinkEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkSuppressDiags</name>
          <state />
        </option>
        <option>
          <name>IlinkTreatAsRem</name>
          <state />
        </option>
        <option>
          <name>IlinkTreatAsWarn</name>
          <state />
        </option>
        <option>
          <name>IlinkTreatAsErr</name>
          <state />
        </option>
        <option>
          <name>IlinkWarningsAreErrors</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkUseExtraOptions</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkExtraOptions</name>
          <state />
        </option>
        <option>
          <name>IlinkLowLevelInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAutoLibEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAdditionalLibs</name>
          <state />
        </option>
        <option>
          <name>IlinkOverrideProgramEntryLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabel</name>
          <state>__iar_program_start</state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>FillerStart</name>
          <state>0x0</state>
        </option>
        <option>
          <name>FillerEnd</name>
          <state>0x0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x0</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkStdoutInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcFullSize</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIElfToolPostProcess</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogAutoLibSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogRedirSymbols</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogUnusedFragments</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcReverseByteOrder</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcUseAsInput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptInline</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptExceptionsAllow</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsForce</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptMergeDuplSections</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptUseVfe</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptForceVfe</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackAnalysisEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackControlFile</name>
          <state />
        </option>
        <option>
          <name>IlinkStackCallGraphFile</name>
          <state />
        </option>
        <option>
          <name>CrcAlgorithm</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IlinkThreadsSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogCallGraph</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IARCHIVE</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IarchiveInputs</name>
          <state />
        </option>
        <option>
          <name>IarchiveOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>IarchiveOutput</name>
          <state>###Unitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BILINK</name>
      <archiveVersion>0</archiveVersion>
      <data />
    </settings>
  </configuration>
  <configuration>
    <name>Release</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>General</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>24</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OGChipSelectEditMenu</name>
          <state>Default	None</state>
        </option>
        <option>
          <name>GFPUDeviceSlave</name>
          <state>Default	None</state>
        </option>
        <option>
          <name>ExePath</name>
          <state>build\iar\release\Exe</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>build\iar\release\Obj</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>build\iar\release\List</state>
        </option>
        <option>
          <name>GEndianMode</name>
          <state>0</state>
        </option>
        <option>
          <name>Input variant</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>Input description</name>
          <state>Automatic choice of formatter.</state>
        </option>
        <option>
          <name>Output variant</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>Output description</name>
          <state>Automatic choice of formatter.</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>OGCoreOrChip</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>Use the normal configuration of the C/C++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>6.50.1.4445</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state>7.70.1.11471</state>
        </option>
        <option>
          <name>GeneralEnableMisra</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVerbose</name>
          <state>0</state>
        </option>
        <option>
          <name>GenLowLevelInterface</name>
          <state>1</state>
        </option>
        <option>
          <name>GEndianModeBE</name>
          <state>1</state>
        </option>
        <option>
          <name>OGBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>GenStdoutInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>GeneralMisraVer</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules04</name>
          <version>0</version>
          <state>011111011111111110111111111011011101111011111010110110011110101111110111111111111101111111111001101111110011110001111111011011111111111111111</state>
        </option>
        <option>
          <name>RTConfigPath2</name>
          <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
        </option>
        <option>
          <name>GBECoreSlave</name>
          <version>24</version>
          <state>39</state>
        </option>
        <option>
          <name>OGUseCmsis</name>
          <state>0</state>
        </option>
        <option>
          <name>OGUseCmsisDspLib</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibThreads</name>
          <state>0</state>
        </option>
        <option>
          <name>CoreVariant</name>
          <version>24</version>
          <state>39</state>
        </option>
        <option>
          <name>FPU2</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>NrRegs</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>NEON</name>
          <state>0</state>
        </option>
        <option>
          <name>GFPUCoreSlave2</name>
          <version>24</version>
          <state>39</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>31</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCDefines</name>
          <state />
          <state>CLOCKS_PER_SEC=RT_TICK_PER_SECOND</state>
          <state>RT_USING_DLIBC</state>
          <state>RT_USING_LIBC</state>
          <state>_DLIB_ADD_EXTRA_SYMBOLS=0</state>
          <state>HC32F460</state>
          <state>__RTTHREAD__</state>
          <state>USE_DDL_DRIVER</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state />
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state />
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state />
        </option>
        <option>
          <name>CCDiagError</name>
          <state />
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>1</version>
          <state>11111110</state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>IEndianMode</name>
          <state>1</state>
        </option>
        <option>
          <name>IProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state />
        </option>
        <option>
          <name>CCLangConformance</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSignedPlainChar</name>
          <state>1</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagWarnAreErr</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state />
        </option>
        <option>
          <name>CompilerMisraOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIncludePath2</name>
          <state />
          <state>$PROJ_DIR$\.</state>
          <state>$PROJ_DIR$\rt-thread\components\drivers\include</state>
          <state>$PROJ_DIR$\applications</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\compilers\common\extension</state>
          <state>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\cmsis\Include</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\compilers\common\extension\fcntl\octal</state>
          <state>$PROJ_DIR$\board</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\posix\ipc</state>
          <state>$PROJ_DIR$\rt-thread\libcpu\arm\cortex-m4</state>
          <state>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\inc</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\posix\io\stdio</state>
          <state>$PROJ_DIR$\board\config</state>
          <state>$PROJ_DIR$\rt-thread\components\finsh</state>
          <state>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\cmsis\Device\HDSC\hc32f4xx\Include</state>
          <state>$PROJ_DIR$\rt-thread\libcpu\arm\common</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\posix\io\poll</state>
          <state>$PROJ_DIR$\libraries\hc32_drivers</state>
          <state>$PROJ_DIR$\rt-thread\components\libc\compilers\common\include</state>
          <state>$PROJ_DIR$\board\ports</state>
          <state>$PROJ_DIR$\rt-thread\include</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCodeSection</name>
          <state>.text</state>
        </option>
        <option>
          <name>IInterwork2</name>
          <state>0</state>
        </option>
        <option>
          <name>IProcessorMode2</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CompilerMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>CompilerMisraRules04</name>
          <version>0</version>
          <state>011111011111111110111111111011011101111011111011110110011111101111110111111111111101111111111001101111110111110001111111011111111111111111111</state>
        </option>
        <option>
          <name>CCPosIndRopi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndNoDynInit</name>
          <state>0</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCppDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccExceptions</name>
          <state>1</state>
        </option>
        <option>
          <name>IccRTTI</name>
          <state>1</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptimizationNoSizeConstraints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategySlave</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>CCGuardCalls</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>AEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state />
        </option>
        <option>
          <name>AWarnRange1</name>
          <state />
        </option>
        <option>
          <name>AWarnRange2</name>
          <state />
        </option>
        <option>
          <name>ADebug</name>
          <state>1</state>
        </option>
        <option>
          <name>AltRegisterNames</name>
          <state>0</state>
        </option>
        <option>
          <name>ADefines</name>
          <state />
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AOutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>AMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsEdit</name>
          <state>100</state>
        </option>
        <option>
          <name>AIgnoreStdInclude</name>
          <state>0</state>
        </option>
        <option>
          <name>AUserIncludes</name>
          <state />
        </option>
        <option>
          <name>AExtraOptionsCheckV2</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptionsV2</name>
          <state />
        </option>
        <option>
          <name>AsmNoLiteralPool</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>OBJCOPY</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OOCOutputFormat</name>
          <version>3</version>
          <state>3</state>
        </option>
        <option>
          <name>OCOutputOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCOutputFile</name>
          <state>rtthread.bin</state>
        </option>
        <option>
          <name>OOCCommandLineProducer</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCObjCopyEnable</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <extensions />
        <cmdline />
        <hasPrio>0</hasPrio>
      </data>
    </settings>
    <settings>
      <name>BICOMP</name>
      <archiveVersion>0</archiveVersion>
      <data />
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <prebuild />
        <postbuild />
      </data>
    </settings>
    <settings>
      <name>ILINK</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>17</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IlinkLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XLinkMisraHandler</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkInputFileSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOutputFile</name>
          <state>rtthread.out</state>
        </option>
        <option>
          <name>IlinkDebugInfoEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkKeepSymbols</name>
          <state />
        </option>
        <option>
          <name>IlinkRawBinaryFile</name>
          <state />
        </option>
        <option>
          <name>IlinkRawBinarySymbol</name>
          <state />
        </option>
        <option>
          <name>IlinkRawBinarySegment</name>
          <state />
        </option>
        <option>
          <name>IlinkRawBinaryAlign</name>
          <state />
        </option>
        <option>
          <name>IlinkDefines</name>
          <state />
        </option>
        <option>
          <name>IlinkConfigDefines</name>
          <state />
        </option>
        <option>
          <name>IlinkMapFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogFile</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInitialization</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogModule</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogSection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogVeneer</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkIcfFile</name>
          <state>$PROJ_DIR$\board\linker_scripts\link.icf</state>
        </option>
        <option>
          <name>IlinkIcfFileSlave</name>
          <state />
        </option>
        <option>
          <name>IlinkEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkSuppressDiags</name>
          <state />
        </option>
        <option>
          <name>IlinkTreatAsRem</name>
          <state />
        </option>
        <option>
          <name>IlinkTreatAsWarn</name>
          <state />
        </option>
        <option>
          <name>IlinkTreatAsErr</name>
          <state />
        </option>
        <option>
          <name>IlinkWarningsAreErrors</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkUseExtraOptions</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkExtraOptions</name>
          <state />
        </option>
        <option>
          <name>IlinkLowLevelInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAutoLibEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAdditionalLibs</name>
          <state />
        </option>
        <option>
          <name>IlinkOverrideProgramEntryLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabel</name>
          <state>__iar_program_start</state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>FillerStart</name>
          <state>0x0</state>
        </option>
        <option>
          <name>FillerEnd</name>
          <state>0x0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x0</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkStdoutInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcFullSize</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIElfToolPostProcess</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogAutoLibSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogRedirSymbols</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogUnusedFragments</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcReverseByteOrder</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcUseAsInput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptInline</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptExceptionsAllow</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsForce</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptMergeDuplSections</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptUseVfe</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptForceVfe</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackAnalysisEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackControlFile</name>
          <state />
        </option>
        <option>
          <name>IlinkStackCallGraphFile</name>
          <state />
        </option>
        <option>
          <name>CrcAlgorithm</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IlinkThreadsSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogCallGraph</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IARCHIVE</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IarchiveInputs</name>
          <state />
        </option>
        <option>
          <name>IarchiveOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>IarchiveOutput</name>
          <state>###Unitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BILINK</name>
      <archiveVersion>0</archiveVersion>
      <data />
    </settings>
  </configuration>
  <group>
    <name>Applications</name>
    <file>
      <name>$PROJ_DIR$\applications\main.c</name>
    </file>
  </group>
  <group>
    <name>Compiler</name>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\common\cctype.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\common\cstdio.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\common\cstdlib.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\common\cstring.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\common\ctime.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\common\cwchar.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\dlib\environ.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\dlib\syscall_close.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\dlib\syscall_lseek.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\dlib\syscall_mem.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\dlib\syscall_open.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\dlib\syscall_read.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\dlib\syscall_remove.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\dlib\syscall_write.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\libc\compilers\dlib\syscalls.c</name>
    </file>
  </group>
  <group>
    <name>CPU</name>
    <file>
      <name>$PROJ_DIR$\rt-thread\libcpu\arm\common\backtrace.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\libcpu\arm\common\div0.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\libcpu\arm\common\showmem.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\libcpu\arm\cortex-m4\context_iar.S</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\libcpu\arm\cortex-m4\cpuport.c</name>
    </file>
  </group>
  <group>
    <name>DeviceDrivers</name>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\drivers\ipc\completion.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\drivers\ipc\dataqueue.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\drivers\ipc\pipe.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\drivers\ipc\ringblk_buf.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\drivers\ipc\ringbuffer.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\drivers\ipc\waitqueue.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\drivers\ipc\workqueue.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\drivers\misc\pin.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\drivers\serial\serial.c</name>
    </file>
  </group>
  <group>
    <name>Drivers</name>
    <file>
      <name>$PROJ_DIR$\board\board.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\board\board_config.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\cmsis\Device\HDSC\hc32f4xx\Source\IAR\startup_hc32f460.s</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32_drivers\drv_gpio.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32_drivers\drv_irq.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32_drivers\drv_usart.c</name>
    </file>
  </group>
  <group>
    <name>Finsh</name>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\finsh\shell.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\finsh\msh.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\finsh\msh_parse.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\components\finsh\cmd.c</name>
    </file>
  </group>
  <group>
    <name>Kernel</name>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\clock.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\components.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\device.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\idle.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\ipc.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\irq.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\kservice.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\mem.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\mempool.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\object.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\scheduler.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\thread.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\rt-thread\src\timer.c</name>
    </file>
  </group>
  <group>
    <name>Libraries</name>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_efm.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_interrupts.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_usart.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_dma.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_aos.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_sram.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_icg.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_fcg.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_gpio.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_rmu.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_clk.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_utility.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_pwc.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32_ll_tmr0.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\hc32_ll_driver\src\hc32f460_ll_interrupts_share.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\libraries\hc32f460_ddl\drivers\cmsis\Device\HDSC\hc32f4xx\Source\system_hc32f460.c</name>
    </file>
  </group>
  <group>
    <name>POSIX</name>
  </group>
</project>
