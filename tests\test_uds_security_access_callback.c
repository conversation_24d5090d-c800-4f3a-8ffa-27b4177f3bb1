/**
 * @file test_uds_security_access_callback.c
 * @brief Test for UDS security access callback interface
 * <AUTHOR> MCU Team
 * @date 2024
 */

#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

// Mock includes
#include "es_uds.h"

static const char *TAG = "TEST_UDS_SEC";

// Test result tracking
static int tests_run = 0;
static int tests_passed = 0;

#define TEST_ASSERT_EQUAL(expected, actual, message) \
    do { \
        tests_run++; \
        if ((expected) == (actual)) { \
            tests_passed++; \
            printf("✓ %s\n", message); \
        } else { \
            printf("✗ %s (expected: %d, actual: %d)\n", message, (int)(expected), (int)(actual)); \
        } \
    } while(0)

#define TEST_ASSERT_NOT_NULL(ptr, message) \
    do { \
        tests_run++; \
        if ((ptr) != NULL) { \
            tests_passed++; \
            printf("✓ %s\n", message); \
        } else { \
            printf("✗ %s (pointer is NULL)\n", message); \
        } \
    } while(0)

// Mock data for testing
static uint8_t mock_seed_data[] = {0x12, 0x34, 0x56, 0x78, 0xAB, 0xCD};
static uint16_t mock_seed_length = sizeof(mock_seed_data);
static bool callback_called = false;
static uint8_t callback_level = 0;
static uint16_t callback_seed_length = 0;

/**
 * @brief Test callback function that generates a simple key
 */
static uint16_t test_key_generator(uint8_t level, const uint8_t *seed, uint16_t seed_length,
                                  uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
    callback_called = true;
    callback_level = level;
    callback_seed_length = seed_length;
    
    // Validate parameters
    if (!seed || seed_length == 0 || !key_buffer || max_key_length < 4) {
        return 0;
    }
    
    // Generate a simple 4-byte key
    key_buffer[0] = seed[0] ^ 0xAA;
    key_buffer[1] = seed[1] ^ 0xBB;
    key_buffer[2] = seed[2] ^ 0xCC;
    key_buffer[3] = seed[3] ^ 0xDD;
    
    return 4;  // Return key length
}

/**
 * @brief Test callback that fails (returns 0)
 */
static uint16_t failing_key_generator(uint8_t level, const uint8_t *seed, uint16_t seed_length,
                                     uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
    return 0;  // Always fail
}

/**
 * @brief Test callback with user data
 */
static uint16_t user_data_key_generator(uint8_t level, const uint8_t *seed, uint16_t seed_length,
                                       uint8_t *key_buffer, uint16_t max_key_length, void *user_data) {
    uint32_t *multiplier = (uint32_t *)user_data;
    
    if (!seed || seed_length == 0 || !key_buffer || max_key_length < 2 || !multiplier) {
        return 0;
    }
    
    // Generate key using user data
    key_buffer[0] = (seed[0] * (*multiplier)) & 0xFF;
    key_buffer[1] = (seed[1] * (*multiplier)) & 0xFF;
    
    return 2;
}

/**
 * @brief Test callback function type validation
 */
static void test_callback_function_type(void)
{
    printf("\n--- Testing Callback Function Type ---\n");
    
    // Test that callback function pointer can be assigned
    es_uds_security_access_key_callback_t callback = test_key_generator;
    TEST_ASSERT_NOT_NULL(callback, "Callback function pointer should be assignable");
    
    // Test callback invocation
    uint8_t key_buffer[16];
    uint16_t key_length = callback(1, mock_seed_data, mock_seed_length, 
                                  key_buffer, sizeof(key_buffer), NULL);
    
    TEST_ASSERT_EQUAL(4, key_length, "Callback should return correct key length");
    TEST_ASSERT_EQUAL(true, callback_called, "Callback should be called");
    TEST_ASSERT_EQUAL(1, callback_level, "Callback should receive correct level");
    TEST_ASSERT_EQUAL(mock_seed_length, callback_seed_length, "Callback should receive correct seed length");
}

/**
 * @brief Test callback parameter validation
 */
static void test_callback_parameter_validation(void)
{
    printf("\n--- Testing Callback Parameter Validation ---\n");
    
    uint8_t key_buffer[16];
    uint16_t key_length;
    
    // Test with NULL seed
    key_length = test_key_generator(1, NULL, 4, key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(0, key_length, "Callback should fail with NULL seed");
    
    // Test with zero seed length
    key_length = test_key_generator(1, mock_seed_data, 0, key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(0, key_length, "Callback should fail with zero seed length");
    
    // Test with NULL key buffer
    key_length = test_key_generator(1, mock_seed_data, mock_seed_length, NULL, 16, NULL);
    TEST_ASSERT_EQUAL(0, key_length, "Callback should fail with NULL key buffer");
    
    // Test with insufficient key buffer size
    key_length = test_key_generator(1, mock_seed_data, mock_seed_length, key_buffer, 2, NULL);
    TEST_ASSERT_EQUAL(0, key_length, "Callback should fail with insufficient buffer size");
}

/**
 * @brief Test callback with user data
 */
static void test_callback_with_user_data(void)
{
    printf("\n--- Testing Callback with User Data ---\n");
    
    uint32_t multiplier = 3;
    uint8_t key_buffer[16];
    
    uint16_t key_length = user_data_key_generator(2, mock_seed_data, mock_seed_length,
                                                 key_buffer, sizeof(key_buffer), &multiplier);
    
    TEST_ASSERT_EQUAL(2, key_length, "User data callback should return correct key length");
    
    // Verify key generation with user data
    uint8_t expected_key0 = (mock_seed_data[0] * multiplier) & 0xFF;
    uint8_t expected_key1 = (mock_seed_data[1] * multiplier) & 0xFF;
    
    TEST_ASSERT_EQUAL(expected_key0, key_buffer[0], "First key byte should be correct");
    TEST_ASSERT_EQUAL(expected_key1, key_buffer[1], "Second key byte should be correct");
}

/**
 * @brief Test failing callback
 */
static void test_failing_callback(void)
{
    printf("\n--- Testing Failing Callback ---\n");
    
    uint8_t key_buffer[16];
    uint16_t key_length = failing_key_generator(1, mock_seed_data, mock_seed_length,
                                               key_buffer, sizeof(key_buffer), NULL);
    
    TEST_ASSERT_EQUAL(0, key_length, "Failing callback should return 0");
}

/**
 * @brief Test key generation algorithms
 */
static void test_key_generation_algorithms(void)
{
    printf("\n--- Testing Key Generation Algorithms ---\n");
    
    uint8_t key_buffer[16];
    uint16_t key_length;
    
    // Test with different seed data
    uint8_t test_seed1[] = {0x01, 0x02, 0x03, 0x04};
    uint8_t test_seed2[] = {0xFF, 0xFE, 0xFD, 0xFC};
    
    // Generate keys with different seeds
    key_length = test_key_generator(1, test_seed1, sizeof(test_seed1), 
                                   key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(4, key_length, "Should generate 4-byte key for test_seed1");
    
    uint8_t key1[4];
    memcpy(key1, key_buffer, 4);
    
    key_length = test_key_generator(1, test_seed2, sizeof(test_seed2), 
                                   key_buffer, sizeof(key_buffer), NULL);
    TEST_ASSERT_EQUAL(4, key_length, "Should generate 4-byte key for test_seed2");
    
    // Keys should be different for different seeds
    bool keys_different = (memcmp(key1, key_buffer, 4) != 0);
    TEST_ASSERT_EQUAL(true, keys_different, "Different seeds should produce different keys");
}

/**
 * @brief Main test function
 */
int main(void)
{
    printf("=== UDS Security Access Callback Tests ===\n");
    
    // Reset test state
    callback_called = false;
    callback_level = 0;
    callback_seed_length = 0;
    
    // Run tests
    test_callback_function_type();
    test_callback_parameter_validation();
    test_callback_with_user_data();
    test_failing_callback();
    test_key_generation_algorithms();
    
    // Print results
    printf("\n=== Test Results ===\n");
    printf("Tests run: %d\n", tests_run);
    printf("Tests passed: %d\n", tests_passed);
    printf("Tests failed: %d\n", tests_run - tests_passed);
    
    if (tests_passed == tests_run) {
        printf("✓ All tests passed!\n");
        return 0;
    } else {
        printf("✗ Some tests failed!\n");
        return 1;
    }
}
