﻿/**
 * @file es_coro.c
 * @brief Stackless coroutine implementation
 */

#include "es_coro.h"
#include "es_drv_os.h"
#include "es_mem.h"
#include "es_log.h"
#include "es_utils.h"
#include <string.h>

#define TAG "CORO"

/* Ensure UINT32_MAX is defined */
#ifndef UINT32_MAX
#define UINT32_MAX (0xFFFFFFFFU)
#endif

#define ES_CORO_ID_MASK (ES_CORO_MAX_CHILDREN - 1)

typedef struct {
    uint8_t bitmap[(ES_CORO_MAX_CHILDREN + 7) / 8]; /**< Bitmap, marking timeout coroutine usage status */
    es_coro_timeout_t coro_pool[ES_CORO_MAX_CHILDREN]; /**< Coroutine pool */
    uint16_t last_freed_id;           // Recently freed coroutine ID (0 means no cache)
    uint8_t last_alloc_pos;          // Last allocation position (for sequential allocation optimization)
} es_coro_manager_t;

/* Global coroutine manager */
static es_coro_manager_t s_coro_manager = {0};

/**
 * @brief Allocate a child coroutine
 */
uint16_t es_coro_alloc(es_coro_t *parent)
{
    uint16_t id = ES_CORO_INVALID_ID;

    // Input parameter validation
    if (parent == NULL) {
        return ES_CORO_INVALID_ID;
    }

    // 1. First check if recently freed ID is available
    if (s_coro_manager.last_freed_id != ES_CORO_INVALID_ID &&
        !ES_BIT_TEST(s_coro_manager.bitmap, s_coro_manager.last_freed_id)) {
        id = s_coro_manager.last_freed_id;
        s_coro_manager.last_freed_id = ES_CORO_INVALID_ID;
        goto alloc_success;
    }

    // 2. Use optimized circular search
    uint8_t start_idx = s_coro_manager.last_alloc_pos;
    uint8_t idx = start_idx;
    do {
        if (!ES_BIT_TEST(s_coro_manager.bitmap, idx)) {
            id = idx;
            s_coro_manager.last_alloc_pos = idx;
            goto alloc_success;
        }
        idx = (idx + 1) & ES_CORO_ID_MASK;
    } while (idx != start_idx);

    // No available coroutine, return invalid ID
    ES_PRINTF_I(TAG, "No free coroutine slot available");
    return ES_CORO_INVALID_ID;

alloc_success:
    // Unified successful allocation handling
    ES_BIT_SET(s_coro_manager.bitmap, id);
    es_coro_timeout_init(&s_coro_manager.coro_pool[id]);
    parent->child_id = id;
    return id;
}

/**
 * @brief Free a child coroutine
 */
void es_coro_free(es_coro_t *parent)
{
    if (parent->child_id == ES_CORO_INVALID_ID || parent->child_id >= ES_CORO_MAX_CHILDREN) {
        return;
    }
    
    ES_BIT_CLEAR(s_coro_manager.bitmap, parent->child_id);
    s_coro_manager.last_freed_id = parent->child_id;  // Record last freed ID
    parent->child_id = ES_CORO_INVALID_ID;
}

/**
 * @brief Get coroutine pointer by parent
 */
es_coro_t* es_coro_get(es_coro_t *parent)
{
    if (parent->child_id == ES_CORO_INVALID_ID || parent->child_id >= ES_CORO_MAX_CHILDREN) {
        return NULL;
    }
    
    return (es_coro_t*)&s_coro_manager.coro_pool[parent->child_id];
}

/**
 * @brief Initialize coroutine
 */
void es_coro_init(es_coro_t *coro)
{
    if (coro == NULL) {
        return;
    }
    
    coro->child_id = ES_CORO_INVALID_ID;
    coro->line = 0;
}

/**
 * @brief Check if coroutine is completed
 */
bool es_coro_is_done(es_coro_t *coro)
{
    if (coro == NULL) {
        return true;
    }
    
    return coro->line == 0;
}

/**
 * @brief Initialize coroutine with timeout
 */
void es_coro_timeout_init(es_coro_timeout_t *coro)
{
    if (coro == NULL) {
        return;
    }
    
    es_coro_init(&coro->coro);
    coro->start_time = es_os_get_tick_ms();
}

/**
 * @brief Check if coroutine has timed out
 */
bool es_coro_timeout_check(es_coro_timeout_t *coro, uint32_t timeout)
{   
    if (coro == NULL) {
        return true;
    }
    return es_os_check_timeout(coro->start_time, timeout);
}

/**
 * @brief Initialize coroutine semaphore
 */
void es_coro_sem_init(es_coro_sem_t *sem, int16_t init_count, int16_t max_count)
{
    if (sem == NULL) {
        return;
    }
    
    if (init_count < 0) {
        init_count = 0;
    }
    
    if (max_count <= 0) {
        max_count = INT16_MAX;
    }
    
    sem->count = init_count;
    sem->max_count = max_count;
}

/**
 * @brief Acquire semaphore
 */
bool es_coro_sem_take(es_coro_sem_t *sem)
{
    if (sem == NULL) {
        return false;
    }
    
    /* When semaphore is 0, acquisition fails */
    if (sem->count <= 0) {
        return false;
    }
    
    sem->count--;
    return true;
}

/**
 * @brief Release semaphore
 */
bool es_coro_sem_give(es_coro_sem_t *sem)
{
    if (sem == NULL) {
        return false;
    }
    
    /* When maximum value is reached, no further increase */
    if (sem->count >= sem->max_count) {
        return false;
    }
    
    sem->count++;
    return true;
}

/**
 * @brief Initialize coroutine lock
 */
void es_coro_lock_init(es_coro_lock_t *lock)
{
    if (lock == NULL) {
        return;
    }
    
    lock->locked = 0;
}

/**
 * @brief Try to acquire lock
 */
bool es_coro_lock_acquire(es_coro_lock_t *lock)
{
    if (lock == NULL) {
        return false;
    }
    
    /* If lock is already occupied, acquisition fails */
    if (lock->locked) {
        return false;
    }
    
    /* Acquire lock */
    lock->locked = 1;
    return true;
}

/**
 * @brief Release lock
 */
void es_coro_lock_release(es_coro_lock_t *lock)
{
    if (lock == NULL) {
        return;
    }
    
    lock->locked = 0;
}

/**
 * @brief Monitor long-running coroutines
 * @param timeout_ms Timeout duration (milliseconds)
 * @return Whether any coroutine has timed out
 */
bool es_coro_monitor_timeout(uint32_t timeout_ms)
{
    bool has_timeout = false;
    uint32_t current_time = es_os_get_tick_ms();

    // Iterate through all coroutines
    for (uint8_t i = 0; i < ES_CORO_MAX_CHILDREN; i++) {
        if (ES_BIT_TEST(s_coro_manager.bitmap, i)) {
            es_coro_timeout_t *coro = &s_coro_manager.coro_pool[i];
            
            // Check if coroutine runtime exceeds specified time
            if ((current_time - coro->start_time) > timeout_ms) {
                ES_LOGW(TAG, "Coroutine[%d] running time exceeds %u ms, current line=%d", 
                    i, timeout_ms, coro->coro.line);
                has_timeout = true;
            }
        }
    }

    return has_timeout;
}


