﻿#include "es_drv_os.h"
#include <string.h>
#include <time.h>
#include "hc32_ll.h"
#include "hc32_ll_utility.h"
#include "hc32_ll_wdt.h"
#include "hc32_ll_rtc.h"
#include "hc32_ll_clk.h"
#include "hc32_ll_efm.h"
#include "hc32_ll_gpio.h"
#include "hc32_ll_pwc.h"
#include "hc32_ll_sram.h"
#include "hc32_pm.h"
#include "es_utils.h"
#include <stdint.h>
#include <stdlib.h>
#include "hc32_pin.h"
#include "ev_hc32f460_lqfp100_v2_lsm6dsl.h"



static uint8_t s_init = 0;
static volatile uint32_t s_tick_count = 0;

#define EXAMPLE_PERIPH_WE               (LL_PERIPH_GPIO | LL_PERIPH_EFM | LL_PERIPH_FCG | \
                                         LL_PERIPH_PWC_CLK_RMU | LL_PERIPH_SRAM)
#define EXAMPLE_PERIPH_WP               (LL_PERIPH_EFM | LL_PERIPH_FCG | LL_PERIPH_SRAM)

#define RT_TICK_PER_SECOND              (1000UL)

// 全局时间戳（volatile保证可见性）
volatile uint32_t system_timestamp = 0;

// 设置当前时间戳（从1970-01-01 00:00:00开始的秒数）
void es_set_timestamp(uint32_t timestamp) {
    system_timestamp = timestamp;
}

// 获取当前时间戳（原子读取）
uint32_t es_get_timestamp(void) {
    return system_timestamp;
}

/**
 * @brief 系统时钟配置
 */
void es_SystemClock_Config(void)
{
  stc_clock_xtal_init_t     stcXtalInit;
    stc_clock_pll_init_t      stcMpllInit;

    (void)CLK_XtalStructInit(&stcXtalInit);
    (void)CLK_PLLStructInit(&stcMpllInit);

    /* Set bus clk div. */
    CLK_SetClockDiv(CLK_BUS_CLK_ALL, (CLK_HCLK_DIV1 | CLK_EXCLK_DIV2 | CLK_PCLK0_DIV1 | CLK_PCLK1_DIV2 | \
                                      CLK_PCLK2_DIV4 | CLK_PCLK3_DIV4 | CLK_PCLK4_DIV2));

    /* Config Xtal and enable Xtal */
    stcXtalInit.u8Mode = CLK_XTAL_MD_OSC;
    stcXtalInit.u8Drv = CLK_XTAL_DRV_ULOW;
    stcXtalInit.u8State = CLK_XTAL_ON;
    stcXtalInit.u8StableTime = CLK_XTAL_STB_2MS;
    (void)CLK_XtalInit(&stcXtalInit);

    /* MPLL config (XTAL / pllmDiv * plln / PllpDiv = 200M). */
    stcMpllInit.PLLCFGR = 0UL;
    stcMpllInit.PLLCFGR_f.PLLM = 1UL - 1UL;
    stcMpllInit.PLLCFGR_f.PLLN = 50UL - 1UL;
    stcMpllInit.PLLCFGR_f.PLLP = 2UL - 1UL;
    stcMpllInit.PLLCFGR_f.PLLQ = 2UL - 1UL;
    stcMpllInit.PLLCFGR_f.PLLR = 2UL - 1UL;
    stcMpllInit.u8PLLState = CLK_PLL_ON;
    stcMpllInit.PLLCFGR_f.PLLSRC = CLK_PLL_SRC_XTAL;
    (void)CLK_PLLInit(&stcMpllInit);
    /* Wait MPLL ready. */
    while (SET != CLK_GetStableStatus(CLK_STB_FLAG_PLL))
    {
        ;
    }

    /* sram init include read/write wait cycle setting */
    SRAM_SetWaitCycle(SRAM_SRAMH, SRAM_WAIT_CYCLE0, SRAM_WAIT_CYCLE0);
    SRAM_SetWaitCycle((SRAM_SRAM12 | SRAM_SRAM3 | SRAM_SRAMR), SRAM_WAIT_CYCLE1, SRAM_WAIT_CYCLE1);

    /* flash read wait cycle setting */
    (void)EFM_SetWaitCycle(EFM_WAIT_CYCLE5);
    /* 3 cycles for 126MHz ~ 200MHz */
    GPIO_SetReadWaitCycle(GPIO_RD_WAIT3);
    /* Switch driver ability */
    (void)PWC_HighSpeedToHighPerformance();
    /* Switch system clock source to MPLL. */
    CLK_SetSysClockSrc(CLK_SYSCLK_SRC_PLL);
}

void  es_SysTick_Configuration(void)
{
    stc_clock_freq_t stcClkFreq;
    uint32_t cnts;

    CLK_GetClockFreq(&stcClkFreq);

    cnts = (uint32_t)stcClkFreq.u32HclkFreq / RT_TICK_PER_SECOND;

    SysTick_Config(cnts);
}

/** Peripheral Clock Configuration
*/
static void es_PeripheralClock_Config(void)
{
    CLK_SetPeriClockSrc(CLK_PERIPHCLK_PCLK);
}

void SysTick_Handler(void)
{
    s_tick_count++;
    if(s_tick_count % 1000 == 0) {
        system_timestamp++;
    }
}

void es_os_make_secret_key(void) {
    extern uint8_t secret_key[24];

    stc_efm_unique_id_t uqid = {0};
    EFM_GetUID(&uqid);
    unsigned char *encrypt_data = (unsigned char *)&uqid;
    int encrypt_len = sizeof(uqid);

    es_md5_ctx_t md5 = {0};
    es_md5_init(&md5);
    es_md5_update(&md5, encrypt_data, encrypt_len);
    es_md5_final(&md5, secret_key);

    //md5 last 8 bytes
    es_md5_init(&md5);
    es_md5_update(&md5, secret_key, 16);
    es_md5_final(&md5, secret_key+8);

    //36345438335004FF2362FFFF36345438335004FF2362FFFF
    //hex to bin
    const char *hex = "36345438335004FF2362FFFF36345438335004FF2362FFFF";
    //hex str to bin
    for(int i = 0; i < 24; i++) {
        char d[3] = {0};
        d[0] = hex[i*2];
        d[1] = hex[i*2+1];
        secret_key[i] = strtol(d, NULL, 16);
    }
}

/**
 * @brief 初始化操作系统相关功能
 * @return int 0表示成功，非0表示失败
 */
int es_os_init(void)
{
    if (!s_init) {
            /* Peripheral registers write unprotected */
        LL_PERIPH_WE(EXAMPLE_PERIPH_WE);

        es_pin_init();
        hc32_pm_init();

        es_SystemClock_Config();
        es_PeripheralClock_Config();
        /* Configure the SysTick */
        es_SysTick_Configuration();

        es_os_make_secret_key();

        s_init = 1;
    }
    return 0;
}


uint32_t es_os_get_tick_ms(void)
{
    return s_tick_count;
}

uint64_t es_os_get_tick_us(void)
{
    /* HC32F460 SysTick通常以ms为单位，这里简单转换为us */
    return (uint64_t)es_os_get_tick_ms() * 1000ULL;
}

void es_os_feed_wdt(void)
{
    /* 喂狗，确保WDT不会复位系统 */
    // WDT_FeedDog();
}

/**
 * @brief 获取当前格式化的时间字符串
 * @param buf 输出缓冲区
 * @param size 缓冲区大小
 * @return char* 返回填充后的缓冲区指针
 */
char* es_os_get_time_str(char* buf, int size)
{
    return "";
    return buf;
}

void es_os_msleep(uint32_t ms)
{
    uint32_t start, now, delta, reload, ms_tick;
    start = SysTick->VAL;
    reload = SysTick->LOAD;
    ms_tick = SystemCoreClock / 1000UL;

    do
    {
        now = SysTick->VAL;
        delta = start > now ? start - now : reload + start - now;
    }
    while (delta < ms_tick * ms);
}



/**
 * @brief 微秒级延时
 * @param us 延时时间（微秒）
 */
void es_os_us_delay(uint32_t us)
{
    uint32_t start, now, delta, reload, us_tick;
    start = SysTick->VAL;
    reload = SysTick->LOAD;
    us_tick = SystemCoreClock / 1000000UL;

    do
    {
        now = SysTick->VAL;
        delta = start > now ? start - now : reload + start - now;
    }
    while (delta < us_tick * us);
}


void es_os_reboot(void) {
    //reboot use scb
    SCB->AIRCR = 0x05FA0004;
}
