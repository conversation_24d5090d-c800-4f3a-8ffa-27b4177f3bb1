/**
 * @file es_mdm.c
 * @brief Module communication implementation
 */

#include "es_mdm.h"
#include "es_log.h"
#include "es_mem.h"
#include "es_drv_os.h"
#include "es_scheduler.h"
#include "es_at_srv.h"
#include "es_flash.h"  /* Add partition related header files */
#include <string.h>
#include <stdlib.h>

// #include "es_cfg.h"
#include "es_time.h"
#include "es_ota.h"
#include "es_frame.h"
#include "es_frame_q.h"
// #include "es_frame_fs.h"
#include "es_frame_v2_fs.h"
#include "es_cfg_v2.h"
#define TAG "MDM"

#define ES_MDM_EVENT_INIT (1 << 0)
#define ES_MDM_EVENT_DATA (1 << 1)
#define ES_MDM_EVENT_SLEEP (1 << 2)
#define ES_MDM_EVENT_WAKEUP (1 << 3)
#define ES_MDM_EVENT_RESET (1 << 4)
#define ES_MDM_EVENT_MQTT_CONNECT (1 << 5)
#define ES_MDM_EVENT_MQTT_DISCONNECT (1 << 6)
#define ES_MDM_EVENT_MQTT_LOGIN (1 << 7)
#define ES_MDM_EVENT_MQTT_PUBLISH (1 << 8)

#define ES_MDM_EVENT_MASK \
    (ES_MDM_EVENT_INIT | ES_MDM_EVENT_DATA | ES_MDM_EVENT_SLEEP | ES_MDM_EVENT_WAKEUP | \
     ES_MDM_EVENT_RESET | ES_MDM_EVENT_MQTT_CONNECT | ES_MDM_EVENT_MQTT_DISCONNECT | \
     ES_MDM_EVENT_MQTT_LOGIN | ES_MDM_EVENT_MQTT_PUBLISH)

#define MQTT_EVT_ERROR (1 << 0)
#define MQTT_EVT_OPEN_SUCCESS (1 << 1)
#define MQTT_EVT_CONNECT_SUCCESS (1 << 2)
#define MQTT_EVT_SUB_SUCCESS (1 << 3)
#define MQTT_EVT_CLOSE_SUCCESS (1 << 4)
#define MQTT_EVT_PUB_SUCCESS (1 << 5)
#define MQTT_EVT_DISCONN_SUCCESS (1 << 6)
#define MQTT_EVT_LOGIN_SUCCESS (1 << 7)
#define MQTT_EVT_ACK_SUCCESS (1 << 8)

/* HTTP event definitions */
#define HTTP_EVT_ERROR (1 << 0)
#define HTTP_EVT_GET_SUCCESS (1 << 1)
#define HTTP_EVT_DATA_SUCCESS (1 << 2)

/* Module singleton instance */
static es_mdm_t s_mdm = {0};

typedef struct {
    const es_partition_t *part;    /* Download partition */
    uint32_t total_file_size;            /* Total file size */
    uint32_t total_rx_size;      /* Current download position */
    uint32_t max_rx_size;    /* Maximum download length per time */
    uint32_t cur_rx_size; /* Current receive length */
    uint32_t http_evt;            /* HTTP event flag */
    uint16_t is_https : 1;                /* Whether it's HTTPS */
    uint16_t port : 15;                     /* Port number */
} es_http_ctx_t;

/* Global HTTP context pointer */
static es_http_ctx_t *s_http_ctx = NULL;
static es_at_srv_sink_t at_srv_mdm_sink = {0};


static frame_tx_t tx_frame = {0};
static frame_queue_t dev_req_flight_queue = {0};
static frame_queue_t dev_rsp_flight_queue = {0};
static uint8_t req_buffer[256] = {0};
static uint8_t rsp_buffer[256] = {0};

void es_mdm_add_to_queue(const frame_tx_t *frame) {
    if(s_mdm.init_ok == 0) {
        return;
    }

    if (frame->head.type == frame_tx_type_min) {
        ES_LOGE(TAG, "tx frame type error");
        return;
    }

    const frame_info_t *info = frame_tx_get_info(frame->head.type);
    if (info == NULL) {
        ES_LOGE(TAG, "tx frame type error: %d", frame->head.type);
        return;
    }

    if (info->meta & META_DEV_RSP) {
        if (!s_mdm.flags.mqtt_login) {
            return;
        }
        if(frame_queue_push(&dev_rsp_flight_queue, frame) != 0) {
            ES_LOGE(TAG, "add to rsp queue error: %s", info->desc);
        }
    } else if (info->meta & META_DEV_REQ) {
        if (!s_mdm.flags.mqtt_login) {
            return;
        }
       if(frame_queue_push(&dev_req_flight_queue, frame) != 0) {
           ES_LOGE(TAG, "add to req queue error: %s", info->desc);
       }
    }
    else if (tx_frame.head.type == frame_tx_type_min && es_frame_v2_is_empty()) {
        memcpy(&tx_frame, frame, sizeof(frame_tx_t));
    }
    else {
        es_frame_v2_push(frame, frame_tx_get_size(frame->head.type));
    }
}

static bool pop_from_queue(frame_tx_t *frame) {
    if(s_mdm.init_ok == 0) {
        return false;
    }

    // Rsp queue
    if(frame_queue_pop(&dev_rsp_flight_queue, frame)) {
        return true;
    }

    // Req queue
    if(frame_queue_pop(&dev_req_flight_queue, frame)) {
        return true;
    }

    // Fs queue
    uint16_t len = sizeof(frame_tx_t);
    if(es_frame_v2_pop(frame, &len) == ES_FRAME_V2_OK) {
        return true;
    }

    return false;
}

static es_async_t es_mdm_urc_nitz_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    es_co_begin(coro);

    //+NITZ: 0,"+32","25/04/25","11:28:56"

    //to timestamp
    int year = 0;
    int month = 0;
    int day = 0;
    int hour = 0;
    int minute = 0;
    int second = 0;

    if(sscanf(buf, "+NITZ: %*d,\"%*[^\"]\",\"%d/%d/%d\",\"%d:%d:%d\"", &year, &month, &day, &hour, &minute, &second) == 6) {
        ES_LOGI(TAG, "year: %d, month: %d, day: %d, hour: %d, minute: %d, second: %d", year, month, day, hour, minute, second);
    } else {
        ES_LOGE(TAG, "NITZ format error");
        es_co_exit;
    }

    year += 2000;
    struct es_tm tm = {
        .tm_year = year - 1900,  // 2025 - 1900
        .tm_mon = month - 1,     // April (0-11)
        .tm_mday = day,
        .tm_hour = hour,
        .tm_min = minute,
        .tm_sec = second,
        .tm_isdst = -1   // Don't consider daylight saving time
    };

    uint32_t timestamp = es_mktime(&tm);
    ES_LOGI(TAG, "timestamp: %d", timestamp);

    es_set_timestamp(timestamp);

    es_co_end;
}

static es_async_t es_at_srv_mdm_sink_func(es_coro_t *coro, const uint8_t *data, uint16_t len)
{
    es_co_begin(coro);

    //publish
    es_co_await(es_mdm_at_mqtt_publish, data, len);

    es_co_end;
}


static es_async_t es_mdm_urc_mipstart_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    es_co_begin(coro);

    ES_PRINTF_D(TAG, "URC: %.*s", len, buf);
    
    if(strstr(buf, "SUCCESS") != NULL) {
        s_mdm.flags.mqtt_connect = 1;
        s_mdm.mqtt_event |= MQTT_EVT_CONNECT_SUCCESS;
    } else {
        s_mdm.mqtt_event |= MQTT_EVT_ERROR;
    }
    
    es_co_end;
}

static es_async_t es_mdm_urc_mconnect_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    es_co_begin(coro);
    
    if(strstr(buf, "SUCCESS") != NULL) {
        s_mdm.flags.mqtt_connect = 1;
        s_mdm.mqtt_event |= MQTT_EVT_CONNECT_SUCCESS;
    } else {
        s_mdm.mqtt_event |= MQTT_EVT_ERROR;
    }
    
    es_co_end;
}

static es_async_t es_mdm_urc_mdisconnect_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    es_co_begin(coro);

    ES_PRINTF_D(TAG, "URC: %.*s", len, buf);

    if(strstr(buf, "SUCCESS") != NULL || strstr(buf, "Already disconnect") != NULL) {
        s_mdm.flags.mqtt_connect = 0;
        s_mdm.mqtt_event |= MQTT_EVT_DISCONN_SUCCESS;
    } else {
        s_mdm.mqtt_event |= MQTT_EVT_ERROR;
    }
    
    es_co_end;
}

static es_async_t es_mdm_urc_mdisconnect_handler2(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    es_co_begin(coro);

    ES_PRINTF_D(TAG, "URC: %.*s", len, buf);

    s_mdm.flags.mqtt_connect = 0;
    s_mdm.mqtt_event |= MQTT_EVT_DISCONN_SUCCESS;
    s_mdm.mqtt_event |= MQTT_EVT_ERROR;

    es_co_end;
}

static es_async_t es_mdm_urc_mipclose_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    es_co_begin(coro);

    ES_PRINTF_D(TAG, "URC: %.*s", len, buf);

    if(strstr(buf, "SUCCESS") != NULL) {
        s_mdm.flags.mqtt_connect = 0;
        s_mdm.mqtt_event |= MQTT_EVT_CLOSE_SUCCESS;
    } else {
        s_mdm.mqtt_event |= MQTT_EVT_ERROR;
    }
    
    es_co_end;
}

static es_async_t es_mdm_urc_msub_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    es_co_begin(coro);

    ES_PRINTF_D(TAG, "URC: %.*s", len, buf);
    
    if(strstr(buf, "SUCCESS") != NULL) {
        s_mdm.mqtt_event |= MQTT_EVT_SUB_SUCCESS;
    } else {
        s_mdm.mqtt_event |= MQTT_EVT_ERROR;
    }
    
    es_co_end;
}

static es_async_t es_mdm_urc_mpub_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    es_co_begin(coro);

    ES_PRINTF_D(TAG, "URC: %.*s", len, buf);

    if(strstr(buf, "SUCCESS") != NULL) {
        s_mdm.mqtt_event |= MQTT_EVT_PUB_SUCCESS;
    } else {
        s_mdm.mqtt_event |= MQTT_EVT_ERROR;
    }
    
    es_co_end;
}


static es_async_t es_mdm_urc_msub_recv_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    static int total = 0;
    frame_tx_t rsp = {0};
    const uint8_t* data = NULL;
    const frame_info_t *f = NULL;

    es_co_begin(coro);

    ES_PRINTF_D(TAG, "URC: %.*s", len, buf);

    ES_CORO_LOCK_ACQUIRE(&at_cli->rx_lock);

    ES_CHECK(err, sscanf(buf, "+MSUB: %*[^,],%d bytes", &total) != 1 || total <= 0, "msub recv parse data length failed");
    ES_CHECK(err, total > sizeof(s_mdm.rx_buf) - 1, "rx buf size too small");

    s_mdm.rx_buf_len = 0;

    while(s_mdm.rx_buf_len < total + 1) {
        es_co_wait_timeout_ex(err, es_uart_read(at_cli->uart_dev, s_mdm.rx_buf + s_mdm.rx_buf_len, 1) == 1, 1000);
        s_mdm.rx_buf_len++;
    }

    ES_CORO_LOCK_RELEASE(&at_cli->rx_lock);

    ES_CHECK(err, s_mdm.rx_buf[s_mdm.rx_buf_len - 1] != '\"', "mqtt recv error1");

    if(total >= 2 && s_mdm.rx_buf[0] == 'A' && s_mdm.rx_buf[1] == 'T') {
        es_at_srv_recv(at_srv_mdm_sink.src_type, s_mdm.rx_buf, total);
    }
    else if(total >= 2 && s_mdm.rx_buf[0] == 0x55 && s_mdm.rx_buf[1] == 0x66) {        
        es_co_await_ex(err, frame_rx_decode, &rsp, s_mdm.rx_buf, total);
        // Check if is srv rsp
        data = s_mdm.rx_buf + offsetof(frame_rx_head_t, type);
        if(data[0] == 0x04 && tx_frame.head.type != frame_tx_type_min && tx_frame.head.seq == (data[1] << 8 | data[2])) {
            s_mdm.mqtt_event |= MQTT_EVT_ACK_SUCCESS;
        } else if(rsp.head.type > frame_tx_type_min && rsp.head.type < frame_tx_type_max) {
            f = frame_tx_get_info(rsp.head.type);
            if(f != NULL && f->meta & META_DEV_RSP) {
                data = s_mdm.rx_buf + offsetof(frame_rx_head_t, seq);
                rsp.head.seq = data[0] << 8 | data[1];
            }
            es_mdm_add_to_queue(&rsp);
        }
    }
		

    es_co_eee();
}

/**
 * @brief HTTP data receive URC handler function
 */
static es_async_t es_mdm_urc_httprecv_handler(es_coro_t *coro, es_at_cli_t *at_cli, const char *buf, int len) {
    static int total = 0;

    es_co_begin(coro);

    ES_PRINTF_D(TAG, "HTTP recv: %.*s", len, buf);

    ES_CORO_LOCK_ACQUIRE(&at_cli->rx_lock);

    ES_CHECK(err, s_http_ctx == NULL, "HTTP recv unknown urc");
    ES_CHECK(err, sscanf(buf, "$HTTPRECV:DATA,%d", &total) != 1 || total <= 0, "http recv parse data length failed");
    ES_CHECK(err, total > sizeof(s_mdm.rx_buf) - 2, "rx buf size too small");

    s_mdm.rx_buf_len = 0;
    while(s_mdm.rx_buf_len < total + 2) {
        es_co_wait_timeout_ex(err, es_uart_read(at_cli->uart_dev, s_mdm.rx_buf + s_mdm.rx_buf_len + s_http_ctx->cur_rx_size, 1) == 1, 3000);
        s_mdm.rx_buf_len++;
    }
    ES_CORO_LOCK_RELEASE(&at_cli->rx_lock);

    len = s_http_ctx->cur_rx_size + s_mdm.rx_buf_len;
    ES_CHECK(err, s_mdm.rx_buf[len - 2] != '\r' || s_mdm.rx_buf[len - 1] != '\n', "http recv \\r error");

    // Process file length information (first receive)
    if (s_http_ctx->total_file_size == 0) {
        char *p = strstr((const char*)s_mdm.rx_buf, "Content-Length:");
        if (p != NULL && (sscanf(p + strlen("Content-Length:"), "%d", &s_http_ctx->total_file_size) == 1)) {
            s_http_ctx->http_evt |= HTTP_EVT_GET_SUCCESS;
            ES_LOGI(TAG, "HTTP download file total size: %d", s_http_ctx->total_file_size);
        } else {
            s_http_ctx->http_evt |= HTTP_EVT_ERROR;
            ES_LOGE(TAG, "http recv parse content length failed");
        }
    } else {
        // Update current received data length
        s_http_ctx->cur_rx_size += total;
    }

    es_co_eee(


        ES_LOGE(TAG, "htt recv error, exit");
        if (s_http_ctx != NULL) {
            s_http_ctx->http_evt |= HTTP_EVT_ERROR;
        }
        ES_CORO_LOCK_RELEASE(&at_cli->rx_lock)
    );
}

static es_async_t es_mdm_at_init(es_coro_t *coro)
{
    es_co_begin(coro);

    // //ATE0\r\n
    es_at_cli_send_ex(err, &s_mdm.at_cli, "ATE0\r\n", 500, 10, 0);

    // //AT\r\n
    es_at_cli_send_ex(err, &s_mdm.at_cli, "AT\r\n", 1000, 8, 0);

    //"AT+GMR\r\n"
    es_co_forever {
        memset(s_mdm.version, 0, sizeof(s_mdm.version));
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+GMR\r\n", 1000, 3, 1);
        if(es_at_cli_parse_by_kw(&s_mdm.at_cli, "+GMR:", "+GMR: \"Revision:%[^,],", s_mdm.version) == 1) {
            ES_LOGI(TAG, "Version: %s", s_mdm.version);
            break;
        }
        ES_CHECK(err, coro->cnt >= 2, "version get failed");
    }

    es_co_forever {
        memset(s_mdm.imei, 0, sizeof(s_mdm.imei));
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+CGSN\r\n", 1000, 3, 1);
        if(es_at_cli_parse_by_kw(&s_mdm.at_cli, "\"86", "\"%15s\"", s_mdm.imei) == 1) {
            ES_LOGI(TAG, "IMEI: %s", s_mdm.imei);
            break;
        }
        ES_CHECK(err, coro->cnt >= 2, "imei get failed");
    }

    es_co_forever {
        memset(s_mdm.iccid, 0, sizeof(s_mdm.iccid));
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+ICCID\r\n", 1000, 3, 1);
        if(es_at_cli_parse_by_kw(&s_mdm.at_cli, "+ICCID:", "+ICCID:%20s", s_mdm.iccid) == 1) {
            ES_LOGI(TAG, "ICCID: %s", s_mdm.iccid);
            break;
        }
        ES_CHECK(err, coro->cnt >= 2, "iccid get failed");
    }

    es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+CEREG=0\r\n", 1000, 3, 0);

    ES_LOGI(TAG, "Init success");
    s_mdm.event &= ~ES_MDM_EVENT_INIT;
    s_mdm.event |= ES_MDM_EVENT_MQTT_CONNECT;

    es_co_eee();
}

static es_async_t es_mdm_at_sleep(es_coro_t* coro)
{
    es_co_begin(coro);

    es_co_end;
}

static es_async_t es_mdm_at_reset(es_coro_t* coro)
{
    es_co_begin(coro);

    s_mdm.event &= ~ES_MDM_EVENT_RESET;
    s_mdm.event |= ES_MDM_EVENT_INIT;

    es_co_end;
}

static es_async_t es_mdm_at_wakeup(es_coro_t* coro)
{
    es_co_begin(coro);

    es_co_end;
}

static es_async_t es_mdm_at_mqtt_close(es_coro_t* coro)
{
    int status = 0;
    es_co_begin(coro);

    //AT+MQTTSTATU\r\n
    es_at_cli_send_ex(err, &s_mdm.at_cli,  "AT+MQTTSTATU\r\n", 50, 3, 1);
    //"+MQTTSTATU:", "+MQTTSTATU: %d", &status
    if(es_at_cli_parse_by_kw(&s_mdm.at_cli, "+MQTTSTATU:", "+MQTTSTATU: %d", &status) == 1 && status == 1) {
        //AT+MDISCONNECT\r\n
        s_mdm.mqtt_event = 0;
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+MDISCONNECT\r\n", 1000, 3, 0);
        es_co_wait_timeout_ex(err, s_mdm.mqtt_event & MQTT_EVT_DISCONN_SUCCESS, 5000);

        //AT+MIPCLOSE\r\n
        s_mdm.mqtt_event = 0;
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+MIPCLOSE\r\n", 1000, 3, 0);
        es_co_wait_timeout_ex(err, s_mdm.mqtt_event & MQTT_EVT_CLOSE_SUCCESS, 5000);
    }

    es_co_eee();
}

static es_async_t es_mdm_at_mqtt_connect(es_coro_t* coro, const char* addr)
{
    static uint16_t port = 1883;
    static uint16_t size_of_addr = 0;
	const char *cp = NULL;
    es_co_begin(coro);

    // close
    es_co_await_ex(err, es_mdm_at_mqtt_close);

    //"AT+MCONFIG=\"%s\",admin,public\r\n", mdm.imei
    es_at_cli_fmt_send_ex(err, &s_mdm.at_cli, 1000, 3, 0, "AT+MCONFIG=\"%s\",admin,public\r\n", s_mdm.imei);

    ES_LOGI(TAG, "MQTT connect: %s", addr);

    //find :
    cp = strchr(addr, ':');
    if(cp != NULL) {
        port = strtol(cp + 1, NULL, 10);
        size_of_addr = cp - addr;
    } else {
        size_of_addr = strlen(addr);
        port = 1883;
    }

    s_mdm.mqtt_event = 0;
    //"AT+MIPSTART=\"%s\",%d,3\r\n", host, port
    es_at_cli_fmt_send_ex(err, &s_mdm.at_cli, 4000, 3, 0, "AT+MIPSTART=\"%.*s\",%d,3\r\n", size_of_addr, addr, port);
    es_co_wait_timeout_ex(err, s_mdm.mqtt_event & MQTT_EVT_CONNECT_SUCCESS, 5000);

    s_mdm.mqtt_event = 0;
    // AT+MCONNECT=1,180\r\n
    es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+MCONNECT=1,180\r\n", 1000, 3, 0);
    es_co_wait_timeout_ex(err, s_mdm.mqtt_event & MQTT_EVT_CONNECT_SUCCESS, 5000);

    s_mdm.mqtt_event = 0;
    //"AT+MSUB=\"thing/%s/s2d\",0\r\n", mdm.imei);
    es_at_cli_fmt_send_ex(err, &s_mdm.at_cli, 1000, 3, 0, "AT+MSUB=\"thing/%s/s2d\",0\r\n", s_mdm.imei);
    es_co_wait_timeout_ex(err, s_mdm.mqtt_event & MQTT_EVT_SUB_SUCCESS, 5000);

    s_mdm.event &= ~ES_MDM_EVENT_MQTT_CONNECT;
    s_mdm.event |= ES_MDM_EVENT_MQTT_LOGIN;

    es_co_eee();
}

//static es_async_t es_mdm_at_mqtt_disconnect(es_coro_t* coro)
//{
//    es_co_begin(coro);

//    es_co_end;
//}

static es_async_t es_mdm_at_mqtt_login(es_coro_t* coro)
{
		frame_tx_t login_frame = {0};
    es_co_begin(coro);

    
    frame_tx_t *frame = &login_frame;
    frame_tx_connect_t *connect = NULL;

    frame->head.type     = frame_tx_type_connect;
    frame->head.seq      = 1;
    connect              = &frame->value.connect;
    connect->proto       = 0x0110;
    connect->mcu_hardver = 0x0001;
    strcpy(connect->mcu_softver.str, "MCU_EN_MC129_2CAALV3.4BO4");
    strcpy(connect->mdm_softver.str, s_mdm.version);
    strcpy(connect->boot_softver.str, "BOOT_MC129_V1.0");

    s_mdm.tx_buf_len = frame_tx_encode(frame, (const uint8_t*)s_mdm.imei, s_mdm.tx_buf, sizeof(s_mdm.tx_buf));
    if(s_mdm.tx_buf_len <= 0) {
        ES_LOGE(TAG, "frame_tx_encode failed");
        es_co_exit;
    }

    es_co_await_ex(err, es_mdm_at_mqtt_publish, s_mdm.tx_buf, s_mdm.tx_buf_len);

    s_mdm.event &= ~ES_MDM_EVENT_MQTT_LOGIN;
    s_mdm.event |= ES_MDM_EVENT_MQTT_PUBLISH;

    es_co_eee();
}

es_async_t es_mdm_at_mqtt_publish(es_coro_t* coro, const uint8_t* data, uint16_t len)
{
    es_co_begin(coro);
    /* Send MQTT publish command */
    es_co_await_ex(err,es_at_cli_cmd_send, &s_mdm.at_cli, &(es_at_cli_req_t){
        .timeout_ms = 4000,
        .end_mark = ">",
    }, "AT+MPUBEX=\"thing/%s/d2s\",0,0,%d\r\n", s_mdm.imei, len);

    es_co_await_ex(err,es_at_cli_cmd_send, &s_mdm.at_cli, &(es_at_cli_req_t){
        .cmd = data,
        .cmd_len = len,
        .timeout_ms = 4000,
    }, NULL);

    s_mdm.mqtt_event = 0;
    //wait publish success
    es_co_wait_timeout_ex(err, s_mdm.mqtt_event != 0, 5000);
    ES_CHECK(err, !(s_mdm.mqtt_event & MQTT_EVT_PUB_SUCCESS), "mqtt publish failed");

    es_co_eee();
}

/**
 * @brief GPS enable control function
 */
static es_async_t es_mdm_at_gps_enable(es_coro_t* coro, int en)
{
    int status = 0;
    es_co_begin(coro);

    //Query current GPS status
    es_at_cli_fmt_send_ex(err, &s_mdm.at_cli, 600, 3, 1, "AT+MGPSC?\r\n", "+MGPSC: %d", &status);
    ES_CHECK(err, es_at_cli_parse_by_kw(&s_mdm.at_cli, "+MGPSC:", "+MGPSC: %d", &status) != 1, "gps status parse failed");

    //Switch status as needed
    if(status == 0 && en == 1) {
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+MGPSC=1\r\n", 600, 3, 0);
    } else if(status == 1 && en == 0) {
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+MGPSC=0\r\n", 600, 3, 0);
    }

    //If GPS is enabled, subscribe to related messages
    if(en) {
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+MGPSGET=\"GNGGA\",1\r\n", 600, 3, 0);
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+MGPSGET=\"GNRMC\",1\r\n", 600, 3, 0);
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT+MGPSGET=\"GPGSV\",1\r\n", 600, 3, 0);
    }

    es_co_eee();
}

/**
 * @brief HTTP download context structure
 */


/**
 * @brief HTTP download function
 */
es_async_t es_mdm_at_http_download(es_coro_t* coro, const char* url)
{
    const char *p;
    
    es_co_begin(coro);

    /* Allocate HTTP context */
    s_http_ctx = (es_http_ctx_t *)es_mem_alloc(sizeof(es_http_ctx_t));
    ES_CHECK(err, s_http_ctx == NULL, "malloc http_ctx failed");
    memset(s_http_ctx, 0, sizeof(es_http_ctx_t));

    /* Find download partition */
    ES_CHECK(err, (s_http_ctx->part = es_partition_find("download")) == NULL, "download partition not found");

    ES_CHECK(err, url == NULL, "url is NULL");

    ES_LOGI(TAG, "url:%s", url);

    ES_CHECK(err, strncmp(url, "http", 4) != 0, "invalid url format");

    s_http_ctx->is_https = (strncmp(url, "https", 5) == 0);
    s_http_ctx->port = s_http_ctx->is_https ? 443 : 80;

    /* Parse port number */
    p = strchr(url + 7, ':');
    if(p != NULL) {
        s_http_ctx->port = strtol(p + 1, NULL, 10);
    }

    /* Open HTTP connection */
    es_at_cli_send_ex(err, &s_mdm.at_cli, "AT$HTTPOPEN\r\n", 1000, 3, 0);

    // /* Set HTTP parameters */
    es_at_cli_fmt_send_ex(err, &s_mdm.at_cli, 3000, 1, 0, "AT$HTTPPARA=%s,%d,%d\r\n", url, s_http_ctx->port, s_http_ctx->is_https);

    if(s_http_ctx->total_rx_size == 0) {
        /* Send HTTP GET request to get file size */
        s_http_ctx->http_evt = 0;
        es_at_cli_send_ex(err, &s_mdm.at_cli, "AT$HTTPACTION=2\r\n", 10000, 1, 0);

        /* Wait for HTTP GET response */
        es_co_wait_timeout_ex(err, s_http_ctx->http_evt != 0, 10000);
        ES_CHECK(err, !(s_http_ctx->http_evt & HTTP_EVT_GET_SUCCESS), "http get failed");
        ES_CHECK(err, s_http_ctx->total_file_size == 0, "file length is 0");
        ES_CHECK(err, es_flash_erase(s_http_ctx->part, 0, s_http_ctx->total_file_size) < 0, "partition erase failed");

        ES_LOGI(TAG, "start download file size: %d", s_http_ctx->total_file_size);
    }

    /* Download file in chunks */
    while(s_http_ctx->total_rx_size < s_http_ctx->total_file_size) {
        es_co_yield;
        s_http_ctx->max_rx_size = sizeof(s_mdm.rx_buf) - 2;
        if(s_http_ctx->total_file_size - s_http_ctx->total_rx_size < s_http_ctx->max_rx_size) {
            s_http_ctx->max_rx_size = s_http_ctx->total_file_size - s_http_ctx->total_rx_size;
        }

        es_co_forever {
            /* Set Range header */
            es_at_cli_fmt_send_ex(retry, &s_mdm.at_cli, 1000, 3, 0, "AT$HTTPRQH=Range,bytes=%d-%d\r\n",  s_http_ctx->total_rx_size,  s_http_ctx->total_rx_size + s_http_ctx->max_rx_size - 1);

            /* Send HTTP GET request to download data chunk */
            s_http_ctx->http_evt = 0;
            s_http_ctx->cur_rx_size = 0;
            es_at_cli_send_ex(retry, &s_mdm.at_cli, "AT$HTTPACTION=0,1\r\n", 10000, 1, 0);

            /* Wait for data reception completion */
            es_co_wait_timeout(s_http_ctx->cur_rx_size == s_http_ctx->max_rx_size || s_http_ctx->http_evt != 0, 10000);

            if(s_http_ctx->cur_rx_size == s_http_ctx->max_rx_size) {
                /* Write to partition */
                ES_CHECK(retry, es_flash_write(s_http_ctx->part, s_http_ctx->total_rx_size,  s_mdm.rx_buf, s_http_ctx->cur_rx_size) < 0, "partition write failed");
                s_http_ctx->total_rx_size += s_http_ctx->cur_rx_size;
                break;
            }

            retry:
            ES_CHECK(err, coro->cnt >= 5, "http download retry times out");
            /* Wait 1 second before retry */
            es_co_sleep(1000);
        }
    }

    /* Close HTTP connection */
    es_at_cli_send_ex(err, &s_mdm.at_cli, "AT$HTTPCLOSE\r\n", 1000, 3, 0);

    /* Release HTTP context */
    es_mem_free_safe((void**)&s_http_ctx);

    ES_LOGI(TAG, "http download success");

    es_co_eee(
        /* Release HTTP context */
        es_mem_free_safe((void**)&s_http_ctx);
        es_at_cli_send(&s_mdm.at_cli, "AT$HTTPCLOSE\r\n", 1000, 3, 0);
    );
}

static es_async_t es_mdm_at_mqtt_frame_publish(es_coro_t* coro) {
    static const frame_info_t *info = NULL;
    es_co_begin(coro);
    if(tx_frame.head.type == frame_tx_type_min) {
        if(!pop_from_queue(&tx_frame)) {
            tx_frame.head.type = frame_tx_type_min;
            es_co_exit;
        }
        info = frame_tx_get_info(tx_frame.head.type);
        if(info == NULL) {
            tx_frame.head.type = frame_tx_type_min;
            es_co_exit;
        }
        s_mdm.cnt = 0;
    }
    if(tx_frame.head.seq == 0 && !(info->meta & META_DEV_RSP)) {
        tx_frame.head.seq = ++s_mdm.seq;
    }
    s_mdm.tx_buf_len = frame_tx_encode(&tx_frame, (const uint8_t*)s_mdm.imei, s_mdm.tx_buf, sizeof(s_mdm.tx_buf));
    if(s_mdm.tx_buf_len <= 0) {
        tx_frame.head.type = frame_tx_type_min;
        es_co_exit;
    }
    
    es_co_await_ex(err,es_mdm_at_mqtt_publish, s_mdm.tx_buf, s_mdm.tx_buf_len);
    if(!(info->meta & META_DEV_RSP)) {
        s_mdm.mqtt_event = 0;
        es_co_wait_timeout_ex(err, s_mdm.mqtt_event != 0, 4000);
        ES_CHECK(err, !(s_mdm.mqtt_event & MQTT_EVT_ACK_SUCCESS), "mqtt wait ack timeout");
    }

    tx_frame.head.type = frame_tx_type_min;

    es_co_exit;

    err:
    s_mdm.cnt++;
    if(s_mdm.cnt == 3) {
        s_mdm.event = ES_MDM_EVENT_MQTT_CONNECT;
        es_co_exit;
    }
    if(s_mdm.cnt >= 5) {
        tx_frame.head.type = frame_tx_type_min;
        s_mdm.event = ES_MDM_EVENT_RESET;
    }
    es_co_end;
}


/**
 * @brief Module main task
 */
static es_async_t es_mdm_task(es_coro_t* coro, void* ctx)
{
    es_mdm_t* mdm = (es_mdm_t*)ctx;

    es_co_begin(coro);

    while (1) {
        es_co_yield;
        if(mdm->event == 0 || mdm->event & ES_MDM_EVENT_RESET == 0) {
            mdm->event = ES_MDM_EVENT_INIT;
        }
        if (mdm->event & ES_MDM_EVENT_SLEEP) {
            es_co_await_ex(reboot,es_mdm_at_sleep);
        }

        if (mdm->event & ES_MDM_EVENT_WAKEUP) {
            es_co_await_ex(reboot,es_mdm_at_wakeup);
        }

        if (mdm->event & ES_MDM_EVENT_RESET) {
            reboot:
            es_co_await(es_mdm_at_reset);
            continue;
        }
        // Wait for initialization success
        if (mdm->event & ES_MDM_EVENT_INIT) {
            es_co_await_ex(reboot,es_mdm_at_init);
        }

//         if (mdm->event & ES_MDM_EVENT_MQTT_CONNECT || mdm->flags.mqtt_connect == 0) {
//             es_co_await_ex(reboot,es_mdm_at_mqtt_connect, es_cfg_string(server_addr));
//         }

        if (mdm->event & ES_MDM_EVENT_MQTT_LOGIN) {
            es_co_await_ex(reboot,es_mdm_at_mqtt_login);
        }

        if (mdm->event & ES_MDM_EVENT_MQTT_PUBLISH) {
            es_co_await(es_mdm_at_mqtt_frame_publish);
        }
    }
    es_co_end;
}

/* Standard URC description and handler function mapping */
static const es_at_cli_urc_desc_t s_mdm_urc_descs[] = {
    ES_AT_CLI_URC_DESC("+MIPSTART", "\r\n", es_mdm_urc_mipstart_handler),
    ES_AT_CLI_URC_DESC("+MCONNECT", "\r\n", es_mdm_urc_mconnect_handler),
    ES_AT_CLI_URC_DESC("+MSUB", "\r\n", es_mdm_urc_msub_handler),   
    ES_AT_CLI_URC_DESC("+MSUB:", "bytes,\"", es_mdm_urc_msub_recv_handler),
    ES_AT_CLI_URC_DESC("+MPUBEX:", "\r\n", es_mdm_urc_mpub_handler),
    ES_AT_CLI_URC_DESC("+NITZ", "\r\n", es_mdm_urc_nitz_handler),
    ES_AT_CLI_URC_DESC("$HTTPRECV:DATA,", "\r\n", es_mdm_urc_httprecv_handler),
    ES_AT_CLI_URC_DESC("+MIPCLOSE", "\r\n", es_mdm_urc_mipclose_handler),
    ES_AT_CLI_URC_DESC("+MDISCONNECT", "\r\n", es_mdm_urc_mdisconnect_handler),
    ES_AT_CLI_URC_DESC("+MQTT:DISCONNECTED", "\r\n", es_mdm_urc_mdisconnect_handler2),
};

/**
 * @brief Get module singleton instance
 */
es_mdm_t *es_mdm_get_instance(void)
{
    return &s_mdm;
}

/**
 * @brief Initialize module
 */
int es_mdm_init(void)
{
    /* If already initialized, return directly */
    if (s_mdm.init_ok) {
        ES_LOGI(TAG, "Module already initialized");
        return 0;
    }
    
    es_mdm_t *mdm = &s_mdm;
    
    /* Prepare UART configuration */
    es_uart_config_t uart_config = {
        .name = "uart4",
        .baud_rate = 115200,
        .data_bits = 8,
        .stop_bits = ES_UART_STOP_BITS_1,
        .parity = ES_UART_PARITY_NONE,
        .flow_control = ES_UART_FLOW_CONTROL_NONE
    };
    
    /* Merge URC descriptions */
    const es_at_cli_urc_desc_t *urc_descs = s_mdm_urc_descs;
    int urc_descs_size = sizeof(s_mdm_urc_descs) / sizeof(s_mdm_urc_descs[0]);
    
    /* Initialize AT command line interface */
    if (es_at_cli_init(&mdm->at_cli, &uart_config, urc_descs, urc_descs_size) != 0) {
        ES_LOGE(TAG, "Failed to initialize AT CLI");
        return -1;
    }

    at_srv_mdm_sink.src_type = es_at_srv_alloc_src_type();
    at_srv_mdm_sink.sink_func = es_at_srv_mdm_sink_func;

    es_at_srv_add_sink(&at_srv_mdm_sink);

    mdm->mdm_task.func = es_mdm_task;
    mdm->mdm_task.ctx = mdm;

    es_scheduler_task_add(es_scheduler_get_default(), &mdm->mdm_task);


    frame_queue_init(&dev_req_flight_queue, req_buffer, sizeof(req_buffer), 0);
    frame_queue_init(&dev_rsp_flight_queue, rsp_buffer, sizeof(rsp_buffer), 0);
    
    
    /* Update status */
    mdm->event = ES_MDM_EVENT_INIT;
    /* Mark as initialized */
    mdm->init_ok = 1;
    
    ES_LOGI(TAG, "Module initialized");
    return 0;
}


/**
 * @brief Handle AT+HTTP command - HTTP download control
 * 
 * Functions:
 * - Download file: AT+HTTP=<url>
 * 
 * @param coro Coroutine context
 * @param ctx Command context
 * @param cmd_type Command type
 * @return Coroutine return value
 */
es_async_t at_srv_cmd_http_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);
    
    if (cmd_type == ES_AT_SRV_CMD_SET) {
        // Check parameters
        if (ctx->param_count < 1) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_PARAM_MISSING);
            es_co_exit;
        }
        
        // Send start download notification
        es_at_srv_fmt_send("+HTTP: Starting download from %s\r\n", ctx->params[0].param);
        
        es_co_await_ex(err, es_mdm_at_http_download, ctx->params[0].param);
        
        // Send download completion notification
        es_at_srv_fmt_send("+HTTP: Download completed\r\n");

        //valid fw 
        if(es_ota_download_fw_valid() != 0) {
            ES_LOGE(TAG, "fw valid failed");
            es_at_srv_fmt_send("+HTTP: Download completed, but fw valid failed\r\n");
            es_co_exit;
        }

        es_at_srv_send_ok();

        es_co_exit;

        err:
        // Send download failure notification
        es_at_srv_fmt_send("+HTTP: Download failed\r\n");
        es_at_srv_send_error_code(ES_AT_SRV_ERR_OPERATION_FAILED);
    }
    else if (cmd_type == ES_AT_SRV_CMD_TEST) {
        // Test command
        es_at_srv_fmt_send("+HTTP: AT+HTTP=<url>\r\n");
        es_at_srv_send_ok();
    }
    else {
        // Unsupported command type
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
    }
    
    es_co_end;
}

es_async_t at_srv_cmd_frame_handler(es_coro_t *coro, es_at_srv_cmd_ctx_t *ctx, es_at_srv_cmd_type_t cmd_type)
{
    es_co_begin(coro);

    if(cmd_type == ES_AT_SRV_CMD_SET) {
        // Check parameters
        if (ctx->param_count < 1) {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_PARAM_MISSING);
            es_co_exit;
        }

        int type = atoi(ctx->params[0].param);
        if(type > frame_tx_type_min && type < frame_tx_type_max) {
            frame_tx_t frame = {0};
            frame.head.type = type;
            // Add to queue
            es_mdm_add_to_queue(&frame);
            es_mdm_add_to_queue(&frame);
            es_at_srv_send_ok();
        }
        else {
            es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
        }
    }
    else {
        es_at_srv_send_error_code(ES_AT_SRV_ERR_INVALID_CMD);
    }
    es_co_end;
}










