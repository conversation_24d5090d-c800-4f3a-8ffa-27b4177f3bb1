/**
 * @file mem_v2_example.c
 * @brief 内存分配器V2使用示例
 * <AUTHOR>
 * @date 2025/1/14
 * @copyright Copyright (c) 2025
 */

#include "es_mem_v2.h"
#include <stdio.h>
#include <string.h>

int main(void) {
    printf("=== Memory Allocator V2 Example ===\n");
    
    // 1. 初始化内存分配器
    es_mem_config_t config = {
        .enable_debug = true,
        .enable_stats = true,
        .total_heap_size = 0  // 使用默认大小
    };
    
    int ret = es_mem_v2_init(&config);
    if (ret != 0) {
        printf("Failed to initialize memory allocator\n");
        return -1;
    }
    printf("Memory allocator initialized successfully\n");
    
    // 2. 基本内存分配和释放
    printf("\n--- Basic Allocation ---\n");
    void *ptr1 = es_mem_v2_alloc(64);
    if (ptr1) {
        printf("Allocated 64 bytes at %p\n", ptr1);
        strcpy((char*)ptr1, "Hello, World!");
        printf("Written: %s\n", (char*)ptr1);
    }
    
    // 3. calloc使用示例
    printf("\n--- Calloc Example ---\n");
    int *numbers = (int*)es_mem_v2_calloc(10, sizeof(int));
    if (numbers) {
        printf("Allocated array for 10 integers (zeroed)\n");
        for (int i = 0; i < 10; i++) {
            numbers[i] = i * i;
        }
        printf("Array filled: ");
        for (int i = 0; i < 10; i++) {
            printf("%d ", numbers[i]);
        }
        printf("\n");
    }
    
    // 4. realloc使用示例
    printf("\n--- Realloc Example ---\n");
    char *buffer = (char*)es_mem_v2_alloc(16);
    if (buffer) {
        strcpy(buffer, "Short");
        printf("Original: %s\n", buffer);
        
        buffer = (char*)es_mem_v2_realloc(buffer, 64);
        if (buffer) {
            strcat(buffer, " but now it's much longer!");
            printf("Expanded: %s\n", buffer);
        }
    }
    
    // 5. 对齐内存分配
    printf("\n--- Aligned Allocation ---\n");
    void *aligned_ptr = es_mem_v2_aligned_alloc(100, 4);
    if (aligned_ptr) {
        printf("Allocated 100 bytes with 4-byte alignment at %p\n", aligned_ptr);
        printf("Address %% 4 = %lu\n", (uintptr_t)aligned_ptr % 4);
    }
    
    // 6. 获取内存统计信息
    printf("\n--- Memory Statistics ---\n");
    es_mem_stats_t stats;
    ret = es_mem_v2_get_stats(&stats);
    if (ret == 0) {
        printf("Total size: %u bytes\n", stats.total_size);
        printf("Used size: %u bytes\n", stats.used_size);
        printf("Free size: %u bytes\n", stats.free_size);
        printf("Allocations: %u\n", stats.alloc_count);
        printf("Active blocks: %u\n", stats.active_blocks);
        printf("Peak usage: %u bytes\n", stats.peak_used);
    }
    
    // 7. 调试功能示例
    printf("\n--- Debug Features ---\n");
    void *debug_ptr = es_mem_v2_alloc_debug(128);
    if (debug_ptr) {
        es_mem_block_info_t info;
        ret = es_mem_v2_get_block_info(debug_ptr, &info);
        if (ret == 0) {
            printf("Block info:\n");
            printf("  Address: %p\n", info.ptr);
            printf("  Requested size: %u bytes\n", info.size);
            printf("  Actual size: %u bytes\n", info.actual_size);
            printf("  Pool ID: %u\n", info.pool_id);
            printf("  Source: %s:%u\n", info.file ? info.file : "unknown", info.line);
        }
    }
    
    // 8. 内存池状态
    printf("\n--- Memory Pool Status ---\n");
    es_mem_v2_dump_pools();
    
    // 9. 安全释放示例
    printf("\n--- Safe Free Example ---\n");
    void *safe_ptr = es_mem_v2_alloc(32);
    printf("Allocated pointer: %p\n", safe_ptr);
    es_mem_v2_free_safe(&safe_ptr);
    printf("After safe free: %p\n", safe_ptr);  // 应该是NULL
    
    // 10. 释放所有内存
    printf("\n--- Cleanup ---\n");
    es_mem_v2_free(ptr1);
    es_mem_v2_free(numbers);
    es_mem_v2_free(buffer);
    es_mem_v2_free(aligned_ptr);
    es_mem_v2_free(debug_ptr);
    
    // 11. 检查内存泄漏
    int leaks = es_mem_v2_check_leaks();
    printf("Memory leaks detected: %d\n", leaks);
    
    // 12. 最终统计
    printf("\n--- Final Statistics ---\n");
    es_mem_v2_dump_stats();
    
    // 13. 反初始化
    ret = es_mem_v2_deinit();
    if (ret == 0) {
        printf("\nMemory allocator deinitialized successfully\n");
    }
    
    printf("=== Example Complete ===\n");
    return 0;
} 