# ES 日志系统文档

## 1. 概述

ES 日志系统是 ES MCU Framework 的重要组件，提供了完整的日志记录、存储和查询功能。该系统支持多级日志、远程查询、串口导出等功能，为系统调试、监控和故障诊断提供了强有力的支持。

### 1.1 主要特性

- **多级日志**: 支持 ERROR、WARN、INFO、DEBUG、VERBOSE 五个日志级别
- **多输出方式**: 支持串口输出、Flash存储、远程传输
- **环形存储**: 使用环形文件系统，自动覆盖旧日志
- **实时查询**: 支持实时日志查询和过滤
- **远程访问**: 支持通过网络远程查询日志
- **AT命令接口**: 提供串口AT命令查询接口
- **时间戳**: 每条日志都包含精确的时间戳

### 1.2 核心组件

- **es_log.h/c**: 日志系统核心实现
- **es_log_fs.h/c**: 基于环形文件系统的日志存储
- **es_log_v2_fs.h/c**: 日志系统 V2 文件存储实现
- **ringfs.h/c**: 环形文件系统实现

## 2. 系统架构

### 2.1 日志系统结构

```
应用层
├── 日志宏 (ES_LOGE, ES_LOGI, etc.)
├── 日志函数 (es_log_write)
└── 日志查询 (AT命令, 远程接口)

处理层
├── 日志格式化
├── 级别过滤
├── 输出分发
└── 缓存管理

存储层
├── 串口输出
├── Flash存储 (RingFS)
├── 网络传输
└── 文件导出

硬件层
├── UART接口
├── Flash存储器
├── 网络接口
└── 文件系统
```

### 2.2 日志级别

```c
typedef enum {
    ES_LOG_LEVEL_NONE = 0,      // 无日志输出
    ES_LOG_LEVEL_ERROR,         // 错误级别
    ES_LOG_LEVEL_WARN,          // 警告级别
    ES_LOG_LEVEL_INFO,          // 信息级别
    ES_LOG_LEVEL_DEBUG,         // 调试级别
    ES_LOG_LEVEL_VERBOSE,       // 详细级别
} es_log_level_t;
```

### 2.3 日志输出接口

```c
typedef struct {
    const char *name;                           // 输出器名称
    void (*sink_func)(es_log_level_t level,     // 输出函数
                     bool to_flash, 
                     const char *log_msg, 
                     uint32_t len);
    struct es_list_head node;                   // 链表节点
} es_log_sink_t;
```

## 3. 日志记录

### 3.1 日志宏定义

#### 3.1.1 标准日志宏

```c
// 记录到Flash的日志宏
#define ES_LOGE(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_ERROR, tag, true, fmt, ##__VA_ARGS__)
#define ES_LOGW(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_WARN, tag, true, fmt, ##__VA_ARGS__)
#define ES_LOGI(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_INFO, tag, true, fmt, ##__VA_ARGS__)
#define ES_LOGD(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_DEBUG, tag, true, fmt, ##__VA_ARGS__)
#define ES_LOGV(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_VERBOSE, tag, true, fmt, ##__VA_ARGS__)

// 仅串口输出的日志宏
#define ES_PRINTF_E(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_ERROR, tag, false, fmt, ##__VA_ARGS__)
#define ES_PRINTF_W(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_WARN, tag, false, fmt, ##__VA_ARGS__)
#define ES_PRINTF_I(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_INFO, tag, false, fmt, ##__VA_ARGS__)
#define ES_PRINTF_D(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_DEBUG, tag, false, fmt, ##__VA_ARGS__)
#define ES_PRINTF_V(tag, fmt, ...) es_log_write(ES_LOG_LEVEL_VERBOSE, tag, false, fmt, ##__VA_ARGS__)
```

#### 3.1.2 十六进制数据打印

```c
// 十六进制数据打印宏
#define ES_LOG_HEX(tag, width, buf, size) es_log_hex_dump(tag, width, buf, size)
```

### 3.2 日志格式

日志输出格式：
```
[时间戳][级别][标签] 日志内容
```

示例：
```
[01-15 10:30:25.123][INFO][SYSTEM] Device initialized successfully
[01-15 10:30:25.456][ERROR][NETWORK] Connection failed: -1
[01-15 10:30:25.789][DEBUG][CONFIG] Parameter 0x0020 set to 50
```

### 3.3 主要API

```c
// 日志系统初始化
int es_log_init(void);

// 写入日志
void es_log_write(es_log_level_t level, const char *tag, bool to_flash, 
                  const char *fmt, ...);

// 十六进制数据打印
void es_log_hex_dump(const char *tag, int width, const void *buf, int size);

// 设置日志级别
void es_log_set_level(es_log_level_t level);

// 获取日志级别
es_log_level_t es_log_get_level(void);

// 启用/禁用日志
void es_log_enable(bool enable);

// 添加日志输出器
int es_log_add_sink(es_log_sink_t *sink);

// 移除日志输出器
int es_log_remove_sink(es_log_sink_t *sink);
```

## 4. 日志存储

### 4.1 环形文件系统 (RingFS)

#### 4.1.1 存储结构

```
Flash 分区布局:
┌─────────────────┬─────────────────┐
│   Sector 0      │   Sector 1      │
│   (32KB)        │   (32KB)        │
├─────────────────┼─────────────────┤
│ Header + Logs   │ Header + Logs   │
└─────────────────┴─────────────────┘

扇区结构:
┌─────────────────┬─────────────────┬─────────────────┐
│     Header      │   Log Entries   │   Free Space    │
│   (256 bytes)   │   (Variable)    │   (Variable)    │
└─────────────────┴─────────────────┴─────────────────┘
```

#### 4.1.2 日志条目结构

```c
typedef struct {
    uint32_t timestamp;     // 时间戳
    uint8_t level;          // 日志级别
    uint8_t tag_len;        // 标签长度
    uint16_t msg_len;       // 消息长度
    char data[];            // 标签 + 消息内容
} log_entry_t;
```

### 4.2 日志存储API

```c
// 初始化日志存储
int es_log_ringfs_init(void);

// 写入日志到存储
int es_log_ringfs_write(const char *msg, uint32_t len);

// 读取日志条目
int es_log_ringfs_read(log_entry_t *entry, uint32_t max_size);

// 查找日志
int es_log_ringfs_find(const char *keyword, log_entry_t *entry, uint32_t max_size);

// 获取日志统计信息
int es_log_ringfs_get_stats(ringfs_stats_t *stats);

// 清空日志
int es_log_ringfs_clear(void);
```

## 5. 日志查询

### 5.1 AT命令接口

#### 5.1.1 支持的AT命令

```c
// 查询日志
AT+LOGV2=<max_count>,[<keyword1>,<keyword2>,...] // 查询日志，支持关键字过滤
AT+LOGV2?                                        // 查询日志统计信息

// 日志定位
AT+LOGV2SEEK=<position>,<offset>                 // 设置日志读取位置
AT+LOGV2SEEK?                                    // 查询当前读取位置

// 日志管理
AT+LOGV2CLR                                      // 清空所有日志
AT+LOGV2TEST=<count>,<interval_ms>               // 生成测试日志
```

#### 5.1.2 使用示例

```bash
# 查询最近10条日志
AT+LOGV2=10

# 查询包含"ERROR"关键字的日志
AT+LOGV2=50,ERROR

# 查询包含多个关键字的日志
AT+LOGV2=100,ERROR,NETWORK,TIMEOUT

# 查询日志统计信息
AT+LOGV2?

# 清空所有日志
AT+LOGV2CLR
```

### 5.2 远程查询接口

```c
// 远程日志查询结构
typedef struct {
    uint32_t start_time;        // 开始时间
    uint32_t end_time;          // 结束时间
    es_log_level_t min_level;   // 最小日志级别
    char keywords[256];         // 关键字列表
    uint16_t max_count;         // 最大返回数量
} log_query_request_t;

// 远程查询API
int es_log_remote_query(const log_query_request_t *request, 
                       log_entry_t *entries, uint16_t *count);
```

## 6. 日志系统 V2

### 6.1 V2 系统特性

- **优化存储**: 更高效的存储格式和压缩算法
- **快速检索**: 支持索引和快速检索
- **批量操作**: 支持批量读写操作
- **内存优化**: 减少内存使用和碎片

### 6.2 V2 API

```c
// 初始化日志V2系统
int es_log_v2_init(void);

// 写入日志条目
int es_log_v2_write(const void *data, uint16_t data_len);

// 读取日志条目
int es_log_v2_read(char *data);

// 重置读取位置
int es_log_v2_rewind(void);

// 查找下一个匹配条目
int es_log_v2_find_next(const char *keyword);

// 获取系统状态
int es_log_v2_get_status(es_log_v2_status_t *status);
```

## 7. 使用示例

### 7.1 基本日志记录

```c
void example_logging(void) {
    // 记录不同级别的日志
    ES_LOGE("SYSTEM", "Critical error occurred: %d", error_code);
    ES_LOGW("NETWORK", "Connection timeout, retrying...");
    ES_LOGI("CONFIG", "Configuration loaded successfully");
    ES_LOGD("SENSOR", "Temperature: %d.%d°C", temp/10, temp%10);
    ES_LOGV("PROTOCOL", "Received frame: type=0x%02X, len=%d", type, len);
    
    // 打印十六进制数据
    uint8_t data[] = {0x01, 0x02, 0x03, 0x04, 0x05};
    ES_LOG_HEX("DATA", 16, data, sizeof(data));
}
```

### 7.2 自定义日志输出器

```c
// 自定义网络日志输出器
void network_log_sink(es_log_level_t level, bool to_flash, 
                     const char *log_msg, uint32_t len) {
    if (level <= ES_LOG_LEVEL_ERROR) {
        // 只发送错误级别的日志到网络
        send_log_to_server(log_msg, len);
    }
}

// 注册自定义输出器
void register_network_sink(void) {
    static es_log_sink_t network_sink = {
        .name = "network",
        .sink_func = network_log_sink,
    };
    
    es_log_add_sink(&network_sink);
}
```

### 7.3 日志查询示例

```c
// 查询最近的错误日志
void query_error_logs(void) {
    log_query_request_t request = {
        .start_time = es_get_timestamp() - 3600000, // 最近1小时
        .end_time = es_get_timestamp(),
        .min_level = ES_LOG_LEVEL_ERROR,
        .max_count = 50,
    };
    strcpy(request.keywords, "ERROR,CRITICAL,FAULT");
    
    log_entry_t entries[50];
    uint16_t count = 0;
    
    int ret = es_log_remote_query(&request, entries, &count);
    if (ret == 0) {
        ES_PRINTF_I("LOG", "Found %d error logs", count);
        for (int i = 0; i < count; i++) {
            // 处理日志条目
            process_log_entry(&entries[i]);
        }
    }
}
```

## 8. 配置选项

### 8.1 编译时配置

```c
// 日志缓冲区大小
#define ES_LOG_BUFFER_SIZE 512

// 最大日志标签长度
#define ES_LOG_MAX_TAG_LEN 16

// 日志存储扇区数量
#define ES_LOG_SECTOR_COUNT 2

// 每个扇区大小
#define ES_LOG_SECTOR_SIZE (32 * 1024)

// 启用远程日志查询
#define ES_LOG_ENABLE_REMOTE_QUERY 1

// 启用日志压缩
#define ES_LOG_ENABLE_COMPRESSION 0
```

### 8.2 运行时配置

```c
// 设置日志级别
es_log_set_level(ES_LOG_LEVEL_INFO);

// 启用/禁用日志
es_log_enable(true);

// 设置日志缓存大小
es_log_set_cache_size(1024);
```

## 9. 最佳实践

### 9.1 日志记录原则

1. **合适的级别**: 根据信息重要性选择合适的日志级别
2. **有意义的标签**: 使用清晰、一致的标签命名
3. **简洁的消息**: 日志消息应简洁明了，包含关键信息
4. **避免敏感信息**: 不要在日志中记录密码等敏感信息

### 9.2 性能优化

1. **级别过滤**: 在发布版本中设置合适的日志级别
2. **缓存机制**: 使用缓存减少Flash写入次数
3. **异步输出**: 使用异步方式输出日志避免阻塞
4. **压缩存储**: 对大量日志数据进行压缩存储

### 9.3 存储管理

1. **定期清理**: 定期清理旧日志释放存储空间
2. **重要日志**: 对重要日志进行备份和保护
3. **存储监控**: 监控存储使用情况防止溢出
4. **故障恢复**: 实现日志系统的故障恢复机制

## 10. 故障排除

### 10.1 常见问题

1. **日志丢失**: 检查存储空间和写入权限
2. **查询失败**: 检查关键字格式和查询参数
3. **性能问题**: 检查日志级别和输出频率
4. **存储损坏**: 检查Flash扇区状态和校验

### 10.2 调试方法

1. **日志统计**: 查看日志系统统计信息
2. **存储检查**: 检查环形文件系统状态
3. **性能监控**: 监控日志写入性能和延迟
4. **工具分析**: 使用专用工具分析日志文件

这个日志系统为 ES MCU Framework 提供了完整的日志解决方案，是系统调试、监控和维护的重要工具。
