/**
 * @file hc32_uart.c
 * @brief 基于HC32F460的UART驱动实现
 */

#include "es_drv_uart.h"
#include <string.h>
#include <stdint.h>
#include "hc32_ll_usart.h"
#include "hc32_ll_gpio.h"
#include "hc32_ll_fcg.h"
#include "hc32_ll_interrupts.h"
#include "es_ringbuffer.h"
#include "hc32_pin.h"

/************************ USART port **********************/

/* 设备常量配置结构体 */
typedef struct {
    CM_USART_TypeDef *uart;           /* UART外设 */
    uint32_t periph_clock;            /* 外设时钟 */
    const char *name;                 /* 设备名称 */
    
    /* GPIO配置 */
    uint8_t rx_port;
    uint16_t rx_pin;
    uint16_t rx_func;
    uint8_t tx_port;
    uint16_t tx_pin;
    uint16_t tx_func;
    
    /* 中断配置 */
    IRQn_Type rxerr_irq_num;
    uint32_t rxerr_irq_prio;
    uint32_t rxerr_int_src;
    
    IRQn_Type rx_irq_num;
    uint32_t rx_irq_prio;
    uint32_t rx_int_src;
    
    IRQn_Type tx_irq_num;
    uint32_t tx_irq_prio;
    
    /* 回调函数 */
    void (*rxerr_handler)(void);
    void (*rx_handler)(void);
} uart_device_config_t;

/* 设备运行时变量结构体 */
typedef struct {
    const uart_device_config_t *config;   /* 指向常量配置的指针 */
    
    /* 接收缓冲区 */
    es_ringbuffer_t *rx_ringbuffer;
    uint8_t *rx_buffer;
    size_t rx_buffer_size;

    /* 发送缓冲区 */
    uint16_t tx_index;
    uint16_t tx_size;
} uart_device_t;

static es_ringbuffer_t s_uart1_rx_ringbuffer;
static es_ringbuffer_t s_uart2_rx_ringbuffer;
static es_ringbuffer_t s_uart3_rx_ringbuffer;
static es_ringbuffer_t s_uart4_rx_ringbuffer;

static uint8_t uart1_rx_buffer[128] = {0};
static uint8_t uart2_rx_buffer[128] = {0};
static uint8_t uart3_rx_buffer[128] = {0};
static uint8_t uart4_rx_buffer[512] = {0};

static void hc32_uart_rxerr_irq_handler(CM_USART_TypeDef *USARTx) {
   if (SET == USART_GetStatus(USARTx, (USART_FLAG_OVERRUN | USART_FLAG_PARITY_ERR | USART_FLAG_FRAME_ERR)))
   {
       USART_ReadData(USARTx);
   }
   USART_ClearStatus(USARTx, (USART_FLAG_PARITY_ERR | USART_FLAG_FRAME_ERR | USART_FLAG_OVERRUN));
}

static void hc32_uart1_rxerr_irq_handler(void) {
    hc32_uart_rxerr_irq_handler(CM_USART1);
}

static void hc32_uart2_rxerr_irq_handler(void) {
    hc32_uart_rxerr_irq_handler(CM_USART2);
}

static void hc32_uart3_rxerr_irq_handler(void) {
    hc32_uart_rxerr_irq_handler(CM_USART3);
}

static void hc32_uart4_rxerr_irq_handler(void) {
    hc32_uart_rxerr_irq_handler(CM_USART4);
}

static void hc32_uart_rx_irq_handler(CM_USART_TypeDef *USARTx, es_ringbuffer_t *ringbuffer) {
    //disable interrupt
    __disable_irq();

    uint8_t data = USART_ReadData(USARTx);
    es_ringbuffer_write(ringbuffer, &data, 1);

    //enable interrupt
    __enable_irq();
}

static void hc32_uart1_rx_irq_handler(void) {
    hc32_uart_rx_irq_handler(CM_USART1, &s_uart1_rx_ringbuffer);
}

static void hc32_uart2_rx_irq_handler(void) {
    hc32_uart_rx_irq_handler(CM_USART2, &s_uart2_rx_ringbuffer);
}

static void hc32_uart3_rx_irq_handler(void) {
    hc32_uart_rx_irq_handler(CM_USART3, &s_uart3_rx_ringbuffer);
}

static void hc32_uart4_rx_irq_handler(void) {
    hc32_uart_rx_irq_handler(CM_USART4, &s_uart4_rx_ringbuffer);
}

static int uart_cfg(CM_USART_TypeDef *USARTx, const stc_usart_uart_init_t* uart_init) {
    #define UART_BAUDRATE_ERR_MAX           (0.025F)
    /* Configure UART */
    uint32_t u32Div;
    float32_t f32Error;
    int32_t i32Ret = LL_ERR;
    USART_DeInit(USARTx);
    USART_UART_Init(USARTx, uart_init, NULL);
    for (u32Div = 0UL; u32Div <= USART_CLK_DIV64; u32Div++)
    {
        USART_SetClockDiv(USARTx, u32Div);
        if ((LL_OK == USART_SetBaudrate(USARTx, uart_init->u32Baudrate, &f32Error)) &&
                ((-UART_BAUDRATE_ERR_MAX <= f32Error) && (f32Error <= UART_BAUDRATE_ERR_MAX)))
        {
            i32Ret = LL_OK;
            break;
        }
    }
    if (i32Ret != LL_OK)
    {
        return -1;
    }
    return 0;
}

/* 定义UART设备常量配置数组 */
static const uart_device_config_t uart_device_configs[] = {
    {
        .uart = CM_USART1,
        .periph_clock = FCG1_PERIPH_USART1,
        .name = "uart1",
        .rx_port = GPIO_PORT_C,
        .rx_pin = GPIO_PIN_04,
        .rx_func = GPIO_FUNC_33,
        .tx_port = GPIO_PORT_A,
        .tx_pin = GPIO_PIN_07,
        .tx_func = GPIO_FUNC_32,
        .rxerr_irq_num = INT010_IRQn,
        .rxerr_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rxerr_int_src = INT_SRC_USART1_EI,
        .rx_irq_num = INT083_IRQn,
        .rx_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rx_int_src = INT_SRC_USART1_RI,
        .tx_irq_num = INT082_IRQn,
        .tx_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rxerr_handler = hc32_uart1_rxerr_irq_handler,
        .rx_handler = hc32_uart1_rx_irq_handler,
    },
    {
        .uart = CM_USART2,
        .periph_clock = FCG1_PERIPH_USART2,
        .name = "uart2",
        .rx_port = GPIO_PORT_A,
        .rx_pin = GPIO_PIN_03,
        .rx_func = GPIO_FUNC_37,
        .tx_port = GPIO_PORT_A,
        .tx_pin = GPIO_PIN_02,
        .tx_func = GPIO_FUNC_36,
        .rxerr_irq_num = INT011_IRQn,
        .rxerr_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rxerr_int_src = INT_SRC_USART2_EI,
        .rx_irq_num = INT085_IRQn,
        .rx_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rx_int_src = INT_SRC_USART2_RI,
        .tx_irq_num = INT084_IRQn,
        .tx_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rxerr_handler = hc32_uart2_rxerr_irq_handler,
        .rx_handler = hc32_uart2_rx_irq_handler,
    },
    {
        .uart = CM_USART3,
        .periph_clock = FCG1_PERIPH_USART3,
        .name = "uart3",
        .rx_port = GPIO_PORT_E,
        .rx_pin = GPIO_PIN_05,
        .rx_func = GPIO_FUNC_33,
        .tx_port = GPIO_PORT_E,
        .tx_pin = GPIO_PIN_04,
        .tx_func = GPIO_FUNC_32,
        .rxerr_irq_num = INT012_IRQn,
        .rxerr_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rxerr_int_src = INT_SRC_USART3_EI,
        .rx_irq_num = INT089_IRQn,
        .rx_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rx_int_src = INT_SRC_USART3_RI,
        .tx_irq_num = INT088_IRQn,
        .tx_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rxerr_handler = hc32_uart3_rxerr_irq_handler,
        .rx_handler = hc32_uart3_rx_irq_handler,
    },
    {
        .uart = CM_USART4,
        .periph_clock = FCG1_PERIPH_USART4,
        .name = "uart4",
        .rx_port = GPIO_PORT_B,
        .rx_pin = GPIO_PIN_09,
        .rx_func = GPIO_FUNC_37,
        .tx_port = GPIO_PORT_E,
        .tx_pin = GPIO_PIN_06,
        .tx_func = GPIO_FUNC_36,
        .rxerr_irq_num = INT013_IRQn,
        .rxerr_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rxerr_int_src = INT_SRC_USART4_EI,
        .rx_irq_num = INT091_IRQn,
        .rx_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rx_int_src = INT_SRC_USART4_RI,
        .tx_irq_num = INT090_IRQn,
        .tx_irq_prio = DDL_IRQ_PRIO_DEFAULT,
        .rxerr_handler = hc32_uart4_rxerr_irq_handler,
        .rx_handler = hc32_uart4_rx_irq_handler,
    },
};

/* 定义UART设备运行时变量数组 */
static uart_device_t uart_devices[] = {
    {
        .config = &uart_device_configs[0],
        .rx_ringbuffer = &s_uart1_rx_ringbuffer,
        .rx_buffer = uart1_rx_buffer,
        .rx_buffer_size = sizeof(uart1_rx_buffer),
        .tx_index = 0,
        .tx_size = 0,
    },
    {
        .config = &uart_device_configs[1],
        .rx_ringbuffer = &s_uart2_rx_ringbuffer,
        .rx_buffer = uart2_rx_buffer,
        .rx_buffer_size = sizeof(uart2_rx_buffer),
        .tx_index = 0,
        .tx_size = 0,
    },
    {
        .config = &uart_device_configs[2],
        .rx_ringbuffer = &s_uart3_rx_ringbuffer,
        .rx_buffer = uart3_rx_buffer,
        .rx_buffer_size = sizeof(uart3_rx_buffer),
        .tx_index = 0,
        .tx_size = 0,
    },
    {
        .config = &uart_device_configs[3],
        .rx_ringbuffer = &s_uart4_rx_ringbuffer,
        .rx_buffer = uart4_rx_buffer,
        .rx_buffer_size = sizeof(uart4_rx_buffer),
        .tx_index = 0,
        .tx_size = 0,
    },
};

#define UART_DEVICE_COUNT (sizeof(uart_devices) / sizeof(uart_devices[0]))

static void uart_init_all(void) {
    stc_usart_uart_init_t uart_init;
    struct hc32_irq_config irq_config;
    
    USART_UART_StructInit(&uart_init);
    uart_init.u32OverSampleBit = USART_OVER_SAMPLE_8BIT;
    uart_init.u32Baudrate = 115200;
    uart_init.u32ClockSrc = USART_CLK_SRC_INTERNCLK;
    uart_init.u32CKOutput = USART_CK_OUTPUT_ENABLE;
    uart_init.u32DataWidth = USART_DATA_WIDTH_8BIT;
    uart_init.u32StopBit = USART_STOPBIT_1BIT;
    uart_init.u32Parity = USART_PARITY_NONE;
    
    /* 初始化所有UART设备 */
    for (size_t i = 0; i < UART_DEVICE_COUNT; i++) {
        const uart_device_config_t *config = uart_devices[i].config;
        
        /* 配置GPIO */
        GPIO_SetFunc(config->rx_port, config->rx_pin, config->rx_func);
        GPIO_SetFunc(config->tx_port, config->tx_pin, config->tx_func);
        
        /* 开启外设时钟 */
        FCG_Fcg1PeriphClockCmd(config->periph_clock, ENABLE);
        
        /* 配置UART */
        uart_cfg(config->uart, &uart_init);
        
        /* 使能UART功能 */
        USART_FuncCmd(config->uart, (USART_RX | USART_INT_RX | USART_TX), ENABLE);
        
        /* 初始化接收缓冲区 */
        es_ringbuffer_init(uart_devices[i].rx_ringbuffer, uart_devices[i].rx_buffer, uart_devices[i].rx_buffer_size);
        
        /* 配置错误中断 */
        irq_config.irq_num = config->rxerr_irq_num;
        irq_config.irq_prio = config->rxerr_irq_prio;
        irq_config.int_src = config->rxerr_int_src;
        hc32_install_irq_handler(&irq_config, config->rxerr_handler, true);
        
        /* 配置接收中断 */
        irq_config.irq_num = config->rx_irq_num;
        irq_config.irq_prio = config->rx_irq_prio;
        irq_config.int_src = config->rx_int_src;
        hc32_install_irq_handler(&irq_config, config->rx_handler, true);
    }
}

static bool s_uart_init_flag = false;

/**
 * @brief 打开UART设备
 * @param name 设备名称
 * @param config UART配置
 * @return UART设备句柄，NULL表示失败
 */
es_uart_dev_t es_uart_open(const es_uart_config_t *config)
{
    if (!s_uart_init_flag) {
        uart_init_all();
        s_uart_init_flag = true;
    }

    /* 查找对应的UART设备 */
    for (size_t i = 0; i < UART_DEVICE_COUNT; i++) {
        if (strcmp(config->name, uart_devices[i].config->name) == 0) {
            return (es_uart_dev_t)&uart_devices[i];
        }
    }
    
    return NULL;
}

/**
 * @brief 关闭UART设备
 * @param dev UART设备句柄
 * @return 0成功，-1失败
 */
int es_uart_close(es_uart_dev_t dev)
{
    return 0;
}

/**
 * @brief 读取数据
 * @param dev UART设备句柄
 * @param buf 数据缓冲区
 * @param size 要读取的字节数
 * @return 实际读取的字节数，-1表示失败
 */
int es_uart_read(es_uart_dev_t dev, void *buf, size_t size)
{
    if (dev == NULL) {
        return -1;
    }

    uart_device_t *uart_dev = (uart_device_t *)dev;

    //check if ringbuffer is empty
    if (es_ringbuffer_is_empty(uart_dev->rx_ringbuffer)) {
        return 0;
    }
    
    __disable_irq();    
    int ret = es_ringbuffer_read(uart_dev->rx_ringbuffer, buf, size);
    __enable_irq();
    return ret;
}

/**
 * @brief 写入数据
 * @param dev UART设备句柄
 * @param buf 数据缓冲区
 * @param size 要写入的字节数
 * @return 实际写入的字节数，-1表示失败
 */
int es_uart_write(es_uart_dev_t dev, const void *buf, size_t size)
{
    if (dev == NULL) {
        return -1;
    }

    uart_device_t *uart_dev = (uart_device_t *)dev;
    CM_USART_TypeDef *uart = uart_dev->config->uart;

    const uint8_t *pbuf = (const uint8_t *)buf;
    size_t count = 0;
    uint32_t timeout;
    
    /* 循环发送数据 */
    while (count < size) {
        /* 等待发送寄存器为空 */
        timeout = 10000; /* 超时时间，可以根据需要调整 */
        while (USART_GetStatus(uart, USART_FLAG_TX_EMPTY) != SET) {
            if (--timeout == 0) {
                return (count > 0) ? (int)count : -1;
            }
        }
        
        /* 发送数据 */
        USART_WriteData(uart, pbuf[count++]);
    }
    
    /* 等待所有数据发送完成 */
    timeout = 10000;
    while (USART_GetStatus(uart, USART_FLAG_TX_CPLT) != SET) {
        if (--timeout == 0) {
            break;
        }
    }
    
    /* 清除传输完成标志 */
    USART_ClearStatus(uart, USART_FLAG_TX_CPLT);
    
    /* 返回实际写入的字节数 */
    return (int)count;
}

es_async_t es_uart_coro_write(es_coro_t *coro, es_uart_dev_t dev, const uint8_t *buf, int size) {
    uart_device_t *uart_dev = (uart_device_t *)dev;
    CM_USART_TypeDef *uart = uart_dev->config->uart;

    es_co_begin(coro);

    //wait until tx buffer is empty
    es_co_wait_timeout(uart_dev->tx_index == 0, 2000);
    
    uart_dev->tx_index = 0;
    uart_dev->tx_size = size;

    while (uart_dev->tx_index < uart_dev->tx_size) {
        es_co_wait_timeout(USART_GetStatus(uart, USART_FLAG_TX_EMPTY) == SET, 1000);
        USART_WriteData(uart, buf[uart_dev->tx_index++]);
    }

    es_co_wait_timeout(USART_GetStatus(uart, USART_FLAG_TX_CPLT) == SET, 1000);

    //clear tx complete flag
    USART_ClearStatus(uart, USART_FLAG_TX_CPLT);

    uart_dev->tx_index = 0;
    uart_dev->tx_size = 0;

    es_co_end;
}

/**
 * @brief 检查是否有可读数据
 * @param dev UART设备句柄
 * @param timeout_ms 超时时间(毫秒)，0表示不等待
 * @return 可用字节数，0表示无数据，-1表示失败
 */
int es_uart_available(es_uart_dev_t dev, uint32_t timeout_ms)
{
    if (dev == NULL) {
        return -1;
    }

    return es_ringbuffer_available(((uart_device_t *)dev)->rx_ringbuffer);
}

/**
 * @brief 清空UART缓冲区
 * @param dev UART设备句柄
 * @return 0成功，-1失败
 */
int es_uart_flush(es_uart_dev_t dev)
{
    if (dev == NULL) {
        return -1;
    }

    uart_device_t *uart_dev = (uart_device_t *)dev;
    CM_USART_TypeDef *uart = uart_dev->config->uart;
    uint32_t timeout = 10000; /* 超时时间，可以根据需要调整 */
    
    /* 等待所有数据发送完成 */
    while (USART_GetStatus(uart, USART_FLAG_TX_CPLT) != SET) {
        if (--timeout == 0) {
            return -1;
        }
    }
    
    /* 清除所有状态标志 */
    USART_ClearStatus(uart, USART_FLAG_ALL);
    
    /* 清空接收缓冲区：读出所有可能的数据 */
    while (USART_GetStatus(uart, USART_FLAG_RX_FULL) == SET) {
        (void)USART_ReadData(uart);
    }
    
    return 0;
}
