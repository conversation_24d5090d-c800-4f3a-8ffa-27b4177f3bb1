# C语言子集到VM字节码编译器

这是一个用Python编写的编译器，可以将C语言的子集编译成VM支持的字节码。

## 功能特性

### 支持的C语言特性

- **基本数据类型**: `int` (32位整数)
- **寄存器变量**: `register int` (映射到VM的8个通用寄存器)
- **算术运算**: `+`, `-`, `*`, `/`, `%`
- **位运算**: `&`, `|`, `^`, `~`, `<<`, `>>`
- **比较运算**: `==`, `!=`, `<`, `<=`, `>`, `>=`
- **控制流**: `if-else`, `while`
- **表达式**: 支持运算符优先级和括号
- **变量**: 声明、初始化和赋值
- **函数**: `main`函数和`return`语句

### VM特性

- **8个32位通用寄存器** (R0-R7)
- **栈式计算架构**
- **条件跳转和循环**
- **完整的算术和逻辑运算**
- **新增的寄存器指令**:
  - `VM_OP_STORE_REG (0x42)`: 存储寄存器值
  - `VM_OP_LOAD_REG (0x43)`: 加载寄存器值

## 文件说明

- `c_to_vm_compiler.py`: 主编译器实现
- `compiler_examples.py`: 编译器示例和测试
- `test_vm_registers.c`: VM寄存器功能测试
- `test_compiled_code.c`: 编译后字节码执行测试

## 使用方法

### 基本编译

```python
from c_to_vm_compiler import Compiler

# C代码
c_code = """
int main() {
    register int a = 10;
    register int b = 20;
    register int result = a + b;
    return result;
}
"""

# 编译
compiler = Compiler()
bytecode = compiler.compile(c_code)

# 保存字节码
compiler.save_bytecode(bytecode, "program.bin")

# 打印字节码
compiler.print_bytecode_hex(bytecode)
```

### 从文件编译

```python
compiler = Compiler()
bytecode = compiler.compile_file("program.c")
compiler.save_bytecode(bytecode, "program.bin")
```

## 示例程序

### 1. 基本算术运算

```c
int main() {
    register int a = 15;
    register int b = 7;
    register int sum = a + b;
    register int diff = a - b;
    register int prod = a * b;
    return sum + diff + prod;
}
```

### 2. 位运算

```c
int main() {
    register int a = 0xF0;
    register int b = 0x0F;
    register int and_result = a & b;
    register int or_result = a | b;
    register int xor_result = a ^ b;
    return and_result + or_result + xor_result;
}
```

### 3. 条件判断

```c
int main() {
    register int x = 25;
    register int y = 30;
    register int result = 0;
    
    if (x > y) {
        result = x - y;
    } else {
        result = y - x;
    }
    
    return result;
}
```

### 4. 循环计算

```c
int main() {
    register int i = 0;
    register int sum = 0;
    
    while (i < 10) {
        sum = sum + i;
        i = i + 1;
    }
    
    return sum;
}
```

### 5. 阶乘计算

```c
int main() {
    register int n = 5;
    register int result = 1;
    register int i = 1;
    
    while (i <= n) {
        result = result * i;
        i = i + 1;
    }
    
    return result;  // 返回 120 (5!)
}
```

## 编译器架构

### 1. 词法分析器 (Lexer)

- 将源代码转换为Token流
- 支持关键字、标识符、数字、运算符等
- 支持十六进制数字 (0x...)

### 2. 语法分析器 (Parser)

- 递归下降解析器
- 构建抽象语法树 (AST)
- 支持运算符优先级

### 3. 代码生成器 (CodeGenerator)

- 遍历AST生成VM字节码
- 寄存器分配和管理
- 跳转地址回填

## VM指令集

编译器生成的字节码使用以下VM指令：

| 指令 | 操作码 | 描述 |
|------|--------|------|
| PUSH8 | 0x01 | 压入8位立即数 |
| PUSH16 | 0x02 | 压入16位立即数 |
| PUSH32 | 0x03 | 压入32位立即数 |
| ADD | 0x10 | 加法 |
| SUB | 0x11 | 减法 |
| MUL | 0x12 | 乘法 |
| DIV | 0x13 | 除法 |
| MOD | 0x14 | 取模 |
| AND | 0x15 | 按位与 |
| OR | 0x16 | 按位或 |
| XOR | 0x17 | 按位异或 |
| NOT | 0x18 | 按位取反 |
| SHL | 0x19 | 左移 |
| SHR | 0x1A | 右移 |
| EQ | 0x20 | 等于 |
| NE | 0x21 | 不等于 |
| LT | 0x22 | 小于 |
| LE | 0x23 | 小于等于 |
| GT | 0x24 | 大于 |
| GE | 0x25 | 大于等于 |
| JMP | 0x30 | 无条件跳转 |
| JZ | 0x31 | 零跳转 |
| JNZ | 0x32 | 非零跳转 |
| STORE_REG | 0x42 | 存储寄存器值 |
| LOAD_REG | 0x43 | 加载寄存器值 |
| RET | 0x41 | 返回 |
| HALT | 0xFF | 停机 |

## 运行测试

```bash
# 运行编译器示例
python applications/compiler_examples.py

# 编译并测试VM寄存器功能
gcc applications/test_vm_registers.c -o test_vm_registers
./test_vm_registers

# 编译并测试生成的字节码
gcc applications/test_compiled_code.c -o test_compiled_code
./test_compiled_code
```

## 限制和注意事项

1. **只支持寄存器变量**: 普通变量需要内存管理，暂未实现
2. **最多8个寄存器**: 受VM硬件限制
3. **只支持int类型**: 暂不支持其他数据类型
4. **不支持函数调用**: 只支持main函数
5. **不支持数组和指针**: 简化实现
6. **不支持复杂控制流**: 如for循环、switch等

## 扩展建议

1. 添加更多数据类型支持
2. 实现函数调用和参数传递
3. 添加数组和指针支持
4. 实现更复杂的控制流结构
5. 添加优化pass
6. 改进错误报告和调试信息

这个编译器为VM提供了一个完整的C语言子集编程环境，可以用于编写和测试各种算法和逻辑。
