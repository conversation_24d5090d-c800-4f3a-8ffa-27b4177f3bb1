﻿/**
 * @file es_fal_cfg.h
 * @brief ES Flash抽象层配置文件
 */
#ifndef ES_FAL_CFG_H
#define ES_FAL_CFG_H

#include "es_flash.h"

#ifdef __cplusplus
extern "C" {
#endif

extern const es_flash_dev_t file_flash_dev;

#define ES_FAL_FLASH_DEV_TABLE { &file_flash_dev }

#define ES_FAL_PARTITION_TABLE  \
{ \
    { "log", &file_flash_dev, 0, 1024 * 1024 }, \
    { "store", &file_flash_dev, 1024 * 1024, 3 * 4096 }, \
    { "blind", &file_flash_dev, 1024 * 1024 + 3 * 4096, 320 * 1024 }, \
    { "download", &file_flash_dev, 1024 * 1024 + 3 * 4096 + 320 * 1024, 1024 * 1024 }, \
}

#ifdef __cplusplus
}
#endif

#endif /* ES_FAL_CFG_H */
