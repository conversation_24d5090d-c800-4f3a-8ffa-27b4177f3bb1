/**
 * @file es_ringobj.c
 * @brief Struct ring buffer module implementation
 * @date 2024/10/1
 */

#include "es_ringobj.h"
#include "es_drv_os.h"


/**
 * @brief Check if a number is a power of 2
 */
static bool is_power_of_two(uint32_t n)
{
    return (n != 0) && ((n & (n - 1)) == 0);
}

/**
 * @brief Initialize struct ring buffer
 */
int es_ringobj_init(es_ringobj_t *rb, void *buffer, uint16_t element_size, uint16_t capacity)
{
    if (rb == NULL || buffer == NULL || element_size == 0 || capacity == 0) {
        return -1;
    }
    
    /* Check if capacity is a power of 2 */
    if (!is_power_of_two(capacity)) {
        return -1;
    }
    
    rb->buffer = buffer;
    rb->element_size = element_size;
    rb->capacity = capacity;
    rb->count = 0;
    rb->head = 0;
    rb->tail = 0;
    rb->mask = capacity - 1;
    
    return 0;
}

/**
 * @brief Reset struct ring buffer
 */
int es_ringobj_reset(es_ringobj_t *rb)
{
    if (rb == NULL) {
        return -1;
    }
    
    rb->count = 0;
    rb->head = 0;
    rb->tail = 0;
    
    return 0;
}

/**
 * @brief Write an element to struct ring buffer
 * @note Interrupt-safe version: uses memory barriers to ensure operation order
 */
int es_ringobj_put(es_ringobj_t *rb, const void *element)
{
    if (rb == NULL || element == NULL) {
        return 0;
    }

    /* Read current tail pointer (volatile ensures reading from memory each time) */
    uint16_t current_tail = rb->tail;
    uint16_t next_tail = (current_tail + 1) & rb->mask;

    /* Check if buffer is full - use head pointer comparison, avoid dependency on count */
    if (next_tail == rb->head) {
        return 0;  /* Buffer is full */
    }

    /* Calculate write position and copy data */
    uint8_t *dest = (uint8_t *)rb->buffer + (current_tail * rb->element_size);
    memcpy(dest, element, rb->element_size);

    /* Memory barrier: ensure data write completion before updating pointer */
    ES_MEMORY_BARRIER();

    /* Update tail pointer (atomic operation) */
    rb->tail = next_tail;

    /* Memory barrier: ensure tail update before updating count */
    ES_MEMORY_BARRIER();

    /* Update count */
    rb->count++;

    return 1;  /* Successfully written */
}

/**
 * @brief Force write an element, overwrite oldest element if buffer is full
 * @note Interrupt-safe version: uses memory barriers to ensure operation order
 */
int es_ringobj_put_force(es_ringobj_t *rb, const void *element)
{
    if (rb == NULL || element == NULL) {
        return 0;
    }

    /* Read current tail pointer */
    uint16_t current_tail = rb->tail;
    uint16_t next_tail = (current_tail + 1) & rb->mask;

    /* Calculate write position and copy data */
    uint8_t *dest = (uint8_t *)rb->buffer + (current_tail * rb->element_size);
    memcpy(dest, element, rb->element_size);

    /* Memory barrier: ensure data write completion before updating pointer */
    ES_MEMORY_BARRIER();

    /* Update tail pointer */
    rb->tail = next_tail;

    /* Memory barrier: ensure tail update before checking and updating other fields */
    ES_MEMORY_BARRIER();

    if (next_tail == rb->head) {
        /* Buffer is full, overwrite oldest element, move head pointer */
        rb->head = (rb->head + 1) & rb->mask;
    } else {
        /* Buffer not full, increase count */
        rb->count++;
    }

    return 1;  /* Successfully written */
}

/**
 * @brief Read an element from struct ring buffer
 * @note Interrupt-safe version: uses memory barriers to ensure operation order
 */
int es_ringobj_get(es_ringobj_t *rb, void *element)
{
    if (rb == NULL || element == NULL) {
        return 0;
    }

    /* Read current head pointer */
    uint16_t current_head = rb->head;

    /* Check if buffer is empty - use head and tail pointer comparison, avoid dependency on count */
    if (current_head == rb->tail) {
        return 0;  /* Buffer is empty */
    }

    /* Calculate read position and copy data */
    uint8_t *src = (uint8_t *)rb->buffer + (current_head * rb->element_size);
    memcpy(element, src, rb->element_size);

    /* Memory barrier: ensure data read completion before updating pointer */
    ES_MEMORY_BARRIER();

    /* Update head pointer */
    rb->head = (current_head + 1) & rb->mask;

    /* Memory barrier: ensure head update before updating count */
    ES_MEMORY_BARRIER();

    /* Update count */
    rb->count--;

    return 1;  /* Successfully read */
}

/**
 * @brief Peek at top element of ring buffer without removing it
 */
int es_ringobj_peek(es_ringobj_t *rb, void *element)
{
    if (rb == NULL || element == NULL) {
        return 0;
    }
    
    /* Check if buffer is empty */
    if (rb->count == 0) {
        return 0;  /* Buffer is empty */
    }
    
    /* Calculate read position and copy data */
    uint8_t *src = (uint8_t *)rb->buffer + (rb->head * rb->element_size);
    memcpy(element, src, rb->element_size);
    
    return 1;  /* Successfully peeked */
}

/**
 * @brief Get element count in ring buffer
 */
uint16_t es_ringobj_count(es_ringobj_t *rb)
{
    if (rb == NULL) {
        return 0;
    }
    
    return rb->count;
}

/**
 * @brief Get remaining capacity of ring buffer
 */
uint16_t es_ringobj_free_count(es_ringobj_t *rb)
{
    if (rb == NULL) {
        return 0;
    }
    
    return rb->capacity - rb->count;
}

/**
 * @brief Check if struct ring buffer is empty
 */
bool es_ringobj_is_empty(es_ringobj_t *rb)
{
    if (rb == NULL) {
        return true;
    }
    
    return (rb->count == 0);
}

/**
 * @brief Check if struct ring buffer is full
 */
bool es_ringobj_is_full(es_ringobj_t *rb)
{
    if (rb == NULL) {
        return false;
    }
    
    return (rb->count >= rb->capacity);
}
