/**
 * @file mem_at_example.c
 * @brief AT+MEM 指令使用示例
 * <AUTHOR> Assistant
 * @date 2025/1/24
 */

#include "es_mem.h"
#include "es_at_srv.h"
#include "es_log.h"

#define TAG "MEM_AT_EXAMPLE"

/**
 * @brief 初始化内存管理和AT服务示例
 */
int mem_at_example_init(void)
{
    int ret;
    
    // 1. 初始化内存管理模块
    ret = es_mem_v2_init();
    if (ret != 0) {
        ES_PRINTF_I(TAG, "Failed to initialize memory manager: %d", ret);
        return -1;
    }
    ES_PRINTF_I(TAG, "Memory manager initialized successfully");
    
    // 2. 初始化AT服务
    ret = es_at_srv_init();
    if (ret != 0) {
        ES_PRINTF_I(TAG, "Failed to initialize AT service: %d", ret);
        return -1;
    }
    ES_PRINTF_I(TAG, "AT service initialized successfully");
    
    // 3. MEM指令已经通过DEMO_AT_SRV_ITEMS自动注册
    // 如果需要动态注册，可以使用以下代码：
    /*
    es_at_cmd_item_t mem_cmd = {
        .cmd_name = "MEM",
        .handler = at_srv_cmd_mem_handler
    };
    ret = es_at_srv_register_user_cmd(&mem_cmd);
    if (ret != 0) {
        ES_PRINTF_I(TAG, "Failed to register MEM command: %d", ret);
        return -1;
    }
    */
    
    ES_PRINTF_I(TAG, "MEM AT command is ready for use");
    
    return 0;
}

/**
 * @brief 内存测试示例函数
 */
void mem_test_example(void)
{
    ES_PRINTF_I(TAG, "=== Memory Test Example ===");
    
    // 显示初始内存状态
    es_mem_v2_dump_stats();
    
    // 分配一些测试内存
    void *ptr1 = es_mem_v2_alloc(64);
    void *ptr2 = es_mem_v2_alloc(128);
    void *ptr3 = es_mem_v2_alloc(256);
    
    ES_PRINTF_I(TAG, "Allocated test memory:");
    ES_PRINTF_I(TAG, "  ptr1 (64 bytes): %p", ptr1);
    ES_PRINTF_I(TAG, "  ptr2 (128 bytes): %p", ptr2);
    ES_PRINTF_I(TAG, "  ptr3 (256 bytes): %p", ptr3);
    
    // 显示分配后的内存状态
    es_mem_v2_dump_stats();
    
    // 释放中间的内存块，制造碎片
    if (ptr2) {
        es_mem_v2_free(ptr2);
        ES_PRINTF_I(TAG, "Freed ptr2 to create fragmentation");
    }
    
    // 显示碎片化后的状态
    es_mem_v2_dump_pools();
    
    // 进行碎片整理
    int merged = es_mem_v2_defragment();
    ES_PRINTF_I(TAG, "Defragmentation merged %d blocks", merged);
    
    // 检查内存完整性
    int integrity = es_mem_v2_verify_integrity();
    ES_PRINTF_I(TAG, "Memory integrity check: %s", 
                integrity == 0 ? "PASSED" : "FAILED");
    
    // 清理剩余内存
    if (ptr1) es_mem_v2_free(ptr1);
    if (ptr3) es_mem_v2_free(ptr3);
    
    // 检查内存泄漏
    int leaks = es_mem_v2_check_leaks();
    ES_PRINTF_I(TAG, "Memory leak check: %d leaks found", leaks);
    
    ES_PRINTF_I(TAG, "=== Memory Test Complete ===");
}

/**
 * @brief 模拟AT指令测试
 */
void simulate_at_commands(void)
{
    ES_PRINTF_I(TAG, "=== Simulating AT Commands ===");
    ES_PRINTF_I(TAG, "You can now use the following AT commands:");
    ES_PRINTF_I(TAG, "  AT+MEM?                       - Query memory status");
    ES_PRINTF_I(TAG, "  AT+MEM=STRESS,100,256        - Stress test: 100 cycles, 256 bytes each");
    ES_PRINTF_I(TAG, "  AT+MEM=STRESS,50,1024        - Stress test: 50 cycles, 1KB each");
    ES_PRINTF_I(TAG, "  AT+MEM=STATS                 - Show detailed statistics");
    ES_PRINTF_I(TAG, "  AT+MEM=DUMP                  - Show memory pool status");
    ES_PRINTF_I(TAG, "  AT+MEM=CHECK                 - Check memory integrity");
    ES_PRINTF_I(TAG, "  AT+MEM=DEFRAG                - Defragment memory");
    ES_PRINTF_I(TAG, "  AT+MEM=LEAKS                 - Check for memory leaks");
}

/**
 * @brief 主函数示例
 */
int main(void)
{
    // 初始化系统
    if (mem_at_example_init() != 0) {
        return -1;
    }
    
    // 运行内存测试示例
    mem_test_example();
    
    // 显示AT指令使用说明
    simulate_at_commands();
    
    // 在实际应用中，这里会进入主循环
    // 处理AT指令和其他系统任务
    
    return 0;
}
