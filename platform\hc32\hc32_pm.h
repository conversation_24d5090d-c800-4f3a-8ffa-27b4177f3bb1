#ifndef _HC32_PM_H_
#define _HC32_PM_H_

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

#define XPM_WKE_ITEMS                                                                 \
    X(ACC, GET_PIN(A, 0), PIN_MODE_INPUT, PIN_IRQ_MODE_RISING_FALLING, PIN_LOW)       \
    X(MAIN_PWR, GET_PIN(E, 3), PIN_MODE_INPUT, PIN_IRQ_MODE_RISING_FALLING, PIN_HIGH) \
    X(BLE, GET_PIN(C, 4), PIN_MODE_INPUT_PULLUP, PIN_IRQ_MODE_FALLING, PIN_HIGH)     \
    X(SENSOR, GET_PIN(A, 12), PIN_MODE_INPUT_PULLDOWN, PIN_IRQ_MODE_RISING, PIN_HIGH) \
    X(MDM, GET_PIN(A, 11), PIN_MODE_INPUT, PIN_IRQ_MODE_FALLING, PIN_HIGH)            \
    X(SMK, GET_PIN(B, 1), PIN_MODE_INPUT, PIN_IRQ_MODE_FALLING, PIN_HIGH)            \

// name, pin, mode, value, active, init
#define XPM_EN_ITEMS                                                  \
    X(ACC, GET_PIN(E, 2), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(LED, GET_PIN(B, 4), PIN_MODE_OUTPUT, PIN_LOW, 0) \
    X(BLE, GET_PIN(C, 7), PIN_MODE_OUTPUT, PIN_HIGH, 1) \
    X(BLE_NRST, GET_PIN(D, 7), PIN_MODE_OUTPUT, PIN_LOW, 1) \
    X(SHIP, GET_PIN(B, 12), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(ADC_MAINPWR, GET_PIN(D, 4), PIN_MODE_OUTPUT, PIN_HIGH, 1) \
    X(OUT_DEV_3V3, GET_PIN(E, 13), PIN_MODE_OUTPUT, PIN_LOW, 0) \
    X(OUT_DEV_5V, GET_PIN(E, 12), PIN_MODE_OUTPUT, PIN_HIGH, 1) \
    X(OUT_MDM_PWRKEY, GET_PIN(D, 15), PIN_MODE_OUTPUT, PIN_HIGH, 1) \
    X(OUT_MDM_EN, GET_PIN(E, 14), PIN_MODE_OUTPUT, PIN_HIGH, 1) \
    X(OUT_MDM_DTR, GET_PIN(D, 14), PIN_MODE_OUTPUT, PIN_LOW, 0) \
    X(OUT_CHARGE, GET_PIN(A, 15), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(OUT_VEH_3V3, GET_PIN(E, 15), PIN_MODE_OUTPUT, PIN_HIGH, 1) \
    X(OUT_SENSOR_PWR, GET_PIN(D, 13), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(LOCK_EN, GET_PIN(B, 15), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(BUCKET_LOCK_EN, GET_PIN(B, 13), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(FUEL_TANK_LOCK_EN, GET_PIN(H, 2), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(YIBIAO_LAMP_EN, GET_PIN(E, 11), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(LOCK_LAMP, GET_PIN(E, 10), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(LOCK_ANTI_THEFT_LAMP, GET_PIN(B, 15), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(KLINE, GET_PIN(D, 3), PIN_MODE_OUTPUT, PIN_HIGH, 1) \
    X(TAPLOCKA, GET_PIN(D, 11), PIN_MODE_OUTPUT, PIN_HIGH, 0) \
    X(TAPLOCKB, GET_PIN(D, 12), PIN_MODE_OUTPUT, PIN_HIGH, 0)


typedef struct pm_wke_cfg_t {
    int16_t pin;
    uint8_t mode : 4;
    uint8_t active_level : 1;
    uint8_t isr_mode : 3;
    void (*isr)(void *args);
} pm_wke_cfg_t;

typedef struct pm_en_cfg_t {
    int16_t pin;
    uint8_t mode : 4;
    uint8_t active_level : 1;
    uint8_t init_level : 1;
} pm_en_cfg_t;

enum pm_wke_type_t {
#define X(NAME, PIN, MODE, ISR_MODE, ACTIVE_LEVEL) PM_WKE_##NAME,
    XPM_WKE_ITEMS
#undef X
    PM_WKE_MAX,
};

enum pm_en_type_t {
#define X(NAME, PIN, MODE, ACTIVE_LEVEL, INIT_LEVEL) PM_EN_##NAME,
    XPM_EN_ITEMS
#undef X
    PM_EN_MAX,
};

int hc32_pm_init(void);

int hc32_pm_wke_enable(int wke_pin_type, int enable);

int hc32_pm_en_enable(int en_pin_type, int enable);

#ifdef __cplusplus
}
#endif

#endif