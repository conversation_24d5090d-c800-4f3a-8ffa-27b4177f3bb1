/**
 * @file test_mem_v2.c
 * @brief 内存分配器V2单元测试
 * <AUTHOR>
 * @date 2025/1/14
 * @copyright Copyright (c) 2025
 */

#include "es_mem_v2.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

#define TAG "TEST_MEM_V2"

// 测试统计
static int test_passed = 0;
static int test_failed = 0;

// 测试宏
#define TEST_ASSERT(condition, message) \
    do { \
        if (condition) { \
            test_passed++; \
            printf("✓ PASS: %s\n", message); \
        } else { \
            test_failed++; \
            printf("✗ FAIL: %s\n", message); \
        } \
    } while(0)

#define TEST_ASSERT_NULL(ptr, message) TEST_ASSERT((ptr) == NULL, message)
#define TEST_ASSERT_NOT_NULL(ptr, message) TEST_ASSERT((ptr) != NULL, message)
#define TEST_ASSERT_EQUAL(expected, actual, message) \
    TEST_ASSERT((expected) == (actual), message)

// 测试函数声明
static void test_init_deinit(void);
static void test_basic_alloc_free(void);
static void test_calloc(void);
static void test_realloc(void);
static void test_aligned_alloc(void);
static void test_stats(void);
static void test_debug_features(void);
static void test_edge_cases(void);
static void test_memory_pools(void);

int main(void) {
    printf("=== Memory Allocator V2 Tests ===\n");
    
    // 运行测试
    test_init_deinit();
    test_basic_alloc_free();
    test_calloc();
    test_realloc();
    test_aligned_alloc();
    test_stats();
    test_debug_features();
    test_edge_cases();
    test_memory_pools();
    
    // 测试结果
    printf("\n=== Test Results ===\n");
    printf("Passed: %d\n", test_passed);
    printf("Failed: %d\n", test_failed);
    
    if (test_failed == 0) {
        printf("🎉 All tests passed!\n");
        return 0;
    } else {
        printf("❌ Some tests failed!\n");
        return -1;
    }
}

static void test_init_deinit(void) {
    printf("\n--- Testing Init/Deinit ---\n");
    
    // 测试默认初始化
    int result = es_mem_v2_init(NULL);
    TEST_ASSERT_EQUAL(0, result, "Default init should succeed");
    
    // 测试重复初始化
    result = es_mem_v2_init(NULL);
    TEST_ASSERT_EQUAL(0, result, "Repeated init should succeed");
    
    result = es_mem_v2_deinit();
    TEST_ASSERT_EQUAL(0, result, "Deinit should succeed");
    
    // 测试自定义配置
    es_mem_config_t config = {
        .enable_debug = true,
        .enable_stats = true,
        .total_heap_size = 32768
    };
    
    result = es_mem_v2_init(&config);
    TEST_ASSERT_EQUAL(0, result, "Custom config init should succeed");
    
    es_mem_v2_deinit();
}

static void test_basic_alloc_free(void) {
    printf("\n--- Testing Basic Alloc/Free ---\n");
    
    es_mem_v2_init(NULL);
    
    // 测试基本分配
    void *ptr1 = es_mem_v2_alloc(16);
    TEST_ASSERT_NOT_NULL(ptr1, "Alloc 16 bytes should succeed");
    
    void *ptr2 = es_mem_v2_alloc(64);
    TEST_ASSERT_NOT_NULL(ptr2, "Alloc 64 bytes should succeed");
    
    void *ptr3 = es_mem_v2_alloc(256);
    TEST_ASSERT_NOT_NULL(ptr3, "Alloc 256 bytes should succeed");
    
    // 测试数据读写
    if (ptr1) memset(ptr1, 0xAA, 16);
    if (ptr2) memset(ptr2, 0xBB, 64);
    if (ptr3) memset(ptr3, 0xCC, 256);
    
    if (ptr1) {
        uint8_t *data1 = (uint8_t *)ptr1;
        TEST_ASSERT_EQUAL(0xAA, data1[0], "Data should be preserved in ptr1");
    }
    if (ptr2) {
        uint8_t *data2 = (uint8_t *)ptr2;
        TEST_ASSERT_EQUAL(0xBB, data2[0], "Data should be preserved in ptr2");
    }
    if (ptr3) {
        uint8_t *data3 = (uint8_t *)ptr3;
        TEST_ASSERT_EQUAL(0xCC, data3[0], "Data should be preserved in ptr3");
    }
    
    // 测试释放
    if (ptr1) es_mem_v2_free(ptr1);
    if (ptr2) es_mem_v2_free(ptr2);
    if (ptr3) es_mem_v2_free(ptr3);
    
    // 测试无效分配
    void *ptr_zero = es_mem_v2_alloc(0);
    TEST_ASSERT_NULL(ptr_zero, "Alloc 0 bytes should return NULL");
    
    es_mem_v2_deinit();
}

static void test_calloc(void) {
    printf("\n--- Testing Calloc ---\n");
    
    es_mem_v2_init(NULL);
    
    // 测试calloc
    void *ptr = es_mem_v2_calloc(10, 4);
    TEST_ASSERT_NOT_NULL(ptr, "Calloc should succeed");
    
    // 验证内存清零
    uint32_t *data = (uint32_t *)ptr;
    bool all_zero = true;
    for (int i = 0; i < 10; i++) {
        if (data[i] != 0) {
            all_zero = false;
            break;
        }
    }
    TEST_ASSERT(all_zero, "Calloc should zero memory");
    
    // 测试溢出检查
    void *ptr_overflow = es_mem_v2_calloc(1000000, 1000000);
    TEST_ASSERT_NULL(ptr_overflow, "Calloc should detect overflow");
    
    es_mem_v2_free(ptr);
    es_mem_v2_deinit();
}

static void test_realloc(void) {
    printf("\n--- Testing Realloc ---\n");
    
    es_mem_v2_init(NULL);
    
    // 从NULL开始分配
    void *ptr = es_mem_v2_realloc(NULL, 100);
    TEST_ASSERT_NOT_NULL(ptr, "Realloc from NULL should succeed");
    
    // 写入数据
    memset(ptr, 0xAA, 100);
    
    // 扩大内存
    void *ptr2 = es_mem_v2_realloc(ptr, 200);
    TEST_ASSERT_NOT_NULL(ptr2, "Realloc to larger size should succeed");
    
    // 验证数据保留
    uint8_t *data = (uint8_t *)ptr2;
    TEST_ASSERT_EQUAL(0xAA, data[0], "Data should be preserved after expand");
    
    // 缩小内存
    void *ptr3 = es_mem_v2_realloc(ptr2, 50);
    TEST_ASSERT_NOT_NULL(ptr3, "Realloc to smaller size should succeed");
    
    // 释放内存
    void *ptr4 = es_mem_v2_realloc(ptr3, 0);
    TEST_ASSERT_NULL(ptr4, "Realloc to 0 should return NULL");
    
    es_mem_v2_deinit();
}

static void test_aligned_alloc(void) {
    printf("\n--- Testing Aligned Alloc ---\n");
    
    es_mem_v2_init(NULL);
    
    // 测试4字节对齐
    void *ptr1 = es_mem_v2_aligned_alloc(100, 4);
    TEST_ASSERT_NOT_NULL(ptr1, "Aligned alloc 4 should succeed");
    TEST_ASSERT_EQUAL(0, (uintptr_t)ptr1 % 4, "Pointer should be 4-byte aligned");
    
    // 测试更大的对齐（应该失败）
    void *ptr2 = es_mem_v2_aligned_alloc(100, 16);
    TEST_ASSERT_NULL(ptr2, "Large alignment should fail in embedded mode");
    
    // 测试无效对齐
    void *ptr_invalid = es_mem_v2_aligned_alloc(100, 7);
    TEST_ASSERT_NULL(ptr_invalid, "Invalid alignment should fail");
    
    if (ptr1) es_mem_v2_free(ptr1);
    if (ptr2) es_mem_v2_free(ptr2);
    es_mem_v2_deinit();
}

static void test_stats(void) {
    printf("\n--- Testing Statistics ---\n");
    
    es_mem_config_t config = {
        .enable_debug = false,
        .enable_stats = true,
        .total_heap_size = 0
    };
    
    es_mem_v2_init(&config);
    
    // 获取初始统计
    es_mem_stats_t stats;
    int result = es_mem_v2_get_stats(&stats);
    TEST_ASSERT_EQUAL(0, result, "Get stats should succeed");
    TEST_ASSERT_EQUAL(0, stats.alloc_count, "Initial alloc count should be 0");
    
    // 分配内存
    void *ptr1 = es_mem_v2_alloc(100);
    void *ptr2 = es_mem_v2_alloc(200);
    
    es_mem_v2_get_stats(&stats);
    TEST_ASSERT_EQUAL(2, stats.alloc_count, "Alloc count should be 2");
    TEST_ASSERT(stats.used_size > 0, "Used size should be positive");
    TEST_ASSERT_EQUAL(2, stats.active_blocks, "Active blocks should be 2");
    
    // 释放内存
    es_mem_v2_free(ptr1);
    es_mem_v2_free(ptr2);
    
    es_mem_v2_get_stats(&stats);
    TEST_ASSERT_EQUAL(2, stats.free_count, "Free count should be 2");
    TEST_ASSERT_EQUAL(0, stats.active_blocks, "Active blocks should be 0");
    
    // 测试统计打印
    printf("--- Stats Dump ---\n");
    es_mem_v2_dump_stats();
    es_mem_v2_dump_pools();
    
    es_mem_v2_deinit();
}

static void test_debug_features(void) {
    printf("\n--- Testing Debug Features ---\n");
    
    es_mem_config_t config = {
        .enable_debug = true,
        .enable_stats = true,
        .total_heap_size = 0
    };
    
    es_mem_v2_init(&config);
    
    // 使用调试分配
    void *ptr = es_mem_v2_alloc_debug(100);
    TEST_ASSERT_NOT_NULL(ptr, "Debug alloc should succeed");
    
    // 获取块信息
    es_mem_block_info_t info;
    int result = es_mem_v2_get_block_info(ptr, &info);
    TEST_ASSERT_EQUAL(0, result, "Get block info should succeed");
    TEST_ASSERT_EQUAL(100, info.size, "Block size should match request");
    TEST_ASSERT(info.actual_size >= 100, "Actual size should be >= requested");
    
    // 测试泄漏检测
    int leaks = es_mem_v2_check_leaks();
    TEST_ASSERT(leaks > 0, "Should detect memory leak");
    
    es_mem_v2_free(ptr);
    
    leaks = es_mem_v2_check_leaks();
    TEST_ASSERT_EQUAL(0, leaks, "Should not detect leaks after free");
    
    // 测试完整性检查
    result = es_mem_v2_verify_integrity();
    TEST_ASSERT_EQUAL(0, result, "Integrity check should pass");
    
    es_mem_v2_deinit();
}

static void test_edge_cases(void) {
    printf("\n--- Testing Edge Cases ---\n");
    
    es_mem_v2_init(NULL);
    
    // 测试NULL指针操作
    es_mem_v2_free(NULL);  // 应该不崩溃
    es_mem_v2_free_safe(NULL);  // 应该不崩溃
    
    void *null_ptr = NULL;
    es_mem_v2_free_safe(&null_ptr);
    TEST_ASSERT_NULL(null_ptr, "Safe free should handle NULL");
    
    // 测试安全释放
    void *ptr = es_mem_v2_alloc(100);
    TEST_ASSERT_NOT_NULL(ptr, "Alloc should succeed");
    
    es_mem_v2_free_safe((void**)&ptr);
    TEST_ASSERT_NULL(ptr, "Safe free should set pointer to NULL");
    
    // 测试边界大小
    void *ptr1 = es_mem_v2_alloc(1);
    TEST_ASSERT_NOT_NULL(ptr1, "Alloc 1 byte should succeed");
    
    void *ptr2 = es_mem_v2_alloc(2048);  // 大内存块
    TEST_ASSERT_NOT_NULL(ptr2, "Large alloc should succeed");
    
    // 测试块大小获取
    int block_size = es_mem_v2_ptr_block_size(ptr1);
    TEST_ASSERT(block_size > 0, "Block size should be positive");
    
    es_mem_v2_free(ptr1);
    es_mem_v2_free(ptr2);
    es_mem_v2_deinit();
}

static void test_memory_pools(void) {
    printf("\n--- Testing Memory Pools ---\n");
    
    es_mem_v2_init(NULL);
    
    // 测试不同大小的分配（应该从不同池分配）
    void *ptrs[8];
    int sizes[] = {16, 32, 64, 128, 256, 512, 1024, 2048};
    
    for (int i = 0; i < 8; i++) {
        ptrs[i] = es_mem_v2_alloc(sizes[i]);
        TEST_ASSERT_NOT_NULL(ptrs[i], "Pool allocation should succeed");
        
        // 验证数据能正常写入
        memset(ptrs[i], 0x55 + i, sizes[i]);
    }
    
    // 验证数据完整性
    for (int i = 0; i < 8; i++) {
        uint8_t *data = (uint8_t*)ptrs[i];
        TEST_ASSERT_EQUAL(0x55 + i, data[0], "Pool data should be preserved");
    }
    
    // 释放所有内存
    for (int i = 0; i < 8; i++) {
        es_mem_v2_free(ptrs[i]);
    }
    
    // 再次分配，测试池的重用
    for (int i = 0; i < 8; i++) {
        ptrs[i] = es_mem_v2_alloc(sizes[i]);
        TEST_ASSERT_NOT_NULL(ptrs[i], "Pool reallocation should succeed");
    }
    
    for (int i = 0; i < 8; i++) {
        es_mem_v2_free(ptrs[i]);
    }
    
    es_mem_v2_deinit();
} 